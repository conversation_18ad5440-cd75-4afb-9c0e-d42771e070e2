# Python image
FROM python:3.9.18-alpine3.19

# Upgrade system packages to address vulnerabilities
RUN apk update && apk upgrade

# Working directory
WORKDIR /app

# requirements.txt
COPY requirements.txt requirements.txt

# Dependencies
RUN pip install -r requirements.txt

# Project code
COPY . .
# # collect static files
# RUN python manage.py collectstatic --noinput

# # run migrations
# RUN python manage.py makemigrations
# RUN python manage.py migrate

# Environment variable for port
ENV PORT=8000

# Port for Django development server
EXPOSE $PORT

# Modified CMD to use the PORT environment variable
CMD ["sh", "-c", "python manage.py runserver 0.0.0.0:${PORT}"]