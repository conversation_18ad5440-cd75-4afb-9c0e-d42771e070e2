# Define the HTML content for new complaint submission
from base.services.emails import send_email


new_complaint_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>New Complaint Submitted</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">New Complaint Submitted</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Complaint Details</h2>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145C9E26;"><strong>Complaint ID:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{complaint_id}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145C9E26;"><strong>Date Submitted:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{complaint_datetime}</td>
                    </tr>
                    
                </table>
                <h3 style="color: #145C9E; margin-bottom: 15px;">Complaint Summary:</h3>
                <p style="background-color: #E6FFE6; padding: 20px; border-radius: 6px; margin-bottom: 30px;">{complaint_summary}</p>
                
                <p style="margin-top: 30px;">Please review the complaint and take appropriate action.</p>
                
                <p>Best regards,<br>Complaints Department</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


def send_new_complaint_email(
    recipient_email,
    complaint_id,
    complaint_datetime,
    patient_name,
    complaint_summary,
):
    subject = "New Complaint Submitted"
    html_body = new_complaint_email.format(
        complaint_id=complaint_id,
        complaint_datetime=complaint_datetime,
        # patient_name=patient_name,
        complaint_summary=complaint_summary,
    )

    data = {
        "email": recipient_email,
    }

    # Send the email
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
