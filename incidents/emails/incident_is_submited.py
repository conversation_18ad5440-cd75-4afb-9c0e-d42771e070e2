from base.services.emails import send_email
from datetime import datetime
import pytz

from base.services.format_date import convert_created_at_to_user_timezone
from base.services.logging.logger import LoggingService

logging_service = LoggingService()

incident_submission_confirmation_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Incident Submission Confirmation</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Quality/Risk Management</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Incident Submission Confirmation</h2>
                <p style="margin-bottom: 15px;">Dear {first_name},</p>
                <p style="margin-bottom: 20px;">Thank you for submitting the incident on {date_created}. The Quality/Risk Management Department will evaluate the incident and assign it to the appropriate department for further review and actions.</p>
                <p style="margin-bottom: 30px;">Thank you</p>
                <p>Best regards,<br>Quality/Risk Management</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply.</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


def send_incident_submission_confirmation(
    recipient_email,
    first_name,
    date_created,
    user,
):
    try:
        subject = "Incident Submission Confirmation"
        html_body = incident_submission_confirmation_email.format(
            first_name=first_name,
            date_created=convert_created_at_to_user_timezone(date_created, user),
        )

        data = {
            "first_name_Value": first_name,
            "email_Value": recipient_email,
        }

        send_email(
            recipient_email=recipient_email,
            data=data,
            html_body=html_body,
            subject=subject,
        )

    except Exception as e:
        loffing_service.log_error(e)
