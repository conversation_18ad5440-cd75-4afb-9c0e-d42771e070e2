# Define the HTML content for complaint report
from base.services.emails import send_email

complaint_report_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Complaint Report</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #FF5733;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Complaint Report</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #FF5733; margin-bottom: 20px;">Complaint Summary</h2>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #FF573326;"><strong>Complaint ID:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{complaint_id}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #FF573326;"><strong>Date Filed:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{complaint_datetime}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #FF573326;"><strong>Nature of Complaint:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{complaint_nature}</td>
                    </tr>
                </table>
                <h3 style="color: #FF5733; margin-bottom: 15px;">Complaint Details:</h3>
                <p style="background-color: #FFEDEB; padding: 20px; border-radius: 6px; margin-bottom: 30px;">{complaint_details}</p>
                
                <p style="margin-top: 30px;">Please address this complaint as soon as possible.</p>
                <p style="margin-bottom: 20px;">If you have any questions or need further information, please contact the complainant or reply to this email.</p>
                <p>Best regards,<br>Complaints Department</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


def send_complaint_report_to_department(
    recipient_email,
    complaint_id,
    complaint_datetime,
    complaint_nature,
    complaint_details,
):
    subject = "New Complaint Report Assigned"
    html_body = complaint_report_email.format(
        complaint_id=complaint_id,
        complaint_datetime=complaint_datetime,
        complaint_nature=complaint_nature,
        complaint_details=complaint_details,
    )

    data = {
        "email": recipient_email,
    }

    # Send the email
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
