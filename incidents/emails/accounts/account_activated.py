from base.services.emails import send_email


account_reactivation_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Account Reactivation Notification</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Account Reactivation</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Hello, {first_name}</h2>
                <p style="margin-bottom: 15px;">Good news! Your account has been reactivated by an administrator. You can now access your account and its features as usual.</p>
                <p>You can log in to your account by clicking the button below:</p>
                <table cellpadding="0" cellspacing="0" border="0" style="margin: 20px auto;">
                    <tr>
                        <td style="background-color: #F87C47; border-radius: 4px; text-align: center;">
                            <a href="{login_link}" style="display: inline-block; padding: 14px 30px; color: white; text-decoration: none; font-weight: bold;">Log In</a>
                        </td>
                    </tr>
                </table>
                <p style="margin-top: 30px;">We’re happy to have you back and look forward to your continued engagement.</p>
                <p>Best regards,<br>The Admin Team</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


# Function to send account reactivation email
def send_account_reactivation_email(recipient_email, first_name, login_link):
    subject = "Your Account Has Been Reactivated"
    html_body = account_reactivation_email.format(
        first_name=first_name,
        login_link=login_link,
    )

    send_email(
        recipient_email=recipient_email,
        data={"first_name": first_name, "email": recipient_email},
        html_body=html_body,
        subject=subject,
    )
