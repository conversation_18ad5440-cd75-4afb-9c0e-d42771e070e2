from base.services.emails import send_email


account_deactivation_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Account Deactivation Notification</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Account Deactivation</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Hello, {first_name}</h2>
                <p style="margin-bottom: 15px;">We regret to inform you that your account has been deactivated by an administrator. This means you will no longer have access to your account or its associated features.</p>
                <p>If you believe this is an error or would like to appeal this action, please contact our support team:</p>
                <table cellpadding="0" cellspacing="0" border="0" style="margin: 20px auto;">
                    <tr>
                        <td style="background-color: #F87C47; border-radius: 4px; text-align: center;">
                            <a href="{support_link}" style="display: inline-block; padding: 14px 30px; color: white; text-decoration: none; font-weight: bold;">Contact Support</a>
                        </td>
                    </tr>
                </table>
                <p style="margin-top: 30px;">We apologize for any inconvenience caused and thank you for your understanding.</p>
                <p>Best regards,<br>The Admin Team</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


# Function to send account deactivation email
def send_account_deactivation_email(recipient_email, first_name, support_link):
    subject = "Your Account Has Been Deactivated"
    html_body = account_deactivation_email.format(
        first_name=first_name,
        support_link=support_link,
    )

    send_email(
        recipient_email=recipient_email,
        data={"first_name": first_name, "email": recipient_email},
        html_body=html_body,
        subject=subject,
    )
