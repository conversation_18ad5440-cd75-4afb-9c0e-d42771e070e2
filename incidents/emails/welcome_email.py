# Define the welcome email HTML content as a variable with placeholders
from base.services.emails import send_email


welcome_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Welcome to Our Platform</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Welcome to Our Platform</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Hello, {first_name}!</h2>
                <p style="margin-bottom: 15px;">We are excited to have you on board. Your account has been created successfully, and you can now log in using the details below:</p>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Email:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{email}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Password:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{password}</td>
                    </tr>
                </table>
                
                <p>Best regards,<br>CHMC Quality/Risk Management Software</p>
            </td>
        </tr>
        <td style="background-color: #F87C47; border-radius: 4px; text-align: center;">
            <a href="{login_link}" style="display: inline-block; padding: 14px 30px; color: white; text-decoration: none; font-weight: bold;">Click here to log in</a>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


# Function to send welcome email
def send_welcome_email(recipient_email, first_name, password, login_link):
    subject = "Welcome to CHMC Quality/Risk Management Platform!"
    html_body = welcome_email.format(
        first_name=first_name,
        email=recipient_email,
        password=password,
        login_link=login_link,
    )

    send_email(
        recipient_email=recipient_email,
        data={"first_name": first_name, "email": recipient_email},
        html_body=html_body,
        subject=subject,
    )
