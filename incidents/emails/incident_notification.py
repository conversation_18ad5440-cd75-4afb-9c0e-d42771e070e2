from base.services.emails import send_email
from datetime import datetime
import pytz
from accounts.models import Profile
from base.services.format_date import convert_created_at_to_user_timezone


incident_notification_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Incident Notification</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Quality/Risk Management</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Incident Submission Notification</h2>
                <p style="margin-bottom: 15px;">Dear {first_name},</p>
                <p style="margin-bottom: 20px;">An incident report has been submitted from <strong>{facility}</strong>. Please review the details below:</p>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Incident ID:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{incident_id}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Incident Type:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{incident_type}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Facility:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{facility}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Date Created:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{date_created}</td>
                    </tr>
                </table>
                <p style="margin-bottom: 30px;">Please review the incident and take the necessary actions.</p>
                <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;">
                    <tr>
                        <td style="background-color: #F87C47; border-radius: 4px; text-align: center;">
                            <a href="{incident_link}" style="display: inline-block; padding: 14px 30px; color: white; text-decoration: none; font-weight: bold;">Click here to view</a>
                        </td>
                    </tr>
                </table>
                <p style="margin-top: 30px;">If you have any questions, please contact the responsible team.</p>
                <p>Best regards,<br>Quality/Risk Management</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply.</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


def send_incident_notification(
    recipient_email,
    first_name,
    incident_id,
    date_created,
    created_by,
    incident_type,
    user,
    # get user, and get his profile and his facility
):

    try:
        profile = Profile.objects.filter(user=created_by).first()
        facility = profile.facility if profile else None
        facility_name = facility.name if facility else None
        incident_date_and_time = convert_created_at_to_user_timezone(date_created, user)
    except Profile.DoesNotExist:
        pass
    subject = "New Incident Submitted"
    html_body = incident_notification_email.format(
        first_name=first_name,
        facility=facility_name if facility_name else "No facility found",
        incident_id=incident_id,
        date_created=incident_date_and_time,
        incident_link="https://q-control.csrlimited.com/",
        incident_type=incident_type,
    )

    data = {
        "first_name_Value": first_name,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
