# Define the password reset request email with reset code
from base.services.emails import send_email


password_reset_request_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Password Reset Request</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Password Reset Request</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Hello, {first_name}</h2>
                <p style="margin-bottom: 15px;">We received a request to reset your password. Please use the code below to reset your password:</p>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Reset Code:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; font-size: 18px;"><strong>{reset_code}</strong></td>
                    </tr>
                </table>
                <p>If you did not request a password reset, please ignore this email or contact support.</p>
                <p>Best regards,<br>The Support Team</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


# Function to send password reset request email
def send_password_reset_request_email(recipient_email, first_name, reset_code):
    subject = "Password Reset Request"
    html_body = password_reset_request_email.format(
        first_name=first_name,
        email=recipient_email,
        reset_code=reset_code,
    )

    send_email(
        recipient_email=recipient_email,
        data={"first_name": first_name, "email": recipient_email},
        html_body=html_body,
        subject=subject,
    )
