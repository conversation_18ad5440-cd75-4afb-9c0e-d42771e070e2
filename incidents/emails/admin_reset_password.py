# Define the admin-initiated password reset email HTML content
from base.services.emails import send_email


admin_password_reset_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Password Reset Notification</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Password Reset</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Hello, {first_name}</h2>
                <p style="margin-bottom: 15px;">An administrator has reset your password. You can now log in with your new credentials:</p>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Email:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{email}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>New Password:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{password}</td>
                    </tr>
                </table>
                <p>You can log in by clicking the button below:</p>
                <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;">
                    <tr>
                        <td style="background-color: #F87C47; border-radius: 4px; text-align: center;">
                            <a href="{login_link}" style="display: inline-block; padding: 14px 30px; color: white; text-decoration: none; font-weight: bold;">Log In</a>
                        </td>
                    </tr>
                </table>
                <p style="margin-top: 30px;">For security reasons, we recommend changing your password once logged in.</p>
                <p>Best regards,<br>The Admin Team</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


# Function to send admin-initiated password reset email
def send_admin_password_reset_email(recipient_email, first_name, password, login_link):
    subject = "Your Password Has Been Reset"
    html_body = admin_password_reset_email.format(
        first_name=first_name,
        email=recipient_email,
        password=password,
        login_link=login_link,
    )

    send_email(
        recipient_email=recipient_email,
        data={"first_name": first_name, "email": recipient_email},
        html_body=html_body,
        subject=subject,
    )
