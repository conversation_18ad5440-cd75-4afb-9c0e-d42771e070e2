# Define the HTML content as a variable with placeholders
from base.services.emails import send_email


password_reset_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Incident Report</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Quality/Risk Management</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Incident Report</h2>
                <p style="margin-bottom: 15px;">Dear {first_name},</p>
                <p style="margin-bottom: 20px;">The following incident report has been assigned to you by <strong>{sender_department}</strong>,  which needs to be reviewed in the Quality Control Program. Please take appropriate action.</p>
                <p style="margin-bottom: 30px;">Incident details below:</p>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Incident Id:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{incident_id}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Incident Type:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{incident_type}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Date created:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{incident_datetime}</td>
                    </tr>
                </table>
                <h3 style="color: #145C9E; margin-bottom: 15px;">Incident Preview:</h3>
                <p style="background-color: #FFF1EB; padding: 20px; border-radius: 6px; margin-bottom: 30px;">{incident_preview}</p>
                <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;">
                    <tr>
                        <td style="background-color: #F87C47; border-radius: 4px; text-align: center;">
                            <a href="{incident_link}" style="display: inline-block; padding: 14px 30px; color: white; text-decoration: none; font-weight: bold;">View incident reports</a>
                        </td>
                    </tr>
                </table>
                <p style="margin-top: 30px;">Please take necessary actions and respond to this incident as soon as possible.</p>
                <p style="margin-bottom: 20px;">If you have any questions or need further information, please contact the {sender_department} department or reply to this email.</p>
                <p>Best regards,<br>Quality/Risk Management Software Department</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


def send_to_department_email(
    recipient_email,
    first_name,
    sender_department,
    incident_datetime,
    incident_preview,
    incident_id,
    incident_type,
    incident_link,
):
    subject = "Incident report needs your attention"
    html_body = password_reset_email.format(
        first_name=first_name,
        email=recipient_email,
        sender_department=sender_department,
        incident_datetime=incident_datetime,
        incident_preview=incident_preview,
        incident_link=incident_link,
        incident_id=incident_id,
        incident_type=incident_type,
    )

    data = {
        "first_name_Value": first_name,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
