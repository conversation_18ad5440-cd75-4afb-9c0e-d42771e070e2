from base.services.emails import send_email
from datetime import datetime
import pytz
from accounts.models import Profile


support_notification_email = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Ticket Notification</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f3f3f3; color: #1E1E1E;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <tr>
            <td style="padding: 30px 20px; text-align: center; background-color: #145C9E;">
                <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">Quality/Risk Management</h1>
            </td>
        </tr>
        <tr>
            <td style="padding: 30px;">
                <h2 style="color: #145C9E; margin-bottom: 20px;">Support Ticket</h2>
                <p style="margin-bottom: 15px;">Dear {first_name},</p>
                <p style="margin-bottom: 20px;">A Ticket report has been submitted from the <strong>{facility}</strong>. Please review the details below:</p>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Ticket ID:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{support_ticket_id}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Facility:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{facility}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Date Created:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{date_created}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Title:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{ticket_title}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Description:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{ticket_description}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Priority:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{ticket_priority}</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #f3f3f3; background-color: #145b9e26;"><strong>Raised by:</strong></td>
                        <td style="padding: 12px; border: 1px solid #f3f3f3;">{created_by}</td>
                    </tr>
                </table>
                <p style="margin-bottom: 30px;">Please review the ticket and take the necessary actions.</p>
                <table cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;">
                    <tr>
                        <td style="background-color: #F87C47; border-radius: 4px; text-align: center;">
                            <a href="{ticket_link}" style="display: inline-block; padding: 14px 30px; color: white; text-decoration: none; font-weight: bold;">Click here to view</a>
                        </td>
                    </tr>
                </table>
                <p style="margin-top: 30px;">If you have any questions, please contact the responsible team.</p>
                <p>Best regards,<br>Quality/Risk Management</p>
            </td>
        </tr>
        <tr>
            <td style="padding: 20px; text-align: center; background-color: #f3f3f3; color: #909090;">
                <p style="margin: 0; font-size: 14px;">This is an automated message. Please do not reply.</p>
            </td>
        </tr>
    </table>
</body>
</html>
"""


def send_support_ticket_notification(
    recipient_email,
    first_name,
    ticket_title,
    ticket_description,
    ticket_priority,
    support_ticket_id,
    date_created,
    created_by,
    group,
    # get user, and get his profile and his facility
):

    try:
        profile = Profile.objects.filter(user=created_by).first()
        facility = profile.facility if profile else None
        facility_name = facility.name if facility else None
        cst = pytz.FixedOffset(-360)
        ticket_date_and_time = (
            datetime.fromisoformat(str(date_created))
            .astimezone(cst)
            .strftime("%A, %B %d, %Y at %H:%M:%S CST")
        )
    except Profile.DoesNotExist:
        pass
    subject = "Support Ticket successfully raised"
    html_body = support_notification_email.format(
        first_name=first_name,
        ticket_title=ticket_title,
        ticket_description=ticket_description,
        ticket_priority=ticket_priority,
        facility=facility_name if facility_name else "No facility found",
        support_ticket_id=support_ticket_id,
        date_created=ticket_date_and_time,
        created_by=created_by,
        ticket_link="https://q-control.csrlimited.com/",
        group=group,
    )

    data = {
        "first_name_Value": first_name,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
