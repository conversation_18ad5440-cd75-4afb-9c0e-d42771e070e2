# incidents permissions

from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse


class IncidentPermissions:
    def __init__(self, user, incident):
        self.user = user
        self.incident = incident
        self.logging_service = LoggingService()

    def can_send_for_review(self) -> RepositoryResponse:
        """
        Check if the user has permission to send the incident for review

        User can send for a review if:
        1. User is in a group Superuser
        1. User is in a group Quality/Risk Manager in the same facility as the incident
        """
