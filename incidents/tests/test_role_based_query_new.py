from django.test import TestCase
from django.contrib.auth.models import User

from accounts.models import Profile
from adverse_drug_reaction.models import AdverseDrugReaction
from base.models import Facility, Department
from incidents.services.role_based_query import RoleBasedIncidentQuery
from tasks.models import ReviewGroups, ReviewProcess, ReviewProcessTasks


class RoleBasedIncidentQuerySuperuserTest(TestCase):
    """Test cases for get_incidents_by_superuser method"""

    def setUp(self):
        """Set up test data"""
        self.role_query = RoleBasedIncidentQuery()
        self.user = User.objects.create_user(
            username="superuser", email="<EMAIL>", password="testpass123"
        )

        self.facility1 = Facility.objects.create(
            name="Test Facility 1", address="123 Test St"
        )

        self.facility2 = Facility.objects.create(
            name="Test Facility 2", address="456 Test Ave"
        )

        self.incident1 = AdverseDrugReaction.objects.create(
            report_facility=self.facility1,
            patient_type="Test Patient",
            created_by=self.user,
        )

        self.incident2 = AdverseDrugReaction.objects.create(
            report_facility=self.facility2,
            patient_type="Test Patient 2",
            created_by=self.user,
        )

        # Incident from different facility (should also be included for superuser)
        self.facility3 = Facility.objects.create(
            name="Other Facility", address="789 Other St"
        )
        self.incident3 = AdverseDrugReaction.objects.create(
            report_facility=self.facility3,
            patient_type="Other Patient",
            created_by=self.user,
        )

    def test_get_incidents_by_superuser_success(self):
        """Test successful retrieval of ALL incidents by superuser"""
        incidents = self.role_query.get_incidents_by_superuser(
            AdverseDrugReaction, self.user
        )

        self.assertEqual(incidents.count(), 3)  # All incidents should be returned

        incident_ids = list(incidents.values_list("id", flat=True))
        self.assertIn(self.incident1.id, incident_ids)
        self.assertIn(self.incident2.id, incident_ids)
        self.assertIn(self.incident3.id, incident_ids)

    def test_get_incidents_by_superuser_no_incidents(self):
        """Test when there are no incidents in the system"""
        # Delete all incidents
        AdverseDrugReaction.objects.all().delete()

        incidents = self.role_query.get_incidents_by_superuser(
            AdverseDrugReaction, self.user
        )

        self.assertEqual(incidents.count(), 0)


class RoleBasedIncidentQueryAdminTest(TestCase):
    """Test cases for get_incidents_by_admin method"""

    def setUp(self):
        """Set up test data"""
        self.role_query = RoleBasedIncidentQuery()
        self.user = User.objects.create_user(
            username="admin", email="<EMAIL>", password="testpass123"
        )

        self.facility1 = Facility.objects.create(
            name="Admin Facility 1", address="123 Admin St"
        )

        self.facility2 = Facility.objects.create(
            name="Admin Facility 2", address="456 Admin Ave"
        )

        self.profile = Profile.objects.create(user=self.user, facility=self.facility1)
        self.profile.access_to_facilities.add(self.facility1, self.facility2)

        self.incident1 = AdverseDrugReaction.objects.create(
            report_facility=self.facility1,
            patient_type="Admin Patient 1",
            created_by=self.user,
        )

        self.incident2 = AdverseDrugReaction.objects.create(
            report_facility=self.facility2,
            patient_type="Admin Patient 2",
            created_by=self.user,
        )

    def test_get_incidents_by_admin_success(self):
        """Test successful retrieval of incidents by admin"""
        incidents = self.role_query.get_incidents_by_admin(
            AdverseDrugReaction, self.user
        )

        self.assertEqual(incidents.count(), 2)

    def test_get_incidents_by_admin_no_facility(self):
        """Test when admin profile has no facility"""
        self.profile.facility = None
        self.profile.save()

        incidents = self.role_query.get_incidents_by_admin(
            AdverseDrugReaction, self.user
        )

        self.assertEqual(incidents.count(), 0)


class RoleBasedIncidentQueryManagerTest(TestCase):
    """Test cases for get_incidents_by_manager method"""

    def setUp(self):
        """Set up test data"""
        self.role_query = RoleBasedIncidentQuery()
        self.user = User.objects.create_user(
            username="manager", email="<EMAIL>", password="testpass123"
        )

        self.facility = Facility.objects.create(
            name="Manager Facility", address="123 Manager St"
        )

        self.department1 = Department.objects.create(
            name="Department 1", facility=self.facility, header_of_department=self.user
        )

        self.department2 = Department.objects.create(
            name="Department 2", facility=self.facility
        )

        self.profile = Profile.objects.create(
            user=self.user, facility=self.facility, department=self.department1
        )
        self.profile.access_to_department.add(self.department1)

        self.incident1 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            department=self.department1,
            patient_type="Manager Patient 1",
            created_by=self.user,
        )

        self.incident2 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            department=self.department2,
            patient_type="Manager Patient 2",
            created_by=self.user,
        )

    def test_get_incidents_by_manager_success(self):
        """Test successful retrieval of incidents by manager"""
        incidents = self.role_query.get_incidents_by_manager(
            AdverseDrugReaction, self.user, self.department1
        )

        self.assertEqual(incidents.count(), 1)
        self.assertEqual(incidents.first().id, self.incident1.id)

    def test_get_incidents_by_manager_with_accessible_to(self):
        """Test manager with accessible_to parameter"""
        incidents = self.role_query.get_incidents_by_manager(
            AdverseDrugReaction,
            self.user,
            self.department1,
            accessible_to="Department 1",
        )

        self.assertEqual(incidents.count(), 1)

    def test_get_incidents_by_manager_no_department(self):
        """Test when no department is provided"""
        incidents = self.role_query.get_incidents_by_manager(
            AdverseDrugReaction, self.user, None
        )

        self.assertEqual(incidents.count(), 0)

    def test_get_incidents_by_manager_no_facility(self):
        """Test when manager profile has no facility"""
        self.profile.facility = None
        self.profile.save()

        incidents = self.role_query.get_incidents_by_manager(
            AdverseDrugReaction, self.user, self.department1
        )

        self.assertEqual(incidents.count(), 0)


class RoleBasedIncidentQueryDirectorTest(TestCase):
    """Test cases for get_incidents_by_director method"""

    def setUp(self):
        """Set up test data"""
        self.role_query = RoleBasedIncidentQuery()
        self.user = User.objects.create_user(
            username="director", email="<EMAIL>", password="testpass123"
        )

        self.facility = Facility.objects.create(
            name="Director Facility", address="123 Director St"
        )

        self.department = Department.objects.create(
            name="Director Department",
            facility=self.facility,
            header_of_department=self.user,
        )

        self.incident1 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            department=self.department,
            patient_type="Director Patient 1",
            created_by=self.user,
        )

        self.incident2 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            patient_type="Director Patient 2",
            created_by=self.user,
        )

    def test_get_incidents_by_director_success(self):
        """Test successful retrieval of incidents by director"""
        incidents = self.role_query.get_incidents_by_director(
            AdverseDrugReaction, self.facility
        )

        self.assertEqual(incidents.count(), 2)

    def test_get_incidents_by_director_with_accessible_to(self):
        """Test director with accessible_to parameter"""
        incidents = self.role_query.get_incidents_by_director(
            AdverseDrugReaction,
            self.facility,
            self.user,
            accessible_to="Director Department",
        )

        self.assertEqual(incidents.count(), 1)

    def test_get_incidents_by_director_no_facility(self):
        """Test when no facility is provided"""
        incidents = self.role_query.get_incidents_by_director(AdverseDrugReaction, None)

        self.assertEqual(incidents.count(), 0)


class RoleBasedIncidentQueryUserTest(TestCase):
    """Test cases for get_incidents_by_user method"""

    def setUp(self):
        """Set up test data"""
        self.role_query = RoleBasedIncidentQuery()
        self.user1 = User.objects.create_user(
            username="user1", email="<EMAIL>", password="testpass123"
        )

        self.user2 = User.objects.create_user(
            username="user2", email="<EMAIL>", password="testpass123"
        )

        self.facility = Facility.objects.create(
            name="User Facility", address="123 User St"
        )

        self.incident1 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            patient_type="User Patient 1",
            created_by=self.user1,
        )

        self.incident2 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            patient_type="User Patient 2",
            created_by=self.user2,
        )

    def test_get_incidents_by_user_success(self):
        """Test successful retrieval of incidents by user"""
        incidents = self.role_query.get_incidents_by_user(
            AdverseDrugReaction, self.user1
        )

        self.assertEqual(incidents.count(), 1)
        self.assertEqual(incidents.first().id, self.incident1.id)

    def test_get_incidents_by_user_no_incidents(self):
        """Test when user has no incidents"""
        new_user = User.objects.create_user(
            username="newuser", email="<EMAIL>", password="testpass123"
        )

        incidents = self.role_query.get_incidents_by_user(AdverseDrugReaction, new_user)

        self.assertEqual(incidents.count(), 0)


class RoleBasedIncidentQueryReviewersTest(TestCase):
    """Test cases for get_incidents_by_reviewers method"""

    def setUp(self):
        """Set up test data"""
        self.role_query = RoleBasedIncidentQuery()
        self.user1 = User.objects.create_user(
            username="reviewer1", email="<EMAIL>", password="testpass123"
        )

        self.user2 = User.objects.create_user(
            username="reviewer2", email="<EMAIL>", password="testpass123"
        )

        self.facility = Facility.objects.create(
            name="Reviewer Facility", address="123 Reviewer St"
        )

        self.profile1 = Profile.objects.create(user=self.user1, facility=self.facility)

        self.profile2 = Profile.objects.create(user=self.user2, facility=self.facility)

        # Create review groups
        self.review_group1 = ReviewGroups.objects.create(
            title="Review Group 1", description="Test review group 1"
        )
        self.review_group1.members.add(self.profile1)

        self.review_group2 = ReviewGroups.objects.create(
            title="Review Group 2", description="Test review group 2"
        )
        self.review_group2.members.add(self.profile2)

        # Create review process and tasks
        self.review_process = ReviewProcess.objects.create(
            name="Test Review Process", description="Test process"
        )

        self.review_task1 = ReviewProcessTasks.objects.create(
            name="Review Task 1", review_process=self.review_process, status="Pending"
        )
        self.review_task1.reviewers.add(self.profile1)

        self.review_task2 = ReviewProcessTasks.objects.create(
            name="Review Task 2", review_process=self.review_process, status="Pending"
        )
        self.review_task2.review_groups.add(self.review_group2)

        # Create incidents
        self.incident1 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            patient_type="Reviewer Patient 1",
            created_by=self.user1,
            review_process=self.review_process,
        )
        self.incident1.review_tasks.add(self.review_task1)

        self.incident2 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            patient_type="Reviewer Patient 2",
            created_by=self.user2,
            review_process=self.review_process,
        )
        self.incident2.review_tasks.add(self.review_task2)

        # Incident with no review tasks (should not be included)
        self.incident3 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            patient_type="No Review Patient",
            created_by=self.user1,
        )

    def test_get_incidents_by_reviewers_direct_reviewer(self):
        """Test retrieval of incidents where user is a direct reviewer"""
        incidents = self.role_query.get_incidents_by_reviewers(
            AdverseDrugReaction, self.user1
        )

        self.assertEqual(incidents.count(), 1)
        self.assertEqual(incidents.first().id, self.incident1.id)

    def test_get_incidents_by_reviewers_group_member(self):
        """Test retrieval of incidents where user is a member of review group"""
        incidents = self.role_query.get_incidents_by_reviewers(
            AdverseDrugReaction, self.user2
        )

        self.assertEqual(incidents.count(), 1)
        self.assertEqual(incidents.first().id, self.incident2.id)

    def test_get_incidents_by_reviewers_multiple_roles(self):
        """Test when user has multiple reviewer roles"""
        # Add user1 to review group 2
        self.review_group2.members.add(self.profile1)

        incidents = self.role_query.get_incidents_by_reviewers(
            AdverseDrugReaction, self.user1
        )

        self.assertEqual(incidents.count(), 2)  # Both incidents should be returned

        incident_ids = list(incidents.values_list("id", flat=True))
        self.assertIn(self.incident1.id, incident_ids)
        self.assertIn(self.incident2.id, incident_ids)

    def test_get_incidents_by_reviewers_no_profile(self):
        """Test when user has no profile"""
        user_without_profile = User.objects.create_user(
            username="noprofile", email="<EMAIL>", password="testpass123"
        )

        incidents = self.role_query.get_incidents_by_reviewers(
            AdverseDrugReaction, user_without_profile
        )

        self.assertEqual(incidents.count(), 0)

    def test_get_incidents_by_reviewers_no_review_tasks(self):
        """Test when user is not assigned to any review tasks"""
        new_user = User.objects.create_user(
            username="noreviews", email="<EMAIL>", password="testpass123"
        )

        new_profile = Profile.objects.create(user=new_user, facility=self.facility)

        incidents = self.role_query.get_incidents_by_reviewers(
            AdverseDrugReaction, new_user
        )

        self.assertEqual(incidents.count(), 0)


class RoleBasedIncidentQueryFilterIncidentsByDepartmentTest(TestCase):
    """Test cases for filter_incidents_by_department method"""

    def setUp(self):
        """Set up test data"""
        self.role_query = RoleBasedIncidentQuery()
        self.user = User.objects.create_user(
            username="deptuser", email="<EMAIL>", password="testpass123"
        )

        self.facility = Facility.objects.create(
            name="Department Facility", address="123 Dept St"
        )

        self.department1 = Department.objects.create(
            name="Accessible Department",
            facility=self.facility,
            header_of_department=self.user,
        )

        self.department2 = Department.objects.create(
            name="Other Department", facility=self.facility
        )

        self.incident1 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            department=self.department1,
            patient_type="Dept Patient 1",
            created_by=self.user,
        )

        self.incident2 = AdverseDrugReaction.objects.create(
            report_facility=self.facility,
            department=self.department2,
            patient_type="Dept Patient 2",
            created_by=self.user,
        )

        # Create a queryset of incidents
        self.incidents = AdverseDrugReaction.objects.filter(
            report_facility=self.facility
        )

    def test_filter_incidents_by_department_success(self):
        """Test successful filtering by department"""
        response = self.role_query.filter_incidents_by_department(
            self.user, self.incidents, "Accessible Department", self.facility
        )

        self.assertTrue(response.success)
        self.assertEqual(response.data.count(), 1)
        self.assertEqual(response.data.first().id, self.incident1.id)

    def test_filter_incidents_by_department_insufficient_access(self):
        """Test when user doesn't have access to department"""
        response = self.role_query.filter_incidents_by_department(
            self.user, self.incidents, "Other Department", self.facility
        )

        self.assertIsNone(response.success)
        self.assertEqual(response.data, [])
        self.assertIn("Insufficient access", response.message)

    def test_filter_incidents_by_department_nonexistent(self):
        """Test when department doesn't exist"""
        response = self.role_query.filter_incidents_by_department(
            self.user, self.incidents, "Nonexistent Department", self.facility
        )

        self.assertIsNone(response.success)
        self.assertEqual(response.data, [])
        self.assertIn("Insufficient access", response.message)
