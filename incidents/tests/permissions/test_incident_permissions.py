"""
Test classes for general incident permissions
"""

from django.test import TransactionTestCase

from base.services.permissions.manage_permissions import PermissionsManagement
from base.tests.factory import UserFactory
from general_patient_visitor.models import GeneralPatientVisitor
from general_patient_visitor.tests.factory import GeneralPatientVisitorFactory
from incidents.services.permissions import IncidentPermissionsService
from tasks.tests.factory import ReviewGroupFactory, ReviewProcessTasksFactory


class TestCreateIncident(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory()
        self.app_label = "general_patient_visitor"

    def test_create_incident_success(self):
        self.user.is_active = True
        self.user.save()

        response = IncidentPermissionsService(
            self.user, self.app_label
        ).can_create_incident()

        if not response.success:
            self.fail(f"User should be able to create incidents, {response.message}")

        self.assertTrue(response.success)

    def test_create_incident_failure(self):
        self.user.is_active = False
        self.user.save()

        response = IncidentPermissionsService(
            self.user, self.app_label
        ).can_create_incident()

        if response.success:
            self.fail(
                f"User should not be able to create incidents, {response.message}"
            )

        self.assertFalse(response.success)


class TestViewIncident(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()

        self.review_task = ReviewProcessTasksFactory()
        self.review_task.review_groups.add(ReviewGroupFactory())

        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory(created_by=self.user)
        self.app_label = "general_patient_visitor"

    def test_view_incident_as_owner(self):
        response = IncidentPermissionsService(
            user=self.incident.created_by,
            app_label=self.app_label,
            incident=self.incident,
        ).can_view_incident()

        if not response.success:
            self.fail(f"User should be able to view incidents, {response.message}")

        self.assertTrue(response.success)

    def test_view_as_reviewer(self):
        self.incident.review_tasks.add(self.review_task)
        reviewer = self.review_task.reviewers.first()
        if not reviewer:
            self.fail("Review task must have at least one reviewer")

        response = IncidentPermissionsService(
            user=reviewer.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_view_incident()

        if not response.success:
            self.fail(f"Reviewer should be able to view incidents, {response.message}")

        self.assertTrue(response.success)

    def test_view_as_review_group_member(self):
        self.incident.review_tasks.add(self.review_task)

        review_group = self.review_task.review_groups.first()
        if not review_group:
            self.fail("Review task must have at least one review group")

        member = review_group.members.first()
        if not member:
            self.fail("Review group must have at least one member")

        response = IncidentPermissionsService(
            user=member.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_view_incident()

        if not response.success:
            self.fail(
                f"Review group member should be able to view incidents, {response.message}"
            )

        self.assertTrue(response.success)

    def test_view_as_view_details_access(self):
        user = UserFactory(is_active=True)

        permission_response = self.permission_manager.add_permissions_to_user(
            user=user,
            model=GeneralPatientVisitor,
            permissions=[{"code_name": "view_details"}],
        )

        if not permission_response.success:
            self.fail(
                f"Failed to add permissions to user, {permission_response.message}"
            )

        # print users permissions for debugging
        print(f"User permissions: {user.get_all_permissions()}")

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_view_incident()

        if not response.success:
            self.fail(
                f"User should be able to view incident details, {response.message}"
            )

        self.assertTrue(response.success)


class TestModifyIncident(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()

        self.review_task = ReviewProcessTasksFactory()
        self.review_task.review_groups.add(ReviewGroupFactory())

        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory(created_by=self.user)
        self.app_label = "general_patient_visitor"

    def test_modify_incident_as_owner(self):
        # Per userFlow.md, regular users cannot modify incidents, even their own
        response = IncidentPermissionsService(
            user=self.incident.created_by,
            app_label=self.app_label,
            incident=self.incident,
        ).can_modify_incident()

        # This should now fail since regular users cannot modify incidents
        self.assertFalse(response.success)
        self.assertIn("Regular users cannot modify incidents", response.message)

    def test_modify_as_reviewer(self):
        self.incident.review_tasks.add(self.review_task)
        reviewer = self.review_task.reviewers.first()
        if not reviewer:
            self.fail("Review task must have at least one reviewer")

        response = IncidentPermissionsService(
            user=reviewer.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_modify_incident()

        if not response.success:
            self.fail(
                f"Reviewer should be able to modify incidents, {response.message}"
            )

        self.assertTrue(response.success)

    def test_modify_as_review_group_member(self):
        self.incident.review_tasks.add(self.review_task)

        review_group = self.review_task.review_groups.first()
        if not review_group:
            self.fail("Review task must have at least one review group")

        member = review_group.members.first()
        if not member:
            self.fail("Review group must have at least one member")

        response = IncidentPermissionsService(
            user=member.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_modify_incident()

        if not response.success:
            self.fail(
                f"Review group member should be able to modify incidents, {response.message}"
            )

        self.assertTrue(response.success)

    def test_modify_with_change_incident_permission(self):
        user = UserFactory(is_active=True)

        permission_response = self.permission_manager.add_permissions_to_user(
            user=user,
            model=GeneralPatientVisitor,
            permissions=[{"code_name": "change_incident"}],
        )

        if not permission_response.success:
            self.fail(
                f"Failed to add permissions to user, {permission_response.message}"
            )

        # print users permissions for debugging
        print(f"User permissions: {user.get_all_permissions()}")

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_modify_incident()

        if not response.success:
            self.fail(f"User should be able to modify incident, {response.message}")

        self.assertTrue(response.success)

    def test_modify_as_director_fails(self):
        """Directors have read-only access and cannot modify incidents"""
        from django.contrib.auth.models import Group

        director_group, _ = Group.objects.get_or_create(name="Director")
        user = UserFactory(is_active=True)
        user.groups.add(director_group)

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_modify_incident()

        self.assertFalse(response.success)
        self.assertIn("Directors have read-only access", response.message)

    def test_modify_as_regular_user_fails(self):
        """Regular users cannot modify incidents, even their own"""
        user = UserFactory(is_active=True)

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_modify_incident()

        self.assertFalse(response.success)
        self.assertIn("Regular users cannot modify incidents", response.message)


class TestDeleteIncident(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory(created_by=self.user)
        self.app_label = "general_patient_visitor"

    def test_delete_incident_as_super_user(self):
        # Create Super user group and add user to it
        from django.contrib.auth.models import Group

        super_user_group, _ = Group.objects.get_or_create(name="Super user")
        self.user.groups.add(super_user_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_delete_incident()

        if not response.success:
            self.fail(
                f"Super user should be able to delete incidents, {response.message}"
            )

        self.assertTrue(response.success)

    def test_delete_incident_as_regular_user_fails(self):
        # Regular user (not in Super user group)
        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_delete_incident()

        if response.success:
            self.fail("Regular user should not be able to delete incidents")

        self.assertFalse(response.success)

    def test_delete_incident_as_owner_fails(self):
        # Even the owner cannot delete incidents (only Super user can)
        response = IncidentPermissionsService(
            user=self.incident.created_by,
            app_label=self.app_label,
            incident=self.incident,
        ).can_delete_incident()

        if response.success:
            self.fail("Incident owner should not be able to delete incidents")

        self.assertFalse(response.success)

    def test_delete_incident_with_no_incident_fails(self):
        # Test with no incident provided
        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=None,
        ).can_delete_incident()

        if response.success:
            self.fail("Should fail when no incident is provided")

        self.assertFalse(response.success)
        self.assertIn("Incident not found", response.message)


class TestListIncidents(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.permission_manager = PermissionsManagement()
        self.app_label = "general_patient_visitor"

    def test_list_incidents_with_view_list_permission(self):
        user = UserFactory(is_active=True)

        permission_response = self.permission_manager.add_permissions_to_user(
            user=user,
            model=GeneralPatientVisitor,
            permissions=[{"code_name": "view_list"}],
        )

        if not permission_response.success:
            self.fail(
                f"Failed to add permissions to user, {permission_response.message}"
            )

        # print users permissions for debugging
        print(f"User permissions: {user.get_all_permissions()}")

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
        ).can_list_incidents()

        if not response.success:
            self.fail(
                f"User should be able to list incidents with view_list permission, {response.message}"
            )

        self.assertTrue(response.success)

    def test_list_incidents_as_super_user(self):
        from django.contrib.auth.models import Group

        super_user_group, _ = Group.objects.get_or_create(name="Super user")
        self.user.groups.add(super_user_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
        ).can_list_incidents()

        if not response.success:
            self.fail(
                f"Super user should be able to list incidents, {response.message}"
            )

        self.assertTrue(response.success)

    def test_list_incidents_as_admin(self):
        from django.contrib.auth.models import Group

        admin_group, _ = Group.objects.get_or_create(name="Admin")
        self.user.groups.add(admin_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
        ).can_list_incidents()

        if not response.success:
            self.fail(f"Admin should be able to list incidents, {response.message}")

        self.assertTrue(response.success)

    def test_list_incidents_as_quality_risk_manager(self):
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.user.groups.add(qrm_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
        ).can_list_incidents()

        if not response.success:
            self.fail(
                f"Quality/Risk manager should be able to list incidents, {response.message}"
            )

        self.assertTrue(response.success)

    def test_list_incidents_as_manager(self):
        from django.contrib.auth.models import Group

        manager_group, _ = Group.objects.get_or_create(name="Manager")
        self.user.groups.add(manager_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
        ).can_list_incidents()

        if not response.success:
            self.fail(f"Manager should be able to list incidents, {response.message}")

        self.assertTrue(response.success)

    def test_list_incidents_as_regular_user_succeeds(self):
        # Regular user with no special permissions should still be able to list their own incidents
        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
        ).can_list_incidents()

        if not response.success:
            self.fail(
                f"Regular user should be able to list their own incidents, {response.message}"
            )

        self.assertTrue(response.success)
        self.assertIn("User can list their own incidents", response.message)


class TestSendToDepartment(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory(created_by=self.user)
        self.app_label = "general_patient_visitor"

    def test_send_to_department_as_quality_risk_manager(self):
        # Create Quality/Risk manager group and add user to it
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.user.groups.add(qrm_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_send_to_department()

        if not response.success:
            self.fail(
                f"Quality/Risk manager should be able to send incidents to department, {response.message}"
            )

        self.assertTrue(response.success)

    def test_send_to_department_as_admin(self):
        """Admin can send incidents to department per userFlow.md"""
        from django.contrib.auth.models import Group

        admin_group, _ = Group.objects.get_or_create(name="Admin")
        self.user.groups.add(admin_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_send_to_department()

        if not response.success:
            self.fail(
                f"Admin should be able to send incidents to department, {response.message}"
            )

        self.assertTrue(response.success)

    def test_send_to_department_as_regular_user_fails(self):
        # Regular user (not in Quality/Risk manager group)
        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_send_to_department()

        if response.success:
            self.fail("Regular user should not be able to send incidents to department")

        self.assertFalse(response.success)

    def test_send_to_department_as_super_user_fails(self):
        # Even Super user cannot send to department (only Quality/Risk manager can)
        from django.contrib.auth.models import Group

        super_user_group, _ = Group.objects.get_or_create(name="Super user")
        self.user.groups.add(super_user_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_send_to_department()

        if response.success:
            self.fail("Super user should not be able to send incidents to department")

        self.assertFalse(response.success)

    def test_send_to_department_as_owner_fails(self):
        # Even the owner cannot send incidents to department (only Quality/Risk manager can)
        response = IncidentPermissionsService(
            user=self.incident.created_by,
            app_label=self.app_label,
            incident=self.incident,
        ).can_send_to_department()

        if response.success:
            self.fail(
                "Incident owner should not be able to send incidents to department"
            )

        self.assertFalse(response.success)

    def test_send_to_department_with_no_incident_fails(self):
        # Test with no incident provided
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.user.groups.add(qrm_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=None,
        ).can_send_to_department()

        if response.success:
            self.fail("Should fail when no incident is provided")

        self.assertFalse(response.success)
        self.assertIn("Incident not found", response.message)


class TestAddReview(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()

        self.review_task = ReviewProcessTasksFactory()
        self.review_task.review_groups.add(ReviewGroupFactory())

        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory(created_by=self.user)
        self.app_label = "general_patient_visitor"

    def test_add_review_as_owner(self):
        # Per userFlow.md, regular users cannot add reviews to incidents, even their own
        response = IncidentPermissionsService(
            user=self.incident.created_by,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_review()

        # This should now fail since regular users cannot add reviews
        self.assertFalse(response.success)
        self.assertIn("Regular users cannot add reviews", response.message)

    def test_add_review_as_reviewer(self):
        self.incident.review_tasks.add(self.review_task)
        reviewer = self.review_task.reviewers.first()
        if not reviewer:
            self.fail("Review task must have at least one reviewer")

        response = IncidentPermissionsService(
            user=reviewer.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_review()

        if not response.success:
            self.fail(
                f"Reviewer should be able to add reviews to incidents, {response.message}"
            )

        self.assertTrue(response.success)

    def test_add_review_as_review_group_member(self):
        self.incident.review_tasks.add(self.review_task)

        review_group = self.review_task.review_groups.first()
        if not review_group:
            self.fail("Review task must have at least one review group")

        member = review_group.members.first()
        if not member:
            self.fail("Review group must have at least one member")

        response = IncidentPermissionsService(
            user=member.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_review()

        if not response.success:
            self.fail(
                f"Review group member should be able to add reviews to incidents, {response.message}"
            )

        self.assertTrue(response.success)

    def test_add_review_with_add_review_permission(self):
        user = UserFactory(is_active=True)

        permission_response = self.permission_manager.add_permissions_to_user(
            user=user,
            model=GeneralPatientVisitor,
            permissions=[{"code_name": "add_review"}],
        )

        if not permission_response.success:
            self.fail(
                f"Failed to add permissions to user, {permission_response.message}"
            )

        # print users permissions for debugging
        print(f"User permissions: {user.get_all_permissions()}")

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_review()

        if not response.success:
            self.fail(
                f"User should be able to add review to incident, {response.message}"
            )

        self.assertTrue(response.success)

    def test_add_review_as_director_fails(self):
        """Directors have read-only access and cannot add reviews"""
        from django.contrib.auth.models import Group

        director_group, _ = Group.objects.get_or_create(name="Director")
        user = UserFactory(is_active=True)
        user.groups.add(director_group)

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_review()

        self.assertFalse(response.success)
        self.assertIn("Directors have read-only access", response.message)

    def test_add_review_as_regular_user_fails(self):
        # Regular user with no special permissions or roles
        user = UserFactory(is_active=True)

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_review()

        if response.success:
            self.fail("Regular user should not be able to add reviews to incidents")

        self.assertFalse(response.success)

    def test_add_review_with_no_incident_fails(self):
        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=None,
        ).can_add_review()

        if response.success:
            self.fail("Should fail when no incident is provided")

        self.assertFalse(response.success)
        self.assertIn("Incident not found", response.message)


class TestAddSeverityRating(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory(created_by=self.user)
        self.app_label = "general_patient_visitor"

    def test_add_severity_rating_as_quality_risk_manager(self):
        # Create Quality/Risk manager group and add user to it
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.user.groups.add(qrm_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_severity_rating()

        if not response.success:
            self.fail(
                f"Quality/Risk manager should be able to add severity rating, {response.message}"
            )

        self.assertTrue(response.success)

    def test_add_severity_rating_as_manager_fails(self):
        """Managers cannot add severity ratings (restricted action per userFlow.md)"""
        from django.contrib.auth.models import Group

        manager_group, _ = Group.objects.get_or_create(name="Manager")
        user = UserFactory(is_active=True)
        user.groups.add(manager_group)

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_severity_rating()

        self.assertFalse(response.success)
        self.assertIn("Managers cannot add severity ratings", response.message)

    def test_add_severity_rating_as_regular_user_fails(self):
        # Regular user (not in Quality/Risk manager group)
        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_severity_rating()

        if response.success:
            self.fail("Regular user should not be able to add severity rating")

        self.assertFalse(response.success)

    def test_add_severity_rating_as_super_user_fails(self):
        # Even Super user cannot add severity rating (only Quality/Risk manager can)
        from django.contrib.auth.models import Group

        super_user_group, _ = Group.objects.get_or_create(name="Super user")
        self.user.groups.add(super_user_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_severity_rating()

        if response.success:
            self.fail("Super user should not be able to add severity rating")

        self.assertFalse(response.success)

    def test_add_severity_rating_as_owner_fails(self):
        # Even the owner cannot add severity rating (only Quality/Risk manager can)
        response = IncidentPermissionsService(
            user=self.incident.created_by,
            app_label=self.app_label,
            incident=self.incident,
        ).can_add_severity_rating()

        if response.success:
            self.fail("Incident owner should not be able to add severity rating")

        self.assertFalse(response.success)

    def test_add_severity_rating_with_no_incident_fails(self):
        # Test with no incident provided
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.user.groups.add(qrm_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=None,
        ).can_add_severity_rating()

        if response.success:
            self.fail("Should fail when no incident is provided")

        self.assertFalse(response.success)
        self.assertIn("Incident not found", response.message)


class TestMarkAsResolved(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory(created_by=self.user)
        self.app_label = "general_patient_visitor"

    def test_mark_as_resolved_as_quality_risk_manager(self):
        # Create Quality/Risk manager group and add user to it
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        # Use a different user to avoid self-closing restriction
        qrm_user = UserFactory(is_active=True)
        qrm_user.groups.add(qrm_group)

        response = IncidentPermissionsService(
            user=qrm_user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_mark_as_resolved()

        if not response.success:
            self.fail(
                f"Quality/Risk manager should be able to mark incident as resolved, {response.message}"
            )

        self.assertTrue(response.success)

    def test_mark_as_resolved_as_regular_user_fails(self):
        # Regular user (not in Quality/Risk manager group)
        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_mark_as_resolved()

        if response.success:
            self.fail("Regular user should not be able to mark incident as resolved")

        self.assertFalse(response.success)

    def test_mark_as_resolved_as_super_user_fails(self):
        # Even Super user cannot mark as resolved (only Quality/Risk manager can)
        from django.contrib.auth.models import Group

        super_user_group, _ = Group.objects.get_or_create(name="Super user")
        self.user.groups.add(super_user_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_mark_as_resolved()

        if response.success:
            self.fail("Super user should not be able to mark incident as resolved")

        self.assertFalse(response.success)

    def test_mark_as_resolved_as_owner_fails(self):
        # Even the owner cannot mark as resolved (only Quality/Risk manager can)
        # This is now enforced by the self-closing prevention rule
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.user.groups.add(qrm_group)  # Make the owner a Quality/Risk manager

        response = IncidentPermissionsService(
            user=self.incident.created_by,  # Owner trying to close their own incident
            app_label=self.app_label,
            incident=self.incident,
        ).can_mark_as_resolved()

        # Should fail due to self-closing prevention
        self.assertFalse(response.success)
        self.assertIn("cannot close their own incidents", response.message)

    def test_mark_as_resolved_with_no_incident_fails(self):
        # Test with no incident provided
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.user.groups.add(qrm_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=None,
        ).can_mark_as_resolved()

        if response.success:
            self.fail("Should fail when no incident is provided")

        self.assertFalse(response.success)
        self.assertIn("Incident not found", response.message)


class TestExportData(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory(created_by=self.user)
        self.app_label = "general_patient_visitor"

    def test_export_data_as_super_user(self):
        """Super user can export all data"""
        from django.contrib.auth.models import Group

        super_user_group, _ = Group.objects.get_or_create(name="Super user")
        self.user.groups.add(super_user_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_export_data()

        self.assertTrue(response.success)
        self.assertIn("Super user can export all data", response.message)

    def test_export_data_as_quality_risk_manager(self):
        """Quality/Risk manager can export all facility logs"""
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.user.groups.add(qrm_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_export_data()

        self.assertTrue(response.success)
        self.assertIn("Quality/Risk manager can export data", response.message)

    def test_export_data_as_director(self):
        """Director can export data (read-only) with facility access"""
        from django.contrib.auth.models import Group

        director_group, _ = Group.objects.get_or_create(name="Director")
        user = UserFactory(is_active=True)
        user.groups.add(director_group)

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_export_data()

        # This might fail or succeed depending on facility access setup
        # The test should check the facility access logic
        self.assertIsNotNone(response)

    def test_export_data_as_regular_user_fails(self):
        """Regular users cannot export data"""
        user = UserFactory(is_active=True)

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_export_data()

        self.assertFalse(response.success)
        self.assertIn("does not have permission to export data", response.message)


class TestManageRestrictedAccess(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.permission_manager = PermissionsManagement()
        self.incident = GeneralPatientVisitorFactory(created_by=self.user)
        self.app_label = "general_patient_visitor"

    def test_manage_restricted_access_as_super_user(self):
        """Super user can manage restricted access"""
        from django.contrib.auth.models import Group

        super_user_group, _ = Group.objects.get_or_create(name="Super user")
        self.user.groups.add(super_user_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_manage_restricted_access()

        self.assertTrue(response.success)
        self.assertIn("Super user can manage restricted access", response.message)

    def test_manage_restricted_access_as_admin(self):
        """Admin can grant/revoke restricted access"""
        from django.contrib.auth.models import Group

        admin_group, _ = Group.objects.get_or_create(name="Admin")
        self.user.groups.add(admin_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_manage_restricted_access()

        self.assertTrue(response.success)
        self.assertIn("Admin can manage restricted access", response.message)

    def test_manage_restricted_access_as_quality_risk_manager(self):
        """Quality/Risk manager can assign restricted access"""
        from django.contrib.auth.models import Group

        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.user.groups.add(qrm_group)

        response = IncidentPermissionsService(
            user=self.user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_manage_restricted_access()

        self.assertTrue(response.success)
        self.assertIn(
            "Quality/Risk manager can assign restricted access", response.message
        )

    def test_manage_restricted_access_as_regular_user_fails(self):
        """Regular users cannot manage restricted access"""
        user = UserFactory(is_active=True)

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_manage_restricted_access()

        self.assertFalse(response.success)
        self.assertIn(
            "Only Admins, Quality/Risk managers, and Super users", response.message
        )

    def test_manage_restricted_access_as_manager_fails(self):
        """Managers cannot manage restricted access"""
        from django.contrib.auth.models import Group

        manager_group, _ = Group.objects.get_or_create(name="Manager")
        user = UserFactory(is_active=True)
        user.groups.add(manager_group)

        response = IncidentPermissionsService(
            user=user,
            app_label=self.app_label,
            incident=self.incident,
        ).can_manage_restricted_access()

        self.assertFalse(response.success)
        self.assertIn(
            "Only Admins, Quality/Risk managers, and Super users", response.message
        )
