from adverse_drug_reaction.tests.factory import AdverseDrugReactionFactory
from base.tests.base_setup import BaseTestSetup

class TestDeleteDraftIncidents(BaseTestSetup):
    """
    Test class for deleting draft incidents.
    """

    def setUp(self):
        super().setUp()
        self.incident = AdverseDrugReactionFactory(created_by=self.user_user)
    
    def test_delete_draft_incidents(self):
        """
        Test deleting draft incidents.
        """
        