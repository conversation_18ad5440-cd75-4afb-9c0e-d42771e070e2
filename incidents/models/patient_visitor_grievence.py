# from django.db import models
# from accounts.models import Profile
# from base.models import BaseModel


# from django.db import models

# from documents.models import Document
# from base.models import Facility
# from base.models import Department
# from reviews.models import Review


# # we need to add search indexes to optimize performance during searching and querying
# class GrievanceBase(BaseModel):
#     INCIDENT_REVIEW_STATUS_CHOICES = [
#         ("Draft", "Draft"),
#         ("Open", "Open"),
#         ("Pending Assigned", "Pending Assigned"),
#         ("Pending Review", "Pending Review"),
#         ("Pending Approval", "Pending Approval"),
#         ("Resolved", "Resolved"),
#     ]
#     report_facility = models.ForeignKey(
#         Facility,
#         blank=True,
#         null=True,
#         on_delete=models.SET_NULL,
#     )
#     status = models.CharField(
#         choices=INCIDENT_REVIEW_STATUS_CHOICES,
#         max_length=255,
#         default="Draft",
#     )
#     current_step = models.IntegerField(default=1, null=True, blank=True)
#     date = models.DateField(null=True, blank=True)
#     patient_name = models.ForeignKey(
#         Profile, null=True, blank=True, on_delete=models.SET_NULL
#     )
#     form_initiated_by = models.ForeignKey(
#         Profile,
#         blank=True,
#         null=True,
#         on_delete=models.SET_NULL,
#         related_name="form_initiated_by_profile",
#     )
#     title = models.CharField(max_length=255, null=True, blank=True)
#     complaint_made_by = models.ForeignKey(
#         Profile,
#         blank=True,
#         null=True,
#         on_delete=models.SET_NULL,
#         related_name="complaint_made_by_profile",
#     )
#     relationship_to_patient = models.CharField(max_length=255, null=True, blank=True)
#     source_of_information = models.CharField(max_length=255, null=True, blank=True)
#     complaint_or_concern = models.TextField(null=True, blank=True)
#     initial_corrective_actions = models.TextField(null=True, blank=True)
#     adverse_patient_outcome = models.BooleanField(default=False)
#     administrator_notified = models.ForeignKey(
#         Profile,
#         null=True,
#         blank=True,
#         on_delete=models.SET_NULL,
#         related_name="administrator_notified",
#     )
#     notification_date = models.DateField(null=True, blank=True)
#     notification_time = models.TimeField(null=True, blank=True)
#     outcome = models.TextField(null=True, blank=True)
#     reviews = models.ManyToManyField(
#         Review, related_name="grievance_reviews_field", blank=True
#     )
#     documents = models.ManyToManyField(
#         Document, related_name="grievance_incident_documents_field", blank=True
#     )

#     department = models.ForeignKey(
#         Department, blank=True, null=True, on_delete=models.SET_NULL
#     )
#     is_resolved = models.BooleanField(default=False)

#     def __str__(self):
#         return f"Grievance Form - {self.patient_name or 'Unknown'} - {self.date or 'No Date'}"


# class Grievance(GrievanceBase):
#     is_modified = models.BooleanField(default=False)


# class GrievanceVersion(GrievanceBase):
#     original_report = models.ForeignKey(
#         Grievance,
#         on_delete=models.CASCADE,
#         related_name="versions",
#         null=True,
#         blank=True,
#     )

#     class Meta:
#         db_table = "grievance_version"
