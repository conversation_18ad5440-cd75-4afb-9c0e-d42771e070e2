# from django.db import models
# from django.contrib.auth.models import User

# from accounts.models import Profile
# from base.models import BaseModel
# from documents.models import Document
# from base.models import Facility
# from base.models import Department
# from reviews.models import Review


# class StaffIncidentType(BaseModel):
#     type = models.CharField(max_length=50)

#     def __str__(self):
#         return self.type


# class StaffWitness(BaseModel):
#     pass
#     witness_name = models.ForeignKey(
#         Profile, on_delete=models.SET_NULL, null=True, blank=True
#     )

#     def __str__(self):
#         return f"Witness Name: {self.witness_name}"


# # we need to add search indexes to optimize performance during searching and querying
# class StaffIncidentReportBase(BaseModel):

#     INCIDENT_REVIEW_STATUS_CHOICES = [
#         ("Draft", "Draft"),
#         ("Open", "Open"),
#         ("Pending Assigned", "Pending Assigned"),
#         ("Pending Review", "Pending Review"),
#         ("Pending Approval", "Pending Approval"),
#         ("Resolved", "Resolved"),
#     ]
#     status = models.CharField(
#         choices=INCIDENT_REVIEW_STATUS_CHOICES,
#         max_length=255,
#         default="Draft",
#     )
#     report_facility = models.ForeignKey(
#         Facility,
#         blank=True,
#         null=True,
#         on_delete=models.SET_NULL,
#     )
#     patient_info = models.ForeignKey(
#         Profile,
#         blank=True,
#         null=True,
#         on_delete=models.SET_NULL,
#     )
#     incident_status = models.CharField(max_length=500, null=True, blank=True)
#     current_step = models.IntegerField(default=1, null=True, blank=True)
#     job_title = models.CharField(max_length=100, blank=True, null=True)
#     supervisor = models.CharField(max_length=100, blank=True, null=True)
#     incident_date = models.DateField(blank=True, null=True)
#     time_of_injury_or_near_miss = models.TimeField(blank=True, null=True)
#     witnesses = models.ManyToManyField(StaffWitness, blank=True)
#     location = models.CharField(max_length=200, blank=True, null=True)
#     activity_at_time_of_incident = models.TextField(blank=True, null=True)
#     incident_description = models.TextField(blank=True, null=True)
#     preventive_measures = models.TextField(blank=True, null=True)
#     body_parts_injured = models.TextField(blank=True, null=True)
#     doctor_consulted = models.BooleanField(default=False, blank=True, null=True)
#     doctor_consulted_info = models.ForeignKey(
#         Profile,
#         on_delete=models.SET_NULL,
#         null=True,
#         blank=True,
#         related_name="doctor_consulted_info",
#     )
#     doctor_consulted_dated = models.DateField(blank=True, null=True)
#     doctor_consulted_time = models.TimeField(blank=True, null=True)
#     previous_injury = models.BooleanField(default=False, blank=True, null=True)
#     previous_injury_date = models.DateField(blank=True, null=True)

#     report_completed = models.BooleanField(default=False, blank=True, null=True)
#     reviews = models.ManyToManyField(
#         Review, related_name="employee_incident_reviews_field", blank=True
#     )
#     documents = models.ManyToManyField(
#         Document, related_name="employee_incident_documents_field", blank=True
#     )
#     department = models.ForeignKey(
#         Department, blank=True, null=True, on_delete=models.SET_NULL
#     )
#     is_resolved = models.BooleanField(default=False, blank=True, null=True)

#     def __str__(self):
#         return f"Incident Report by {self.patient_info} on {self.incident_date}"


# class StaffIncidentReport(StaffIncidentReportBase):
#     is_modified = models.BooleanField(default=False, blank=True, null=True)


# class StaffIncidentReportVersion(StaffIncidentReportBase):
#     original_report = models.ForeignKey(
#         StaffIncidentReport,
#         on_delete=models.CASCADE,
#         related_name="versions",
#         null=True,
#         blank=True,
#     )

#     class Meta:
#         db_table = "staff_incident_version"
