# from django.db import models
# from django.contrib.auth.models import User

# from base.models import BaseModel
# from base.models import Facility


# class Department(models.Model):
#     name = models.CharField(max_length=255)
#     description = models.TextField(blank=True)
#     parent = models.ForeignKey("self", on_delete=models.CASCADE, null=True, blank=True)
#     header_of_department = models.ForeignKey(
#         User, on_delete=models.SET_NULL, null=True, blank=True
#     )
#     members = models.ManyToManyField(User, related_name="incident_members")

#     created_by = models.ForeignKey(
#         User, on_delete=models.SET_NULL, null=True, related_name="department_created_by"
#     )
#     updated_by = models.ForeignKey(
#         User, on_delete=models.SET_NULL, null=True, related_name="department_updated_by"
#     )
#     created_at = models.DateTimeField(auto_now_add=True, null=True)
#     updated_at = models.DateTimeField(auto_now=True, null=True)
#     facility = models.ForeignKey(
#         Facility,
#         related_name="department_facility",
#         null=True,
#         blank=True,
#         on_delete=models.CASCADE,
#     )

#     def __str__(self):
#         return self.name
