from datetime import datetime
import os
from accounts.models import Profile
from base.services.format_date import convert_created_at_to_user_timezone
from base.services.logging.logger import LoggingService
from incidents.emails.incident_is_submited import send_incident_submission_confirmation
from incidents.emails.incident_notification import send_incident_notification
from incidents.emails.send_to_department import send_to_department_email
from incidents.emails.support_ticket_notification import (
    send_support_ticket_notification,
)
from incidents.emails.ticket_submitted import send_ticket_submission_confirmation
from base.models import Department
from django.contrib.auth.models import User
from datetime import datetime
import pytz

logging_service = LoggingService()


def send_incident_to_department(data, incident, incident_type, user):

    assignees = []

    if not "department" in data:
        return (
            False,
            "Department id is required",
        )

    if "assignees" in data:
        for assignee in data.get("assignees"):
            user = User.objects.filter(
                id=assignee["user_id"]
            ).first()  # Use .first() to get a single user
            if user:
                assignees.append(user)
    try:
        department = Department.objects.get(id=data["department"])
        incident.department = department
        incident.assignees.add(*assignees)
        incident.save()

        incident.department = department
        incident.assignees.add(*assignees)
        incident.save()

        incident_preview = data["comment"]
        incident_url = f"{os.getenv('MAIN_DOMAIN_NAME')}/"
        # send email to department head with the incident details
        for assignee in assignees:
            send_to_department_email(
                recipient_email=assignee.email,
                sender_department=department.name,
                incident_datetime=convert_created_at_to_user_timezone(
                    incident.created_at, assignee
                ),
                incident_preview=incident_preview,
                incident_id=incident.id,
                incident_type=incident_type,
                incident_link=incident_url,
                first_name=assignee.first_name,
            )
        return (True, "Incident is sent to department successfully")

    except Department.DoesNotExist:
        return (False, f"Department with name '{data['department']}' is not found")
    except Exception as e:
        logging_service.log_error(e)
        return (False, "Internal server error")


def send_incident_submission_email(incident, incident_type):
    group = "Manager"
    managers = Profile.objects.filter(
        access_to_facilities=incident.report_facility,
        user__groups__name=group,
    )

    for manager in managers:
        try:
            send_incident_notification(
                recipient_email=manager.user.email,
                first_name=manager.user.first_name,
                incident_id=incident.id,
                date_created=incident.created_at,
                created_by=incident.created_by,
                incident_type=incident_type,
                user=manager.user,
            )
        except RuntimeError as e:
            logging_service.log_error(e)
            continue

    if incident.created_by and incident.created_by.email:
        try:
            send_incident_submission_confirmation(
                recipient_email=incident.created_by.email,
                first_name=incident.created_by.first_name,
                date_created=incident.created_at,
                user=incident.created_by,
            )
        except RuntimeError as e:
            logging_service.log_error(e)
            pass


def send_ticket_email(support_ticket, group):
    # get users with in a group name tech support
    tech_support_users = User.objects.filter(groups__name="Tech Support")

    if len(tech_support_users) > 0:
        for user in tech_support_users:
            send_support_ticket_notification(
                recipient_email=user.email,
                first_name=user.first_name,
                ticket_title=support_ticket.title,
                ticket_description=support_ticket.description,
                ticket_priority=support_ticket.priority,
                support_ticket_id=support_ticket.id,
                date_created=support_ticket.created_at,
                created_by=support_ticket.created_by,
                group=group,
            )
        send_ticket_submission_confirmation(
            recipient_email=support_ticket.created_by.email,
            first_name=support_ticket.created_by.first_name,
            date_created=support_ticket.created_at,
        )
