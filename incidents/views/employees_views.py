from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from incidents.serializers.employee_serializers import (
    InitialReportSerializer,
    IncidentDescriptionSerializer,
    FinalReportSerializer,
    ReportCompletedSerializer,
    FullReportSerializer,
)
from staff_incident_reports.models import StaffIncidentReport


@api_view(["POST"])
def initial_report(request):

    serializer = InitialReportSerializer(data=request.data)
    if serializer.is_valid():
        instance = serializer.save()
        return Response({"id": instance.pk}, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["PATCH"])
def update_incident_description(request):
    try:
        report = StaffIncidentReport.objects.get(pk=request.data["pk"])
    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Not Found"}, status=status.HTTP_404_NOT_FOUND)

    serializer = IncidentDescriptionSerializer(report, data=request.data, partial=True)
    if serializer.is_valid():
        instance = serializer.save()
        return Response({"id": instance.pk}, status=status.HTTP_200_OK)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["PATCH"])
def update_final_report(request):
    try:
        report = StaffIncidentReport.objects.get(pk=request.data["pk"])
    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Not Found"}, status=status.HTTP_404_NOT_FOUND)

    serializer = FinalReportSerializer(report, data=request.data, partial=True)
    if serializer.is_valid():
        instance = serializer.save()
        return Response({"id": instance.pk}, status=status.HTTP_200_OK)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["PATCH"])
def update_report_completed(request):
    try:
        report = StaffIncidentReport.objects.get(pk=request.data["pk"])
    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Report not found"}, status=status.HTTP_404_NOT_FOUND)

    serializer = ReportCompletedSerializer(report, data=request.data, partial=True)
    if serializer.is_valid():
        instance = serializer.save()
        return Response(
            {"id": instance.pk, "report_completed": instance.report_completed},
            status=status.HTTP_200_OK,
        )
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["GET"])
def list_reports(request):

    reports = StaffIncidentReport.objects.all()
    serializer = FullReportSerializer(reports, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(["GET"])
def retrieve_report(request):
    try:
        report = StaffIncidentReport.objects.get(pk=request.data["pk"])
    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Report not found"}, status=status.HTTP_404_NOT_FOUND)

    serializer = FullReportSerializer(report)
    return Response(serializer.data, status=status.HTTP_200_OK)
