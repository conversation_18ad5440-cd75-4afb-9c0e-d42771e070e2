from django.contrib.auth.models import User
from rest_framework import status

from api.views.auth.permissions_list import (
    is_admin_user,
    is_super_user,
)
from base.constants import ReviewStatus
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from django.db.models import Model
from django.core.exceptions import ValidationError
from typing import Type, List

logging_service = LoggingService()


