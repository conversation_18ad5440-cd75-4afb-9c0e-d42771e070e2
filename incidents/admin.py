from django.contrib import admin
from adverse_drug_reaction.models import AdverseDrugReaction, ObserversName
from base.models import GeneralWitness
from staff_incident_reports.models import *
from patient_visitor_grievance.models import *
from general_patient_visitor.models import *  # modified

from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from patient_visitor_grievance.models import (
    Grievance,
    GrievanceInvestigation,
    GrievanceInvestigationInvolvedParty,
)
from staff_incident_reports.models import *

# from medication_error.models import MedicationError

from workplace_violence_reports.models import *
from base.models import Department
from activities.models import ActivityLog


class IncidentTypeAdmin(admin.ModelAdmin):
    list_display = ("id", "type")


class DepartmentAdmin(admin.ModelAdmin):
    list_display = ("id", "name")


class StaffIncidentReportAdmin(admin.ModelAdmin):
    list_display = ("id", "job_title", "date_of_injury_or_near_miss", "status")


# class EmployeeWitnessAdmin(admin.ModelAdmin):
#     list_display = ("witness_name",)


class GrievanceAdmin(admin.ModelAdmin):
    list_display = ("id", "date", "title")


class FeedbackPartyAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "relationship_to_patient")


class IncidentAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "category",
        "patient_visitor",
        "incident_date",
        "incident_type",
    )


class GeneralWitnessAdmin(admin.ModelAdmin):
    list_display = ("id", "name")


class HealthIncidentInvestigationAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name_of_injured_staff",
        "date_of_event",
        "date_of_hire",
    )


class WitnessAdmin(admin.ModelAdmin):
    list_display = ("id", "incident", "name")


class IncidentTypeAdmin(admin.ModelAdmin):
    # list_display = ("id", "description")
    pass


class InjuryDescriptionAdmin(admin.ModelAdmin):
    list_display = ("id", "description")


class ExtendedIncidentTypeAdmin(admin.ModelAdmin):
    list_display = ("id", "description")


class AssailantRelationshipAdmin(admin.ModelAdmin):
    list_display = ("id", "description")


class BackgroundAdmin(admin.ModelAdmin):
    list_display = ("id", "description")


class WorkplaceViolenceIncidentReportAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "type_of_incident",
        "date_of_incident",
        "initiator",
        "location",
    )


class IncidentFeedbackPartyAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "relationship_to_patient")


class GrievanceInvestigationAdmin(admin.ModelAdmin):
    list_display = (
        "conducted_by",
        "start_date",
        "end_date",
        "matter_closed",
        "date_matter_closed",
    )


class GrievanceInvestigationInvolvedPartyAdmin(admin.ModelAdmin):
    list_display = ("name", "relationship_to_patient")


class LostAndFoundAdmin(admin.ModelAdmin):
    list_display = (
        "property_name",
        "date_reported",
    )


class MedicationErrorReportAdmin(admin.ModelAdmin):
    list_display = (
        "date_of_error",
        "location",
    )


class IncidentInvolvedPartyAdmin(admin.ModelAdmin):
    list_display = ("party", "title")


class IncidentPersonInjuredAdmin(admin.ModelAdmin):
    list_display = ("person", "injury_description")


class IncidentWitnessAdmin(admin.ModelAdmin):
    list_display = ("witness",)


class WorkPlaceViolenceAdmin(admin.ModelAdmin):
    list_display = (
        "type_of_incident",
        "date_of_incident",
        "time_of_incident",
        "incident_type",
    )


class AdverseDrugReactionAdmin(admin.ModelAdmin):
    list_display = (
        "patient_type",
        "incident_date",
    )


class ObserversNameAdmin(admin.ModelAdmin):
    list_display = ("name",)


class ActivityAdmin(admin.ModelAdmin):
    list_display = ("id", "user", "activity_type", "timestamp")


admin.site.register(IncidentInvolvedParty, IncidentInvolvedPartyAdmin)
admin.site.register(IncidentPersonInjured, IncidentPersonInjuredAdmin)
admin.site.register(IncidentWitness, IncidentWitnessAdmin)
admin.site.register(WorkPlaceViolence, WorkPlaceViolenceAdmin)
admin.site.register(MedicationError, MedicationErrorReportAdmin)
admin.site.register(GrievanceInvestigation, GrievanceInvestigationAdmin)
admin.site.register(
    GrievanceInvestigationInvolvedParty, GrievanceInvestigationInvolvedPartyAdmin
)
admin.site.register(LostAndFound, LostAndFoundAdmin)
admin.site.register(StaffIncidentType, IncidentTypeAdmin)
admin.site.register(StaffIncidentInvestigation, HealthIncidentInvestigationAdmin)
admin.site.register(GeneralPatientVisitor, IncidentAdmin)
admin.site.register(GeneralWitness, GeneralWitnessAdmin)
admin.site.register(Grievance, GrievanceAdmin)
admin.site.register(StaffIncidentReport, StaffIncidentReportAdmin)
# admin.site.register(StaffWitness, EmployeeWitnessAdmin)
admin.site.register(AdverseDrugReaction, AdverseDrugReactionAdmin)
admin.site.register(ObserversName, ObserversNameAdmin)
admin.site.register(Department, DepartmentAdmin)
admin.site.register(ActivityLog, ActivityAdmin)
