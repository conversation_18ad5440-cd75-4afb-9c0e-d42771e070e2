# from rest_framework import serializers
# from accounts.serializers import GetProfileSerializer, ProfileSerializer
# from adverse_drug_reaction.models import (
#     AdverseDrugReaction,
#     AdverseDrugReactionVisitorVersion,
#     ObserversName,
#     AdverseDrugReactionOutcome,
#     AdverseDrugReactionClassification,
# )


# class ObserversSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = ObserversName
#         fields = ["id", "name"]


# class AdverseDrugReactionOutcomeSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = AdverseDrugReactionOutcome
#         fields = "__all__"


# class AdverseDrugReactionClassificationSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = AdverseDrugReactionClassification
#         fields = "__all__"


# class AdverseDrugReactionCreateSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = AdverseDrugReaction
#         fields = "__all__"


# class AdverseDrugReactionUpdateSerializer(serializers.ModelSerializer):

#     class Meta:
#         model = AdverseDrugReaction
#         fields = "__all__"


# class AdverseDrugReactionUpdateVersionSerializer(serializers.ModelSerializer):

#     class Meta:
#         model = AdverseDrugReactionVisitorVersion
#         fields = "__all__"


# class ListAdverseDrugReactionSerializer(serializers.ModelSerializer):
#     observers_name = GetProfileSerializer()
#     name_of_physician_notified = GetProfileSerializer()
#     name_of_family_notified = GetProfileSerializer()
#     patient_name = GetProfileSerializer()

#     class Meta:
#         model = AdverseDrugReaction
#         fields = "__all__"
