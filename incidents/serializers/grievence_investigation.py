# from rest_framework import serializers

# from accounts.models import Profile
# from accounts.serializers import GetProfileSerializer
# from documents.models import Document
# from documents.serializers import DocumentSerializer

# from patient_visitor_grievance.models import (
#     GrievanceInvestigation,
#     GrievanceInvestigationInvolvedParty,
# )
# from reviews.models import Review


# class GrievanceInvestigationInvolvedPartySerializer(serializers.ModelSerializer):
#     class Meta:
#         model = GrievanceInvestigationInvolvedParty
#         fields = "__all__"


# class GrievanceInvestigationSerializer(serializers.ModelSerializer):
#     documents = serializers.PrimaryKeyRelatedField(
#         many=True, queryset=Document.objects.all(), required=False
#     )
#     involved_parties = serializers.PrimaryKeyRelatedField(
#         many=True,
#         queryset=GrievanceInvestigationInvolvedParty.objects.all(),
#         required=False,
#     )
#     reviews = serializers.PrimaryKeyRelatedField(
#         many=True, queryset=Review.objects.all(), required=False
#     )
#     conducted_by = serializers.PrimaryKeyRelatedField(
#         queryset=Profile.objects.all(), allow_null=True, required=False
#     )

#     class Meta:
#         model = GrievanceInvestigation
#         fields = "__all__"


# class RetrieveGrievanceInvestigationSerializer(serializers.ModelSerializer):
#     involved_parties = GrievanceInvestigationInvolvedPartySerializer(many=True)
#     extension_letter_copy = DocumentSerializer()
#     response_letter_copy = DocumentSerializer()
#     conducted_by = GetProfileSerializer()

#     class Meta:
#         model = GrievanceInvestigation
#         fields = "__all__"
