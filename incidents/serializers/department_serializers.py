# from rest_framework import serializers
# from accounts.serializers import UserSerializer
# from facilities.serializers import FacilitySerializer
# from base.models import Department


# class DepartmentNestedSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Department
#         fields = ("name",)


# class DepartmentSerializer(serializers.ModelSerializer):
#     parent = DepartmentNestedSerializer()
#     header_of_department = UserSerializer()
#     updated_by = UserSerializer()
#     facility = FacilitySerializer()
#     members = UserSerializer(many=True)

#     class Meta:
#         model = Department
#         fields = "__all__"


# class UpdateDepartmentSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Department
#         fields = "__all__"
