# from rest_framework import serializers
# from accounts.serializers import GetProfileSerializer
# from facilities.serializers import FacilitySerializer
# from lost_and_found.models import LostAndFound, LostAndFoundVersion


# class LostAndFoundSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = LostAndFound
#         fields = "__all__"


# class LostAndFoundVersionSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = LostAndFoundVersion
#         fields = "__all__"


# class LostAndFoundLostSerializer(serializers.ModelSerializer):
#     report_facility = FacilitySerializer()
#     person_reporting_info = GetProfileSerializer()
#     person_taking_report_info = GetProfileSerializer()
#     person_taking_report_info = GetProfileSerializer()

#     class Meta:
#         model = LostAndFound
#         fields = "__all__"
