# # serializers.py

# from rest_framework import serializers

# from accounts.models import Profile
# from accounts.serializers import GetProfileSerializer, UserSerializer
# from base.models import GeneralWitness
# from general_patient_visitor.models import GeneralPatientVisitor
# from staff_incident_reports.models import StaffIncidentInvestigation

# from general_patient_visitor.models import GeneralPatientVisitor


# class IncidentSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = GeneralPatientVisitor
#         fields = "__all__"


# class WitnessSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = GeneralWitness
#         fields = "__all__"


# class HealthInvestigationSerializer(serializers.ModelSerializer):

#     class Meta:
#         model = StaffIncidentInvestigation
#         fields = "__all__"


# class GetHealthInvestigationSerializer(serializers.ModelSerializer):
#     name_of_injured_staff = GetProfileSerializer()
#     created_by = UserSerializer()
#     doctor_info = GetProfileSerializer()

#     class Meta:
#         model = StaffIncidentInvestigation
#         fields = "__all__"
