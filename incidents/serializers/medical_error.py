# from rest_framework import serializers
# from medication_error.models import MedicationError, MedicationErrorVersion
# from accounts.serializers import GetProfileSerializer, UserSerializer
# from general_patient_visitor.serializers import FacilitySerializer
# from accounts.serializers import GetProfileSerializer


# class MedicalErrorSerializer(serializers.ModelSerializer):
#     created_by = UserSerializer(read_only=True)
#     updated_by = UserSerializer(read_only=True)

#     class Meta:
#         model = MedicationError
#         fields = "__all__"


# class MedicalErrorVersionSerializer(serializers.ModelSerializer):
#     created_by = UserSerializer(read_only=True)
#     updated_by = UserSerializer(read_only=True)

#     class Meta:
#         model = MedicationErrorVersion
#         fields = "__all__"


# class MedicalErrorListSerializer(serializers.ModelSerializer):
#     created_by = UserSerializer(read_only=True)
#     updated_by = UserSerializer(read_only=True)
#     report_facility = FacilitySerializer()
#     patient = GetProfileSerializer()
#     provider_info = GetProfileSerializer()

#     class Meta:
#         model = MedicationError
#         fields = "__all__"
