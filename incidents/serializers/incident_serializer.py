# # serializers.py

# from rest_framework import serializers
# from accounts.serializers import GetProfileSerializer, ProfileSerializer
# from base.models import GeneralWitness
# from documents.models import Document
# from base.models import Facility

# from general_patient_visitor.models import (
#     GeneralPatientVisitor,
#     GeneralPatientVisitorVersion,
# )
# from reviews.models import Review


# class IncidentSerializer(serializers.ModelSerializer):

#     reviews = serializers.PrimaryKeyRelatedField(
#         many=True, queryset=Review.objects.all(), required=False
#     )
#     documents = serializers.PrimaryKeyRelatedField(
#         many=True, queryset=Document.objects.all(), required=False
#     )

#     class Meta:
#         model = GeneralPatientVisitor
#         fields = "__all__"


# class FacilitySerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Facility
#         fields = ["id", "name"]


# class IncidentListSerializer(serializers.ModelSerializer):

#     reviews = serializers.PrimaryKeyRelatedField(
#         many=True, queryset=Review.objects.all(), required=False
#     )
#     documents = serializers.PrimaryKeyRelatedField(
#         many=True, queryset=Document.objects.all(), required=False
#     )
#     report_facility = FacilitySerializer()
#     patient_visitor = GetProfileSerializer()
#     physician_notified = GetProfileSerializer()
#     family_notified = GetProfileSerializer()
#     notified_by = GetProfileSerializer()

#     class Meta:
#         model = GeneralPatientVisitor
#         fields = "__all__"


# class IncidentUpdateSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = GeneralPatientVisitor
#         fields = "__all__"


# class GeneralPatientVisitorVersionSerializer(serializers.ModelSerializer):
#     reviews = serializers.PrimaryKeyRelatedField(
#         many=True, queryset=Review.objects.all(), required=False
#     )
#     documents = serializers.PrimaryKeyRelatedField(
#         many=True, queryset=Document.objects.all(), required=False
#     )

#     class Meta:
#         model = GeneralPatientVisitorVersion
#         fields = "__all__"


# class WitnessSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = GeneralWitness
#         fields = "__all__"
