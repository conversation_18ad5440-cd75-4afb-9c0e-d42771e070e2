from rest_framework import serializers
from accounts.serializers import GetProfileSerializer
from facilities.serializers import FacilitySerializer
from incidents.models import StaffIncidentReport
from staff_incident_reports.models import StaffIncidentReportVersion
from accounts.models import Profile


class InitialReportSerializer(serializers.ModelSerializer):

    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

    # def __init__(self, *args, **kwargs):
    #     super(InitialReportSerializer, self).__init__(*args, **kwargs)

    #     for field in self.fields.values():
    #         field.required = True


class IncidentDescriptionSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()

    class Meta:
        model = StaffIncidentReport
        fields = [
            "id",
            "location",
            "activity_at_time_of_incident",
            "incident_description",
            "preventive_measures",
        ]

    def __init__(self, *args, **kwargs):
        super(IncidentDescriptionSerializer, self).__init__(*args, **kwargs)

        for field in self.fields.values():
            field.required = True


class FinalReportSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()

    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super(FinalReportSerializer, self).__init__(*args, **kwargs)

        for field in self.fields.values():
            field.required = True


class ReportCompletedSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()

    class Meta:
        model = StaffIncidentReport
        fields = ["id", "report_completed"]

    def __init__(self, *args, **kwargs):
        super(ReportCompletedSerializer, self).__init__(*args, **kwargs)

        for field in self.fields.values():
            field.required = True


class FullReportSerializer(serializers.ModelSerializer):
    patient_info = GetProfileSerializer()
    doctor_consulted_info = GetProfileSerializer()
    report_facility = FacilitySerializer()

    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super(FullReportSerializer, self).__init__(*args, **kwargs)

        for field in self.fields.values():
            field.required = True

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class ModifyReportSerializer(serializers.ModelSerializer):
    patient_info = serializers.PrimaryKeyRelatedField(
        queryset=Profile.objects.all(), allow_null=True
    )
    doctor_consulted_info = serializers.PrimaryKeyRelatedField(
        queryset=Profile.objects.all(), allow_null=True
    )

    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class ModifyReportVersionSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffIncidentReportVersion
        fields = "__all__"


class IDResponseSerializer(serializers.Serializer):
    id = serializers.IntegerField()

    class Meta:
        model = StaffIncidentReport
        fields = ["id"]
