# from rest_framework import serializers

# from accounts.serializers import GetProfileSerializer, UserSerializer
# from facilities.serializers import FacilitySerializer
# from patient_visitor_grievance.models import Grievance, GrievanceVersion


# class GrievanceSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Grievance
#         fields = "__all__"


# class GrievanceSerializerVersion(serializers.ModelSerializer):
#     class Meta:
#         model = GrievanceVersion
#         fields = "__all__"


# class GrievanceListSerializer(serializers.ModelSerializer):
#     created_by = UserSerializer()
#     updated_by = UserSerializer()
#     report_facility = FacilitySerializer()
#     patient_name = GetProfileSerializer()
#     form_initiated_by = GetProfileSerializer()
#     complaint_made_by = GetProfileSerializer()
#     administrator_notified = GetProfileSerializer()

#     class Meta:
#         model = Grievance
#         fields = "__all__"
