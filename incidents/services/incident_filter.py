from datetime import datetime
from typing import Type
from accounts.models import Profile
from accounts.services.permions import AccountsPermissionsService
from accounts.services.profile.services import ProfileService
from base.services.department.services import DepartmentService
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.services.responses import RepositoryResponse
from base.utils.date_field_mapper import get_date_field_for_model
from facilities.services.facility_services import FacilityService
from django.db.models import Model

from incidents.permissions import IncidentPermissions
from incidents.services.permissions import IncidentPermissionsService
from incidents.services.role_based_query import RoleBasedIncidentQuery

facility_service = FacilityService()
department_service = DepartmentService()
profile_service = ProfileService()
permissions_management = PermissionsManagement()
logging_service = LoggingService()


class IncidentFilterService:

    def filter_incidents_by_access_to_facilities(
        self, profile: Profile, model: Model
    ) -> RepositoryResponse:
        """
        Filter incidents by access to facilities
        """

        access_ids = profile.access_to_facilities.values_list("id", flat=True)

        incidents = model.objects.filter(report_facility__id__in=access_ids)
        return RepositoryResponse(
            data=incidents,
            success=True,
            message="Incidents retrieved successfully for facilities",
        )

    def filter_incidents_by_access_to_departments(self, profile: Profile, model: Model):
        """
        Filter incidents by access to departments
        """
        access_ids = profile.access_to_department.values_list("id", flat=True)
        incidents = model.objects.filter(department__id__in=access_ids)
        return RepositoryResponse(
            data=incidents,
            success=True,
            message="Incidents retrieved successfully for departments",
        )

    def filter_incidents_by_facility(
        self, profile: Profile, model: Model
    ) -> RepositoryResponse:
        """
        Filter incidents by facility
        """

        incidents = model.objects.filter(report_facility__id=profile.facility.id)
        return RepositoryResponse(
            data=incidents,
            success=True,
            message="Incidents retrieved successfully for facility",
        )

    def filter_incidents_by_department(
        self, department_id: int, model: Model
    ) -> RepositoryResponse:
        """
        Filter incidents by department
        """

        incidents = model.objects.filter(department_id=department_id)

        return RepositoryResponse(
            data=incidents,
            success=True,
            message="Incidents retrieved successfully for department",
        )

    def apply_filters(self, incidents, filters, user, model: Type[Model] = None):
        """
        Apply filters to incidents queryset
        """
        user_profile = Profile.objects.get(user=user)
        permissions = IncidentPermissionsService(
            user=user,
            app_label=model._meta.app_label,
        )
        account_service = AccountsPermissionsService(user)

        # check permissions

        # 1. Check if they are super user  quality risk manager
        if account_service._user_in_group(["Super user"]):
            # -show all incidents
            pass
        # 2. Check if they are admin or director
        elif account_service._user_in_group(["Admin", "Director"]):
            # - Filter incidents by facility and access to facility
            response = RoleBasedIncidentQuery().get_incidents_by_admin(
                Model=model, user=user
            )

        # 3. Check if they are manager
        elif account_service._user_in_group(["Manager"]):
            # - Filter incidents by department and access to department
            incidents = self.filter_incidents_by_access_to_departments(
                user_profile, model
            )
        else:
            pass
        # 4. Check if assigned to the incident

        #  - Check if they are reviewers or member of a review group

        start_date = filters.get("start_date")
        end_date = filters.get("end_date")
        date_field = get_date_field_for_model(model)

        if filters.get("status"):
            incidents = incidents.filter(status=filters["status"])

        if filters.get("department_id"):
            incidents = incidents.filter(department_id=filters["department_id"])

        if filters.get("user_id"):
            incidents = incidents.filter(created_by_id=filters["user_id"])

        if filters.get("facility_id"):
            incidents = incidents.filter(report_facility_id=filters["facility_id"])

        if filters.get("current_step"):
            incidents = incidents.filter(current_step=filters["current_step"])

        if filters.get("is_resolved"):
            incidents = incidents.filter(is_resolved=filters["is_resolved"])

        if filters.get("is_modified"):
            incidents = incidents.filter(is_modified=filters["is_modified"])

        if (start_date or end_date) and date_field:
            incidents = incidents.exclude(**{f"{date_field}__isnull": True})
            if date_field:
                try:
                    if start_date:
                        start = datetime.strptime(start_date, "%Y-%m-%d").date()
                        incidents = incidents.filter(**{f"{date_field}__gte": start})
                    if end_date:
                        end = datetime.strptime(end_date, "%Y-%m-%d").date()
                        incidents = incidents.filter(**{f"{date_field}__lte": end})
                except ValueError as e:
                    logging_service.log_error(e)
            else:
                print(f"No date field defined for model: {model.__name__}")

        """
        If a model is provided, filter by its fields
        """
        if model:
            valid_fields = {
                field.name: field
                for field in model._meta.get_fields()
                if hasattr(field, "get_internal_type")
            }

            for key, value in filters.items():
                if "__" in key:
                    base_field = key.split("__")[0]
                    if base_field in valid_fields or hasattr(model, base_field):
                        incidents = incidents.filter(**{key: value})
                elif key in valid_fields:
                    """Skip already-handled filters"""
                    if key not in [
                        "status",
                        "department_id",
                        "user_id",
                        "facility_id",
                        "current_step",
                        "is_resolved",
                        "is_modified",
                    ]:
                        field_type = valid_fields[key].get_internal_type()

                        if field_type == "CharField" or field_type == "TextField":
                            """Case-insensitive match for string-based fields"""
                            incidents = incidents.filter(**{f"{key}__iexact": value})
                        else:
                            """Regular exact match for other field types"""
                            incidents = incidents.filter(**{key: value})

        return incidents
