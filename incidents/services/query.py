from django.contrib.auth.models import User
from typing import Type
from django.db.models import Model
from accounts.models import Profile
from accounts.services.permisions import AccountsPermissionsService
from accounts.services.profile.services import ProfileService
from base.services.department.services import DepartmentService
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.services.responses import RepositoryResponse
from facilities.services.facility_services import FacilityService
from incidents.services.incident_filter import IncidentFilterService
from incidents.services.permissions import IncidentPermissionsService
from incidents.services.query_utils import QueryUtilsServices
from incidents.services.role_based_query import RoleBasedIncidentQuery

logging_service = LoggingService()
facility_service = FacilityService()
department_service = DepartmentService()
profile_service = ProfileService()
permissions_management = PermissionsManagement()
logging_service = LoggingService()
filter_service = IncidentFilterService()
query_utils = QueryUtilsServices()


class IncidentQueryService:
    def __init__(self, user, model: Type[Model], serializer):
        self.model = model
        self.user = user
        self.serializer = serializer
        self.permissions = IncidentPermissionsService(
            user=self.user,
            app_label=self.model._meta.app_label,
        )

    def get_incidents(
        self,
        user: User,
        model: Type[Model],
        serializer,
        prefetch_fields=[],
        related_fields=[],
        filters={},
    ) -> RepositoryResponse:
        """
        This is a helper function to get incidents
        User it to retrieve incidents, filter them and paginate them
        Filters include:
            1. page: Page number
            2. page_size: Number of incidents to retrieve per page
            3. status: Status of incidents to retrieve
            4. department_id: Department to retrieve incidents for
            5. user_id: User id to retrieve incidents for a specific user
            6. facility_id: Facility to retrieve incidents for specific user
        """

        user_profile = Profile.objects.get(user=user)

        account_service = AccountsPermissionsService(user)
        try:
            # check permissions
            permissions_response = self.permissions.can_list_incidents()
            if not permissions_response.success:
                return RepositoryResponse(
                    data=None, success=False, message=permissions_response.message
                )

            # Initialize role-based query instance
            role_query = RoleBasedIncidentQuery(
                prefetch_fields=prefetch_fields, related_fields=related_fields
            )

            # Determine user role and get appropriate incidents
            if account_service._user_in_group(["Super user"]):
                # 1. Check if they are super user quality risk manager - show all incidents
                incidents = role_query.get_incidents_by_superuser(
                    Model=model, user=user
                )
                # Apply filters for superuser
                if filters:
                    incidents = filter_service.apply_filters(
                        incidents=incidents, filters=filters, model=model, user=user
                    )
            elif account_service._user_in_group(["Admin"]):
                # 2. Check if they are admin - show all incidents in all facilities
                incidents = role_query.get_incidents_by_admin(Model=model, user=user)
                # Apply filters for admin
                if filters:
                    incidents = filter_service.apply_filters(
                        incidents=incidents, filters=filters, model=model, user=user
                    )
            elif account_service._user_in_group(["Director"]):
                # 3. Check if they are director - show all incidents in their facilities
                incidents = role_query.get_incidents_by_director(
                    Model=model, user=user, facility=user_profile.facility
                )
                # Apply filters for director
                if filters:
                    incidents = filter_service.apply_filters(
                        incidents=incidents, filters=filters, model=model, user=user
                    )
            elif account_service._user_in_group(["Manager"]):
                # 4. Check if they are manager - show all incidents in their department
                incidents = role_query.get_incidents_by_manager(
                    Model=model, user=user, department=user_profile.department
                )
                # Apply filters for manager
                if filters:
                    incidents = filter_service.apply_filters(
                        incidents=incidents, filters=filters, model=model, user=user
                    )
            else:
                # 5. Regular users - get incidents they created OR are assigned to review
                user_incidents = role_query.get_incidents_by_user(
                    Model=model, user=user
                )
                reviewer_incidents = role_query.get_incidents_by_reviewers(
                    Model=model, user=user
                )

                # Apply filters before union if provided
                if filters:
                    user_incidents = filter_service.apply_filters(
                        incidents=user_incidents,
                        filters=filters,
                        model=model,
                        user=user,
                    )
                    reviewer_incidents = filter_service.apply_filters(
                        incidents=reviewer_incidents,
                        filters=filters,
                        model=model,
                        user=user,
                    )

                # Combine and deduplicate incidents
                incidents = user_incidents.union(reviewer_incidents)
            response = query_utils.get_latest_incident_reports(
                incidents,
                serializer,
            )
            if not response.success:
                return RepositoryResponse(
                    data=None,
                    success=False,
                    message="Error retrieving incidents",
                )

            return RepositoryResponse(
                data=response.data,
                success=True,
                message="Incidents retrieved successfully",
            )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Internal server error",
            )

    def get_incident_by_id(self, incident_id):
        """
        Get incident by id
        """
        try:
            incident = self.model.objects.get(id=incident_id)

            permissions = IncidentPermissionsService(
                user=self.user, app_label=self.model._meta.app_label, incident=incident
            )
            permissions_response = permissions.can_view_incident()
            if not permissions_response.success:
                return RepositoryResponse(
                    data=None, success=False, message=permissions_response.message
                )

            success, serialized_incident, modifications, message = (
                self.get_latest_version(
                    incident_data=incident,
                    modelName=self.model,
                    incidentSerializer=self.serializer,
                    incident_id=incident_id,
                )
            )
            serialize_original = self.serializer(incident).data

            if not success:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=message,
                )

            return RepositoryResponse(
                data={
                    "incident": serialized_incident,
                    "original_incident": serialize_original,
                    "modifications": modifications,
                },
                success=True,
                message="Incident retrieved successfully",
            )

        except self.model.DoesNotExist:
            return RepositoryResponse(
                data=None,
                success=False,
                message="Incident not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Internal server error",
            )

    def get_latest_version(
        self, incident_data, modelName, incidentSerializer, incident_id
    ):
        modifications = {}
        incident_latest_version = None
        try:
            if incident_data.versions.count() > 0:
                incident_latest_version = (
                    modelName.objects.get(id=incident_id)
                    .versions.order_by("-created_at")
                    .first()
                )
            else:
                incident_latest_version = incident_data
            incident = incidentSerializer(incident_latest_version).data
            incident["status"] = incident_data.status
            modifications = incident_data.versions.all().order_by("created_at")

            modifications = {
                "count": modifications.count(),
                "versions": [
                    {
                        "date": incident_data.created_at,
                        "id": incident_data.id,
                        "original": True,
                        "latest": incident_data.id == incident_latest_version.id,
                    }
                ]
                + [
                    {
                        "date": modification.created_at,
                        "id": modification.id,
                        "latest": modification.id == incident_latest_version.id,
                        "original": False,
                    }
                    for modification in modifications
                    if modification.id
                ],
            }

            return True, incident, modifications, "success"

        except Exception as e:
            logging_service.log_error(e)
            return False, None, None, "Internal server error"
