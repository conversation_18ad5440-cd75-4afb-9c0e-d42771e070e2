from accounts.models import (
    Profile,
)
from api.views.auth.permissions_list import is_admin_user, is_super_user
from base.constants import ReviewStatus
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse, RepositoryResponse
from datetime import datetime, timedelta
from typing import Type, List
from django.db import transaction
from django.core.exceptions import ValidationError
from django.forms.models import model_to_dict
from django.db.models import Model
from django.contrib.auth.models import User
from rest_framework import status

from incidents.emails.incident_notification import send_incident_notification
from tasks.models import ReviewProcess, ReviewProcessTasks, ReviewTemplates

logging_service = LoggingService()


class WorkFlowUtils:
    """
    A utility class for managing workflow-related operations.
    """

    def __init__(self, user: User, model: Type[Model]):
        """
        Initialize the WorkFlowUtils class.

        Args:
            user (User): The user associated with the workflow.
            model (Type[Model]): The model class for the incident.
        """
        self.model = model
        self.user = user

    def handle_review_template(
        self, review_template_id, incident_id
    ) -> RepositoryResponse:
        """
        Handle the review template for the incident, creating a ReviewProcess and ReviewProcessTasks.

        Args:
            review_template_id (int): ID of the ReviewTemplates instance.
            incident_id (int): ID of the model instance.

        Returns:
            RepositoryResponse: Result of the operation.
        """
        try:
            # Fetch the review template and incident
            review_template = ReviewTemplates.objects.get(id=review_template_id)
            incident = self.model.objects.get(id=incident_id)

            with transaction.atomic():
                # Create ReviewProcess from ReviewTemplates

                incident.review_process = ReviewProcess.objects.create(
                    name=review_template.name,
                    description=review_template.description,
                )
                incident.save()

                # Create ReviewProcessTasks from ReviewTemplateTasks
                for task in review_template.review_template_tasks.all():

                    number_of_days = (
                        task.number_of_days_to_complete or 3
                    )  # Default to 3 if null
                    deadline = datetime.now().date() + timedelta(days=number_of_days)

                    try:
                        new_task = ReviewProcessTasks.objects.create(
                            name=task.name,
                            description=task.description,
                            task_priority=task.task_priority,
                            deadline=deadline,
                            review_process=incident.review_process,
                            require_approval_for_all_groups=task.require_approval_for_all_groups,
                        )

                        # Copy ManyToMany fields
                        new_task.review_groups.set(task.review_groups.all())

                    except Exception as e:
                        logging_service.log_error(
                            f"Failed to create task {task.name}: {e}"
                        )
                        continue

                # TODO: Handle notification to review group members
                # check a task that has priority 1
                self.handle_template_emails(self.user, incident)
                return RepositoryResponse(
                    success=True,
                    message="Review template associated successfully.",
                    data=incident,
                )

        except ReviewTemplates.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Review template not found.",
                data=None,
            )
        except self.model.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Incident not found.",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(f"Error associating review template: {e}")
            return RepositoryResponse(
                success=False,
                message="An error occurred while associating the review template.",
                data=None,
            )

    def handle_assigned_members(self, incident_id, data) -> RepositoryResponse:
        """
        Handle the assigned members for the review template.
        """
        try:
            #  get members
            members = []
            incident = self.model.objects.get(id=incident_id)
            for member in data["assignees"]:
                try:
                    member_instance = Profile.objects.get(id=member)
                    members.append(member_instance)
                except Profile.DoesNotExist:
                    continue

            # create a review_process
            # TODO: We will need to move this to a proper app
            try:
                review_process, _ = ReviewProcess.objects.get_or_create(
                    name="Assigned members",
                    description="Automatically created review process for assigned members",
                )
                # create a task
                new_task = ReviewProcessTasks.objects.create(
                    review_process=review_process,
                    name=f"Assigned incident - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    description=data.get("description", ""),
                    task_priority=1,
                    require_approval_for_all_groups=data.get(
                        "require_approval_for_all_groups", False
                    ),
                    deadline=datetime.now().date()
                    + timedelta(days=data.get("number_of_days", 3)),
                )
            except Exception as e:
                logging_service.log_error(e)
                return RepositoryResponse(
                    success=False,
                    message="Failed to create task.",
                    data=None,
                )

            new_task.reviewers.add(*members)
            incident.review_process = review_process
            incident.review_tasks.add(new_task)

            users = [member.user for member in members if member.user]
            incident.assignees.add(*users)

            incident.save()
            new_task.save()

            """Handle notification to the reviewers"""
            self.handle_template_emails(
                self.user,
                incident=incident,
                members=members,
            )
            return RepositoryResponse(
                success=True,
                message="Assigned members updated successfully.",
                data=incident,
            )
        except self.model.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Incident not found.",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while updating assigned members.",
                data=None,
            )

    def handle_template_emails(self, user, incident, members=[]):
        try:

            incident_tasks = ReviewProcessTasks.objects.filter(
                review_process=incident.review_process
            )

            notified_members = set()
            if not members:
                for task in incident_tasks:

                    if task.task_priority == 1:

                        review_groups = task.review_groups.all()
                        for group in review_groups:
                            group_members = group.members.all()
                            for member in group_members:
                                if member not in notified_members:

                                    notified_members.add(member)
            else:
                notified_members = members

            for member in notified_members:
                send_incident_notification(
                    recipient_email=member.user.email,
                    first_name=member.user.first_name,
                    incident_id=incident.id,
                    date_created=incident.created_at,
                    created_by=incident.created_by,
                    incident_type="Hello there",
                    user=self.user,
                )

        except Exception as e:
            logging_service.log_error(e)
