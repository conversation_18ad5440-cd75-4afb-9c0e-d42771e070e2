from accounts.models import Profile
from accounts.services.permisions import AccountsPermissionsService
from base.services.responses import RepositoryResponse
from typing import Type
from django.contrib.auth.models import User


class IncidentPermissionsService:
    def __init__(self, user: Type[User], app_label, incident=None):
        self.user = user
        self.incident = incident
        self.account_service = AccountsPermissionsService(self.user)
        self.app_label = app_label

    def can_create_incident(self) -> RepositoryResponse:
        """
        Can create incident
        Conditions:
        -User must be authenticated and logged in
        -Any authenticated user can create incidents (no additional role restrictions)
        """
        if self.user.is_active:
            return RepositoryResponse(
                success=True,
                message="User can create incidents",
            )
        # Any authenticated user can create incidents
        return RepositoryResponse(
            success=False,
            message="User cannot create incidents, account is not active",
        )

    def can_view_incident(self) -> RepositoryResponse:
        """
        Can view incident
        Conditions:
        -User must be logged in
        -User must be the owner of the incident
        -Or Your profile in any review_task.reviewers (direct reviewer assignment)
        -Or Your profile in any review_task.review_groups.members (group member assignment)
        -Or have incident.view_details permission
        -Or User is in [Super user, Admin, Quality/Risk manager]
        """
        # check if incident has created_by
        if not self.incident or not self.incident.created_by:
            return RepositoryResponse(
                success=False,
                message="Incident or user who created it is not found",
            )
        # Check if incident exists
        if not self.incident:
            return RepositoryResponse(success=False, message="Incident not found")

        # Owner can always view
        if self._is_owner():
            return RepositoryResponse(success=True, message="User is owner of incident")

        # Check if user is in review tasks
        if self._is_in_review_tasks():
            return RepositoryResponse(
                success=True, message="User is assigned to review tasks"
            )

        # Check explicit permission
        if self.account_service._user_has_per([f"{self.app_label}.view_details"]):
            return RepositoryResponse(
                success=True, message="User has explicit view permission"
            )

        # Check role-based access with facility/department validation
        if self._has_role_based_access():
            return RepositoryResponse(
                success=True, message="User has role-based access"
            )

        # Default deny
        return RepositoryResponse(
            success=False,
            message="You do not have permission to view this incident",
            data="You must be the owner, assigned to review tasks, have explicit permissions, or be in authorized role with facility access",
        )

    def can_modify_incident(self) -> RepositoryResponse:
        """
        Can modify incident

        Condition:
        -Your Profile in any review_task.reviewers (direct reviewer assignment)
        -Or Your Profile in any review_task.review_groups.members (group member assignment)
        -Or have incident.can_view_incident permission
        -Or User is in [Super user, Admin, Quality/Risk manager]
        -NOTE: Directors have read-only access and cannot modify incidents
        -NOTE: Regular users cannot modify incidents, even their own (per userFlow.md), UNLESS they are assigned reviewers
        """

        # Check if incident exists
        if not self.incident:
            return RepositoryResponse(success=False, message="Incident not found")

        # Directors have read-only access and cannot modify incidents
        if self.account_service._user_in_group(["Director"]):
            return RepositoryResponse(
                success=False,
                message="Directors have read-only access and cannot modify incidents",
            )

        # Check if user is in review tasks (takes precedence over group restrictions)
        if self._is_in_review_tasks():
            return RepositoryResponse(
                success=True, message="User is assigned to review tasks"
            )

        # Check explicit permission
        if self.account_service._user_has_per([f"{self.app_label}.change_incident"]):
            return RepositoryResponse(
                success=True, message="User has explicit modify permission"
            )

        # Check role-based access with facility/department validation (excluding Directors)
        if self._has_role_based_access_for_modification():
            return RepositoryResponse(
                success=True, message="User has role-based access"
            )

        # Regular users (not in any special group) cannot modify incidents, even their own
        user_groups = self.user.groups.values_list("name", flat=True)
        special_groups = [
            "Super user",
            "Admin",
            "Quality/Risk manager",
            "Manager",
            "Director",
            "User Editor",
        ]
        if not any(group in special_groups for group in user_groups):
            return RepositoryResponse(
                success=False,
                message="Regular users cannot modify incidents, even their own reports",
            )

        # Default deny
        return RepositoryResponse(
            success=False,
            message="You do not have permission to modify this incident",
            data="You must be assigned to review tasks, have explicit permissions, or be in authorized role with facility access",
        )

    def can_delete_incident(self) -> RepositoryResponse:
        """
        Can delete incident

        Conditions:
        -Only super user can delete and incident
        """

        # Check if incident exists
        if not self.incident:
            return RepositoryResponse(success=False, message="Incident not found")

        # Only Super user can delete incidents
        if self.account_service._user_in_group(["Super user"]):
            return RepositoryResponse(
                success=True, message="Super user can delete incidents"
            )

        # Default deny
        return RepositoryResponse(
            success=False, message="Only Super users can delete incidents"
        )

    def can_list_incidents(self) -> RepositoryResponse:
        """
        Can list incidents
        Conditions:
        -User must be logged in
        -User must be the owner of the incident
        -Or Your profile in any review_task.reviewers (direct reviewer assignment)
        -Or Your profile in any review_task.review_groups.members (group member assignment)
        -Or have incident.can_view_incident permission
        -Or User is in [Super user, Admin, Quality/Risk manager]
        """

        # For listing incidents, we don't need a specific incident
        # Any authenticated user with appropriate permissions can list incidents

        # Check explicit permission
        if self.account_service._user_has_per([f"{self.app_label}.view_list"]):
            return RepositoryResponse(
                success=True, message="User has explicit list permission"
            )

        # Check role-based access (no facility/department validation needed for listing)
        if self.account_service._user_in_group(
            ["Super user", "Admin", "Quality/Risk manager", "Manager"]
        ):
            return RepositoryResponse(
                success=True, message="User has role-based access"
            )

        # Default: authenticated users can list their own incidents
        return RepositoryResponse(
            success=True, message="User can list their own incidents"
        )

    def can_send_to_department(self) -> RepositoryResponse:
        """
        Can send incident to department
        Conditions:
        -Quality/Risk manager and Admin can send incident to department
        """

        # Check if incident exists
        if not self.incident:
            return RepositoryResponse(success=False, message="Incident not found")

        # Quality/Risk Manager and Admin can send to department
        if self.account_service._user_in_group(["Quality/Risk manager", "Admin"]):
            return RepositoryResponse(
                success=True,
                message="User can send incident to department",
            )

        # Default deny
        return RepositoryResponse(
            success=False,
            message="Only Quality/Risk managers and Admins can send incidents to departments",
        )

    def can_add_review(self) -> RepositoryResponse:
        """
        Can add review to incident
        Conditions:
        -User must be logged in
        -Or Your profile in any review_task.reviewers (direct reviewer assignment)
        -Or Your profile in any review_task.review_groups.members (group member assignment)
        -Or have incident.can_view_incident permission
        -Or User is in [Super user, Admin, Quality/Risk manager, Manager]
        -NOTE: Directors have read-only access and cannot add reviews
        -NOTE: Regular users cannot add reviews to incidents, even their own, UNLESS they are assigned reviewers
        """
        # Check if incident exists
        if not self.incident:
            return RepositoryResponse(success=False, message="Incident not found")

        # Directors have read-only access and cannot add reviews
        if self.account_service._user_in_group(["Director"]):
            return RepositoryResponse(
                success=False,
                message="Directors have read-only access and cannot add reviews",
            )

        # Check if user is in review tasks (takes precedence over group restrictions)
        if self._is_in_review_tasks():
            return RepositoryResponse(
                success=True, message="User is assigned to review tasks"
            )

        # Check explicit permission
        if self.account_service._user_has_per([f"{self.app_label}.add_review"]):
            return RepositoryResponse(
                success=True, message="User has explicit add review permission"
            )

        # Check role-based access with facility/department validation
        if self._has_role_based_access_for_modification():
            return RepositoryResponse(
                success=True, message="User has role-based access"
            )

        # Regular users (not in any special group) cannot add reviews
        user_groups = self.user.groups.values_list("name", flat=True)
        special_groups = [
            "Super user",
            "Admin",
            "Quality/Risk manager",
            "Manager",
            "Director",
            "User Editor",
        ]
        if not any(group in special_groups for group in user_groups):
            return RepositoryResponse(
                success=False,
                message="Regular users cannot add reviews to incidents",
            )

        # Default deny
        return RepositoryResponse(
            success=False,
            message="You do not have permission to add reviews to this incident",
            data="You must be assigned to review tasks, have explicit permissions, or be in authorized role with facility access",
        )

    def can_add_severity_rating(self) -> RepositoryResponse:
        """
        Can add severity rating to incident
        Conditions:
        -Only Quality/Risk manager can add severity rating
        -Managers cannot add severity ratings (restricted action)
        """

        # Check if incident exists
        if not self.incident:
            return RepositoryResponse(success=False, message="Incident not found")

        # Managers cannot add severity ratings (restricted action)
        if self.account_service._user_in_group(["Manager"]):
            return RepositoryResponse(
                success=False,
                message="Managers cannot add severity ratings (restricted action)",
            )

        # Only Quality/Risk Manager can add severity rating
        if self.account_service._user_in_group(["Quality/Risk manager"]):
            return RepositoryResponse(
                success=True, message="Quality/Risk manager can add severity rating"
            )

        # Default deny
        return RepositoryResponse(
            success=False, message="Only Quality/Risk managers can add severity ratings"
        )

    def can_mark_as_resolved(self) -> RepositoryResponse:
        """
        Can mark incident as resolved
        Conditions:
        -Only Quality/Risk manager can mark incident as resolved
        -Users cannot close their own incidents (system-enforced rule)
        """

        # Check if incident exists
        if not self.incident:
            return RepositoryResponse(success=False, message="Incident not found")

        # Prevent users from closing their own incidents (system-enforced rule)
        if self._is_owner():
            return RepositoryResponse(
                success=False,
                message="Users cannot close their own incidents (system-enforced rule)",
            )

        # Only Quality/Risk Manager can mark as resolved
        if self.account_service._user_in_group(["Quality/Risk manager"]):
            return RepositoryResponse(
                success=True,
                message="Quality/Risk manager can mark incident as resolved",
            )

        # Default deny
        return RepositoryResponse(
            success=False,
            message="Only Quality/Risk managers can mark incidents as resolved, and users cannot close their own incidents",
        )

    def can_export_data(self) -> RepositoryResponse:
        """
        Can export incident data/logs
        Conditions:
        -Admins can export facility logs
        -Quality/Risk managers can export all facility logs
        -Directors can export data (read-only)
        -Managers can export dept logs (limited)
        -Super users can export all data
        """

        # Super user can export all data
        if self.account_service._user_in_group(["Super user"]):
            return RepositoryResponse(
                success=True,
                message="Super user can export all data",
            )

        # Quality/Risk Manager can export all facility logs
        if self.account_service._user_in_group(["Quality/Risk manager"]):
            return RepositoryResponse(
                success=True,
                message="Quality/Risk manager can export data",
            )

        # Admin can export facility logs with facility access
        if self.account_service._user_in_group(["Admin"]):
            if self._has_facility_access():
                return RepositoryResponse(
                    success=True,
                    message="Admin can export facility data",
                )
            else:
                return RepositoryResponse(
                    success=False,
                    message="Admin needs facility access to export data",
                )

        # Director can export data (read-only) with facility access
        if self.account_service._user_in_group(["Director"]):
            if self._has_facility_access():
                return RepositoryResponse(
                    success=True,
                    message="Director can export data (read-only)",
                )
            else:
                return RepositoryResponse(
                    success=False,
                    message="Director needs facility access to export data",
                )

        # Manager can export dept logs (limited) with department access
        if self.account_service._user_in_group(["Manager"]):
            if self._has_department_access():
                return RepositoryResponse(
                    success=True,
                    message="Manager can export dept logs (limited)",
                )
            else:
                return RepositoryResponse(
                    success=False,
                    message="Manager needs department access to export data",
                )

        # Default deny
        return RepositoryResponse(
            success=False,
            message="User does not have permission to export data",
        )

    def can_manage_restricted_access(self) -> RepositoryResponse:
        """
        Can grant/revoke restricted access permissions
        Conditions:
        -Only Admins and Super users can grant/revoke restricted access
        -Quality/Risk managers can assign restricted access
        """

        # Super user has full access management rights
        if self.account_service._user_in_group(["Super user"]):
            return RepositoryResponse(
                success=True,
                message="Super user can manage restricted access",
            )

        # Admin can grant/revoke restricted access
        if self.account_service._user_in_group(["Admin"]):
            return RepositoryResponse(
                success=True,
                message="Admin can manage restricted access",
            )

        # Quality/Risk Manager can assign restricted access
        if self.account_service._user_in_group(["Quality/Risk manager"]):
            return RepositoryResponse(
                success=True,
                message="Quality/Risk manager can assign restricted access",
            )

        # Default deny
        return RepositoryResponse(
            success=False,
            message="Only Admins, Quality/Risk managers, and Super users can manage restricted access",
        )

    def can_view_users_incidents(
        self, profile_id, logged_in_profile_id
    ) -> RepositoryResponse:
        """
        Can view another user's incidents

        Conditions
        -Profile ids must be equal
        -User must be in groups "Admin, Quality/Risk manager"
        """
        #  check permissions
        if (
            not profile_id == logged_in_profile_id
            and not self.account_service._user_in_group(
                ["Admin", "Quality/Risk manager"]
            )
        ):
            return RepositoryResponse(
                success=False,
                message="You do not have permission to view this user's incidents",
                data="Profiles must be equal or user must be in Admin/Quality/Risk manager group",
            )
        return RepositoryResponse(
            success=True,
            message="User has permission to view this user's incidents",
        )

    def _is_owner(self):
        if not self.incident or not self.incident.created_by:
            return False
        return self.incident.created_by.id == self.user.id

    def _is_in_review_tasks(self):
        """Check if user is assigned to any review task (as reviewer or group member)"""
        try:
            profile = Profile.objects.get(user=self.user)

            # Check all review tasks for this incident
            for review_task in self.incident.review_tasks.all():
                # Check if user is direct reviewer
                if profile in review_task.reviewers.all():
                    return True

                # Check if user is member of any review group
                for review_group in review_task.review_groups.all():
                    if profile in review_group.members.all():
                        return True

            return False
        except Profile.DoesNotExist:
            return False

    def _has_role_based_access(self):
        """Check if user has role-based access with facility/department validation"""
        # Super user - no facility restriction needed
        if self.account_service._user_in_group(["Super user"]):
            return True

        # Admin - needs facility access
        if self.account_service._user_in_group(["Admin"]):
            return self._has_facility_access()

        # Quality/Risk Manager - needs facility access
        if self.account_service._user_in_group(["Quality/Risk manager"]):
            return self._has_facility_access()

        # Director - needs facility access (read-only)
        if self.account_service._user_in_group(["Director"]):
            return self._has_facility_access()

        # Manager - needs department access
        if self.account_service._user_in_group(["Manager"]):
            return self._has_department_access()

        return False

    def _has_role_based_access_for_modification(self):
        """Check if user has role-based access for modification (excludes Directors)"""
        # Super user - no facility restriction needed
        if self.account_service._user_in_group(["Super user"]):
            return True

        # Admin - needs facility access
        if self.account_service._user_in_group(["Admin"]):
            return self._has_facility_access()

        # Quality/Risk Manager - needs facility access
        if self.account_service._user_in_group(["Quality/Risk manager"]):
            return self._has_facility_access()

        # Manager - needs department access
        if self.account_service._user_in_group(["Manager"]):
            return self._has_department_access()

        # Directors are excluded from modification access
        return False

    def _has_facility_access(self):
        """Check if user has access to incident's facility"""
        try:
            profile = Profile.objects.get(user=self.user)
            if (
                hasattr(profile, "access_to_facilities")
                and self.incident.report_facility
            ):
                return (
                    self.incident.report_facility in profile.access_to_facilities.all()
                )
            return False
        except Profile.DoesNotExist:
            return False

    def _has_department_access(self):
        """Check if user has access to incident's department"""
        try:
            profile = Profile.objects.get(user=self.user)
            if hasattr(profile, "access_to_department") and self.incident.department:
                return self.incident.department in profile.access_to_department.all()
            return False
        except Profile.DoesNotExist:
            return False
