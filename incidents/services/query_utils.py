from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage

from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse

logging_service = LoggingService()


class QueryUtilsServices:

    def _paginate(self, incidents, page, page_size):
        """
        Paginate incidents queryset
        """
        if page <= 0 or page_size <= 0:
            raise ValueError("Page and page_size must be positive integers.")
        paginator = Paginator(incidents, page_size)
        try:
            incidents = paginator.page(page)
        except PageNotAnInteger:
            incidents = paginator.page(1)
        except EmptyPage:
            incidents = paginator.page(paginator.num_pages)

        return incidents

    def get_latest_incident_reports(self, incidents, serializer) -> RepositoryResponse:
        incidents_list = incidents
        versions = []
        try:
            for item in incidents_list:
                if hasattr(item, "versions") and callable(
                    getattr(item.versions, "last", None)
                ):
                    version = item.versions.last()
                else:
                    version = None
                if version:
                    serialized_version = serializer(version).data
                    serialized_version["original_report"] = item.id
                    serialized_version["status"] = item.status
                    versions.append(serialized_version)
                else:
                    versions.append(serializer(item).data)
            return RepositoryResponse(
                data=versions,
                success=True,
                message="Incidents retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Internal server error",
            )
