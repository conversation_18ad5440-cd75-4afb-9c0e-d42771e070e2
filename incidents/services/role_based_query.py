from accounts.models import Profile
from accounts.services.profile.services import ProfileService
from base.models import Department
from base.services.department.services import DepartmentService
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.services.responses import RepositoryResponse
from facilities.services.facility_services import FacilityService
from django.db.models import Q

facility_service = FacilityService()
department_service = DepartmentService()
profile_service = ProfileService()
permissions_management = PermissionsManagement()
logging_service = LoggingService()


class RoleBasedIncidentQuery:
    def __init__(self, prefetch_fields=None, related_fields=None):
        self.prefetch_fields = prefetch_fields or []
        self.related_fields = related_fields or []

    # 1. Check if they are super user quality risk manager - show all incidents
    def get_incidents_by_superuser(self, Model, user):
        try:
            # Super users can see all incidents - no filtering needed
            incidents = Model.objects.all().prefetch_related(
                *self.prefetch_fields, *self.related_fields
            )

            return incidents
        except Exception as e:
            logging_service.log_error(e)
            return Model.objects.none()

    # 2. Check if they are admin or director - Filter incidents by facility and access to facility
    def get_incidents_by_admin(self, Model, user):
        try:
            try:
                profile = Profile.objects.get(user=user)
            except Profile.DoesNotExist:
                return Model.objects.none()

            if not profile.facility:
                return Model.objects.none()

            access_to_facility_ids = profile.access_to_facilities.values_list(
                "id", flat=True
            )
            # Filter incidents using report_facility__id
            incidents = Model.objects.filter(
                report_facility__id__in=access_to_facility_ids
            ).prefetch_related(*self.prefetch_fields, *self.related_fields)

            return incidents

        except Exception as e:
            logging_service.log_error(e)
            return Model.objects.none()

    # 3. Check if they are manager - Filter incidents by department and access to department
    def get_incidents_by_manager(self, Model, user, department, accessible_to=""):
        """
        Filter incidents by departments a manager has access to

        Args:
            Model: Model class to query
            user: User instance requesting the data
            department: Department instance
            accessible_to: Optional specific department name to filter by
        """
        try:
            if not department:
                return Model.objects.none()

            try:
                profile = Profile.objects.get(user=user)
            except Profile.DoesNotExist:
                return Model.objects.none()

            if not profile.facility:
                return Model.objects.none()

            # Get accessible department IDs more efficiently
            department_ids = profile.access_to_department.values_list("id", flat=True)
            incidents = Model.objects.filter(
                department__id__in=department_ids
            ).prefetch_related(*self.prefetch_fields, *self.related_fields)

            if accessible_to:
                # Apply department filter directly without returning RepositoryResponse
                try:
                    department = Department.objects.filter(
                        header_of_department=user,
                        name=accessible_to,
                        facility=profile.facility,
                    ).first()
                    if department:
                        incidents = incidents.filter(department=department)
                    else:
                        # If department not found, return empty QuerySet
                        return Model.objects.none()
                except Exception as e:
                    logging_service.log_error(e)
                    return Model.objects.none()

            return incidents

        except Exception as e:
            logging_service.log_error(e)
            return Model.objects.none()

    # Additional helper method for director filtering (similar to admin but for single facility)
    def get_incidents_by_director(self, Model, facility, user=None, accessible_to=""):
        try:
            if not facility:
                return Model.objects.none()

            incidents = Model.objects.filter(report_facility=facility).prefetch_related(
                *self.prefetch_fields, *self.related_fields
            )
            if accessible_to and user:
                # Apply department filter directly without returning RepositoryResponse
                try:
                    department = Department.objects.filter(
                        header_of_department=user,
                        name=accessible_to,
                        facility=facility,
                    ).first()
                    if department:
                        incidents = incidents.filter(department=department)
                    else:
                        # If department not found, return empty QuerySet
                        return Model.objects.none()
                except Exception as e:
                    logging_service.log_error(e)
                    return Model.objects.none()

            return incidents
        except Exception as e:
            logging_service.log_error(e)
            return Model.objects.none()

    def filter_incidents_by_department(self, user, incidents, accessible_to, facility):

        try:
            department = Department.objects.filter(
                header_of_department=user,
                name=accessible_to,
                facility=facility,
            ).first()
            if department:
                incidents = incidents.filter(department=department).prefetch_related(
                    *self.prefetch_fields, *self.related_fields
                )
                return RepositoryResponse(
                    success=True,
                    data=incidents,
                    message="Incidents retrieved successfully for department",
                )
            else:
                return RepositoryResponse(
                    success=None,
                    data=[],
                    message=f"Insufficient access to {accessible_to} department ",
                )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Error filtering incidents by department",
            )

    # filter incidents by permissions(user)
    def get_incidents_by_user(self, Model, user):
        try:
            incidents = Model.objects.filter(created_by=user).prefetch_related(
                *self.prefetch_fields, *self.related_fields
            )
            return incidents
        except Exception as e:
            logging_service.log_error(e)
            return Model.objects.none()

    # 4. Check if assigned to the incident - filter incidents by review_tasks.reviewers or review_tasks.review_groups.members
    def get_incidents_by_reviewers(self, user, Model=None, **kwargs):
        """
        Get incidents where the user is assigned as a reviewer.
        This includes both direct reviewer assignments and review group memberships.
        """
        try:
            user_profile = Profile.objects.get(user=user)
        except Profile.DoesNotExist:
            return Model.objects.none()

        try:
            # Special case for GrievanceInvestigation which doesn't have standard review structure
            if Model.__name__ == "GrievanceInvestigation":
                # For now, return empty QuerySet as GrievanceInvestigation doesn't support standard reviewers
                # TODO: Implement proper reviewer logic for investigations if needed
                return Model.objects.none()

            # For other models with review_tasks field
            incidents = (
                Model.objects.filter(
                    Q(review_tasks__reviewers=user_profile)
                    | Q(review_tasks__review_groups__members=user_profile)
                )
                .distinct()
                .prefetch_related(*self.prefetch_fields, *self.related_fields)
            )

            return incidents
        except Exception as e:
            logging_service.log_error(e)
            return Model.objects.none()
