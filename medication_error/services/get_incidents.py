from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse

incidents_service = GetIncidentsService()


class MedicationErrorServices:
    def get_incidents_list(
        self,
        user,
        facility_id=None,
        department_id=None,
    ):

        try:
            incidents = incidents_service.get_incidents(
                app_label="medication_error",
                model_name="MedicationError",
                user=user,
                department_id=department_id,
                facility_id=facility_id,
                accessible_to="Pharmacy",
            )

            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                    data=None,
                    code=400,
                )
            return APIResponse(
                success=True,
                message="Incidents retrieved successfully",
                data=incidents.data,
                code=200,
            )
        except Exception as e:
            LoggingService().log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )
