from accounts.services.user_profile.service import UserProfileService
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.constants import ReviewStatus
from base.services.incidents.base import IncidentService
from base.services.responses import APIResponse
from incidents.services.permissions import IncidentPermissionsService
from medication_error.models import MedicationError
from base.services.logging.logger import LoggingService
from medication_error.new_serializers import GetMedicationErrorSerializer
from medication_error.seriaizers import MedicalErrorVersionSerializer
from incidents.services.workflow import IncidentWorkflow
from rest_framework import status
from django.core.exceptions import ValidationError
from incidents.views.send_to_department import send_incident_submission_email
from activities.services import ActivityLogService
from activities.models import ActivityType
from tasks.models import ReviewTemplates


logging_service = LoggingService()
user_profile_service = UserProfileService()


class MedicationErrorActionsService:
    """
    Service class for handling Medication Error incident actions.
    """

    def __init__(self, user, incident_id, data):
        self.user = user
        self.data = data
        self.incident_id = incident_id
        self.workflow_services = IncidentWorkflow(model=MedicationError, user=self.user)
        self.general_incident = IncidentService()
        try:
            self.incident = (
                MedicationError.objects.select_related(
                    "report_facility",
                    "department",
                    "created_by",
                    "patient",
                    "provider_info",
                )
                .prefetch_related(
                    "documents",
                    "reviews",
                )
                .get(id=incident_id)
            )
        except MedicationError.DoesNotExist:
            self.incident = None
        self.permissions = IncidentPermissionsService(
            user=self.user,
            app_label=MedicationError._meta.app_label,
            incident=self.incident,
        )

    def modify_incident(self) -> APIResponse:
        """Modifies an existing Medication Error incident."""
        try:
            # check permissions
            permission_response = self.permissions.can_modify_incident()
            if not permission_response.success:
                return APIResponse(
                    success=False,
                    message=permission_response.message,
                    code=status.HTTP_403_FORBIDDEN,
                    data=None,
                )

            report_facility = self.incident.report_facility
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, self.incident.report_facility)
                and not is_manager_user(self.user, self.incident.department)
            ) and not self.incident.created_by == self.user:
                return APIResponse(
                    success=False,
                    message="You do not have permission to modify this incident",
                    code=status.HTTP_403_FORBIDDEN,
                    data=None,
                )

            if "patient" in self.data:
                patient_profile = user_profile_service.get_or_create_profile(
                    self.data["patient"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["patient"] = patient_profile.data.id

            if "provider_info" in self.data:
                provider_profile = user_profile_service.get_or_create_profile(
                    self.data["provider_info"]
                )
                if not provider_profile.success:
                    return APIResponse(
                        success=False,
                        message=provider_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["provider_info"] = provider_profile.data.id

            # Ensure essential fields are present for serializer, defaulting to existing incident values
            self.data["report_facility"] = report_facility.id
            self.data["department"] = self.incident.department.id
            self.data["created_by"] = self.incident.created_by.id
            self.data["original_report"] = self.incident.id

            version_serializer = MedicalErrorVersionSerializer(data=self.data)
            if version_serializer.is_valid():
                version_serializer.save()
                old_status = self.incident.status
                self.incident.is_modified = True
                self.incident.updated_by = self.user
                self.incident.status = self.data.get("status", ReviewStatus.DRAFT)
                self.incident.save()

                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=self.incident,
                    activity_type=ActivityType.MODIFIED,
                    description="Incident modified"
                )

                new_status = self.incident.status
                if old_status != new_status:
                    ActivityLogService.create_activity(
                        user=self.user,
                        content_object=self.incident,
                        activity_type=ActivityType.STATUS_CHANGED,
                        description=f"Status changed from {old_status} to {new_status}"
                    )
                return APIResponse(
                    success=True,
                    message="Medication Error incident modified successfully",
                    code=status.HTTP_200_OK,
                    data=version_serializer.data,
                )
            else:
                logging_service.log_error(version_serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data provided",
                    code=status.HTTP_400_BAD_REQUEST,
                    data=version_serializer.errors,
                )
        except MedicationError.DoesNotExist:
            return APIResponse(
                success=False,
                message="Medication Error incident not found",
                code=status.HTTP_404_NOT_FOUND,
                data=None,
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Validation error",
                code=status.HTTP_400_BAD_REQUEST,
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error modifying incident",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def send_for_review(self) -> APIResponse:
        """Sends the Medication Error incident for review."""
        try:
            response = self.workflow_services.send_for_a_review(
                self.incident_id, self.data
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None,
                )

            if self.data.get("assignees") not in [None, [], ""]:
                assignees = list(response.data.assignees.all())
                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=response.data,
                    activity_type=ActivityType.SENT_TO_REVIEWER,
                    highlight_objects=None,
                    destination_objects=assignees
                )
            if self.data.get("review_template") is not None:
                review_template = ReviewTemplates.objects.get(id=self.data["review_template"])
                assignees = list(review_template.assignees.all())
                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=response.data,
                    activity_type=ActivityType.SENT_TO_REVIEWER,
                    highlight_objects=review_template,
                    destination_objects=assignees
                )

            serializer = GetMedicationErrorSerializer(response.data)
            return APIResponse(
                success=True,
                message="Medication Error incident sent for review",
                code=status.HTTP_200_OK,
                data=serializer.data,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error sending incident for review",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def mark_closed(self) -> APIResponse:
        """Marks the Medication Error incident as resolved."""
        try:
            response = self.workflow_services.mark_as_resolved(
                incident=self.incident, user=self.user
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None,
                )

            ActivityLogService.create_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.RESOLVED,
                description="Incident marked as resolved"
            )

            return APIResponse(
                success=True,
                message="Medication Error incident marked as resolved",
                code=status.HTTP_200_OK,
                data=None,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error marking incident as resolved",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def delete_medication_error_draft_incidents(self) -> APIResponse:
        """Deletes draft incidents for Medication Error."""
        try:
            response = self.workflow_services.delete_drafts(
                user=self.user,
                model=MedicationError,
                incident_ids=self.data.get("incident_ids", None),
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=status.HTTP_400_BAD_REQUEST,
                    data=None,
                )

            ActivityLogService.create_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.DELETED,
                description="Draft incidents deleted"
            )

            return APIResponse(
                success=True,
                message="Draft incidents deleted successfully",
                code=status.HTTP_200_OK,
                data=response.data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error deleting draft incidents",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )
