from rest_framework import serializers
from accounts.serializers import UserProfileSerializer, UserSerializer
from facilities.serializers import FacilitySerializer
from medication_error.models import MedicationError
from base.serializers import BaseModelSerializer, DepartmentSerializer


class GetMedicationErrorSerializer(BaseModelSerializer):
    provider_info = serializers.SerializerMethodField()
    report_facility = FacilitySerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)
    patient = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = MedicationError
        fields = "__all__"

    def get_patient(self, obj):
        if obj.patient:
            return {
                "id": obj.patient.id,
                "first_name": obj.patient.first_name,
                "last_name": obj.patient.last_name,
                "email": obj.patient.email,
            }
        return None

    def get_provider_info(self, obj):
        if obj.provider_info:
            return {
                "id": obj.provider_info.id,
                "first_name": obj.provider_info.first_name,
                "last_name": obj.provider_info.last_name,
                "email": obj.provider_info.email,
            }
        return None
