from django.db import models
from accounts.models import UserProfile
from base.models import BaseModel
from base.services.permissions.mixins import IncidentsPermissionsMixin
from documents.models import Document
from base.models import Facility
from base.models import Department
from reviews.models import Review
from tasks.models import ReviewProcess, ReviewProcessTasks


# we need to add search indexes to optimize performance during searching and querying
class MedicationErrorBase(BaseModel):
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]
    report_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES,
        max_length=255,
        default="Draft",
    )

    current_step = models.IntegerField(default=1, null=True, blank=True)
    staff_status = models.CharField(max_length=500, null=True, blank=True)
    # Patient Details
    patient = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_patient_info",
    )
    day_of_the_week = models.CharField(max_length=255, null=True, blank=True)
    date_of_error = models.DateField(null=True, blank=True)
    time_of_error = models.TimeField(null=True, blank=True)
    location = models.CharField(max_length=255, null=True, blank=True)

    # error duration
    days = models.IntegerField(null=True, blank=True)
    hours = models.IntegerField(null=True, blank=True)

    # Provider/Reporter Details
    provider_info = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_provider_info",
    )
    provider_title = models.CharField(max_length=255, null=True, blank=True)
    provider_classification = models.CharField(max_length=255, null=True, blank=True)
    date_of_report = models.DateField(null=True, blank=True)
    time_of_report = models.TimeField(null=True, blank=True)

    # Medication/Dose Involved
    drug_ordered = models.CharField(max_length=255, null=True, blank=True)
    drug_given = models.CharField(max_length=255, null=True, blank=True)
    dose = models.CharField(max_length=255, null=True, blank=True)
    drug_ordered_route = models.CharField(max_length=255, null=True, blank=True)
    drug_given_route = models.CharField(max_length=255, null=True, blank=True)
    medication_time = models.TimeField(null=True, blank=True)
    expiration = models.CharField(max_length=255, null=True, blank=True)

    # Incident Description
    what_happened = models.CharField(max_length=255, null=True, blank=True)
    form_of_error = models.CharField(max_length=255, null=True, blank=True)
    description_of_error = models.TextField(null=True, blank=True)
    contributing_factors = models.TextField(null=True, blank=True)

    # Error Classification
    error_category = models.CharField(max_length=255, null=True, blank=True)

    # Comments and Actions
    comments = models.TextField(null=True, blank=True)
    actions_taken = models.TextField(null=True, blank=True)
    severity_rating = models.DecimalField(
        decimal_places=2, null=True, blank=True, max_digits=9
    )

    reviews = models.ManyToManyField(
        Review, related_name="%(class)s_medication_error_reviews_field", blank=True
    )
    documents = models.ManyToManyField(
        Document,
        related_name="%(class)s_medical_error_incident_documents_field",
        blank=True,
    )
    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )

    is_resolved = models.BooleanField(default=False)

    def __str__(self):
        return f"Medication Error Report - {self.patient} ({self.date_of_error})"

    class Meta:
        abstract = True
        permissions = IncidentsPermissionsMixin.custom_permissions


class MedicationError(MedicationErrorBase):
    is_modified = models.BooleanField(default=False)
    review_process = models.ForeignKey(
        ReviewProcess,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_review_process",
    )
    review_tasks = models.ManyToManyField(
        ReviewProcessTasks,
        blank=True,
        related_name="%(class)s_review_tasks",
    )


class MedicationErrorVersion(MedicationErrorBase):
    original_report = models.ForeignKey(
        MedicationError,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="versions",
    )

    class Meta:
        db_table = "medication_error_version"
