from base.tests.factory import (
    DepartmentFactory,
    ProfileFactory,
    UserProfileFactory,
    FacilityFactory,
)
from documents.models import Document
from medication_error.models import MedicationError
from medication_error.tests.factory import MedicationErrorFactory
from base.tests.base_setup import BaseTestSetup
from medication_error.services.actions import MedicationErrorActionsService
from medication_error.services.operations import MedicationErrorService
from base.tests.factory import UserProfileFactory, DocumentFactory
from tasks.tests.factory import ReviewTemplateFactory


class TestMedicationErrorServiceCase(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = MedicationErrorService(user=self.super_user)

    def test_get_incidents_as_superuser(self):
        """Creating incidents for super user access"""
        # create 5 new incidents for new facility
        for _ in range(5):
            MedicationErrorFactory(
                department=self.super_user_dept,
                report_facility=self.super_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            MedicationErrorFactory()

        response = self.service.get_incidents_list()

        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data), 7)


class TestMedicationErrorServiceAsAdmin(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = MedicationErrorService(user=self.admin_user)

    def test_get_incidents_as_admin(self):
        """Admin users should see incidents from facilities they have access to"""
        # Create 2 incidents for admin's facility
        for _ in range(2):
            MedicationErrorFactory(
                department=self.admin_user_dept,
                report_facility=self.admin_user_fac,
                created_by=self.user_user,
            )

        # Create 1 incident for another facility
        MedicationErrorFactory(
            department=DepartmentFactory(),
            report_facility=FacilityFactory(),
            created_by=self.user_user,
        )

        response = self.service.get_incidents_list()

        self.assertEqual(response.success, True)
        # Admin should only see incidents from their accessible facilities
        self.assertEqual(len(response.data), 2)


class TestMedicationErrorServiceAsDirector(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = MedicationErrorService(user=self.director_user)

    def test_get_incidents_as_director(self):
        """Director users should see incidents from their facility"""
        # Create 4 incidents for director's facility
        for _ in range(4):
            MedicationErrorFactory(
                department=self.director_user_dept,
                report_facility=self.director_user_fac,
                created_by=self.user_user,
            )

        # Create 3 incidents for another facility
        for _ in range(3):
            MedicationErrorFactory()

        response = self.service.get_incidents_list()

        self.assertEqual(response.success, True)
        # Director should only see incidents from their facility
        self.assertEqual(len(response.data), 4)


class TestMedicationErrorServiceAsManager(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = MedicationErrorService(user=self.manager_user)

    def test_get_incidents_as_manager(self):
        """Manager users should see incidents from departments they have access to"""
        # Create 3 incidents for manager's department
        for _ in range(3):
            MedicationErrorFactory(
                department=self.manager_user_dept,
                report_facility=self.manager_user_fac,
                created_by=self.user_user,
            )

        # Create 2 incidents for another facility
        for _ in range(2):
            MedicationErrorFactory()

        response = self.service.get_incidents_list()

        self.assertEqual(response.success, True)
        # Manager should only see incidents from their accessible departments
        self.assertEqual(len(response.data), 3)


class TestMedicationErrorServiceAsRegularUser(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = MedicationErrorService(user=self.user_user)

    def test_get_incidents_as_regular_user(self):
        """Regular users should see incidents they created or are assigned to review"""
        # Create 2 incidents created by the regular user
        for _ in range(2):
            MedicationErrorFactory(
                created_by=self.user_user,
                department=self.user_user_dept,
                report_facility=self.user_user_fac,
            )

        # Create 3 incidents by other users (should not be visible)
        for _ in range(3):
            MedicationErrorFactory(
                created_by=self.admin_user,
                department=self.admin_user_dept,
                report_facility=self.admin_user_fac,
            )

        response = self.service.get_incidents_list()

        self.assertEqual(response.success, True)
        # Regular user should only see incidents they created
        self.assertEqual(len(response.data), 2)


class TestMedicationErrorServiceGetIncidentById(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = MedicationErrorService(user=self.super_user)

    def test_get_incident_by_id(self):
        incident = MedicationErrorFactory(created_by=self.super_user)
        response = self.service.get_incident_by_id(incident.id)

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_incident_by_id_not_found(self):
        response = self.service.get_incident_by_id(9999)
        self.assertFalse(response.success)


class TestMedicationErrorModifyIncidentNewUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = MedicationErrorFactory(created_by=self.super_user)
        self.service = MedicationErrorActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "patient": {
                    "first_name": "John",
                    "last_name": "Doe",
                    "email": "<EMAIL>",
                    "profile_type": "Patient",
                },
                "provider_info": {
                    "first_name": "Dr",
                    "last_name": "Smith",
                    "profile_type": "Staff",
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_fields(self):
        self.service.data.get("patient").pop("profile_type")

        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestMedicationErrorModifyIncidentExistingUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = MedicationErrorFactory(created_by=self.super_user)
        self.service = MedicationErrorActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "patient": {
                    "user_id": self.patient.id,
                },
                "provider_info": {
                    "user_id": self.provider_info.id,
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_fields(self):
        self.service.data.get("provider_info").pop("user_id")

        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_modify_incident_invalid_user_id(self):
        self.service.data.get("provider_info")["user_id"] = 9999

        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestMedicationErrorSendReviewIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = MedicationErrorFactory(created_by=self.super_user)
        self.review_template = ReviewTemplateFactory()
        self.assignee_1 = ProfileFactory()
        self.assignee_2 = ProfileFactory()
        self.assignee_3 = ProfileFactory()
        self.service = MedicationErrorActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "review_template": self.review_template.id,
            },
        )

    def test_send_for_review_success(self):
        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_send_for_review_invalid_template(self):
        self.service.data["review_template"] = 9999

        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_missing_template(self):
        self.service.data.pop("review_template")

        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_assignees_and_template(self):
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]

        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_assignees(self):
        self.service.data.pop("review_template")
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]

        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestMedicationErrorServiceMarkClose(BaseTestSetup):
    def setUp(self):
        super().setUp()
        # Create incident with one user, but close it with another user to avoid the business rule
        self.incident = MedicationErrorFactory(created_by=self.user_user)
        self.service = MedicationErrorActionsService(
            user=self.super_user,  # Super user closing incident created by regular user
            incident_id=self.incident.id,
            data={
                "action": "mark-closed",
            },
        )

    def test_mark_close_success(self):
        response = self.service.mark_closed()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestCreateMedicationErrorServiceCase(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = MedicationErrorService(user=self.super_user)
        self.patient = UserProfileFactory(profile_type="Patient")
        self.provider = UserProfileFactory(profile_type="Provider")
        self.valid_data = {
            # Basic Info
            "status": "Draft",
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "current_step": 1,
            "staff_status": "On duty",
            # Patient Info
            "patient": {"user_id": self.patient.id},
            "day_of_the_week": "Monday",
            "date_of_error": "2025-05-21",
            "time_of_error": "14:30:00",
            "location": "Ward A",
            # Error Duration
            "days": 1,
            "hours": 2,
            # Provider Details
            "provider_info": {"user_id": self.provider.id},
            "provider_title": "RN",
            "provider_classification": "Nurse",
            "date_of_report": "2025-05-21",
            "time_of_report": "15:00:00",
            # Medication/Dose Details
            "drug_ordered": "Aspirin",
            "drug_given": "Tylenol",
            "dose": "500mg",
            "drug_ordered_route": "Oral",
            "drug_given_route": "Oral",
            "medication_time": "14:30:00",
            "expiration": "2026-05-21",
            # Incident Description
            "what_happened": "Wrong medication given",
            "form_of_error": "Wrong drug",
            "description_of_error": "Detailed description of the error",
            "contributing_factors": "Similar packaging",
            # Error Classification
            "error_category": "Wrong medication",
            # Comments and Actions
            "comments": "Additional comments",
            "actions_taken": "Corrective actions taken",
            "severity_rating": "3.50",
            # Related Items
            "documents": [],
            "reviews": [],
            "review_tasks": [],
        }

    def test_create_incident_success(self):
        """Test successful creation of medication error incident"""
        response = self.service.create_incident(self.valid_data)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertIsNotNone(response.data["id"])

        # Verify fields were saved correctly
        incident = response.data
        self.assertEqual(incident["drug_ordered"], self.valid_data["drug_ordered"])
        self.assertEqual(incident["drug_given"], self.valid_data["drug_given"])
        self.assertEqual(incident["error_category"], self.valid_data["error_category"])

    def test_create_incident_minimum_required_fields(self):
        """Test creation with only required fields"""
        minimal_data = {
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "drug_ordered": "Aspirin",
            "drug_given": "Tylenol",
            "error_category": "Wrong medication",
        }

        response = self.service.create_incident(minimal_data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)

    def test_create_incident_missing_required_fields(self):
        """Test creation with missing required fields"""
        invalid_data = self.valid_data.copy()
        invalid_data.pop("drug_ordered")  # Remove required field

        response = self.service.create_incident(invalid_data)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_create_incident_with_new_users(self):
        """Test creation with new patient and provider profiles"""
        data = self.valid_data.copy()
        data.update(
            {
                "patient": {
                    "first_name": "John",
                    "last_name": "Patient",
                    "email": "<EMAIL>",
                    "profile_type": "Patient",
                },
                "provider_info": {
                    "first_name": "Jane",
                    "last_name": "Provider",
                    "email": "<EMAIL>",
                    "profile_type": "Provider",
                },
            }
        )

        response = self.service.create_incident(data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)

    def test_create_incident_with_documents(self):
        """Test creation with document attachments"""
        document = Document.objects.create(
            name="Test Document",
            document_url="path/to/test.pdf",
        )

        data = self.valid_data.copy()
        data["documents"] = [document.id]

        response = self.service.create_incident(data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertEqual(len(response.data["documents"]), 1)


class TestUpdateMedicationErrorServiceCase(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = MedicationErrorService(user=self.super_user)
        self.provider = UserProfileFactory(profile_type="Provider")

        # Create an incident to update
        self.incident = MedicationErrorFactory(
            report_facility=self.super_user_fac,
            department=self.super_user_dept,
            created_by=self.super_user,
        )

        self.update_data = {
            # Basic Info
            "status": "Open",
            "department": self.manager_user_dept.id,
            "staff_status": "Updated Status",
            # Patient Details
            "patient": {"user_id": self.patient.id},
            "day_of_the_week": "Tuesday",
            "date_of_error": "2025-05-22",
            "time_of_error": "15:30:00",
            "location": "Updated Location",
            # Error Details
            "days": 2,
            "hours": 3,
            # Provider Details
            "provider_info": {"user_id": self.provider.id},
            "provider_title": "MD",
            "provider_classification": "Doctor",
            # Medication Details
            "drug_ordered": "Updated Drug",
            "drug_given": "Updated Given",
            "dose": "100mg",
            "drug_ordered_route": "IV",
            "drug_given_route": "IV",
            # Additional Info
            "what_happened": "Updated description",
            "form_of_error": "Wrong route",
            "description_of_error": "Updated detailed description",
            "contributing_factors": "Updated factors",
            "error_category": "Updated Category",
            "severity_rating": "4.50",
        }

    def test_update_incident_success(self):
        """Test successful update of medication error incident"""
        response = self.service.update_incident(self.incident.id, self.update_data)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(
            response.data["drug_ordered"], self.update_data["drug_ordered"]
        )
        self.assertEqual(
            response.data["error_category"], self.update_data["error_category"]
        )

    def test_update_incident_with_status_change(self):
        """Test updating status to Open triggers email"""
        data = {"status": "Open"}

        response = self.service.update_incident(self.incident.id, data)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["status"], "Open")

    def test_update_incident_with_new_users(self):
        """Test updating incident with new user profiles"""
        data = {
            "patient": {
                "first_name": "Updated",
                "last_name": "Patient",
                "email": "<EMAIL>",
                "profile_type": "Patient",
            },
            "provider_info": {
                "first_name": "Updated",
                "last_name": "Provider",
                "email": "<EMAIL>",
                "profile_type": "Provider",
            },
        }

        response = self.service.update_incident(self.incident.id, data)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_update_incident_with_documents(self):
        """Test updating incident with documents"""
        document = Document.objects.create(
            name="Updated Doc",
            document_url="path/to/updated.pdf",
        )

        data = {"documents": [document.id]}
        response = self.service.update_incident(self.incident.id, data)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(len(response.data["documents"]), 1)

    def test_update_nonexistent_incident(self):
        """Test updating non-existent incident fails"""
        response = self.service.update_incident(99999, self.update_data)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_update_incident_unauthorized(self):
        """Test update fails without proper permissions"""
        unauthorized_service = MedicationErrorService(user=self.user_user)

        response = unauthorized_service.update_incident(
            self.incident.id, self.update_data
        )

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)
