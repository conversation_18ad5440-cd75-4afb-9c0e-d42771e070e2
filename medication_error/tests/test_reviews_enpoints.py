from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserFactory
from lost_and_found.tests.factory import LostAndFoundFactory
from medication_error.tests.factory import MedicationErrorFactory
from reviews.tests.factory import ReviewFactory


class TestGetIncidentReviews(BaseTestSetup):
    """Test getting incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    # cases for medication error incident reviews
    def test_get_medication_error_reviews(self):
        """Test getting reviews for a medication error incident"""
        incident = MedicationErrorFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(5, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)
        self._authenticate_user(self.user)
        response = self.client.get(
            f"/api/incidents/medication-error/{incident.id}/reviews/",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to get reviews: {response.data}")
        # check number of reviews returned
        self.assertEqual(len(response.data), 5)


class TestCreateIncidentReview(BaseTestSetup):
    """Test creating incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    # cases for medication error incident reviews
    def test_create_medication_error_review(self):
        """Test creating a review for a medication error incident"""
        incident = MedicationErrorFactory()
        # clear any existing reviews
        incident.reviews.clear()
        self._authenticate_user(self.user)
        response = self.client.post(
            f"/api/incidents/medication-error/{incident.id}/reviews/",
            {"content": "This is a test review"},
        )

        if not response.status_code == 201:
            self.fail(f"Failed to create review: {response.data}")
        # check that the review was created
        self.assertEqual(incident.reviews.first().content, "This is a test review")
