import requests
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken
from rest_framework.permissions import IsAuthenticated
from accounts.models import Profile, UserPreference
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from base.services.permissions.permissions import APIKeyAuthentication
from core.settings import SECRET_KEY
from django.contrib.auth.models import User

from base.models import Department

logging_service = LoggingService()


# Function to handle login and generate JWT token
@api_view(["POST"])
def login(request):

    if request.method == "POST":
        email = request.data.get("email")
        try:
            user = User.objects.get(email=email)
            if not user.is_active:
                return Response(
                    {"message": "User account is not active"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )
            logged_in, access_token, refresh_token, user_info = get_user_info(user)
            if logged_in:
                return Response(
                    {
                        "access": str(access_token),
                        "refresh": str(refresh_token),
                        "user_info": user_info,
                    }
                )
        except User.DoesNotExist:
            return Response(
                {"message": "Opps, you many not have access to this..."},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"message": "Error authenticating with backend"},
                status=status.HTTP_401_UNAUTHORIZED,
            )


def get_user_info(user):
    try:
        access_token = AccessToken.for_user(user)
        access_token["first_name"] = user.first_name
        access_token["last_name"] = user.last_name
        access_token["email"] = user.email
        refresh_token = RefreshToken.for_user(user)
        profile, department, facility, facilities, preferences = user_details(user)
        user_info = {
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email": user.email,
            "id": user.id,
            "department": (
                {
                    "name": department.name,
                    "id": department.id,
                }
                if department
                else None
            ),
            "facility": (
                {
                    "id": facility.id,
                    "name": facility.name,
                }
                if facility
                else None
            ),
            "accounts": [
                {
                    "id": account.id,
                    "name": account.name,
                }
                for account in facilities
                if facilities or []
            ],
            "phone_number": profile.phone_number if profile else None,
            "gender": profile.gender if profile else None,
            "preferences": {
                "timezone": preferences.user_timezone if preferences else None,
            },
            "mfa_enabled": profile.mfa_enabled if profile else False,
        }

        return (
            True,
            access_token,
            refresh_token,
            user_info,
        )
    except Exception as e:
        logging_service.log_error(e)
        return False, None, None, None


@api_view(["POST"])
def verify_user_api(request):
    access_token = request.data.get("access")
    if access_token:
        headers = {
            "Authorization": f"Bearer {access_token}",
        }
        response = requests.get("https://graph.microsoft.com/v1.0/me", headers=headers)
        if response.status_code == 200:
            data = response.json()
            try:
                user = User.objects.get(email=data.get("mail"))
                if not user.is_active:
                    return Response(
                        {"message": "User account is not active"},
                        status=status.HTTP_401_UNAUTHORIZED,
                    )
                logged_in, access_token, refresh_token, user_info = get_user_info(user)
                if logged_in:
                    return Response(
                        {
                            "access": str(access_token),
                            "refresh": str(refresh_token),
                            "user_info": user_info,
                        }
                    )
            except User.DoesNotExist:
                return Response(
                    {"message": "Opps, you many not have access to this..."},
                    status=status.HTTP_401_UNAUTHORIZED,
                )
        return Response({"data": data}, status=status.HTTP_200_OK)
    return Response({"error": "No token provided"}, status=status.HTTP_400_BAD_REQUEST)


# a function to get user details.
# details are profile, facility, and department


def user_details(user):
    # profile
    profile = [None]
    department = None
    facility = None
    facilities = []
    preferences = None
    if user:
        try:
            profile = Profile.objects.filter(user=user).first()
            department = Department.objects.filter(members=user).first()
            facility = profile.facility if profile else None
            facilities = profile.access_to_facilities.all() if profile else []
            preferences = UserPreference.objects.get(user=user)
        except Profile.DoesNotExist:
            pass
        except UserPreference.DoesNotExist:
            pass
    return (profile, department, facility, facilities, preferences)


# Function to handle protected view
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def protected_view(request):
    # Your view logic
    return Response({"message": "This is a protected view"})


import jwt


def verify_token(access_token):
    if not isinstance(access_token, str):
        return (False, "Invalid token")

    try:
        access_token = access_token.split()[1]
        decoded_token = jwt.decode(access_token, SECRET_KEY, algorithms=["HS256"])
        return (True, "Is valid")
    except jwt.ExpiredSignatureError:
        message = "User is authenticated"
        return (False, message)

    except jwt.InvalidTokenError:
        user = None
        message = "Invalid token"
        return (False, message)


@api_view(["GET"])
@permission_classes(IsAuthenticated)
def verify_token_api(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        isValid, message = verify_token(access_token)
        if isValid:
            return Response({"message": message}, status=status.HTTP_200_OK)

        else:
            return Response({"message": message}, status=status.HTTP_401_UNAUTHORIZED)
