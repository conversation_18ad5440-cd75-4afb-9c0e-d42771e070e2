import random
import string
from django.contrib.auth.models import User
from accounts.models import PasswordResetCode
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.services.auth import verify_user
from incidents.emails.password_reset_code_email import send_password_reset_request_email


@api_view(["POST"])
def request_password_reset(request):
    email = request.data.get("email")

    if not email:
        return Response(
            {"error": "Email is required"}, status=status.HTTP_400_BAD_REQUEST
        )
    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        return Response(
            {"error": "No user with this email found"}, status=status.HTTP_404_NOT_FOUND
        )
    code = "".join(random.choices(string.ascii_uppercase + string.digits, k=6))
    PasswordResetCode.objects.create(
        email=email,
        code=code,
        expires_at=timezone.now() + timezone.timedelta(minutes=30),
    )

    send_password_reset_request_email(email, user.first_name, code)
    return Response(
        {"message": "A code has been sent to your email"}, status=status.HTTP_200_OK
    )


@api_view(["POST"])
def reset_password(request):
    email = request.data.get("email")
    code = request.data.get("code")
    new_password = request.data.get("new_password")

    if not (code and new_password):
        return Response(
            {"error": "All fields are required"}, status=status.HTTP_400_BAD_REQUEST
        )

    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        return Response(
            {"error": "No user with this email found"}, status=status.HTTP_404_NOT_FOUND
        )

    try:
        reset_code = PasswordResetCode.objects.get(
            email=email, code=code, expires_at__gte=timezone.now()
        )
    except PasswordResetCode.DoesNotExist:
        return Response(
            {"error": "Invalid or expired reset code"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    user.set_password(new_password)
    user.save()
    reset_code.delete()
    return Response({"message": "Password reset successful"}, status=status.HTTP_200_OK)


# user can change their password. this function requires old password and a new password


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def change_password_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if request.method == "POST":
        old_password = request.data.get("old_password")
        new_password = request.data.get("new_password")

        if not (old_password and new_password):
            return Response(
                {"error": "All fields are required"}, status=status.HTTP_400_BAD_REQUEST
            )

        if not user.check_password(old_password):
            return Response(
                {"error": "Incorrect old password"}, status=status.HTTP_401_UNAUTHORIZED
            )

        user.set_password(new_password)
        user.save()
        return Response(
            {"message": "Password changed successfully"}, status=status.HTTP_200_OK
        )
