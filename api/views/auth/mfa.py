import pyotp
import qrcode
from io import BytesIO
from django.contrib.auth.models import User
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from django.http import HttpResponse

from accounts.models import Profile
from api.views.auth.login import get_user_info
from base.services.logging.logger import LoggingService

logging_service = LoggingService()
service = "Quality Control"


def handle_mfa(user):

    secret = pyotp.random_base32()
    profile = Profile.objects.get(user=user)
    profile.totp = secret
    profile.mfa_enabled = True
    profile.save()
    # Generate QR code
    qr_code_url = pyotp.totp.TOTP(secret).provisioning_uri(
        user.email, issuer_name=service
    )

    # Save secret key to the database
    user.mfa_secret = secret
    user.save()

    return qr_code_url


@api_view(["GET"])
def verify_mfa(request, user_email):
    try:
        user = User.objects.get(email=user_email)
        qr_code_url = handle_mfa(user)
        qr_code_img = qrcode.make(qr_code_url)

        buffer = BytesIO()
        qr_code_img.save(buffer, format="PNG")
        buffer.seek(0)

        return HttpResponse(buffer, content_type="image/png")
    except User.DoesNotExist:
        return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
def verify_mfa_code(request, user_email):
    code = request.data.get("code")
    email = request.data.get("email")
    if not code:
        return Response({"error": "Code is required"}, status=400)

    try:
        user = User.objects.get(email=email)
        profile = Profile.objects.get(user=user)
    except User.DoesNotExist:
        return Response({"error": "User not found"}, status=404)
    except Profile.DoesNotExist:
        return Response({"error": "Profile not found"}, status=404)

    totp = pyotp.TOTP(profile.totp)

    is_verified = totp.verify(code)

    if is_verified:
        logged_in, access_token, refresh_token, user_info = get_user_info(user)
        if logged_in:
            return Response(
                {
                    "access": str(access_token),
                    "refresh": str(refresh_token),
                    "user_info": user_info,
                }
            )
        else:
            return Response({"error": "Failed to log in"}, status=401)
    else:
        return Response({"error": "Invalid code"}, status=400)
