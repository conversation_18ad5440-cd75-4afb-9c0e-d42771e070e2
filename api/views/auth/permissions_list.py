from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated

from django.contrib.auth.models import Group

from accounts.models import Profile
from base.services.auth import verify_user
from base.models import Department
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement

admin_profile = None
user_profile = None

logging_service = LoggingService()
permissions_management = PermissionsManagement()


def get_profile(user):
    try:
        user_profile = Profile.objects.get(user=user)
        return user_profile
    except Profile.DoesNotExist:
        return None


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def permissions_list_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        permissions = []
        user_permissions = Group.objects.all()
        for permission in user_permissions:
            permissions.append(permission.name)
        return Response({"permissions": permissions}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {"message": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_permissions(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        user_groups = user.groups.all()
        permissions = []
        for group in user_groups:
            permissions.append(group.name)
        return Response({"permissions": permissions}, status=status.HTTP_200_OK)


def has_permissions(user, required_permissions):
    user_permissions = Group.objects.filter(user=user)

    permissions = []
    for permission in user_permissions:
        permissions.append(permission.name)

    # if any of the required permissions in permissions, return true

    if any(perm in permissions for perm in required_permissions):
        return True  # user has required permissions
    else:
        return False  # user does not have required permissions


def check_user_group(user, group_name):
    """
    Generic function to check if a user belongs to a specific group.

    Args:
        user: User object
        group_name: Name of the group to check

    Returns:
        bool: True if user belongs to the group, False otherwise
    """
    return user.groups.filter(name=group_name).exists()


def check_facility_access(user_profile, facility):
    """
    Check if user has access to a specific facility.

    Args:
        user_profile: UserProfile object
        facility: Facility object

    Returns:
        bool: True if user has access to facility, False otherwise
    """
    return user_profile and (
        user_profile.facility == facility
        or facility in user_profile.access_to_facilities.all()
    )


def check_department_access(user, user_profile, department):
    """
    Check if user has access to a specific department.

    Args:
        user: User object
        user_profile: UserProfile object
        department: Department object

    Returns:
        bool: True if user has access to department, False otherwise
    """
    return (
        department.header_of_department == user
        or department in user_profile.access_to_department.all()
    )


def is_super_user(user):
    """Check if user is a Super User."""
    return check_user_group(user, "Super user")


def is_corporate_user(user):
    """Check if user is a Corporate user."""
    return check_user_group(user, "Corporate")


def is_admin_user(user, facility=None):
    """
    Check if user is an Admin with access to the specified facility.
    """
    if not facility:
        return False
    if not check_user_group(user, "Admin"):
        return False
    user_profile = get_profile(user)
    return check_facility_access(user_profile, facility)


def is_manager_user(user, department=None):
    """
    Check if user is a Manager with access to the specified department.
    """
    if not department:
        return False
    if not check_user_group(user, "Manager"):
        return False

    user_profile = get_profile(user)
    return check_department_access(user, user_profile, department)


def is_director_user(user, facility=None):
    """
    Check if user is a Director with access to the specified facility.
    """
    if not facility:

        return False
    if not check_user_group(user, "Director"):
        return False

    user_profile = get_profile(user)
    return check_facility_access(user_profile, facility)


def is_user_editor(user):
    """
    Check if user is a User Editor.
    """
    return check_user_group(user, "User Editor")


def is_specific_department(user, department_name, facility):
    return Department.objects.filter(
        header_of_department=user,
        name=department_name,
        facility=facility,
    ).exists()


def is_basic_user(user):
    """Check if user is a basic User."""
    return check_user_group(user, "User")


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_permissions(request):
    if request.method == "POST":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
    if not has_permissions(user, ["Admin", "managers"]):
        return Response(
            {"status": "failed", "message": "You do not have the required permissions"},
            status=status.HTTP_403_FORBIDDEN,
        )
    if "permissions" in request.data:
        permissions = request.data["permissions"]
        for permission in permissions:
            if "name" in permission:
                try:
                    group, created = Group.objects.get_or_create(
                        name=permission["name"]
                    )
                except Exception as e:
                    logging_service.log_error(e)
            else:
                return Response(
                    {"status": "failed", "message": "Invalid permission data"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
    else:
        return Response(
            {
                "status": "failed",
                "message": "Missing 'permissions' field in request data",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
    return Response(
        {"message": "Permissions added successfully"}, status=status.HTTP_200_OK
    )


def cleaned_data(data, fields):
    # cleaned_data = {}
    for field in fields:
        data.pop(field, None)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_permissions_by_app(request):
    """
    Returns user permissions organized by Django apps.

    Example response:
    {
        "accounts": ["add_profile", "change_profile", "view_profile"],
        "tasks": ["can_approve_task", "can_complete_task", "can_create_task"],
        "base": ["add_facility", "change_department"]
    }
    """
    try:
        # Get the authenticated user's ID
        user_id = request.user.id

        # Get permissions organized by app
        response = permissions_management.get_user_permissions_by_app(user_id)

        if not response.success:
            return Response(
                {"error": response.message},
                status=(
                    status.HTTP_400_BAD_REQUEST
                    if "not found" in response.message.lower()
                    else status.HTTP_500_INTERNAL_SERVER_ERROR
                ),
            )

        return Response(response.data, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def all_possible_permissions_by_app(request):
    """
    Returns all possible permissions in the system organized by Django apps.
    Only shows permissions for models defined in MODEL_MAPPING (business logic models),
    filtering out system permissions like sessions, contenttypes, admin, etc.

    Example response:
    {
        "accounts": [
            {
                "codename": "add_profile",
                "name": "Can add profile",
                "model": "profile"
            },
            {
                "codename": "change_profile",
                "name": "Can change profile",
                "model": "profile"
            }
        ],
        "tasks": [
            {
                "codename": "can_approve_task",
                "name": "Can approve task",
                "model": "reviewprocesstasks"
            }
        ]
    }
    """
    try:
        # Get all possible permissions organized by app
        response = permissions_management.get_all_possible_permissions_by_app()

        if not response.success:
            return Response(
                {"error": response.message},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        return Response(response.data, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    return data
