import json
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

DATA_FILE_PATH = "data.json"


@api_view(["GET"])
def get_constants_data_list(request):
    """
    Retrieve a list of constants from a JSON file.

    This endpoint reads data from a JSON file located at `DATA_FILE_PATH` and returns it
    as a response. If the file is not found or contains invalid JSON, appropriate error
    responses are returned.

    Returns:
        Response: A JSON response containing the status, message, and data or error details.
    """
    try:
        with open(DATA_FILE_PATH, "r") as f:
            data = json.load(f)
        return Response(
            {
                "status": "success",
                "message": "Data retrieved successfully",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )
    except FileNotFoundError:
        return Response(
            {
                "status": "failed",
                "error": "Data file not found",
            },
            status=status.HTTP_404_NOT_FOUND,
        )
    except json.JSONDecodeError:
        return Response(
            {
                "status": "failed",
                "error": "Invalid JSON format in the data file",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
