from accounts.models import Profile
from base.services.logging.logger import LoggingService
from complaints.models import Complaint
from core.settings import MAIN_DOMAIN_NAME
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth.models import User

from api.views.auth.permissions_list import (
    get_profile,
    has_permissions,
    is_admin_user,
    is_director_user,
    is_manager_user,
    is_super_user,
    is_user_editor,
)
from base.services.auth import generate_random_password, verify_user
from base.models import Facility
from general_patient_visitor.models import GeneralPatientVisitor
from incidents.emails.accounts.account_activated import send_account_reactivation_email
from incidents.emails.accounts.account_deactivate import send_account_deactivation_email
from incidents.emails.welcome_email import send_welcome_email
from base.models import Department


from django.contrib.auth.models import User, Group
from adverse_drug_reaction.models import AdverseDrugReaction

from patient_visitor_grievance.models import Grievance, GrievanceInvestigation
from lost_and_found.models import LostAndFound

from staff_incident_reports.models import (
    StaffIncidentInvestigation,
    StaffIncidentReport,
)
from workplace_violence_reports.models import WorkPlaceViolence
from django.db.models import Q
from django.db import transaction

logging_service = LoggingService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_users_list_api(request):
    try:
        users = User.objects.filter(
            Q(groups__isnull=False),
            Q(profile__is_patient_visitor=False),
        ).distinct()

        """
        If request.user is super user, show all users
        if user is admin or director, show users in the same facility
        if user is manager, show users in the same department
        """
        profile = Profile.objects.get(user=request.user)
        department = Department.objects.filter(
            header_of_department=request.user
        ).first()

        users_list = []
        filtered_users = []
        if is_super_user(request.user) or is_user_editor(request.user):
            filtered_users = users
        elif is_admin_user or is_director_user(request.user, profile.facility):
            filtered_users = users.filter(profile__facility=profile.facility)

        elif is_manager_user(request.user, department):
            # show users in the same department
            if department:
                filtered_users = department.members.all()
            else:
                filtered_users = []
        else:
            return Response(
                {
                    "error": "You don't have access to view this information.",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        for user in filtered_users:
            # Use user.incident_members.all() to get user's departments
            profile = Profile.objects.filter(user=user).first()
            departments = user.incident_members.all()
            user_data = {
                "id": user.id,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "is_active": user.is_active,
                "phone_number": (
                    profile.phone_number if profile and profile.phone_number else None
                ),
                "department": [
                    {"name": department.name, "id": department.id}
                    for department in departments
                ],
                "date_created": user.date_joined,
            }
            users_list.append(user_data)
        return Response(users_list)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_user_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    try:
        profile = Profile.objects.get(user=user)
    except Profile.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Admin profile not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    if not is_super_user(user) and not is_admin_user(user, profile.facility):

        return Response(
            {
                "status": "failed",
                "message": "You do not have the required permissions",
            },
            status=status.HTTP_403_FORBIDDEN,
        )
    new_user, created, message = handle_new_user(user, request.data)
    if created:
        return Response(
            {"message": "User created successfully", "user": new_user},
            status=status.HTTP_201_CREATED,
        )

    else:
        return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)


# add many users to the database
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_multiple_users_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    try:
        profile = Profile.objects.get(user=user)
    except Profile.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Admin profile not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    if (
        not is_super_user(user)
        and not is_admin_user(user, profile.facility)
        and not is_user_editor(user)
    ):
        return Response(
            {
                "status": "failed",
                "message": "You do not have the required permissions",
            },
            status=status.HTTP_403_FORBIDDEN,
        )
    if request.method == "POST":
        data = request.data.get("users")
        if not data:
            return Response(
                {"error": "No users provided"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            users = []
            for user_data in data:
                if (
                    not user_data.get("email")
                    or not user_data.get("first_name")
                    or not user_data.get("last_name")
                    or not user_data.get("department_name")
                ):
                    return Response(
                        {
                            "error": "One of users is missing first_name, last_name, email or department",
                            "submitted_data": user_data,
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                new_user, created, message = handle_new_user(user, user_data)
                if created:
                    users.append(new_user)
                else:
                    continue
            if len(users) > 0 and len(data) == len(users):

                return Response(
                    {
                        "message": "Users created successfully",
                        "users": [users],
                    },
                    status=status.HTTP_201_CREATED,
                )
            if len(data) > len(users):
                return Response(
                    {
                        "message": f"{len(data) - len(users)} users were not created due to: duplicate data or errors",
                        "users": [user.email for user in users],
                    },
                    status=status.HTTP_201_CREATED,
                )

            return Response(
                {
                    "message": f"{len(users)} users were created successfully",
                    "users": [user.email for user in users],
                },
            )
        except Exception as e:
            return Response(
                {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
            )


def handle_new_user(user, data):

    if not is_user_editor(user):
        (None, False, "You are not allowed to create users")

    email = data.get("email")
    first_name = data.get("first_name")
    last_name = data.get("last_name")
    department_id = data.get("department_id")
    role = data.get("role")
    is_corporate = data.get("is_corporate")
    phone_number = data.get("phone_number")
    gender = data.get("gender")
    date_of_birth = data.get("date_of_birth")
    address = data.get("address")
    birth_country = data.get("birth_country")
    facility = data.get("facility")
    city = data.get("city")
    zip_code = data.get("zip_code")
    if not email or not first_name or not last_name or not department_id:
        return (
            {},
            False,
            "Email, first_name, last_name and department_id are required",
        )

    if User.objects.filter(email=email).exists():
        return (
            {},
            False,
            "A user with that email already exists",
        )
    from django.db import transaction

    try:
        with transaction.atomic():
            new_user = User.objects.create_user(
                username=email,
                email=email,
                first_name=first_name,
                last_name=last_name,
            )
            facility_obj = Facility.objects.get(id=facility["id"])
            # Handle Facility

            facility_obj.staff_members.add(new_user)
            # Handle Department
            department_obj = Department.objects.get(
                id=department_id, facility=facility_obj
            )

            # Handle Role
            group, created = Group.objects.get_or_create(name=role)
            new_user.groups.add(group)
            department_obj.members.add(new_user)

            # genera a strong passowrd for the new user
            password = generate_random_password()
            new_user.set_password(password)
            new_user.save()

            # handle profile
            new_profile, profile_created = Profile.objects.get_or_create(
                user=new_user,
                phone_number=phone_number,
                gender=gender,
                date_of_birth=date_of_birth,
                address=address,
                birth_country=birth_country,
                city=city,
                zip_code=zip_code,
                facility=facility_obj,
            )

            # handle permissions
            permissions_added = False
            if "permissions" in data:
                permissions_added, facilities, departments = set_user_permissions(
                    profile=new_profile,
                    data=data,
                )

            send_welcome_email(
                new_user.email, new_user.first_name, password, MAIN_DOMAIN_NAME
            )
            return (
                {
                    "user": {
                        "email": new_user.email,
                        "username": new_user.username,
                        "first_name": new_user.first_name,
                        "last_name": new_user.last_name,
                        "phone_number": new_profile.phone_number if created else None,
                        "gender": new_profile.gender if created else None,
                        "date_of_birth": new_profile.date_of_birth if created else None,
                        "address": new_profile.address if created else None,
                        "birth_country": new_profile.birth_country if created else None,
                        "facility": new_profile.facility.name if created else None,
                        "permissions": [
                            {"name": group.name, "id": group.id}
                            for group in new_user.groups.all()
                        ],
                        "department": {
                            "name": department_obj.name,
                            "id": department_obj.id,
                        },
                    },
                    "permissions": True,
                },
                True,
                "User created successfully",
            )
    except Department.DoesNotExist:
        return (
            {},
            False,
            "Department not found",
        )
    except Facility.DoesNotExist:
        return (
            {},
            False,
            "Facility not found",
        )
    except Exception as e:
        raise
    return (
        {},
        False,
        {"error": "Internal server error"},
    )


def set_user_permissions(profile, data):
    # get facilities and departments from the permissions
    facilities = []
    facilities_objs = []
    departments = []
    departments_objs = []

    if "permissions" in data:
        for permission in data["permissions"]:
            if "facilities" in permission:
                facilities.extend(permission["facilities"])
            if "departments" in permission:
                departments.extend(permission["departments"])

    if "facility" in data:
        try:
            single_facility = Facility.objects.filter(id=data["facility"]["id"]).first()
            if single_facility:
                profile.facility = single_facility
                profile.save()
        except Facility.DoesNotExist:
            pass

    try:

        # Fetch and set facilities
        if facilities:
            facility_ids = [facility["id"] for facility in facilities]
            facilities_objs = list(Facility.objects.filter(id__in=facility_ids))
        if facilities_objs:
            profile.access_to_facilities.set(facilities_objs)

        # Fetch and set departments
        if departments:
            department_ids = [department["id"] for department in departments]
            departments_objs = list(Department.objects.filter(id__in=department_ids))
        if departments_objs:
            profile.access_to_department.set(departments_objs)
        profile.save()
        return True, facilities, departments
    except Exception as e:
        logging_service.log_error(e)
        return False, [], []


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_user_api(request, user_id):
    if not request.user.is_superuser:
        return Response(
            {"error": "Only admin users can delete users"},
            status=status.HTTP_403_FORBIDDEN,
        )
    try:

        success, message = delete_user(user_id)
        if success:
            return Response({"message": message}, status=status.HTTP_200_OK)
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
    except User.DoesNotExist:
        return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def delete_multiple(request):
    if not request.user.is_superuser:
        return Response(
            {"error": "Only superusers can delete users"},
            status=status.HTTP_403_FORBIDDEN,
        )

    users_to_delete = request.data.get("user", [])
    if not users_to_delete or not isinstance(users_to_delete, list):
        return Response(
            {"error": "Invalid data. 'users' should be a list of user IDs."},
            status=status.HTTP_400_BAD_REQUEST,
        )

    errors = []
    user_ids = [user.get("id") for user in users_to_delete]
    for user_id in user_ids:
        success, message = delete_user(user_id)

        if not success:
            errors.append({"user_id": user_id, "error": message})

    if errors:
        return Response({"errors": errors}, status=status.HTTP_400_BAD_REQUEST)
    else:
        return Response(
            {"message": "Users deleted successfully"}, status=status.HTTP_200_OK
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def deactivate_user_api(request, user_id):
    user = request.user
    user_profile = get_profile(user)
    if (
        not is_super_user(user)
        and not is_user_editor(user)
        and not is_admin_user(
            user,
            user_profile.facility,
        )
    ):
        return Response(
            {"error": "You do not have permission to deactivate a user"},
            status=status.HTTP_403_FORBIDDEN,
        )
    try:
        success, user_to_deactivate, message = deactivate_user(user_id)
        if success:
            try:
                send_account_deactivation_email(
                    user_to_deactivate.email,
                    user_to_deactivate.first_name,
                    "mailto:<EMAIL>",
                )
            except Exception as e:
                logging_service.log_error(e)
            return Response({"message": message}, status=status.HTTP_200_OK)
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_404_NOT_FOUND
        )


def deactivate_user(user_id):
    try:
        with transaction.atomic():
            user_to_deactivate = User.objects.get(id=user_id)
            user_to_deactivate.is_active = False
            user_to_deactivate.save()

        return True, user_to_deactivate, "User deactivated successfully"
    except User.DoesNotExist:
        return False, None, "User not found"
    except Exception as e:
        return False, None, "Internal server error"


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def reactivate_user_api(request, user_id):
    user = request.user
    if not is_super_user(user) and not is_user_editor(user):
        return Response(
            {"error": "You do not have permission to reactivate a user"},
            status=status.HTTP_403_FORBIDDEN,
        )
    try:
        success, user_to_activate, message = reactivate_user(user_id)
        if success:
            try:
                send_account_reactivation_email(
                    user_to_activate.email,
                    user_to_activate.first_name,
                    MAIN_DOMAIN_NAME,
                )
            except Exception as e:
                logging_service.log_error(e)
            return Response({"message": message}, status=status.HTTP_200_OK)
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_404_NOT_FOUND
        )


def reactivate_user(user_id):
    try:
        with transaction.atomic():
            user_to_activate = User.objects.get(id=user_id)
            user_to_activate.is_active = True
            user_to_activate.save()

        return True, user_to_activate, "User reactivated successfully"
    except User.DoesNotExist:
        return False, None, "User not found"
    except Exception as e:
        return False, None, "Internal server error"


def delete_user(user_id):
    try:
        with transaction.atomic():
            user_to_delete = User.objects.get(id=user_id)
            if user_to_delete.is_staff or user_to_delete.is_superuser:
                return (
                    False,
                    "Cannot delete this user because they have higher permissions",
                )

            else:
                profile = Profile.objects.filter(user=user_to_delete).first()
                complaints = Complaint.objects.filter(created_by=user_to_delete)

                # Delete all related incidents
                delete_all_incidents(user_to_delete)

                # Delete associated complaints and profile
                complaints.delete()
                if profile:
                    profile.delete()

                user_to_delete.delete()

        return True, "User deleted successfully"
    except Exception as e:
        return False, "Internal server error"


def delete_all_incidents(user):
    # Bulk deletion for each incident type
    AdverseDrugReaction.objects.filter(created_by=user).delete()
    Grievance.objects.filter(created_by=user).delete()
    GrievanceInvestigation.objects.filter(created_by=user).delete()
    LostAndFound.objects.filter(created_by=user).delete()
    StaffIncidentInvestigation.objects.filter(created_by=user).delete()
    StaffIncidentReport.objects.filter(created_by=user).delete()
    GeneralPatientVisitor.objects.filter(created_by=user).delete()
    WorkPlaceViolence.objects.filter(created_by=user).delete()
