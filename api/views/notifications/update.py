from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from base.models import UserNotification

# mark notification as read


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def mark_notifications_as_read(request):
    notification_ids = request.data.get("notifications_ids")

    if not notification_ids:
        return Response(
            {"message": "No notifications provided"}, status=status.HTTP_400_BAD_REQUEST
        )
    try:

        notification_list = UserNotification.objects.filter(
            id__in=notification_ids, to=request.user
        )
        if not notification_list.exists():
            return Response(
                {"message": "No user notifications found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        notification_list.update(is_read=True)

        return Response(
            {"message": "Notification marked as read"}, status=status.HTTP_200_OK
        )

    except Exception as e:
        return Response(
            {"message": f"Error occurred"},
            status=status.HTTP_400_BAD_REQUEST,
        )


# clear/delete notification from database
#  we should remove the user from the assignees. We can only delete delete the notification if it has not assignees and is_read is true


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def clear_notification(request):

    notification_ids = request.data.get("notifications_ids")
    if not notification_ids:
        return Response(
            {"message": "No notifications provided"}, status=status.HTTP_400_BAD_REQUEST
        )

    try:
        notifications = UserNotification.objects.filter(
            id__in=notification_ids, to=request.user, is_read=True
        )
        if not notifications.exists():
            return Response(
                {"message": "No notifications found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        notifications.delete()
        return Response({"message": "Notification deleted"}, status=status.HTTP_200_OK)
    except Exception as e:
        # raise
        return Response(
            {"message": f"Error occurred"},
            status=status.HTTP_400_BAD_REQUEST,
        )
