from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from base.models import UserNotification


class UserNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserNotification
        fields = [
            "id",
            "created_at",
            "notification_type",
            "notification_category",
            "message",
            "is_read",
            "item_id",
        ]


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_notifications(request):
    user = request.user
    notifications = UserNotification.objects.filter(to=user).order_by("-created_at")

    serializer = UserNotificationSerializer(notifications, many=True).data
    return Response(serializer, status=status.HTTP_200_OK)
