from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.models import Profile
from base.models import UserNotification
from base.services.notifications import save_notification

# new notification api


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def new_notification_api(request):

    try:
        user_profile = Profile.objects.get(user=request.user)
        facility = user_profile.access_to_facilities.first()
        if not facility:
            return Response(
                {"error": "User does not belong to any facility"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        notification, is_saved = save_notification(
            facility=facility,
            group_name="Quality-RiskManager",
            message="A test notification has been saved",
            notification_type="info",
            notification_category="system",
        )
        if is_saved:
            return Response(
                {"message": "Notification saved successfully"},
                status=status.HTTP_201_CREATED,
            )

        else:
            return Response(
                {"error": "Failed to save notification"},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Profile.DoesNotExist:
        return Response(
            {"error": "User profile not found"}, status=status.HTTP_404_NOT_FOUND
        )
