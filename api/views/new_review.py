from django.apps import apps
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from reviews.models import Review
from reviews.serializers import ReviewsSerializer
from rest_framework.response import Response
from rest_framework import status


def create_review(app_label, model_name, incident_id, user, content):
    try:
        # Get the model class dynamically
        model_class = apps.get_model(app_label, model_name)

        # Retrieve the incident instance
        incident = model_class.objects.get(pk=incident_id)
        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_manager_user(user, incident.report_facility.department)
        ):
            return Response(
                {"error": "You do not have enough rights to update this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
        # Create or get the review
        new_review, created = Review.objects.get_or_create(
            created_by=user,
            content=content,
        )

        if created:
            # Add the review to the incident
            incident.reviews.add(new_review)
            incident.save()
            reviews = incident.reviews.all()
            reviews_serializer = ReviewsSerializer(reviews, many=True)
            return Response(
                {"review_added": True, "reviews": reviews_serializer.data},
                status=status.HTTP_201_CREATED,
            )
        else:
            reviews = incident.reviews.all()
            reviews_serializer = ReviewsSerializer(reviews, many=True)
            return Response(
                {"review_added": False, "reviews": reviews_serializer.data},
                status=status.HTTP_200_OK,
            )

    except Exception as e:
        return Response(
            {"error": f"Error creating review"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
