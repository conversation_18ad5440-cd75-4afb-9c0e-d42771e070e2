"""
Complaints API Views Module

This module contains API endpoints for managing complaints in the quality control system.
It provides comprehensive CRUD operations and workflow management for complaints.

Available Endpoints:
    - complaints_list_create_api: GET (list with pagination/filtering) and POST (create)
    - complaints_details_api: GET, PUT, DELETE, and PATCH (with actions) for individual complaints

Features:
    - Pagination support for listing complaints
    - Filtering capabilities for complaint queries
    - Workflow actions (e.g., send to department)
    - Comprehensive error handling and logging
    - Authentication required for all endpoints

Dependencies:
    - Django REST Framework for API functionality
    - Custom service layers for business logic
    - Logging service for error tracking
"""

from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import JSONParser

from base.services.auth import verify_user
from complaints.models import Complaint
from complaints.services.workflow import ComplaintsWorkflow
from reviews.serializers import ComplaintSerializer
from complaints.services.operations import ComplaintsService
from complaints.services.query import ComplaintsQueryService
from base.services.logging.logger import LoggingService


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def complaints_list_create_api(request):
    """
    Handles API requests for listing and creating complaints based on the HTTP method.

    Args:
        request (HttpRequest): The HTTP request object containing method, user, and data.

    Supported Methods:
        - GET: Retrieve a paginated list of complaints with optional filtering.
            - Query Parameters:
                - page (int, optional): Page number for pagination (default: 1).
                - page_size (int, optional): Number of items per page (default: 10).
                - Additional filters can be passed as query parameters.
        - POST: Create a new complaint with the provided data.
            - Requires complaint data in request body.
            - Automatically associates the complaint with the authenticated user.

    Returns:
        Response: A DRF Response object containing:
            - For GET: Paginated list of complaints with filtering applied.
            - For POST: Created complaint data.
            - Appropriate HTTP status code.

    Raises:
        Returns HTTP 400 for invalid pagination parameters.
        Returns HTTP 500 for internal server errors.

    Permission Required:
        IsAuthenticated - User must be authenticated to access this endpoint.
    """
    try:
        if request.method == "GET":
            query_service = ComplaintsQueryService()
            # Extract query parameters for filtering
            filters = request.query_params.dict()
            page = int(request.GET.get("page", 1))
            page_size = int(request.GET.get("page_size", 10))

            response = query_service.get_complaints(
                filters=filters if filters else {}, page=page, page_size=page_size
            )
            return Response(response.data, status=response.code)

        elif request.method == "POST":
            service = ComplaintsService()
            complaint_data = request.data
            response = service.create_complaint(complaint_data, request.user)
            return Response(response.data, status=response.code)

    except ValueError as e:
        # Handle pagination errors
        return Response(
            {"error": "Invalid page or page_size parameter"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        LoggingService().log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def complaints_details_api(request, complaint_id):
    """
    Handles API requests for complaint details based on the HTTP method.

    Args:
        request (HttpRequest): The HTTP request object containing method, user, and data.
        complaint_id (int): The unique identifier of the complaint.

    Supported Methods:
        - GET: Retrieve complaint details by ID.
            - Returns complete complaint information including all related data.
        - PUT: Update complaint details by ID with provided data.
            - Requires complaint data in request body.
            - Updates all modifiable fields of the complaint.
        - DELETE: Delete a complaint by ID.
            - Permanently removes the complaint from the system.
            - Returns confirmation of deletion.
        - PATCH: Perform specific actions on a complaint.
            - Requires 'action' field in request data.
            - Supported actions:
                - 'send_to_department': Forwards complaint to specified department.
                  Required fields: 'department_id' (int).

    Request Body (for PATCH with send_to_department action):
        {
            "action": "send_to_department",
            "department_id": <int>
        }

    Returns:
        Response: A DRF Response object containing:
            - For GET: Complete complaint details and metadata.
            - For PUT: Updated complaint data.
            - For DELETE: Confirmation message.
            - For PATCH: Action result data.
            - Appropriate HTTP status code.

    Raises:
        Returns HTTP 400 for:
            - Invalid actions or missing required fields.
            - Missing 'action' parameter in PATCH requests.
            - Invalid department_id for send_to_department action.
        Returns HTTP 404 if complaint with given ID is not found.
        Returns HTTP 405 for unsupported HTTP methods.
        Returns HTTP 500 for internal server errors.

    Permission Required:
        IsAuthenticated - User must be authenticated to access this endpoint.
    """
    try:
        query_service = ComplaintsQueryService()
        if request.method == "GET":
            response = query_service.get_complaint_by_id(complaint_id)
            return Response(response.data, status=response.code)

        elif request.method == "PUT":
            service = ComplaintsService()
            response = service.update_complaint(complaint_id, request.data, request.user)
            return Response(response.data, status=response.code)

        elif request.method == "DELETE":
            service = ComplaintsService()
            response = service.delete_complaint(complaint_id, request.user)
            return Response(response.data, status=response.code)

        elif request.method == "PATCH":
            # check action
            action = request.data.get("action")
            if not "action" in request.data or not request.data.get("action"):
                return Response(
                    {
                        "error": "Action is required",
                        "valid_actions": ["send_to_department"],
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if action == "send_to_department":
                service = ComplaintsService()
                response = service.send_to_department(
                    user=request.user,
                    complaint_id=complaint_id,
                    department_id=request.data.get("department_id"),
                )
                if not response.success:
                    return Response({"error": response.message}, status=response.code)
                return Response(response.data, status=response.code)
            else:
                return Response(
                    {"error": "Invalid action"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        LoggingService().log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
