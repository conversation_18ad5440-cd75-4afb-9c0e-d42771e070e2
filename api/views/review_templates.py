from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.services.permissions.review_groups import ReviewGroupsService
from base.services.logging.logger import LoggingService
from tasks.services.review_templates import ReviewTemplateService

logging_service = LoggingService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def review_template_api_view(request):
    review_template_service = ReviewTemplateService(request.user)

    try:
        if request.method == "GET":
            params = request.query_params
            members = review_template_service.get_review_templates(params)
            if not members.success:
                return Response(
                    {"message": members.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                members.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            data = request.data
            response = review_template_service.create_review_template(data=data)
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "An error occurred while processing your request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "DELETE"])
@permission_classes([IsAuthenticated])
def review_template_details_api_view(request, template_id):
    review_template_service = ReviewTemplateService(request.user)

    try:
        if request.method == "GET":
            template = review_template_service.get_review_template_by_id(
                template_id=template_id
            )
            if not template.success:
                return Response(
                    {"message": template.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                template.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PUT":
            data = request.data
            response = review_template_service.update_review_template(
                template_id=template_id, data=data
            )
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "DELETE":
            response = review_template_service.delete_review_template(
                template_id=template_id
            )
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                {"message": "Review template deleted successfully."},
                status=status.HTTP_204_NO_CONTENT,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "An error occurred while processing your request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
