from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.logging.logger import LoggingService
from documents.services.operations import DocumentsOperations
from documents.services.query import DocumentQuery



document_service = DocumentsOperations()
query_documents = DocumentQuery()
logging_service = LoggingService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def documents_api(request):
    try:
        if request.method == "GET":
            query_params = request.query_params.dict()

            response = query_documents.get_documents(query_params=query_params)

            if response.success:
                return Response(
                    response.data,
                    status=response.code,
                )   
            else:
                return Response(
                    {"error": response.message}, status=response.code
                )
        elif request.method == "POST":
            user = request.user
            files = request.FILES.getlist("files")

            response = document_service.upload_document(files=files, user=user)

            if response.success:
                return Response(
                    {"files": response.data["files"]},
                    status=response.code,
                )
            else:
                return Response(
                    {"error": response.message}, status=response.code
                )
        else:
            return Response(
                {"error": "Method not allowed"}, status=status.HTTP_405_METHOD_NOT_ALLOWED
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
