from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from base.services.auth import verify_user
from documents.models import Document
from documents.serializers import DocumentSerializer
from documents.views import generate_signed_url


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_document_details_api(request, document_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response({"error": message}, status=status.HTTP_401_UNAUTHORIZED)

    try:
        document = Document.objects.get(id=document_id)
        # url = generate_signed_url(document.file)

        return Response(
            {"document": DocumentSerializer(document).data},
            status=status.HTTP_200_OK,
        )
    except Document.DoesNotExist:
        return Response(
            {"error": "Document not found"}, status=status.HTTP_404_NOT_FOUND
        )
