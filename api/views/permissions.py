from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from adverse_drug_reaction.models import AdverseDrugReaction
from base.models import Department, Facility
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.services.permissions.mixins import BasePermissionsMixin
from base.utils.model_mapping import MODEL_MAPPING
from complaints.models import Complaint
from general_patient_visitor.models import GeneralPatientVisitor
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from patient_visitor_grievance.models import Grievance, GrievanceInvestigation
from staff_incident_reports.models import StaffIncidentReport
from workplace_violence_reports.models import WorkPlaceViolence
from django.contrib.auth.models import Group, User
from accounts.models import Profile


# permission groups
permission_management = PermissionsManagement()
logging_service = LoggingService()


@api_view(["GET", "POST", "PATCH", "DELETE"])
# @permission_classes([IsAuthenticated])


def permission_groups_api_view(request):
    permission_management = PermissionsManagement()
    logging_service = LoggingService()

    try:
        if request.method == "GET":
            # get parameters
            search = request.query_params.get("search", None)
            page = request.query_params.get("page", 1)
            page_size = request.query_params.get("page_size", 10)

            params = {
                "search": search,
                "page": page,
                "page_size": page_size,
            }
            try:
                groups = permission_management.get_permissions_groups(
                    request.user,
                    params=params,
                )
                if not groups.success:
                    return Response(
                        {"error": groups.message}, status=status.HTTP_400_BAD_REQUEST
                    )
                return Response(groups.data, status=status.HTTP_200_OK)
            except Exception as e:
                logging_service.log_error(e)
                return Response(
                    {"error": "Error getting permission groups"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        elif request.method == "POST":
            failed = []
            try:
                group = permission_management.check_group(request.data["group_name"])
                if not group.success:
                    return Response(
                        {"error": group.message}, status=status.HTTP_400_BAD_REQUEST
                    )

                for permission in request.data["permissions"]:
                    model = None
                    if permission["feature"] in MODEL_MAPPING:
                        model = MODEL_MAPPING[permission["feature"]]

                        permissions = permission_management.add_permissions_to_group(
                            group.data, permission["permissions"], model
                        )
                        if not permissions.success:
                            failed.append(permission["feature"])

                return Response(
                    {
                        "message": f"Added permissions to {len(request.data['permissions']) - len(failed)} groups; {len(failed)} not found: {failed}"
                    },
                    status=status.HTTP_200_OK,
                )
            except Exception as e:

                logging_service.log_error(e)
                return Response(
                    {"error": "Internal server error"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        elif request.method == "PATCH":
            failed = []
            try:
                group = Group.objects.get(id=request.data["id"])
                
                if "group_name" in request.data and request.data.get("group_name"):
                    if group.name != request.data["group_name"]:
                        if Group.objects.filter(name=request.data["group_name"]).exists():
                            return Response(
                                {"error": "Group with this name already exists"},
                                status=status.HTTP_400_BAD_REQUEST,
                            )
                        group.name = request.data["group_name"]
                        group.save()
                
                if "permissions" in request.data:
                    group.permissions.clear()
                    if request.data["permissions"]:
                        for permission in request.data["permissions"]:
                            model = None
                            if permission["feature"] in MODEL_MAPPING:
                                model = MODEL_MAPPING[permission["feature"]]
                                
                                permissions = permission_management.add_permissions_to_group(
                                    group, permission["permissions"], model
                                )
                                if not permissions.success:
                                    failed.append(permission["feature"])

                return Response(
                    {
                        "message": f"Updated group successfully. Added permissions to {len(request.data.get('permissions', [])) - len(failed)} features; {len(failed)} failed: {failed}"
                    },
                    status=status.HTTP_200_OK,
                )
            except Group.DoesNotExist:
                return Response(
                    {"error": "Group not found"}, status=status.HTTP_404_NOT_FOUND
                )
            except Exception as e:
                logging_service.log_error(e)
                return Response(
                    {"error": f"Internal server error {e}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        elif request.method == "DELETE":
            try:
                group = Group.objects.get(id=request.data["id"])
                
                if group.user_set.exists():
                    return Response(
                        {"error": "Cannot delete group: users are assigned to this group"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                if group.permissions.exists():
                    return Response(
                        {"error": "Cannot delete group: permissions are attached to this group"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                group.delete()
                return Response(
                    {"message": "Group deleted successfully"},
                    status=status.HTTP_200_OK,
                )
            except Group.DoesNotExist:
                return Response(
                    {"error": "Group not found"}, status=status.HTTP_404_NOT_FOUND
                )
            except Exception as e:
                logging_service.log_error(e)
                return Response(
                    {"error": "Internal server error"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
# @permission_classes([IsAuthenticated])
def permission_group_details_api_view(request, group_id):
    try:
        response = permission_management.get_permissions_group_details(group_id)
        if not response.success:
            return Response(
                {"error": response.message}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(response.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
# @permission_classes([IsAuthenticated])
def permission_group_users_api_view(request, group_id):
    try:
        response = permission_management.get_group_users(group_id)
        if not response.success:
            return Response(
                {"error": response.message}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(response.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# remove permissions from group
@api_view(["DELETE"])
# @permission_classes([IsAuthenticated])
def remove_permissions_from_group_api_view(request, group_id):
    try:
        failed = []
        group = Group.objects.get(id=group_id)
        if "permissions" in request.data and request.data.get("permissions"):
            for permission in request.data["permissions"]:
                model = None
                if permission["feature"] in MODEL_MAPPING:
                    model = MODEL_MAPPING[permission["feature"]]

                    permissions = permission_management.remove_permissions_from_group(
                        group, permission["permissions"], model
                    )
                    if not permissions.success:
                        failed.append(permission["feature"])
        return Response(
            {
                "message": f"removed permissions to {len(request.data['permissions'] if 'permissions' in request.data else []) - len(failed)} groups; {len(failed)} not found: {failed}"
            },
            status=status.HTTP_200_OK,
        )
    except Group.DoesNotExist:
        return Response({"error": "Group not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# incident permissions
@api_view(["GET", "POST", "PATCH", "DELETE"])
# @permission_classes([IsAuthenticated])
def feature_permission_api_view(request, feature):
    """
    Handles API requests to retrieve permissions for a specific feature.

    Args:
        request (HttpRequest): The HTTP request object containing method and other metadata.
        feature (str): The feature for which permissions are being requested.
                       Supported features include:
                       - "general"
                       - "drug_reaction"
                       - "staff_incident_reports"
                       - "patient_visitor_grievance"
                       - "medication_error"
                       - "lost_and_found"
                       - "workplace_violence_reports"
                       - "department"
                       - "facility"
                       - "complaint"
                       - "profiles"

    Returns:
        Response: A Django REST framework Response object containing:
                  - HTTP 200 OK with permission data if the request is successful.
                  - HTTP 400 BAD REQUEST if the feature is invalid.
                  - HTTP 500 INTERNAL SERVER ERROR if an exception occurs.

    Raises:
        Exception: Logs any unexpected exceptions that occur during execution.

    Notes:
        - The function determines the appropriate model based on the `feature` parameter.
        - If the feature is invalid, an error response is returned with a list of valid incident types.
        - Permissions are retrieved using the `permission_management.get_incident_model_permissions` method.
    """
    try:
        if request.method == "GET":
            model = None
            # Get all incident permissions
            if feature == "general":
                model = GeneralPatientVisitor
            elif feature == "drug_reaction":
                model = AdverseDrugReaction

            elif feature == "staff_incident_reports":
                model = StaffIncidentReport
            elif feature == "patient_visitor_grievance":
                model = Grievance

            elif feature == "medication_error":
                model = MedicationError

            elif feature == "lost_and_found":
                model = LostAndFound
            elif feature == "workplace_violence_reports":
                model = WorkPlaceViolence

            # other models profiles | department | facilities | complaints
            elif feature == "department":
                model = Department
            elif feature == "facility":
                model = Facility
            elif feature == "complaint":
                model = Complaint
            elif feature == "profiles":
                model = Profile
            else:
                return Response(
                    status=status.HTTP_400_BAD_REQUEST,
                    data={
                        "error": "Invalid incident type",
                        "incident_types": "general, staff, adr, grievance, medication_error,l ost_and_found, work_place",
                    },
                )
            if model:
                permission = permission_management.get_incident_model_permissions(
                    model=model, type="incident"
                )
            else:
                permission = permission_management.get_incident_model_permissions(
                    model=GeneralPatientVisitor, type="incident"
                )
            return Response(
                status=status.HTTP_200_OK,
                data=permission.data,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            data={"error": "Internal server error"},
        )


# user permissions
@api_view(["GET", "POST", "PATCH", "DELETE"])
@permission_classes([IsAuthenticated])
def user_permission_api_view(request):
    pass


# base permissions
@api_view(["GET", "POST", "PATCH", "DELETE"])
# @permission_classes([IsAuthenticated])
def base_permission_api_view(request, feature):
    try:
        if request.method == "GET":
            model = None

            # Get all base permissions
            if feature == "users":
                return Response(
                    status=status.HTTP_400_BAD_REQUEST,
                    data=[],
                )
            else:
                permissions = [
                    {"code_name": codename, "name": name}
                    for codename, name in BasePermissionsMixin.custom_permissions
                ]
                return Response(
                    status=status.HTTP_200_OK,
                    data=permissions,
                )
    except Exception as e:
        return Response(
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            data={"error": "Internal server error"},
        )
