from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.contenttypes.models import ContentType

from activities.models import ActivityLog
from activities.serializers import ActivityLogListSerializer
from activities.services import ActivityService
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.logging.logger import LoggingService
from base.utils.model_mapping import MODEL_MAPPING

logging_service = LoggingService()


@api_view(["GET"])
def activities_list(request, incident_id):
    """
    Enhanced activities list endpoint that supports both legacy incident_id
    and new content_type/object_id approach.
    """
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident_type = request.GET.get('incident_type')

        if incident_type and incident_type in MODEL_MAPPING:
            model_class = MODEL_MAPPING[incident_type]
            content_type = ContentType.objects.get_for_model(model_class)

            activities_list = ActivityLog.objects.filter(
                content_type=content_type,
                object_id=incident_id
            ).order_by("-timestamp")
        else:
            # For legacy support, try to find activities by object_id across all content types
            activities_list = ActivityLog.objects.filter(
                object_id=incident_id
            ).order_by("-timestamp")

        serializer = ActivityLogListSerializer(activities_list, many=True)

        return Response({
            "data": serializer.data,
            "count": activities_list.count()
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
