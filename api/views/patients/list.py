from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from accounts.models import Profile
from accounts.serializers import GetProfileSerializer
import time


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def patients_list_api(request):

    patients_list = Profile.objects.all()  # filter(is_patient_visitor=True)
    serializer = GetProfileSerializer(patients_list, many=True)
    time.sleep(3)
    return Response(serializer.data, status=status.HTTP_200_OK)
