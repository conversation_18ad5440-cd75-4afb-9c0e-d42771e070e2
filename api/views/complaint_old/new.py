# reviews/views.py
from django.contrib.auth.models import User
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON>ars<PERSON>
from base.services.auth import verify_user
from base.services.forms import check_missing_fields, check_user_facility
from complaints.models import Complaint
from incidents.emails.new_complaint import send_new_complaint_email
from base.models import Department
from reviews.serializers import ComplaintSerializer
from datetime import datetime
import os


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_complaint_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"error": "Authentication failed"}, status=status.HTTP_401_UNAUTHORIZED
        )
    facilit_response = check_user_facility(request.data, user)
    if facilit_response.success:
        request.data["complain_facility"] = facilit_response.data
    else:
        return facilit_response.data
    required_fields = [
        "details",
    ]
    missing_fields_response = check_missing_fields(request.data, required_fields)

    if missing_fields_response:
        return missing_fields_response

    date_of_complaint = request.data.get("date_of_complaint")
    patient_name = request.data.get("patient_name")
    medical_record_number = request.data.get("medical_record_number")
    complaint_nature = request.data.get("complaint_nature")
    department = request.data.get("department")
    complaint_type = request.data.get("complaint_type")
    resolved_by_staff = request.data.get("resolved_by_staff")
    assigned_to = request.data.get("assigned_to")
    how_complaint_was_taken = request.data.get("how_complaint_was_taken")
    details = request.data.get("details")
    phone_number = request.data.get("phone_number")
    complain_facility = request.data.get("complain_facility")

    try:
        new_complaint, created = Complaint.objects.get_or_create(
            created_by=user,
            phone_number=phone_number,
            date_of_complaint=date_of_complaint,
            patient_name=patient_name,
            medical_record_number=medical_record_number,
            complaint_nature=complaint_nature,
            complaint_type=complaint_type,
            resolved_by_staff=resolved_by_staff,
            how_complaint_was_taken=how_complaint_was_taken,
            details=details,
            complain_facility=complain_facility,
        )
        if created:

            incident_date_and_time = datetime.fromisoformat(
                str(new_complaint.created_at)
            ).strftime("%A, %B %d, %Y at %I:%M:%S %p %Z")

            admin_users = User.objects.filter(groups__name="Manager")
            for admin_user in admin_users:

                send_new_complaint_email(
                    admin_user.email,
                    new_complaint.id,
                    incident_date_and_time,
                    patient_name,
                    new_complaint.details,
                )

            return Response(
                {
                    "message": "Complaint is submitted successfully, but no staff members assigned",
                    "complaint": {
                        "id": new_complaint.id,
                        "patient_name": new_complaint.patient_name,
                        "medical_record_number": new_complaint.medical_record_number,
                        "complaint_nature": new_complaint.complaint_nature,
                        "phone_number": new_complaint.phone_number,
                        "complaint_type": new_complaint.complaint_type,
                        "how_complaint_was_taken": new_complaint.how_complaint_was_taken,
                        "details": new_complaint.details,
                        "resolved_by_staff": new_complaint.resolved_by_staff,
                        "complain_facility": new_complaint.complain_facility.name,
                    },
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"message": "Complaint already exists"}, status=status.HTTP_409_CONFLICT
            )

    except Exception as e:
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
