from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from rest_framework import status
from api.views.auth.permissions_list import has_permissions
from api.views.complaint_old.serializers import UpdateComplaintSerializer
from base.services.forms import check_missing_fields
from complaints.models import Complaint
from base.services.auth import verify_user
from incidents.emails.send_complaint_to_department import (
    send_complaint_report_to_department,
)
from base.models import Department
from django.contrib.auth.models import User
from datetime import datetime
import pytz


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def send_complaint_to_department_api(request, complaint_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    requited_fields = ["department", "assignees"]
    missing_fields_response = check_missing_fields(
        required_fields=requited_fields, data=request.data
    )
    if missing_fields_response:
        return missing_fields_response
    assignees = []
    try:
        complaint = Complaint.objects.get(id=complaint_id)
        if not has_permissions(user, ["Manager"]):
            return Response(
                {
                    "status": "failed",
                    "message": "You do not have the required permissions",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        department = Department.objects.get(name=request.data.get("department"))
        complaint.department.add(department)
        for assignee in request.data.get("assignees"):
            user = User.objects.filter(
                id=assignee["user_id"]
            ).first()  # Use .first() to get a single user
            if user:
                assignees.append(user)

        complaint.assignees.add(*assignees)
        complaint.save()
        for user in assignees:
            cst = pytz.FixedOffset(-360)

            incident_date_and_time = (
                datetime.fromisoformat(str(complaint.created_at))
                .astimezone(cst)
                .strftime("%A, %B %d, %Y at %H:%M:%S CST")
            )
            send_complaint_report_to_department(
                recipient_email=user.email,
                complaint_id=complaint.id,
                complaint_datetime=incident_date_and_time,
                complaint_nature=complaint.complaint_nature,
                complaint_details=complaint.details,
            )

        return Response(
            {"message": "Complaint is sent to department"}, status=status.HTTP_200_OK
        )
    except Department.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Department not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Complaint.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Complaint not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
