from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>NPars<PERSON>

from base.services.auth import verify_user
from complaints.models import Complaint
from reviews.serializers import ComplaintSerializer


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def complaints_list_api(request):
    try:
        complaints = Complaint.objects.all()
        serializer = ComplaintSerializer(complaints, many=True)
        return Response({"complaints": serializer.data}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def user_complaints_list_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response({"error": message}, status=status.HTTP_401_UNAUTHORIZED)
    try:
        complaints = Complaint.objects.filter(created_by=user)
        serializer = ComplaintSerializer(complaints, many=True)
        return Response({"complaints": serializer.data}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
