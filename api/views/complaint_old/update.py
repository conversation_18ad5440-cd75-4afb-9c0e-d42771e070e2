from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from rest_framework import status
from api.views.auth.permissions_list import has_permissions
from api.views.complaint_old.serializers import UpdateComplaintSerializer
from complaints.models import Complaint
from base.services.auth import verify_user


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_complaint_api(request, complaint_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        complaint = Complaint.objects.get(id=complaint_id)
        if (
            has_permissions(user, ["Admin", "Super User"])
            and not complaint.created_by == user
        ):
            return Response(
                {
                    "status": "failed",
                    "message": "You are not authorized to update this complaint",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        elif len(complaint.department.all()) > 0:
            return Response(
                {
                    "status": "failed",
                    "message": "You cannot update a complaint assigned to a department",
                    "department": [
                        {"id": department.id, "name": department.name}
                        for department in complaint.department.all()
                    ],
                },
                status=status.HTTP_403_FORBIDDEN,
            )
    except Complaint.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Complaint not found"},
            status=status.HTTP_404_NOT_FOUND,
        )

    serializer = UpdateComplaintSerializer(complaint, data=request.data, partial=True)

    if serializer.is_valid():
        serializer.save()
        return Response(
            {
                "status": "success",
                "message": "Complaint updated successfully",
                "complaint": serializer.data,
            },
            status=status.HTTP_200_OK,
        )
    else:
        return Response(
            {"status": "failed", "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_complaint_api(request, complaint_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        complaint = Complaint.objects.get(id=complaint_id)
        if not complaint.created_by == user:
            return Response(
                {
                    "status": "failed",
                    "message": "You are not authorized to delete this complaint",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        elif len(complaint.department.all()) > 0:
            return Response(
                {
                    "status": "failed",
                    "message": "You cannot delete a complaint assigned to department",
                }
            )
        complaint.delete()
        return Response(
            {"status": "success", "message": "Complaint deleted successfully"},
            status=status.HTTP_200_OK,
        )

    except Complaint.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Complaint not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
