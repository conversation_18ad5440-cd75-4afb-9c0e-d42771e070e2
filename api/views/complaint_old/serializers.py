from rest_framework import serializers
from complaints.models import Complaint


class UpdateComplaintSerializer(serializers.ModelSerializer):
    class Meta:
        model = Complaint
        fields = [
            "patient_name",
            "phone_number",
            "medical_record_number",
            "complaint_nature",
            "complaint_type",
            "resolved_by_staff",
            "how_complaint_was_taken",
            "details",
        ]
