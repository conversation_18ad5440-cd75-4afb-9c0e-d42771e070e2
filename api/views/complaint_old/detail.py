# reviews/views.py
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from api.views.auth.permissions_list import has_permissions
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.logging.logger import LoggingService
from complaints.models import Complaint
from reviews.serializers import ComplaintSerializer

logging_service = LoggingService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def complaint_detail_api(request, complaint_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"error": "Authentication failed"}, status=status.HTTP_401_UNAUTHORIZED
        )

    try:
        complaint = Complaint.objects.get(id=complaint_id)
        if not complaint.created_by == user and has_permissions(
            user, ["Manager", "managers"]
        ):
            return Response(
                {"error": "You are not authorized to view this complaint"},
                status=status.HTTP_403_FORBIDDEN,
            )
        serializer = ComplaintSerializer(complaint)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except Complaint.DoesNotExist:
        return Response(
            {"error": "Complaint not found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )
