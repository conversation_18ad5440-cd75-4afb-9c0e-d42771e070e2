from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.services.permissions.review_groups import ReviewGroupsService
from base.services.logging.logger import LoggingService

logging_service = LoggingService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def review_groups_api_view(request):
    review_group_service = ReviewGroupsService(request.user)

    try:
        if request.method == "GET":
            params = request.query_params
            groups = review_group_service.get_review_groups(params)
            if not groups.success:
                return Response(
                    {"message": groups.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                groups.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            data = request.data
            response = review_group_service.create_review_group(data=data)
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "An error occurred while processing your request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "DELETE"])
@permission_classes([IsAuthenticated])
def review_group_details_api_view(request, group_id):
    review_group_service = ReviewGroupsService(request.user)

    try:
        if request.method == "GET":
            group = review_group_service.get_group_by_id(group_id=group_id)
            if not group.success:
                return Response(
                    {"message": group.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                group.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PUT":
            data = request.data
            response = review_group_service.update_review_group(
                group_id=group_id, data=data
            )
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "DELETE":
            response = review_group_service.delete_review_group(group_id=group_id)
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                {"message": "Review group deleted successfully."},
                status=status.HTTP_204_NO_CONTENT,
            )
        else:
            return Response(
                {"message": "Method not allowed."},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "An error occurred while processing your request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PATCH"])
@permission_classes([IsAuthenticated])
def review_group_members_api_view(request, group_id):
    review_group_service = ReviewGroupsService(request.user)

    try:
        if request.method == "GET":
            members = review_group_service.get_review_group_members(group_id=group_id)
            if not members.success:
                return Response(
                    {"message": members.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                members.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PATCH":
            member_id = request.data.get("member_id", None)
            action = request.data.get("action", None)

            if not member_id:
                return Response(
                    {"error", "Member id is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not action:
                return Response(
                    {"error", "Action is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if action not in ["add", "remove"]:
                return Response(
                    {"error", "Invalid action, Use 'add' or 'remove'."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if action == "add":

                response = review_group_service.add_member(
                    group_id=group_id,
                    member_d=member_id,
                )
                if not response.success:
                    return Response(
                        {"message": response.message},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )

            elif action == "remove":
                response = review_group_service.remove_member(
                    group_id=group_id,
                    member_id=member_id,
                )
                if not response.success:
                    return Response(
                        {"message": response.message},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )

            else:
                return Response(
                    {"message": "Invalid action. Use 'add' or 'remove'."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"message": "Method not allowed."},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "An error occurred while processing your request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
