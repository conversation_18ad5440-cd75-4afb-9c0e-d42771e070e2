from django.apps import apps
from rest_framework.response import Response
from rest_framework import status

from api.views.auth.permissions_list import is_admin_user, is_super_user
from reviews.serializers import ReviewsSerializer


def get_incident_reviews(user, model_name, app_label, incident_id):
    try:
        # Get the model class dynamically
        model_class = apps.get_model(app_label, model_name)

        # Retrieve the incident instance
        incident = model_class.objects.get(pk=incident_id)
        if not is_super_user(user) and not is_admin_user(
            user, incident.report_facility
        ):
            return Response(
                {"error": "You do not have enough rights to update this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
        # Get all reviews for the incident
        reviews = incident.reviews.all().order_by("-created_at")
        serializer = ReviewsSerializer(reviews, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    except model_class.DoesNotExist:
        return Response(
            {"error": f"No incident with that id exists"},
            status=status.HTTP_404_NOT_FOUND,
        )
