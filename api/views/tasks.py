from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.services.user_profile.query import UserTasksQuery
from base.services.logging.logger import LoggingService
from tasks.services.actions import TaskActions
from tasks.services.operations import TaskService
from tasks.services.query import TasksQueryService
from tasks.services.template_tasks import ReviewTasksService

logging_service = LoggingService()
user_tasks_query = UserTasksQuery()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def review_template_task_api_view(request, template_id):
    tasks_service = ReviewTasksService(request.user)

    try:
        if request.method == "POST":
            data = request.data
            response = tasks_service.create_task(template_id, data)
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        elif request.method == "GET":
            params = request.query_params
            response = tasks_service.get_tasks(template_id, params)
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_404_NOT_FOUND,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "Method not allowed."},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "An error occurred while processing your request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def review_template_task_details_api_view(request, template_id, task_id):
    tasks_service = ReviewTasksService(request.user)

    try:
        if request.method == "GET":
            response = tasks_service.get_task_by_id(task_id)
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_404_NOT_FOUND,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PUT":
            data = request.data
            response = tasks_service.update_task(task_id, data)
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "DELETE":
            response = tasks_service.delete_task(task_id)
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_404_NOT_FOUND,
                )
            return Response(
                {"message": "Task deleted successfully."},
                status=status.HTTP_204_NO_CONTENT,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "An error occurred while processing your request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def review_template_task_review_api_view(request, template_id, task_id):
    tasks_service = ReviewTasksService(request.user)
    try:
        if request.method == "PATCH":
            """This is going to be used to add or remove review groups"""
            action = request.data.get("action", None)
            if not action:
                return Response(
                    {"message": "Action is required."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if action == "add":
                response = tasks_service.add_review_groups(task_id, request.data)
                if not response.success:
                    return Response(
                        {"message": response.message},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            if action == "remove":
                response = tasks_service.remove_review_groups(task_id, request.data)
                if not response.success:
                    return Response(
                        {"message": response.message},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "Invalid action."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "An error occurred while processing your request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def user_tasks_api(request, profile_id):
    try:
        if request.method == "GET":
            filters = request.query_params.dict()
            response = user_tasks_query.get_user_tasks(
                request.user, profile_id, filters
            )
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            pass
        else:
            return Response(
                {"message": "Method not allowed."},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "An error occurred while processing your request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def tasks_list_create_api(request):
    """
    Handles the listing and creation of tasks.

    Args:
        request (HttpRequest): The HTTP request object containing user and data.

    Returns:
        Response: A DRF Response object with appropriate status and data/message.
    """
    tasks_service = TasksQueryService()
    if request.method == "GET":
        response = tasks_service.get_tasks(request.query_params)
        if not response.success:
            return Response(
                {"message": response.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            response.data,
            status=status.HTTP_200_OK,
        )
    else:
        return Response(
            {"message": "Method not allowed."},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def tasks_details_api(request, task_id):
    """
    Handles task detail operations based on HTTP request method.

    Args:
        request (HttpRequest): The HTTP request object containing user and data.
        task_id (int): The unique identifier of the task.

    Supported Methods:
        - GET: Retrieve details of a specific task.
        - PUT: Update a specific task with provided data.
        - DELETE: Delete a specific task.
        - PATCH: Perform an action on the task (submit, complete, approve).

    Returns:
        Response: A DRF Response object with appropriate status and data/message.

    Possible Actions (PATCH):
        - "submit": Submit the task.
        - "complete": Mark the task as complete.
        - "approve": Approve the task.

    Error Handling:
        Returns HTTP 400 with an error message if the operation fails or required data is missing.
    """
    tasks_service = TaskService(request.user)
    tasks_actions_service = TaskActions(task_id, request.user)
    if request.method == "GET":
        response = tasks_service.get_task_by_id(task_id)
        if not response.success:
            return Response(
                {"message": response.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            response.data,
            status=status.HTTP_200_OK,
        )
    elif request.method == "PUT":
        response = tasks_service.update_task(task_id=task_id, data=request.data)
        if not response.success:
            return Response(
                {"message": response.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            response.data,
            status=status.HTTP_200_OK,
        )

    elif request.method == "DELETE":
        response = tasks_service.delete_task(task_id=task_id)
        if not response.success:
            return Response(
                {"message": response.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            {"message": "Task deleted successfully."},
            status=status.HTTP_204_NO_CONTENT,
        )
    elif request.method == "PATCH":

        """check action"""

        if not "action" in request.data or not request.data.get("action"):
            return Response(
                {"message": "Action is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        action = request.data.get("action")

        if action == "submit":
            submit_response = tasks_actions_service.submit_task()
            if not submit_response.success:
                return Response(
                    {"message": submit_response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                submit_response.data,
                status=status.HTTP_200_OK,
            )
        elif action == "complete":
            complete_response = tasks_actions_service.complete_task()
            if not complete_response.success:
                return Response(
                    {"message": complete_response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                complete_response.data,
                status=status.HTTP_200_OK,
            )
        elif action == "approve":
            approve_response = tasks_actions_service.approve_task()
            if not approve_response.success:
                return Response(
                    {"message": approve_response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                approve_response.data,
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": f"Invalid action: {action}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    else:
        return Response(
            {"message": "Method not allowed."},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )


class TaskActionsService:
    def __init__(self, user, task_id, data):
        self.user = user
        self.tasks_service = ReviewTasksService(user=user)
