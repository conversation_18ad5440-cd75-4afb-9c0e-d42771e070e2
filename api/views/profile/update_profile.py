from django.contrib.auth.models import User, Group
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from accounts.models import Profile
from base.services.auth import verify_user
from base.models import Department


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def update_self_profile(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response({"error": message}, status=status.HTTP_401_UNAUTHORIZED)

    profile, updated, message = update_profile(request.data, user)
    if updated:
        return Response(
            {
                "profile": {
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "phone_number": profile.phone_number,
                    "gender": profile.gender,
                    "date_of_birth": profile.date_of_birth,
                    "address": profile.address,
                    "birth_country": profile.birth_country,
                }
            },
            status=status.HTTP_200_OK,
        )
    else:
        return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)


def update_profile(data, user):

    try:
        user.first_name = data.get("first_name", user.first_name)
        user.last_name = data.get("last_name", user.last_name)
        user.email = data.get("email", user.email)
        user.save()

        phone_number = data.get("phone_number")
        gender = data.get("gender")
        date_of_birth = data.get("date_of_birth")
        address = data.get("address")
        birth_country = data.get("birth_country")

        # update user
        user.first_name = data.get("first_name") if data.get("first_name") else None
        user.last_name = data.get("last_name") if data.get("last_name") else None
        user.email = data.get("email") if data.get("email") else None
        user.save()

        # update profile
        profile, created = Profile.objects.get_or_create(user=user)
        profile.phone_number = phone_number
        profile.gender = gender
        profile.date_of_birth = date_of_birth
        profile.address = address
        profile.birth_country = birth_country
        profile.save()

        return profile, True, "Profile updated successfully"
    except Exception as e:
        return None, False, "Internal server error"


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_profile_api(request, profile_id):
    try:
        profile = Profile.objects.get(id=profile_id)
        profile.delete()
        return Response(
            {"message": "Profile deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )
    except Exception as e:
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )
