from django.contrib.auth.models import User, Group
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from accounts.models import Profile, UserPreference
from base.services.auth import verify_user
from base.models import Department


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_details(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response({"error": message}, status=status.HTTP_401_UNAUTHORIZED)

    profile_data = {}

    departments = [
        {"name": department.name, "id": department.id}
        for department in user.incident_members.all()
    ]
    preferences = []
    try:
        if Profile.objects.filter(user=user).exists():
            profile = Profile.objects.filter(user=user).first()
            profile_data = {
                "id": profile.id,
                "is_corporate": profile.is_corporate,
                "phone_number": profile.phone_number,
                "gender": profile.gender,
                "date_of_birth": profile.date_of_birth,
                "address": profile.address,
                "birth_country": profile.birth_country,
                "facility": (
                    profile.facility.name if profile.facility and profile else None
                ),
            }
            preferences = UserPreference.objects.filter(user=user).first()

        return Response(
            {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "permissions": [group.name for group in user.groups.all()],
                "profile": profile_data,
                "departments": departments,
                "preferences": {
                    "timezone": preferences.user_timezone if preferences else None,
                },
            },
            status=status.HTTP_200_OK,
        )
    except Profile.DoesNotExist:
        return Response(
            {"error": "User profile not found"}, status=status.HTTP_404_NOT_FOUND
        )
