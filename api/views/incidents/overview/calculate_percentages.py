from django.db.models import Count
from django.apps import apps


def get_percentages(app_name, model_name, field_name):
    try:
        Model = apps.get_model(app_name, model_name)
    except LookupError:
        return {"error": f"Model '{model_name}' not found in app '{app_name}'"}

    field = Model._meta.get_field(field_name)

    # Get the choices if they exist
    choices_dict = dict(field.choices) if field.choices else {}

    data = (
        Model.objects.values(field_name)
        .annotate(count=Count(field_name))
        .order_by(field_name)
    )

    total_count = sum(item["count"] for item in data)

    percentages = []
    for item in data:
        value = item[field_name]
        count = item["count"]
        percentage = round((count / total_count) * 100, 2) if total_count > 0 else 0

        # Get the human-readable name for the value
        name = choices_dict.get(value, str(value))

        percentages.append(
            {
                "name": name,
                "count": count,
                "percentage": percentage,
            }
        )

    percentages.sort(key=lambda x: x["percentage"], reverse=True)
    return percentages


# Example usage:
# incident_percentages = get_percentages('incidents', 'Incident', 'incident_type')
