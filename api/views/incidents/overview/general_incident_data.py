from django.db.models import Count
from django.db.models.functions import Round

from general_patient_visitor.models import GeneralPatientVisitor


def calculate_incident_type_percentages():
    general_incidents_data = (
        GeneralPatientVisitor.objects.values("incident_type")
        .annotate(count=Count("incident_type"))
        .order_by("incident_type")
    )

    total_general_incidents = sum(item["count"] for item in general_incidents_data)

    percentages = []
    for item in general_incidents_data:
        incident_type = item["incident_type"]
        count = item["count"]

        percentage = (
            round((count / total_general_incidents) * 100, 2)
            if total_general_incidents > 0
            else 0
        )

        incident_type_name = dict(GeneralPatientVisitor.INCIDENT_TYPE_CHOICES).get(
            incident_type, incident_type
        )

        percentages.append(
            {
                "name": incident_type_name,
                "count": count,
                "percentage": percentage,
            }
        )

    percentages.sort(key=lambda x: x["percentage"], reverse=True)

    return percentages
