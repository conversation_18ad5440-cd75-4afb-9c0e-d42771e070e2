from rest_framework.response import Response
from rest_framework import status
from base.services.auth import verify_user
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from general_patient_visitor.models import GeneralPatientVisitor
from adverse_drug_reaction.models import AdverseDrugReaction
from patient_visitor_grievance.models import Grievance
from patient_visitor_grievance.models import GrievanceInvestigation
from staff_incident_reports.models import StaffIncidentReport
from staff_incident_reports.models import StaffIncidentInvestigation
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from workplace_violence_reports.models import WorkPlaceViolence


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_incidents_draft_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        formatted_drafts = format_drafts(
            GeneralPatientVisitor.objects.filter(status="Draft"),
            "general patient visitor",
            user,
        )
        formatted_adr_drafts = format_drafts(
            AdverseDrugReaction.objects.filter(status="Draft"),
            "adverse drug reaction",
            user,
        )
        formatted_grievance_drafts = format_drafts(
            Grievance.objects.filter(status="Draft"),
            "patient/visitor grievance",
            user,
        )
        formatted_grievance_investigation_drafts = format_drafts(
            GrievanceInvestigation.objects.filter(status="Draft"),
            "grievance investigation",
            user,
        )
        formatted_employee_incident_drafts = format_drafts(
            StaffIncidentReport.objects.filter(status="Draft"),
            "staff",
            user,
        )
        formatted_health_investigation_drafts = format_drafts(
            StaffIncidentInvestigation.objects.filter(status="Draft"),
            "staff health investigation",
            user,
        )
        formatted_lost_and_found_drafts = format_drafts(
            LostAndFound.objects.filter(status="Draft"), "lost and found", user
        )
        formatted_medical_error_drafts = format_drafts(
            MedicationError.objects.filter(status="Draft"), "medical error", user
        )
        formatted_workplace_violence_drafts = format_drafts(
            WorkPlaceViolence.objects.filter(status="Draft"), "workplace violence", user
        )

        all_drafts = {
            "general_incident": formatted_drafts,
            "adverse_drug_reaction": formatted_adr_drafts,
            "grievance_incident": formatted_grievance_drafts,
            "grievance_investigation": formatted_grievance_investigation_drafts,
            "employee_incident": formatted_employee_incident_drafts,
            "health_investigation": formatted_health_investigation_drafts,
            "lost_and_found": formatted_lost_and_found_drafts,
            "medical_error": formatted_medical_error_drafts,
            "workplace_violence": formatted_workplace_violence_drafts,
        }

        return Response(all_drafts, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


def format_drafts(drafts, category, user):
    user_filtered_drafts = drafts.filter(created_by=user)

    sorted_drafts = sorted(
        user_filtered_drafts, key=lambda x: x.created_at, reverse=True
    )

    return [
        {
            "id": draft.id,
            "status": draft.status,
            "created_at": draft.created_at,
            "current_step": draft.current_step,
            "category": category,
        }
        for draft in sorted_drafts
    ]
