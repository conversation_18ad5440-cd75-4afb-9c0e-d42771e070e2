from rest_framework import status
from django.db.models import Count
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_spectacular.utils import extend_schema
from django.db.models.functions import TruncMonth
from collections import defaultdict
from calendar import month_name

from api.views.incidents.overview.general_incident_data import (
    calculate_incident_type_percentages,
)
from base.services.auth import verify_user
from drf_spectacular.utils import extend_schema
from datetime import datetime

from adverse_drug_reaction.models import (
    AdverseDrugReaction,
)
from base.models import Department
from base.services.logging.logger import LoggingService
from staff_incident_reports.models import (
    StaffIncidentReport,
)
from general_patient_visitor.models import GeneralPatientVisitor
from patient_visitor_grievance.models import Grievance
from patient_visitor_grievance.models import (
    GrievanceInvestigation,
)
from staff_incident_reports.models import StaffIncidentInvestigation
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from workplace_violence_reports.models import (
    WorkPlaceViolence,
)
from adverse_drug_reaction.models import AdverseDrugReaction
from complaints.models import Complaint
from staff_incident_reports.models import StaffIncidentInvestigation
from patient_visitor_grievance.models import GrievanceInvestigation
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from staff_incident_reports.models import StaffIncidentReport
from patient_visitor_grievance.models import Grievance

logging_service = LoggingService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def incident_overview_data(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"error": "Authentication failed"}, status=status.HTTP_401_UNAUTHORIZED
            )

        try:

            # queries for all models
            general_incidents = GeneralPatientVisitor.objects.all()
            adverse_drug_reactions = AdverseDrugReaction.objects.all()
            employee_incidents = StaffIncidentReport.objects.all()
            grievances = Grievance.objects.all()
            grievance_investigations = GrievanceInvestigation.objects.all()
            health_investigations = StaffIncidentInvestigation.objects.all()
            lost_and_founds = LostAndFound.objects.all()
            medical_errors = MedicationError.objects.all()
            workplace_violences = WorkPlaceViolence.objects.all()
            departments = Department.objects.all()

            # counts for each model
            counts = {
                "departments": len(departments),
                "general": len(general_incidents),
                "adverse_drug_reactions": len(adverse_drug_reactions),
                "employee_incidents": len(employee_incidents),
                "grievances": len(grievances),
                "grievance_investigations": len(grievance_investigations),
                "health_investigations": len(health_investigations),
                "lost_and_founds": len(lost_and_founds),
                "medical_errors": len(medical_errors),
                "workplace_violences": len(workplace_violences),
            }

            general_incident_data = calculate_incident_type_percentages()
            overview_data = {
                "numbers": counts,
                "general_incidents": general_incident_data,
            }
            return Response({"overview": overview_data}, status=status.HTTP_200_OK)
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# general GeneralPatientVisitor overview
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def general_incidents_overview(request):
    try:
        incidents = GeneralPatientVisitor.objects.all()
        incidents_count = incidents.count()

        # fall related type percentage
        fall_related_type_incidents = incidents.exclude(
            fall_related_type__isnull=True
        ).exclude(fall_related_type__exact="")
        fall_related_incident = fall_related_type_incidents.first()
        fall_related_type_count = fall_related_type_incidents.count()
        fall_related_type_percentage = (
            (fall_related_type_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        # treatment type percentage
        treatment_type_incidents = incidents.exclude(
            treatment_type__isnull=True
        ).exclude(treatment_type__exact="")
        treatment_related_incident = treatment_type_incidents.first()
        treatment_type_count = treatment_type_incidents.count()
        treatment_type_percentage = (
            (treatment_type_count / incidents_count) * 100 if incidents_count > 0 else 0
        )

        # equipment malfunction percentage
        equipment_malfunction_incidents = incidents.exclude(
            equipment_malfunction__isnull=True
        ).exclude(equipment_malfunction__exact="")
        equipment_malfunction_incident = equipment_malfunction_incidents.first()
        equipment_malfunction_count = equipment_malfunction_incidents.count()
        equipment_malfunction_percentage = (
            (equipment_malfunction_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        other_status_incidents = incidents.exclude(other_status__isnull=True).exclude(
            other_status__exact=""
        )
        other_status_incident = other_status_incidents.first()
        other_status_incident_count = other_status_incidents.count()
        other_status_incidents_percentage = (
            (other_status_incident_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        data = [
            {
                "type": "Fall related",
                "percentage": fall_related_type_percentage,
                "incident_date": (
                    fall_related_incident.incident_date
                    if fall_related_incident
                    else None
                ),
                "facility": (
                    fall_related_incident.report_facility.name
                    if fall_related_incident and fall_related_incident.report_facility
                    else None
                ),
                "department": (
                    fall_related_incident.department.name
                    if fall_related_incident and fall_related_incident.department
                    else None
                ),
            },
            {
                "type": "Treatment related",
                "percentage": treatment_type_percentage,
                "incident_date": (
                    treatment_related_incident.incident_date
                    if treatment_related_incident
                    else None
                ),
                "facility": (
                    treatment_related_incident.report_facility.name
                    if treatment_related_incident
                    and treatment_related_incident.report_facility
                    else None
                ),
                "department": (
                    treatment_related_incident.department.name
                    if treatment_related_incident
                    and treatment_related_incident.department
                    else None
                ),
            },
            {
                "type": "Equipment malfunction related",
                "percentage": equipment_malfunction_percentage,
                "incident_date": (
                    equipment_malfunction_incident.incident_date
                    if equipment_malfunction_incident
                    else None
                ),
                "facility": (
                    equipment_malfunction_incident.report_facility.name
                    if equipment_malfunction_incident
                    and equipment_malfunction_incident.report_facility
                    else None
                ),
                "department": (
                    equipment_malfunction_incident.department.name
                    if equipment_malfunction_incident
                    and equipment_malfunction_incident.department
                    else None
                ),
            },
            {
                "type": "Other status",
                "percentage": other_status_incidents_percentage,
                "incident_date": (
                    other_status_incident.incident_date
                    if other_status_incident
                    else None
                ),
                "facility": (
                    other_status_incident.report_facility.name
                    if other_status_incident and other_status_incident.report_facility
                    else None
                ),
                "department": (
                    other_status_incident.department.name
                    if other_status_incident and other_status_incident.department
                    else None
                ),
            },
        ]

        return Response({"data": data}, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": f"an error occurred while fetching"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# adverse drug reaction GeneralPatientVisitor overview
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def adverse_drug_reaction_incidents_overview(request):
    try:
        incidents = AdverseDrugReaction.objects.all()
        incidents_count = incidents.count()

        # inpatient
        inpatient_type_data = incidents.filter(patient_type="In Patient")
        inpatient_type = inpatient_type_data.first()
        inpatient_type_count = inpatient_type_data.count()
        inpatient_type_percentage = (
            (inpatient_type_count / incidents_count) * 100 if incidents_count > 0 else 0
        )

        # outpatient
        outpatient_type_data = incidents.filter(patient_type="Out Patient")
        outpatient_type = outpatient_type_data.first()
        outpatient_type_count = outpatient_type_data.count()
        outpatient_type_percentage = (
            (outpatient_type_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        # Er
        er_type_data = incidents.filter(patient_type="Er")
        er_type = er_type_data.first()
        er_type_count = er_type_data.count()
        er_type_percentage = (
            (er_type_count / incidents_count) * 100 if incidents_count > 0 else 0
        )

        data = [
            {"Total incidents": incidents_count},
            {
                "patient type": "In Patient",
                "id": inpatient_type.id if inpatient_type else None,
                "percentage": round(inpatient_type_percentage, 2),
                "incident_date": (
                    inpatient_type.incident_date if inpatient_type else None
                ),
                "facility": (
                    inpatient_type.report_facility.name
                    if inpatient_type and inpatient_type.report_facility
                    else None
                ),
                "department": (
                    inpatient_type.department.name
                    if inpatient_type and inpatient_type.department
                    else None
                ),
            },
            {
                "patient type": "Out Patient",
                "id": outpatient_type.id if outpatient_type else None,
                "percentage": round(outpatient_type_percentage, 2),
                "incident_date": (
                    outpatient_type.incident_date if outpatient_type else None
                ),
                "facility": (
                    outpatient_type.report_facility.name
                    if outpatient_type and outpatient_type.report_facility
                    else None
                ),
                "department": (
                    outpatient_type.department.name
                    if outpatient_type and outpatient_type.department
                    else None
                ),
            },
            {
                "patient type": "Er",
                "id": er_type.id if er_type else None,
                "percentage": round(er_type_percentage, 2),
                "incident_date": er_type.incident_date if er_type else None,
                "facility": (
                    er_type.report_facility.name
                    if er_type and er_type.report_facility
                    else None
                ),
                "department": (
                    er_type.department.name if er_type and er_type.department else None
                ),
            },
        ]

        return Response({"data": data}, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": f"an error occurred while fetching"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# workplace GeneralPatientVisitor overview
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def workplace_violence_overview(request):
    try:
        incidents = WorkPlaceViolence.objects.all()
        incidents_count = incidents.count()
        type_1_incidents = incidents.filter(incident_type="Type 1")
        type_1_incident = type_1_incidents.first()
        type_1_incidents_counts = type_1_incidents.count()
        type_1_incidents_percentage = (
            (type_1_incidents_counts / incidents_count) * 100 if incidents_count else 0
        )

        type_2_incidents = incidents.filter(incident_type="Type 2")
        type_2_incident = type_2_incidents.first()
        type_2_incidents_counts = type_2_incidents.count()
        type_2_incidents_percentage = (
            (type_2_incidents_counts / incidents_count) * 100 if incidents_count else 0
        )

        type_3_incidents = incidents.filter(incident_type="Type 3")
        type_3_incident = type_3_incidents.first()
        type_3_incidents_counts = type_3_incidents.count()
        type_3_incidents_percentage = (
            (type_3_incidents_counts / incidents_count) * 100 if incidents_count else 0
        )

        type_4_incidents = incidents.filter(incident_type="Type 4")
        type_4_incident = type_4_incidents.first()
        type_4_incidents_counts = type_4_incidents.count()
        type_4_incidents_percentage = (
            (type_4_incidents_counts / incidents_count) * 100 if incidents_count else 0
        )

        type_5_incidents = incidents.filter(incident_type="Type 5")
        type_5_incident = type_5_incidents.first()
        type_5_incidents_counts = type_5_incidents.count()
        type_5_incidents_percentage = (
            (type_5_incidents_counts / incidents_count) * 100 if incidents_count else 0
        )

        data = [
            {
                "type": "Type 1",
                "percentage": round(type_1_incidents_percentage, 0),
                "incident_date": (
                    type_1_incident.date_of_incident if type_1_incident else None
                ),
                "facility": (
                    type_1_incident.report_facility.name
                    if type_1_incident and type_1_incident.report_facility
                    else None
                ),
                "department": (
                    type_1_incident.department.name
                    if type_1_incident and type_1_incident.department
                    else None
                ),
            },
            {
                "type": "Type 2",
                "percentage": round(type_2_incidents_percentage, 0),
                "incident_date": (
                    type_2_incident.date_of_incident if type_2_incident else None
                ),
                "facility": (
                    type_2_incident.report_facility.name
                    if type_2_incident and type_2_incident.report_facility
                    else None
                ),
                "department": (
                    type_2_incident.department.name
                    if type_2_incident and type_2_incident.department
                    else None
                ),
            },
            {
                "type": "Type 3",
                "percentage": round(type_3_incidents_percentage, 0),
                "incident_date": (
                    type_3_incident.date_of_incident if type_3_incident else None
                ),
                "facility": (
                    type_3_incident.report_facility.name
                    if type_3_incident and type_3_incident.report_facility
                    else None
                ),
                "department": (
                    type_3_incident.department.name
                    if type_3_incident and type_3_incident.department
                    else None
                ),
            },
            {
                "type": "Type 4",
                "percentage": round(type_4_incidents_percentage, 0),
                "incident_date": (
                    type_4_incident.date_of_incident if type_4_incident else None
                ),
                "facility": (
                    type_4_incident.report_facility.name
                    if type_4_incident and type_4_incident.report_facility
                    else None
                ),
                "department": (
                    type_4_incident.department.name
                    if type_4_incident and type_4_incident.department
                    else None
                ),
            },
            {
                "type": "Type 5",
                "percentage": round(type_5_incidents_percentage, 0),
                "incident_date": (
                    type_5_incident.date_of_incident if type_5_incident else None
                ),
                "facility": (
                    type_5_incident.report_facility.name
                    if type_5_incident and type_5_incident.report_facility
                    else None
                ),
                "department": (
                    type_5_incident.department.name
                    if type_5_incident and type_5_incident.department
                    else None
                ),
            },
        ]

        return Response(
            {"total_incidents": incidents_count, "data": data},
            status=status.HTTP_200_OK,
        )

    except Exception as e:

        logging_service.log_error(e)
        return Response(
            {"error": f"an error occurred while fetching"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# complaints overview
@extend_schema(request=Complaint, responses=Complaint)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def complaints_overview(request):
    try:
        complaints = Complaint.objects.all()

        data = [
            {
                "id": complaint.id,
                "patient_name": complaint.patient_name,
                "medical_record_number": complaint.medical_record_number,
                "date_of_complaint": complaint.date_of_complaint,
                "resolved_by_staff": complaint.resolved_by_staff,
                "facility": (
                    complaint.complain_facility.name
                    if complaint.complain_facility
                    else None
                ),
                "department": (
                    complaint.department.name if complaint.department else None
                ),
            }
            for complaint in complaints
        ]

        return Response({"data": data}, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# Staff health investigation GeneralPatientVisitor overview
@extend_schema(request=StaffIncidentInvestigation, responses=StaffIncidentInvestigation)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def employee_health_investigation_overview(request):
    year = request.query_params.get("year", datetime.now().year)

    try:
        year = int(year)
        incidents = (
            StaffIncidentInvestigation.objects.filter(incident_date__year=year)
            .annotate(month=TruncMonth("incident_date"))
            .values(
                "id",
                "month",
                "report_facility__name",
                "department__name",
                "incident_date",
            )
            .annotate(total_incidents=Count("id"))
            .order_by("month")
        )

        # storing incidents by months
        incidents_by_month = defaultdict(lambda: {"total_incidents": 0})

        for GeneralPatientVisitor in incidents:
            month_name_str = GeneralPatientVisitor["month"].strftime("%B")
            incidents_by_month[month_name_str] = {
                "id": GeneralPatientVisitor["id"],
                "month": month_name_str,
                "total_incidents": GeneralPatientVisitor["total_incidents"],
                "facility": GeneralPatientVisitor["report_facility__name"],
                "department": GeneralPatientVisitor["department__name"],
                "incident_date": GeneralPatientVisitor["incident_date"].strftime(
                    "%Y-%m-%d"
                ),
            }

        data = []

        for month_idx in range(1, 13):
            month_name_str = month_name[month_idx]

            if month_name_str not in incidents_by_month:
                incidents_by_month[month_name_str] = {
                    "id": None,
                    "month": month_name_str,
                    "total_incidents": 0,
                    "facility": None,
                    "department": None,
                    "incident_date": None,
                }

            data.append(incidents_by_month[month_name_str])

        return Response({"data": data}, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# grievance investigation GeneralPatientVisitor overview
@extend_schema(request=GrievanceInvestigation, responses=GrievanceInvestigation)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def grievance_investigation_overview(request):
    year = request.query_params.get("year", datetime.now().year)

    try:
        year = int(year)
        incidents = (
            GrievanceInvestigation.objects.filter(start_date__year=year)
            .annotate(month=TruncMonth("start_date"))
            .values(
                "id", "month", "report_facility__name", "department__name", "start_date"
            )
            .annotate(total_incidents=Count("id"))
            .order_by("month")
        )

        # storing incidents by months
        incidents_by_month = defaultdict(lambda: {"total_incidents": 0})

        for GeneralPatientVisitor in incidents:
            month_name_str = GeneralPatientVisitor["month"].strftime("%B")
            incidents_by_month[month_name_str] = {
                "id": GeneralPatientVisitor["id"],
                "month": month_name_str,
                "total_incidents": GeneralPatientVisitor["total_incidents"],
                "facility": GeneralPatientVisitor["report_facility__name"],
                "department": GeneralPatientVisitor["department__name"],
                "incident_date": GeneralPatientVisitor["start_date"].strftime(
                    "%Y-%m-%d"
                ),
            }

        data = []

        for month_idx in range(1, 13):
            month_name_str = month_name[month_idx]

            if month_name_str not in incidents_by_month:
                incidents_by_month[month_name_str] = {
                    "id": None,
                    "month": month_name_str,
                    "total_incidents": 0,
                    "facility": None,
                    "department": None,
                    "incident_date": None,
                }

            data.append(incidents_by_month[month_name_str])

        return Response({"data": data}, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# lost and found overview
@extend_schema(request=LostAndFound, responses=LostAndFound)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def lost_and_found_overview(request):
    try:
        incidents = LostAndFound.objects.all()
        incidents_count = incidents.count()

        is_found = incidents.filter(is_found=True)
        is_found_incident = is_found.first()
        is_found_count = is_found.count()
        is_found_percentage = (
            (is_found_count / incidents_count) * 100 if incidents_count > 0 else 0
        )

        is_not_found = incidents.filter(is_found=False)
        is_not_found_incident = is_not_found.first()
        is_not_found_count = is_not_found.count()
        is_not_found_percentage = (
            (is_not_found_count / incidents_count) * 100 if incidents_count > 0 else 0
        )

        data = (
            (
                [
                    {
                        "type": "is found",
                        "id": is_found_incident.id,
                        "percentage": round(is_found_percentage, 2),
                        "incident_date": is_found_incident.date_reported,
                        "facility": (
                            is_found_incident.report_facility.name
                            if is_found_incident and is_found_incident.report_facility
                            else None
                        ),
                        "department": (
                            is_found_incident.department.name
                            if is_found_incident and is_found_incident.department
                            else None
                        ),
                    },
                    {
                        "type": "is not found",
                        "id": is_not_found_incident.id,
                        "percentage": round(is_not_found_percentage, 2),
                        "incident_date": is_not_found_incident.date_reported,
                        "facility": (
                            is_not_found_incident.report_facility.name
                            if is_not_found_incident
                            and is_not_found_incident.report_facility
                            else None
                        ),
                        "department": (
                            is_not_found_incident.department.name
                            if is_not_found_incident
                            and is_not_found_incident.department
                            else None
                        ),
                    },
                ]
                if is_found_incident
                else []
            ),
        )

        return Response(
            {"total_incidents": incidents_count, "data": data},
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# medication error
@extend_schema(request=MedicationError, responses=MedicationError)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def medication_error_incident_overview(request):
    try:
        incidents = MedicationError.objects.all()
        incidents_count = incidents.count()

        transcribing_incidents = incidents.filter(description_of_error="TRANSCRIBING")
        transcribing_incident = transcribing_incidents.first()
        transcribing_incidents_count = transcribing_incidents.count()
        transcribing_incidents_percentage = (
            (transcribing_incidents_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        prescribing_incidents = incidents.filter(description_of_error="PRESCRIBING")
        prescribing_incident = prescribing_incidents.first()
        prescribing_incidents_count = prescribing_incidents.count()
        prescribing_incidents_percentage = (
            (prescribing_incidents_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        dispensing_incidents = incidents.filter(description_of_error="DISPENSING")
        dispensing_incident = dispensing_incidents.first()
        dispensing_incidents_count = dispensing_incidents.count()
        dispensing_incidents_percentage = (
            (dispensing_incidents_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        administering_incidents = incidents.filter(description_of_error="ADMINISTERING")
        administering_incident = administering_incidents.first()
        administering_incidents_count = administering_incidents.count()
        administering_incidents_percentage = (
            (administering_incidents_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        procurement_storage_incidents = incidents.filter(
            description_of_error="PROCUREMENT & STORAGE"
        )
        procurement_storage_incident = procurement_storage_incidents.first()
        procurement_storage_incidents_count = procurement_storage_incidents.count()
        procurement_storage_incidents_percentage = (
            (procurement_storage_incidents_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        monitoring_incidents = incidents.filter(description_of_error="MONITORING")
        monitoring_incident = monitoring_incidents.first()
        monitoring_incidents_count = monitoring_incidents.count()
        monitoring_incidents_percentage = (
            (monitoring_incidents_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        data = [
            {
                "description error": "transcribing",
                "id": transcribing_incident.id if transcribing_incident else None,
                "percentage": round(transcribing_incidents_percentage, 2),
                "incident_date": (
                    transcribing_incident.date_of_error
                    if transcribing_incident
                    else None
                ),
                "facility": (
                    transcribing_incident.report_facility.name
                    if transcribing_incident and transcribing_incident.report_facility
                    else None
                ),
                "department": (
                    transcribing_incident.department.name
                    if transcribing_incident and transcribing_incident.department
                    else None
                ),
            },
            {
                "description error": "prescribing",
                "id": prescribing_incident.id if prescribing_incident else None,
                "percentage": round(prescribing_incidents_percentage, 2),
                "incident_date": (
                    prescribing_incident.date_of_error
                    if prescribing_incident and prescribing_incident.date_of_error
                    else None
                ),
                "facility": (
                    prescribing_incident.report_facility.name
                    if prescribing_incident and prescribing_incident.report_facility
                    else None
                ),
                "department": (
                    prescribing_incident.department.name
                    if prescribing_incident and prescribing_incident.department
                    else None
                ),
            },
            {
                "description error": "dispensing",
                "id": dispensing_incident.id if dispensing_incident else None,
                "percentage": round(dispensing_incidents_percentage, 2),
                "incident_date": (
                    dispensing_incident.date_of_error
                    if dispensing_incident and dispensing_incident.date_of_error
                    else None
                ),
                "facility": (
                    dispensing_incident.report_facility.name
                    if dispensing_incident and dispensing_incident.report_facility
                    else None
                ),
                "department": (
                    dispensing_incident.department.name
                    if dispensing_incident and dispensing_incident.department
                    else None
                ),
            },
            {
                "description error": "administering",
                "id": administering_incident.id if administering_incident else None,
                "percentage": round(administering_incidents_percentage, 2),
                "incident_date": (
                    administering_incident.date_of_error
                    if administering_incident and administering_incident.date_of_error
                    else None
                ),
                "facility": (
                    administering_incident.report_facility.name
                    if administering_incident and administering_incident.report_facility
                    else None
                ),
                "department": (
                    administering_incident.department.name
                    if administering_incident and administering_incident.department
                    else None
                ),
            },
            {
                "description error": "procurement & storage",
                "id": (
                    procurement_storage_incident.id
                    if procurement_storage_incident
                    else None
                ),
                "percentage": round(procurement_storage_incidents_percentage, 2),
                "incident_date": (
                    procurement_storage_incident.date_of_error
                    if procurement_storage_incident
                    and procurement_storage_incident.date_of_error
                    else None
                ),
                "facility": (
                    procurement_storage_incident.report_facility.name
                    if procurement_storage_incident
                    and procurement_storage_incident.report_facility
                    else None
                ),
                "department": (
                    procurement_storage_incident.department.name
                    if procurement_storage_incident
                    and procurement_storage_incident.department
                    else None
                ),
            },
            {
                "description error": "Monitoring",
                "id": monitoring_incident.id if monitoring_incident else None,
                "percentage": round(monitoring_incidents_percentage, 2),
                "incident_date": (
                    monitoring_incident.date_of_error
                    if monitoring_incident and monitoring_incident.date_of_error
                    else None
                ),
                "facility": (
                    monitoring_incident.report_facility.name
                    if monitoring_incident and monitoring_incident.report_facility
                    else None
                ),
                "department": (
                    monitoring_incident.department.name
                    if monitoring_incident and monitoring_incident.department
                    else None
                ),
            },
        ]

        return Response(
            {"total_incidents": incidents_count, "data": data},
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# Staff GeneralPatientVisitor overview
@extend_schema(request=StaffIncidentReport, responses=StaffIncidentReport)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def employee_incident_overview(request):
    try:
        incidents = StaffIncidentReport.objects.all()
        incidents_count = incidents.count()

        injury_incidents = incidents.filter(incident_status="Injury")
        injury_incident = injury_incidents.first()
        injury_incidents_count = injury_incidents.count()
        injury_incidents_percentage = (
            (injury_incidents_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        illness_incidents = incidents.filter(incident_status="Illness")
        illness_incident = illness_incidents.first()
        illness_incidents_count = illness_incidents.count()
        illness_incidents_percentage = (
            (illness_incidents_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        near_miss_incidents = incidents.filter(incident_status="Near miss")
        near_miss_incident = near_miss_incidents.first()
        near_miss_incidents_count = near_miss_incidents.count()
        near_miss_incidents_percentage = (
            (near_miss_incidents_count / incidents_count) * 100
            if incidents_count > 0
            else 0
        )

        data = [
            {
                "status": "Injury",
                "percentage": round(injury_incidents_percentage, 2),
                "id": injury_incident.id if injury_incident else None,
                "incident_date": (
                    injury_incident.incident_date
                    if injury_incident and injury_incident.incident_date
                    else None
                ),
                "facility": (
                    injury_incident.report_facility.name
                    if injury_incident and injury_incident.report_facility
                    else None
                ),
                "department": (
                    injury_incident.department.name
                    if injury_incident and injury_incident.department
                    else None
                ),
            },
            {
                "status": "Illness",
                "percentage": round(illness_incidents_percentage, 2),
                "id": illness_incident.id if illness_incident else None,
                "incident_date": (
                    illness_incident.incident_date
                    if illness_incident and illness_incident.incident_date
                    else None
                ),
                "facility": (
                    illness_incident.report_facility.name
                    if illness_incident and illness_incident.report_facility
                    else None
                ),
                "department": (
                    illness_incident.department.name
                    if illness_incident and illness_incident.department
                    else None
                ),
            },
            {
                "status": "Near miss",
                "percentage": round(near_miss_incidents_percentage, 2),
                "id": near_miss_incident.id if near_miss_incident else None,
                "incident_date": (
                    near_miss_incident.incident_date
                    if near_miss_incident and near_miss_incident.incident_date
                    else None
                ),
                "facility": (
                    near_miss_incident.report_facility.name
                    if near_miss_incident and near_miss_incident.report_facility
                    else None
                ),
                "department": (
                    near_miss_incident.department.name
                    if near_miss_incident and near_miss_incident.department
                    else None
                ),
            },
        ]

        return Response(
            {"total incidents": incidents_count, "data": data},
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# grievance GeneralPatientVisitor overview
@extend_schema(request=StaffIncidentReport, responses=StaffIncidentReport)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def grievance_incident_overview(request):
    year = request.query_params.get("year", datetime.now().year)

    try:
        year = int(year)
        incidents = (
            Grievance.objects.filter(notification_date__year=year)
            .annotate(month=TruncMonth("notification_date"))
            .values(
                "id",
                "month",
                "report_facility__name",
                "department__name",
                "notification_date",
            )
            .annotate(total_incidents=Count("id"))
        )

        incidents_by_month = defaultdict(lambda: {"total_incidents": 0})

        for GeneralPatientVisitor in incidents:
            month_name_str = GeneralPatientVisitor["month"].strftime("%B")
            incidents_by_month[month_name_str] = {
                "id": GeneralPatientVisitor["id"],
                "month": month_name_str,
                "total_incidents": GeneralPatientVisitor["total_incidents"],
                "facility": GeneralPatientVisitor["report_facility__name"],
                "department": GeneralPatientVisitor["department__name"],
                "incident_date": GeneralPatientVisitor["notification_date"].strftime(
                    "%Y-%m-%d"
                ),
            }

        data = []

        for month_idx in range(1, 13):
            month_name_str = month_name[month_idx]

            if month_name_str not in incidents_by_month:
                incidents_by_month[month_name_str] = {
                    "id": None,
                    "month": month_name_str,
                    "total_incidents": 0,
                    "facility": None,
                    "department": None,
                    "incident_date": None,
                }
            data.append(incidents_by_month[month_name_str])

        return Response({"data": data}, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
