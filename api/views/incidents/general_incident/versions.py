from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated


from general_patient_visitor.models import (
    GeneralPatientVisitor,
    GeneralPatientVisitorVersion,
)
from general_patient_visitor.serializers import (
    GeneralPatientVisitorVersionSerializer,
    IncidentListSerializer,
)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def general_incident_version(request, incident_id, version_id):
    try:
        incident = GeneralPatientVisitorVersion.objects.get(id=version_id)
        serializer = IncidentListSerializer(incident).data
        return Response(serializer, status=status.HTTP_200_OK)
    except GeneralPatientVisitorVersion.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def general_incident_original_version(request, incident_id):
    try:
        incident = GeneralPatientVisitor.objects.get(id=incident_id)
        serializer = IncidentListSerializer(incident).data
        return Response(serializer, status=status.HTTP_200_OK)
    except GeneralPatientVisitor.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_400_BAD_REQUEST
        )
