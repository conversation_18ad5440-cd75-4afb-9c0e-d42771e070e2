from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.services.forms import check_user_facility
from general_patient_visitor.models import GeneralPatientVisitor
from general_patient_visitor.serializers import IncidentListSerializer
from general_patient_visitor.services.actions import GPVActionsService
from general_patient_visitor.services.documents import GPVDocumentService
from general_patient_visitor.services.get_incident import GeneralPatientVisitorService
from general_patient_visitor.services.operations import GPVService
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService
from reviews.services.review import ReviewsOperations


logging_service = LoggingService()
incidents_service = GetIncidentsService()
general_incident = GeneralPatientVisitorService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def general_incidents_api(request):
    gpv_service = GPVService(request.user)
    try:
        if request.method == "GET":
            facility_id = request.query_params.get("facility_id")
            department_id = request.query_params.get("department_id")

            filters = request.query_params.dict()
            incidents = gpv_service.get_incidents_list(filters=filters)

            if not incidents.success:
                return Response(
                    {"error": incidents.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incidents.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            facility = check_user_facility(request.data, request.user)

            if not facility.success:
                return Response(
                    {"error": facility.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            request.data["report_facility"] = facility.data

            response = gpv_service.create_incident(request.data, request.user)
            if response.success:
                return Response(response.data, status=status.HTTP_201_CREATED)
            else:
                return Response(response.message, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error getting incidents"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "PATCH"])
@permission_classes([IsAuthenticated])
def general_incident_details_api(request, id):
    gpv_service = GPVService(request.user)
    action_services = GPVActionsService(
        user=request.user,
        incident_id=id,
        data=request.data,
    )
    try:
        if request.method == "GET":
            incident = gpv_service.get_incident_by_id(incident_id=id)
            if not incident.success:
                return Response(
                    {"error": incident.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incident.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PUT":
            response = gpv_service.update_incident(
                incident_id=id,
                data=request.data,
                user=request.user,
            )

            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PATCH":
            action = request.data.pop("action", None)
            if not action:
                return Response(
                    {
                        "error": "Action is required",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if action == "send-for-review":
                response = action_services.send_for_review()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "modify":
                response = action_services.modify_incident()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "mark-closed":
                response = action_services.mark_closed()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
        elif action == "delete-draft":
            response = action_services.delete_gpv_draft_incidents()
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )

        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error getting incidents"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def general_incident_document_api(request, incident_id):
    """
    API endpoint to handle documents related to general patient visitor incidents.
    """
    try:
        service = GPVDocumentService(incident_id=incident_id, user=request.user)
        if request.method == "GET":
            params = request.query_params.dict()
            response = service.get_documents(params=params)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=response.code,
            )
        elif request.method == "POST":
            files = request.FILES.getlist("files")
            response = service.create_document(files)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=response.code,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error handling incident documents"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def general_incident_reviews_api(request, incident_id):

    service = ReviewsOperations(
        user=request.user,
        model_name="GeneralPatientVisitor",
        app_label="general_patient_visitor",
        incident_id=incident_id,
    )
    if request.method == "GET":
        try:
            response = service.get_incident_reviews()
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Error getting incident reviews"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    elif request.method == "POST":
        content = request.data.get("content")
        response = service.create_review(content)
        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            response.data,
            status=status.HTTP_201_CREATED,
        )

    else:
        return Response(
            {"error": "Method not allowed"},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )
