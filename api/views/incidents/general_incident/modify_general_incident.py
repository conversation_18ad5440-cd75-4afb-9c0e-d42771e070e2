from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import J<PERSON>NParser
from api.views.incidents.general_incident.new_incident import get_patient_profile
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from general_patient_visitor.models import GeneralPatientVisitor
from general_patient_visitor.serializers import (
    GeneralPatientVisitorVersionSerializer,
    IncidentUpdateSerializer,
)
from drf_spectacular.utils import extend_schema
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from general_patient_visitor.services.get_incident import GeneralPatientVisitorService
from incidents.views.send_to_department import send_incident_submission_email

logging_service = LoggingService()
patient_visitor_version = GeneralPatientVisitorService()


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def modify_general_incident(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    try:
        response = patient_visitor_version.create_version(
            incident_id=incident_id, incident_data=request.data, user=user
        )
        if not response.success:
            return Response(
                {
                    "status": "failed",
                    "message": response.message,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            response.data,
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        logging_service.log_error(e)

        return Response(
            {
                "status": "failed",
                "message": "An error occurred while processing the request",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    request_data = request.data.copy()
    report_facility = None
    try:
        incident = GeneralPatientVisitor.objects.get(id=incident_id)
        report_facility = incident.report_facility
        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_manager_user(user, incident.department)
        ) and not incident.created_by == user:
            return Response(
                {
                    "status": "failed",
                    "message": "You do not have permission to update the incident",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        facility = incident.report_facility
        if "patient_visitor" in request.data:
            patient_visitor_profile, message = get_patient_profile(
                data=request.data["patient_visitor"], facility=facility
            )
            if patient_visitor_profile:
                request_data["patient_visitor"] = patient_visitor_profile.id

        if "physician_notified" in request.data:
            physician_notified_profile, message = get_patient_profile(
                data=request.data["physician_notified"], facility=facility
            )
            if physician_notified_profile:

                request_data["physician_notified"] = physician_notified_profile.id

        if "family_notified" in request.data:
            family_notified_profile, message = get_patient_profile(
                data=request.data["family_notified"], facility=facility
            )
            if family_notified_profile:
                request_data["family_notified"] = family_notified_profile.id

        if "notified_by" in request.data:
            notified_by_profile, message = get_patient_profile(
                data=request.data["notified_by"], facility=facility
            )
            if notified_by_profile:
                request_data["notified_by"] = notified_by_profile.id
        request_data["original_report"] = incident.id
        request_data["report_facility"] = report_facility.id
        request_data["created_by"] = user.id
        request_data["status"] = request.data.get("status", "Draft")

        features = {"incident versioning": True}
        version_serializer = GeneralPatientVisitorVersionSerializer(data=request_data)

        if version_serializer.is_valid():

            version_serializer.save()
            incident.is_modified = True
            incident.updated_by = user
            incident.status = request.data.get("status", "Draft")
            incident.save()
            if "status" in request.data and request.data.get("status") == "Open":
                incident.status = "Open"
            incident.save()
            if "status" in request.data and request.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=incident, incident_type="General Patient/Visitor"
                )
            return Response(
                {
                    "status": "success",
                    "message": "Incident was successfully updated",
                    "incident": version_serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "failed",
                    "error": f"failed to update incident {version_serializer.errors}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except GeneralPatientVisitor.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
