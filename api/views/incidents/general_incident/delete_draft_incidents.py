from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import has_permissions
from base.services.auth import verify_user
from general_patient_visitor.models import GeneralPatientVisitor
from incidents.views.delete_drafts import delete_drafts


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_general_draft_incidents(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    incident_ids = request.data.get("incident_ids", None)
    user_id = request.data.get("user_id", None)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    # admin can delete a user's drafts
    # users can delete their own draft

    return delete_drafts(
        user=user,
        incident_model=GeneralPatientVisitor,
        user_id=user_id,
        incident_ids=incident_ids,
    )
