# views.py

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from base.services.auth import verify_user
from general_patient_visitor.models import GeneralPatientVisitor

from general_patient_visitor.serializers import (
    IncidentListSerializer,
    IncidentSerializer,
)
from drf_spectacular.utils import extend_schema

from general_patient_visitor.services.get_incident import GeneralPatientVisitorService


incident_service = GeneralPatientVisitorService()


@extend_schema(request=IncidentSerializer, responses=IncidentSerializer)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([J<PERSON>NParser])
def general_incident_detail(request, incident_id):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        try:
            incident_response = incident_service.get_incident_by_id(user, incident_id)
            if not incident_response.success:
                return Response(
                    {"message": incident_response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incident_response.data,
                status=status.HTTP_200_OK,
            )
        except Exception:
            return Response(
                {"message": "Err getting incident"},
                status=status.HTTP_400_BAD_REQUEST,
            )
