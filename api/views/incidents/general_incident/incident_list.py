# views.py

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import has_permissions
from base.services.auth import (
    check_departments_access,
    check_facilities_access,
    verify_user,
)
from base.services.forms import check_missing_fields
from base.models import Facility
from base.services.incidents.get_incidents import GetIncidentsService
from general_patient_visitor.models import GeneralPatientVisitor
from general_patient_visitor.serializers import (
    IncidentListSerializer,
    IncidentSerializer,
)


incident_service = GetIncidentsService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_incidents(request):
    facility_id = request.query_params.get("facility_id")
    department_id = request.query_params.get("department_id")
    incidents = incident_service.get_incidents(
        app_label="general_patient_visitor",
        model_name="GeneralPatientVisitor",
        user=request.user,
        facility_id=facility_id,
        department_id=department_id,
    )
    incidents_found, versions, message = get_latest_incident_reports(
        incidents.data, IncidentListSerializer
    )
    if incidents_found:
        return Response({"incidents": versions}, status=status.HTTP_200_OK)
    else:
        return Response({"message": message}, status=status.HTTP_400_BAD_REQUEST)


def get_limited_access_incident(user, incidents):
    facilities_ids, response, has_access = check_facilities_access(user)
    department_ids, response, has_department_access = check_departments_access(user)

    if not has_access and has_department_access:
        return [], response, False

    incidents_data = incidents.order_by("-created_at")

    incidents_list = [
        incident
        for incident in incidents_data
        if incident.report_facility and incident.report_facility.id in facilities_ids
    ]

    incidents_in_department = [
        incident
        for incident in incidents_list
        if incident.department and incident.department.id in department_ids
    ]

    return incidents_in_department, response, True


def get_latest_incident_reports(incidents, serializer):
    incidents_list = incidents
    versions = []
    try:
        for item in incidents_list:
            version = item.versions.last()
            if version:
                serialized_version = serializer(version).data
                serialized_version["original_report"] = item.id
                serialized_version["status"] = item.status
                versions.append(serialized_version)
            else:
                versions.append(serializer(item).data)
        return True, versions, "message"
    except Exception as e:
        return False, [], "Internal server error"
