# views.py
from datetime import datetime
from django.core.exceptions import ValidationError
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import (
    check_anonymous,
    get_patient_profile,
)
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.incidents.base import IncidentService
from base.services.logging.logger import LoggingService
from general_patient_visitor.services.get_incident import GeneralPatientVisitorService
from incidents.emails.incident_is_submited import send_incident_submission_confirmation
from incidents.emails.incident_notification import send_incident_notification
from incidents.emails.send_to_department import send_to_department_email
from base.models import Department
from general_patient_visitor.models import GeneralPatientVisitor
from general_patient_visitor.serializers import (
    IncidentListSerializer,
    IncidentSerializer,
)
from drf_spectacular.utils import extend_schema
from base.services.forms import check_missing_fields
import os
from dotenv import load_dotenv

from incidents.views.send_to_department import (
    send_incident_submission_email,
)

load_dotenv()

logging_service = LoggingService()
patient_visitor_service = GeneralPatientVisitorService()
incident_service = IncidentService()


@extend_schema(request=IncidentSerializer, responses=IncidentSerializer)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def update_incident_api(request, incident_id):
    if request.method == "PATCH":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        # getting data from the request
        data = request.data

        # checking missing fields
        required_fields = []

        check_missing_fields_response = check_missing_fields(
            required_fields=required_fields, data=data
        )
        if check_missing_fields_response:
            return check_missing_fields_response

        try:
            incident = GeneralPatientVisitor.objects.get(id=incident_id)

            data = check_anonymous(data, user)
            incident_response = patient_visitor_service.update_incident(
                incident.id, data, user
            )
            if not incident_response.success:
                return Response(
                    {"error": incident_response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incident_response.data,
                status=status.HTTP_200_OK,
            )
        except GeneralPatientVisitor.DoesNotExist:
            return Response(
                {"error": "Incident not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Error updating the incident. Please try again later"},
                status=status.HTTP_400_BAD_REQUEST,
            )


@extend_schema(request=IncidentSerializer, responses=IncidentSerializer)
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def send_general_incident_to_department(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    try:
        incident = GeneralPatientVisitor.objects.get(id=incident_id)

        if not is_super_user(user) and not is_admin_user(
            user, incident.report_facility
        ):
            return Response(
                {"error": "You do not have permission to access this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
        incident_type = "General Patient/Visitor"
        response = incident_service.send_incident_to_department(
            data, incident, incident_type, user
        )

        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": message,
                "incident": IncidentSerializer(incident).data,
            }
        )

    except GeneralPatientVisitor.DoesNotExist:
        return Response(
            {"error": "Incident not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error sending incident to department"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@extend_schema(request=IncidentSerializer, responses=IncidentSerializer)
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def mark_general_incident_resolved(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    data["status"] = "Closed"

    required_fields = ["status"]

    check_missing_fields_response = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_response:
        return check_missing_fields_response

    try:
        incident = GeneralPatientVisitor.objects.get(id=incident_id)
        response = incident_service.mark_as_resolved(
            incident,
            user,
        )
        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": "Incident marked as resolved successfully",
                "incident": {
                    "id": incident.id,
                    "is_resolved": incident.is_resolved,
                },
            }
        )
    except GeneralPatientVisitor.DoesNotExist:
        return Response(
            {"error": "Incident not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except ValidationError as e:
        logging_service.log_error(e)
        return Response(
            {
                "message": "Validation error",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {
                "message": "Error marking incident as resolved",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
