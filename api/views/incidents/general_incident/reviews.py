from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import has_permissions
from api.views.new_review import create_review
from base.services.auth import verify_user
from general_patient_visitor.models import GeneralPatientVisitor
from drf_spectacular.utils import extend_schema
from reviews.models import Review

from reviews.serializers import ReviewsSerializer


@extend_schema(request=ReviewsSerializer, responses=ReviewsSerializer)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_general_incident_review(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)
    if not has_permissions(["Super User", "Admin", "Manager"]):
        return Response(
            {"message": "You do not have the required permissions"},
            status=status.HTTP_403_FORBIDDEN,
        )
    if request.data.get("content") is None:
        return Response(
            {"message": "Content for the review is required"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    try:
        incident = GeneralPatientVisitor.objects.filter(pk=incident_id).first()
        new_review, created = Review.objects.get_or_create(
            created_by=user,
            content=request.data.get("content"),
        )
        review_added = False

        if created:
            review_added = True
            incident.reviews.add(new_review.id)
            incident.save()

        reviews = incident.reviews.all()
        reviews_serializer = ReviewsSerializer(reviews, many=True)
        return Response(
            {"review_added": review_added, "reviews": reviews_serializer.data},
            status=status.HTTP_201_CREATED,
        )

    except GeneralPatientVisitor.DoesNotExist:
        return Response(
            {"message": "Incident is not found"}, status=status.HTTP_404_NOT_FOUND
        )


@extend_schema(request=ReviewsSerializer, responses=ReviewsSerializer)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_general_incident_review_test(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)
    if request.data.get("content") is None:
        return Response(
            {"message": "Content for the review is required"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    response = create_review(
        "general_patient_visitor",
        "GeneralPatientVisitor",
        incident_id,
        user,
        request.data.get("content"),
    )

    return response


@extend_schema(request=ReviewsSerializer, responses=ReviewsSerializer)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def general_incident_reviews(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)
    try:
        incident = GeneralPatientVisitor.objects.get(pk=incident_id)
        reviews = incident.reviews.all().order_by("-created_at")
        reviews_serializer = ReviewsSerializer(reviews, many=True)
        return Response(reviews_serializer.data, status=status.HTTP_200_OK)
    except GeneralPatientVisitor.DoesNotExist:
        return Response(
            {"message": "Incident is not found"}, status=status.HTTP_404_NOT_FOUND
        )
