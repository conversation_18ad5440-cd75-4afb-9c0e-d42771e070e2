# views.py
from django.contrib.auth.models import User
import re
from datetime import datetime
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.models import Profile
from api.views.auth.permissions_list import cleaned_data
from base.services.auth import verify_user
from base.services.format_date import convert_created_at_to_user_timezone
from base.services.forms import check_missing_fields, check_user_facility
from base.services.notifications import save_notification

from general_patient_visitor.serializers import IncidentSerializer
from drf_spectacular.utils import extend_schema

from general_patient_visitor.services.get_incident import GeneralPatientVisitorService


incidents_service = GeneralPatientVisitorService()


@extend_schema(request=IncidentSerializer, responses=IncidentSerializer)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_incident(request):  # users can select a facility while submitting an incident

    if request.method == "POST":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        facility, has_facility = check_user_facility(request.data, user)
        if has_facility:
            request.data["report_facility"] = facility
        else:
            return facility

        response = incidents_service.create_incident(request.data, user)
        if response.success:
            return Response(response.data, status=status.HTTP_201_CREATED)
        else:
            return Response(response.message, status=status.HTTP_400_BAD_REQUEST)


def get_patient_profile(data, facility):
    user_profile = None
    user = None
    profile_info = {}

    # If 'patient_visitor' in data: first name and last name should be from the object if patient_visitor.
    # That applies to the rest of the fields where we are relating to the profile

    # for patient_visitor in Patient Visitor Model
    if data:
        user_data = data.get("user_data")
        user, response, created = create_new_user(user_data)
        if created:
            profile_data = data.get("profile_data")

            user_profile, response, created = update_or_profile(
                user=user, profile_data=profile_data
            )

            if user_profile:
                return user_profile, response
            else:
                return None, response
        else:
            return None, response
    else:
        return None, "No data provided"


def create_new_user(user_data):
    if user_data is None:
        return (
            None,
            Response(
                {"error": "User data is required at least user_id"},
                status=status.HTTP_400_BAD_REQUEST,
            ),
            False,
        )
    user_id = user_data.get("user_id")
    first_name = user_data.get("first_name")
    last_name = user_data.get("last_name")
    email = user_data.get("email")

    # If user_id is provided, update the user object
    if user_id:
        try:
            user = User.objects.get(id=user_id)
            # If user is found, update their details

            if first_name:
                user.first_name = first_name
            if last_name:
                user.last_name = last_name
            if email:
                user.email = email
            user.save()
            return user, "User account updated", True
        except User.DoesNotExist:
            return (
                None,
                "User not found",
                False,
            )

    # Create a new user object if no user_id is provided
    else:
        if not all([first_name, last_name]):
            return (
                None,
                "First name and last name are required",
                False,
            )

        try:
            # Check if the user with the email already exists
            if email and User.objects.filter(email=email).exists():
                user = User.objects.get(email=email)
                return (
                    user,
                    "user already exists",
                    False,
                )

            # Create the new user
            username = f"{first_name}_{last_name}"
            user = User.objects.create_user(
                username=generate_username(username),
                first_name=first_name,
                last_name=last_name,
                email=email,
            )

            return user, "New user created", True

        except Exception as e:
            return (
                None,
                "Internal server error",
                False,
            )


def update_or_profile(user, profile_data):
    try:
        user_profile = Profile.objects.get(user=user)
        for key, value in profile_data.items():
            setattr(user_profile, key, value)
        user_profile.save()

        return user_profile, "profile_updated", True
    except Profile.DoesNotExist:
        user_profile, created = Profile.objects.get_or_create(
            user=user, defaults=profile_data
        )
        if created:
            return user_profile, "profile_created", True
        else:
            return None, "Error creating a profile", False
    except Exception as e:
        return None, "Internal server error", False


def check_anonymous(data, user, status_field="status", anonymous_field="anonymous"):

    if data.get(status_field) == "Open" and data.get(anonymous_field) is True:
        data["created_by"] = None
        data["updated_by"] = None
    else:
        data["created_by"] = user.id
        data["updated_by"] = user.id

    return data


def generate_username(base_username):
    """
    Generate a unique username by adding or incrementing a numeric suffix.
    If 'john_doe' exists, returns 'john_doe_2', then 'john_doe_3', etc.

    Args:
        base_username: The initial username to check

    Returns:
        str: A unique username
    """
    if not User.objects.filter(username=base_username).exists():
        return base_username

    # Check if the username already ends with a number
    pattern = r"^(.+?)_(\d+)?$"
    match = re.match(pattern, base_username)

    if match:
        # If username already has a number, use the base without number
        base = match.group(1)
    else:
        base = base_username

    # Find the next available number
    count = 1
    while True:
        new_username = f"{base}_{count}"
        if not User.objects.filter(username=new_username).exists():
            return new_username
        count += 1
