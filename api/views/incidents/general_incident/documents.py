from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from documents.models import Document
from documents.views import (
    get_incident_documents,
    handle_incident_document,
    delete_incident_document,
)
import os
from general_patient_visitor.models import GeneralPatientVisitor


logging_service = LoggingService()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def general_incident_documents_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    files = request.FILES.getlist("files")
    folder_name = "general-incidents"

    if not files:
        return Response(
            {"message": "No files provided"}, status=status.HTTP_400_BAD_REQUEST
        )
    try:
        incident = GeneralPatientVisitor.objects.get(id=incident_id)
        if not is_super_user(user) and not is_admin_user(
            user, incident.report_facility
        ):
            return Response(
                {
                    "error": "You do not have permission to upload documents to this incident"
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        response_data = handle_incident_document(files, folder_name, incident, user)
        return Response(response_data, status=status.HTTP_200_OK)

    except GeneralPatientVisitor.DoesNotExist:
        return Response(
            {"message": "Incident does not exist"}, status=status.HTTP_400_BAD_REQUEST
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": f"Failed to upload file(s)"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_general_incident_document_list(request, incident_id):
    documents, found = get_incident_documents(GeneralPatientVisitor, incident_id)
    if found:
        return Response(documents, status=status.HTTP_200_OK)
    else:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_general_incident_document(request, incident_id, document_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident = GeneralPatientVisitor.objects.get(id=incident_id)

        success, message = delete_incident_document(incident, document_id)

        if success:
            return Response(
                {
                    "message": "Document deleted successfully",
                    "deleted document": document_id,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)

    except GeneralPatientVisitor.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Exception as e:
        return Response(
            {"error": f"Failed to delete document"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
