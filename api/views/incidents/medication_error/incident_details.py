from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.services.auth import verify_user
from base.services.incidents.get_incidents import GetIncidentsService
from medication_error.seriaizers import (
    MedicalErrorListSerializer,
    MedicalErrorSerializer,
)
from medication_error.models import MedicationError
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_specific_department,
    is_super_user,
)

incidents_service = GetIncidentsService()


@extend_schema(request=MedicalErrorSerializer, responses=MedicalErrorSerializer)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([J<PERSON>NParser])
def medical_error_details_api(request, medication_error_id):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        try:
            incident = MedicationError.objects.get(id=medication_error_id)
            if (
                not is_super_user(user)
                and not is_admin_user(user, incident.report_facility)
                and not is_specific_department(
                    user=user,
                    department_name="Pharmacy",
                    facility=incident.report_facility,
                )
                and not incident.created_by == user
            ):
                return Response(
                    {"error": "You do not have permission to access this incident"},
                    status=status.HTTP_403_FORBIDDEN,
                )
            serializer = MedicalErrorListSerializer(incident).data

            success, incident, modifications, message = (
                incidents_service.get_latest_version(
                    incident_data=incident,
                    modelName=MedicationError,
                    incidentSerializer=MedicalErrorListSerializer,
                    incident_id=medication_error_id,
                )
            )
            if success:
                return Response(
                    {"incident": incident, "modifications": modifications},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": message}, status=status.HTTP_400_BAD_REQUEST
                )
        except MedicationError.DoesNotExist:
            return Response(
                {"error": "This medication error does not exist"},
                status=status.HTTP_404_NOT_FOUND,
            )
