from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from medication_error.models import MedicationError, MedicationErrorVersion
from medication_error.new_serializers import GetMedicationErrorSerializer
from medication_error.seriaizers import (
    MedicalErrorListSerializer,
    MedicalErrorSerializer,
    MedicalErrorVersionSerializer,
)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def medication_error_incident_version(request, incident_id, version_id):
    try:
        incident = MedicationErrorVersion.objects.get(id=version_id)
        serializer = GetMedicationErrorSerializer(incident).data
        return Response(serializer, status=status.HTTP_200_OK)
    except MedicationErrorVersion.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def medication_error_original_version(request, incident_id):
    try:
        incident = MedicationError.objects.get(id=incident_id)
        serializer = MedicalErrorListSerializer(incident).data
        return Response(serializer, status=status.HTTP_200_OK)
    except MedicationError.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_400_BAD_REQUEST
        )
