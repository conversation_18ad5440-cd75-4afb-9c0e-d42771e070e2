from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import JSONParser
from api.views.auth.permissions_list import has_permissions
from api.views.get_reviews import get_incident_reviews
from api.views.new_review import create_review
from base.services.auth import verify_user
from medication_error.models import MedicationError
from reviews.models import Review


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_medical_error_review_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)
    if not has_permissions(user, ["Admin", "Super User", "Manager"]):
        return Response(
            {"message": "You do not have the required permissions"},
            status=status.HTTP_403_FORBIDDEN,
        )
    if request.data.get("content") is None:
        return Response({"message": "content is missing"})

    response = create_review(
        "medication_error",
        "MedicationError",
        incident_id,
        user,
        request.data.get("content"),
    )

    return response


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def medical_error_reviews_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)
    if not has_permissions(user, ["Admin", "Super User", "Manager"]):
        return Response(
            {"message": "You do not have the required permissions"},
            status=status.HTTP_403_FORBIDDEN,
        )
    response = get_incident_reviews(
        user, "MedicationError", "medication_error", incident_id
    )

    return response
