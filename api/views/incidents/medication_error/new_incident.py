from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from base.services.notifications import save_notification
from medication_error.models import MedicationError
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import <PERSON><PERSON><PERSON>ars<PERSON>
from base.services.forms import check_missing_fields, check_user_facility

from medication_error.seriaizers import MedicalErrorSerializer
from api.views.incidents.general_incident.new_incident import get_patient_profile


@extend_schema(request=MedicalErrorSerializer, responses=MedicalErrorSerializer)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON>ars<PERSON>])
def new_medical_error(request):
    if request.method == "POST":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        required_fields = [
            "date_of_error",
            "time_of_error",
            "location",
        ]

        missing_fields_response = check_missing_fields(
            data=request.data, required_fields=required_fields
        )
        if missing_fields_response:
            return missing_fields_response
        facility, has_facility = check_user_facility(request.data, user)
        if has_facility:
            request.data["report_facility"] = facility
        else:
            return facility

        request_data = request.data.copy()

        try:

            if "patient" in request.data:
                patient_profile, message = get_patient_profile(
                    data=request.data["patient"], facility=facility
                )
                if patient_profile:
                    request_data["patient"] = patient_profile.id

            serializer = MedicalErrorSerializer(data=request_data)
            if serializer.is_valid():
                medical_error = serializer.save(created_by=user, updated_by=user)
                save_notification(
                    facility=facility,
                    group_name="Admin",
                    notification_type="info",
                    notification_category="incident",
                    message="A new incident is submitted",
                    item_id=medical_error.id,
                )
                return Response(
                    {
                        "status": "success",
                        "message": "Medical Error Report created successfully",
                        "medical_error": MedicalErrorSerializer(medical_error).data,
                    },
                    status=status.HTTP_201_CREATED,
                )
            else:
                return Response(
                    {
                        "message": "Failed to create Medical Error Report",
                        "errors": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            return Response(
                {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
            )

    return Response(
        {"message": "Invalid request method"}, status=status.HTTP_400_BAD_REQUEST
    )
