from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.services.auth import verify_user
from base.models import Facility
from medication_error.models import MedicationError
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSO<PERSON>arser
from medication_error.seriaizers import (
    MedicalErrorSerializer,
    MedicalErrorVersionSerializer,
)
from incidents.views.send_to_department import send_incident_submission_email
from api.views.incidents.general_incident.new_incident import get_patient_profile


# modify the whole incident
@extend_schema(request=MedicalErrorSerializer, responses=MedicalErrorSerializer)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def modify_medication_error_incident(request, medication_error_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    request_copy = request.data.copy()
    report_facility = None
    try:
        incident = MedicationError.objects.get(id=medication_error_id)
        report_facility = incident.report_facility
        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_manager_user(user, incident.department)
        ) and not incident.created_by == user:
            return Response(
                {"error": "You do not have permission to update this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
        report_facility_data = request.data.get("report_facility")

        if report_facility_data and isinstance(report_facility_data, list):
            facility_name = report_facility_data[0].get("name")
            if facility_name:
                facility, created = Facility.objects.get_or_create(name=facility_name)
                request.data["report_facility"] = facility.id

        if "patient" in request.data:
            patient_profile, message = get_patient_profile(
                data=request.data["patient"], facility=incident.report_facility
            )
            if patient_profile:
                request_copy["patient"] = patient_profile.id

        if "provider_info" in request.data:
            provider_info_profile, message = get_patient_profile(
                data=request.data["provider_info"], facility=incident.report_facility
            )
            if provider_info_profile:
                request_copy["provider_info"] = provider_info_profile.id
        request_copy["original_report"] = incident.id
        request_copy["created_by"] = user.id
        request_copy["status"] = request.data.get("status", "Draft")
        request_copy["report_facility"] = report_facility.id
        new_version = MedicalErrorVersionSerializer(data=request_copy)
        if new_version.is_valid():
            new_version.save()
            incident.is_modified = True
            incident.updated_by = user
            incident.status = request.data.get("status", "Draft")
            incident.save()
            if "status" in request.data and request.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=incident,
                    incident_type="Medication Error/Near Miss Report",
                )
            return Response(
                {
                    "status": "success",
                    "message": "Incident was successfully resolved",
                    "incident": new_version.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "failed",
                    "error": f"failed to update incident {new_version.errors,}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except MedicationError.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
