from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import get_patient_profile
from base.services.auth import verify_user
from base.services.incidents.base import IncidentService
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from medication_error.models import MedicationError
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import J<PERSON>NParser
from medication_error.seriaizers import MedicalErrorSerializer
from base.services.forms import check_missing_fields
from incidents.views.send_to_department import (
    send_incident_submission_email,
)

incident_service = IncidentService()
logging_service = LoggingService()


@extend_schema(request=MedicalErrorSerializer, responses=MedicalErrorSerializer)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def update_medical_error(request, id):
    if request.method != "PATCH":
        return Response(
            {"message": "Invalid request"}, status=status.HTTP_400_BAD_REQUEST
        )

    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        medical_error = MedicationError.objects.get(id=id)
        if (
            not has_permissions(user, ["Super User", "Admin", "Manager"])
            and not medical_error.created_by == user
        ):
            return Response(
                {"error": "You do not have permission to update this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
    except MedicationError.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Medical error report not found"},
            status=status.HTTP_404_NOT_FOUND,
        )

    data = request.data
    request_data = request.data.copy()
    required_fields = []
    try:
        if "provider_info" in data and data["provider_info"] is not None:
            required_fields = [
                "provider_title",
                "provider_classification",
                "date_of_report",
                "time_of_report",
            ]

        if "drug_ordered" in data and data["drug_ordered"] is not None:
            required_fields = [
                "drug_ordered",
                "drug_given",
                "drug_ordered_route",
                "drug_given_route",
            ]

        if "what_happened" in data and data["what_happened"] is not None:
            required_fields = [
                "what_happened",
                "form_of_error",
            ]

        if "description_of_error" in data and data["description_of_error"] is not None:
            required_fields = [
                "description_of_error",
            ]

        if "contributing_factors" in data and data["contributing_factors"] is not None:
            required_fields = [
                "contributing_factors",
            ]

        if "error_category" in data and data["error_category"] is not None:
            required_fields = ["error_category"]

        if "comments" in data and data["comments"] is not None:
            required_fields = ["comments", "actions_taken"]

        for field in required_fields:
            if field not in data:
                return Response(
                    {"status": "failed", "message": f"Missing field: {field}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if "provider_info" in request.data:
            provider_info_profile, message = get_patient_profile(
                data=request.data["provider_info"],
                facility=medical_error.report_facility,
            )
            if provider_info_profile:
                request_data["provider_info"] = provider_info_profile.id

        serializer = MedicalErrorSerializer(
            medical_error, data=request_data, partial=True
        )
        if serializer.is_valid():
            serializer.save()

            if "status" in request.data and request.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=medical_error,
                    incident_type="Medication Error/Near miss report",
                )
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@extend_schema(request=MedicalErrorSerializer, responses=MedicationError)
@api_view(["PUT"])
@parser_classes([JSONParser])
def mark_medical_error_incident_as_resolved(request, medication_error_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    data["status"] = "Closed"

    required_fields = ["status"]

    check_missing_fields_response = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_response:
        return check_missing_fields_response

    try:
        incident = MedicationError.objects.get(id=medication_error_id)
        response = incident_service.mark_as_resolved(incident, user)

        if not response.success:
            return Response(
                {"status": "failed", "message": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": "Incident was successfully resolved",
                "incident": {
                    "id": incident.id,
                    "is_resolved": incident.is_resolved,
                },
            },
            status=status.HTTP_200_OK,
        )

    except MedicationError.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def send_medical_error_to_department_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    try:
        incident = MedicationError.objects.get(id=incident_id)

        if not is_super_user(user) and not is_admin_user(
            user, incident.report_facility
        ):
            return Response(
                {"error": "You do not have permission to access this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
        incident_type = ("Medication Error/Near miss report",)
        response = incident_service.send_incident_to_department(
            data, incident, incident_type, user
        )
        if not response.success:
            return Response(
                {"status": "failed", "message": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": message,
                "incident": MedicalErrorSerializer(incident).data,
            },
            status=status.HTTP_200_OK,
        )

    except MedicationError.DoesNotExist:
        return Response(
            {"error": f"Incident with id '{id}' is not found"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )
