from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.models import Profile
from api.views.auth.permissions_list import has_permissions
from api.views.incidents.general_incident.incident_list import (
    get_latest_incident_reports,
    get_limited_access_incident,
)
from base.services.auth import verify_user
from base.models import Department
from medication_error.models import MedicationError
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import J<PERSON>NParser
from medication_error.seriaizers import (
    MedicalErrorListSerializer,
    MedicalErrorSerializer,
)
from medication_error.services.get_incidents import MedicationErrorServices


medication_error_service = MedicationErrorServices()


@extend_schema(request=MedicalErrorSerializer, responses=MedicalErrorSerializer)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def list_medical_error_incident(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        incidents = medication_error_service.get_incidents_list(
            user,
            facility_id=request.query_params.get("facility_id"),
            department_id=request.query_params.get("department_id"),
        )
        if not incidents.success:
            return Response(
                {"error": incidents.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = MedicalErrorListSerializer(incidents.data, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    else:
        return Response(
            {"message": "Invalid request"}, status=status.HTTP_400_BAD_REQUEST
        )
