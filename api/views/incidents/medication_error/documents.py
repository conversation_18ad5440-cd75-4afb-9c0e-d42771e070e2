from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from medication_error.models import MedicationError
from documents.views import (
    get_incident_documents,
    handle_incident_document,
    delete_incident_document,
)

logging_service = LoggingService()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def medication_error_documents_api(request, medication_error_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    files = request.FILES.getlist("files")
    folder_name = "lost-and-found-incident"
    try:
        incident = MedicationError.objects.get(id=medication_error_id)

        data = handle_incident_document(files, folder_name, incident, user)
        return Response(data, status=status.HTTP_201_CREATED)
    except MedicationError.DoesNotExist:
        return Response(
            {"error": "Failed to upload file(s)"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to upload file"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def medication_error_documents_list_api(request, medication_error_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    documents, found = get_incident_documents(MedicationError, medication_error_id)
    if found:
        return Response(documents, status=status.HTTP_200_OK)
    else:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_medication_error_documents(request, incident_id, document_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident = MedicationError.objects.get(id=incident_id)

        success, message = delete_incident_document(incident, document_id)

        if success:
            return Response(
                {
                    "message": "Document deleted successfully",
                    "deleted document": document_id,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
    except MedicationError.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": f"Failed to delete document"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
