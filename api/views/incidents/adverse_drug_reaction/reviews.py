from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import J<PERSON>NParser
from api.views.get_reviews import get_incident_reviews
from api.views.new_review import create_review
from base.services.auth import verify_user
from adverse_drug_reaction.models import AdverseDrugReaction
from reviews.models import Review


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_adverse_drug_reaction_review(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    if request.data.get("content") is None:
        return Response({"message": "content is missing"})

    response = create_review(
        "adverse_drug_reaction",
        "AdverseDrugReaction",
        incident_id,
        user,
        request.data.get("content"),
    )

    return response


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON><PERSON><PERSON>])
def adverse_drug_reaction_reviews_list(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    response = get_incident_reviews(
        user, "AdverseDrugReaction", "adverse_drug_reaction", incident_id
    )

    return response
