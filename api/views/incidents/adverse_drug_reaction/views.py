from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from adverse_drug_reaction.models import AdverseDrugReaction
from adverse_drug_reaction.serializers import ListAdverseDrugReactionSerializer
from adverse_drug_reaction.services.actions import ADRActionsService
from adverse_drug_reaction.services.documents import ADRDocumentService
from adverse_drug_reaction.services.operations import ADRService
from adverse_drug_reaction.services.tasks import ADRTasksService
from adverse_drug_reaction.tests.test_services import TestADRTasksService
from api.views.incidents.general_incident.incident_list import (
    get_latest_incident_reports,
)
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.incidents.send_for_review import SendReviewService
from base.services.logging.logger import LoggingService
from reviews.services.review import ReviewsOperations


logging_service = LoggingService()

logging_service = LoggingService()
incidents_service = GetIncidentsService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def adverse_drug_reactions_api(request):
    adr_service = ADRService(request.user)
    try:
        if request.method == "GET":
            facility_id = request.query_params.get("facility_id")
            department_id = request.query_params.get("department_id")

            filters = request.query_params.dict()
            incidents = adr_service.get_incidents_list(filters=filters)

            if not incidents.success:
                return Response(
                    {"error": incidents.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incidents.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            response = adr_service.create_incident(request.data)
            if not response.success:
                return Response(
                    {"message": response.message},
                    status=response.code,
                )

            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error getting incidents"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "PATCH"])
@permission_classes([IsAuthenticated])
def adverse_drug_reactions_details_api(request, id):
    adr_service = ADRService(request.user)
    action_services = ADRActionsService(
        user=request.user,
        incident_id=id,
        data=request.data,
    )
    try:
        if request.method == "GET":
            response = adr_service.get_incident_by_id(id)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PUT":
            response = adr_service.update_incident(id, request.data)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PATCH":
            action = request.data.pop("action", None)
            if not action:
                return Response(
                    {
                        "error": "Action is required",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if action == "send-for-review":
                response = action_services.send_for_review()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )

            elif action == "modify":
                response = action_services.modify_incident()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "mark-closed":
                response = action_services.mark_closed()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "delete-draft":
                response = action_services.delete_draft_incident()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": "Invalid action"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            "Internal server error",
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def adverse_drug_reactions_tasks(request, incident_id):
    """
    This function handles the tasks related to adverse drug reactions."""
    try:
        adr_service = ADRTasksService(incident_id=incident_id, user=request.user)
        response = adr_service.get_tasks()

        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            response.data,
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            "Internal server error",
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def adverse_drug_reactions_documents_api(request, incident_id):
    """
    This function handles the document retrieval for adverse drug reactions.
    """
    if request.method == "GET":
        service = ADRDocumentService(incident_id=incident_id, user=request.user)
        try:
            params = request.query_params.dict()
            response = service.get_documents(params)

            if not response.success:

                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                "Internal server error",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    elif request.method == "POST":
        service = ADRDocumentService(incident_id=incident_id, user=request.user)
        files = request.FILES.getlist("files")
        try:
            response = service.create_document(files)

            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                "Internal server error",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def get_adverse_drug_reaction_reviews_api(request, incident_id):
    """
    This function handles the retrieval of adverse drug reaction reviews.
    """
    adr_service = ReviewsOperations(
        user=request.user,
        model_name="AdverseDrugReaction",
        app_label="adverse_drug_reaction",
        incident_id=incident_id,
    )
    if request.method == "GET":
        try:
            response = adr_service.get_incident_reviews()
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                "Internal server error",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    elif request.method == "POST":
        try:
            content = request.data.get("content")
            if not content:
                return Response(
                    {"error": "Content is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            response = adr_service.create_review(content)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                "Internal server error",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    else:
        return Response(
            {"error": "Method not allowed"},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )
