from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from rest_framework.response import Response
from accounts.models import Profile
from api.views.incidents.general_incident.new_incident import get_patient_profile
from base.services.auth import verify_user
from base.services.forms import check_user_facility
from base.services.notifications import save_notification
from adverse_drug_reaction.models import AdverseDrugReaction
from adverse_drug_reaction.serializers import (
    AdverseDrugReactionCreateSerializer,
    ObserversSerializer,
    AdverseDrugReactionOutcomeSerializer,
)
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import J<PERSON><PERSON>arser


@extend_schema(request=ObserversSerializer, responses=ObserversSerializer)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_observer(request):
    serializer = ObserversSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    request=AdverseDrugReactionCreateSerializer,
    responses=AdverseDrugReactionCreateSerializer,
)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_adverse_drug_reaction_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    try:
        facility, has_facility = check_user_facility(request.data, user)
        if has_facility:
            request.data["report_facility"] = facility
        else:
            return facility

        request_data = request.data.copy()

        if "patient_name" in request.data:
            patient_profile, message = get_patient_profile(
                request.data["patient_name"], facility
            )
            if patient_profile:
                request_data["patient_name"] = patient_profile.id

        if "observers_name" in request.data:
            observers_profile, message = get_patient_profile(
                request.data["observers_name"], facility
            )
            if observers_profile:
                request_data["observers_name"] = observers_profile.id

        if "name_of_physician_notified" in request.data:
            physician_notified_profile, message = get_patient_profile(
                request.data["name_of_physician_notified"], facility
            )
            if physician_notified_profile:
                request_data["name_of_physician_notified"] = (
                    physician_notified_profile.id
                )

        if "name_of_family_notified" in request.data:
            family_notified_profile, message = get_patient_profile(
                request.data["name_of_family_notified"], facility
            )
            if family_notified_profile:
                request_data["name_of_family_notified"] = family_notified_profile.id

        request_data["created_by"] = user.id
        serializer = AdverseDrugReactionCreateSerializer(data=request_data)

        if serializer.is_valid():
            serializer.save()
            # save notification and assign it to quality managers
            save_notification(
                facility=facility,
                group_name="Admin",
                notification_type="info",
                notification_category="incident",
                message="A new incident is submitted",
                item_id=serializer.data["id"],
            )
            return Response(
                {
                    "data": serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        raise


@extend_schema(
    request=AdverseDrugReactionOutcomeSerializer,
    responses=AdverseDrugReactionOutcomeSerializer,
)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_adverse_drug_reaction_outcome_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    serializer = AdverseDrugReactionOutcomeSerializer(data=request.data)

    if serializer.is_valid():
        serializer.save(created_by=user, updated_by=user)
        return Response(
            {
                "status": "success",
                "message": "Incident created successfully",
                "Incident": serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )
    else:
        return Response(
            {"error": "Failed to create adverse and drug reaction"},
            serializer.errors,
            status=status.HTTP_400_BAD_REQUEST,
        )
