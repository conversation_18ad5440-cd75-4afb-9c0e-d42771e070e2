from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.models import Profile
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import (
    check_anonymous,
    get_patient_profile,
)
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.incidents.base import IncidentService
from base.services.logging.logger import LoggingService
from incidents.emails.send_to_department import send_to_department_email
from adverse_drug_reaction.models import AdverseDrugReaction
from base.models import Department
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser
from adverse_drug_reaction.serializers import (
    AdverseDrugReactionUpdateSerializer,
)
from datetime import datetime
import os
from dotenv import load_dotenv

from incidents.views.send_to_department import (
    send_incident_submission_email,
)

incident_service = IncidentService()
logging_service = LoggingService()


@extend_schema(
    request=AdverseDrugReactionUpdateSerializer,
    responses=AdverseDrugReactionUpdateSerializer,
)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def update_adverse_drug_reaction(request, id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    request_data = request.data.copy()
    try:
        adverse_drug_reaction = AdverseDrugReaction.objects.get(id=id)
        profile = Profile.objects.get(user=user)
        if (
            not is_super_user(user)
            and not is_admin_user(user, profile.facility)
            and not is_manager_user(user, adverse_drug_reaction.department)
            and not user == adverse_drug_reaction.created_by
        ):
            return Response(
                {"error": "You do not have enough rights to update this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
        facility = adverse_drug_reaction.report_facility
        if "patient_name" in request.data:
            patient_profile, message = get_patient_profile(
                request.data["patient_name"], facility
            )
            if patient_profile:
                request_data["patient_name"] = patient_profile.id

        if "observers_name" in request.data:
            observers_profile, message = get_patient_profile(
                request.data["observers_name"], facility
            )
            if observers_profile:
                request_data["observers_name"] = observers_profile.id

        if "name_of_physician_notified" in request.data:
            physician_notified_profile, message = get_patient_profile(
                request.data["name_of_physician_notified"], facility
            )
            if physician_notified_profile:
                request_data["name_of_physician_notified"] = (
                    physician_notified_profile.id
                )

        if "name_of_family_notified" in request.data:
            family_notified_profile, message = get_patient_profile(
                request.data["name_of_family_notified"], facility
            )
            if family_notified_profile:
                request_data["name_of_family_notified"] = family_notified_profile.id

        data = check_anonymous(request_data, user)
        serializer = AdverseDrugReactionUpdateSerializer(
            adverse_drug_reaction, data=data, partial=True
        )
        if serializer.is_valid():
            serializer.save()
            if "status" in request.data and request.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=adverse_drug_reaction,
                    incident_type="Anaphylaxis/Adverse Drug Reaction",
                )
            return Response(
                {
                    "status": "success",
                    "message": "Incident Was Successfully Saved",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "Failed to save incident data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Profile.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Admin's profile not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except AdverseDrugReaction.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Adverse drug reaction report not found"},
            # status=status.HTTP_404_NOT_FOUND,
        )


# marking the incident as resolved
@extend_schema(
    request=AdverseDrugReactionUpdateSerializer,
    responses=AdverseDrugReactionUpdateSerializer,
)
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def mark_adverse_drug_reaction_as_resolved(request, id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    data = request.data

    data["status"] = "Closed"

    required_fields = ["status"]

    check_missing_fields_responses = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_responses:
        return check_missing_fields_responses

    try:
        adverse_drug_reaction_incident = AdverseDrugReaction.objects.get(id=id)

        response = incident_service.mark_as_resolved(
            adverse_drug_reaction_incident,
            user,
        )
        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": "Incident was successfully resolved",
                "incident": {
                    "id": adverse_drug_reaction_incident.id,
                    "is_resolved": adverse_drug_reaction_incident.is_resolved,
                },
            },
            status=status.HTTP_200_OK,
        )

    except AdverseDrugReaction.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )


# send incident to department
@extend_schema(
    request=AdverseDrugReactionUpdateSerializer,
    responses=AdverseDrugReactionUpdateSerializer,
)
@api_view(["PUT"])
@parser_classes([JSONParser])
def send_adverse_drug_reaction_to_department(request, id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    try:
        incident = AdverseDrugReaction.objects.get(id=id)

        if not is_super_user(user) and not is_admin_user(
            user, incident.report_facility
        ):
            return Response(
                {
                    "error": "You do not have permission to send this incident to the department"
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        incident_type = "Anaphylaxis/Adverse Drug Reaction"
        response = incident_service.send_incident_to_department(
            data, incident, incident_type, user
        )
        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": message,
                "incident": AdverseDrugReactionUpdateSerializer(incident).data,
            },
            status=status.HTTP_200_OK,
        )

    except AdverseDrugReaction.DoesNotExist:
        return Response(
            {"error": f"Incident with id '{id}' is not found"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )
