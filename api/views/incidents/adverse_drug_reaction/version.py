from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from adverse_drug_reaction.models import (
    AdverseDrugReaction,
    AdverseDrugReactionVisitorVersion,
)
from adverse_drug_reaction.serializers import (
    AdverseDrugReactionUpdateVersionSerializer,
    ListAdverseDrugReactionSerializer,
)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def adverse_drug_reaction_version(request, incident_id, version_id):
    try:
        version = AdverseDrugReactionVisitorVersion.objects.get(id=version_id)
        serializer = ListAdverseDrugReactionSerializer(version)
        return Response(serializer.data, status=status.HTTP_200_OK)

    except AdverseDrugReactionVisitorVersion.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_404_NOT_FOUND
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def adverse_drug_reaction_original_version(request, incident_id):
    try:
        incident = AdverseDrugReaction.objects.get(id=incident_id)
        serializer = ListAdverseDrugReactionSerializer(incident)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except AdverseDrugReaction.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_404_NOT_FOUND
        )
