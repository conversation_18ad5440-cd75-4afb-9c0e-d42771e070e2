from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.incidents.general_incident.new_incident import get_patient_profile
from base.services.auth import verify_user
from adverse_drug_reaction.models import AdverseDrugReaction
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import <PERSON><PERSON><PERSON>ars<PERSON>
from base.models import Department
from adverse_drug_reaction.serializers import (
    AdverseDrugReactionUpdateSerializer,
    AdverseDrugReactionUpdateVersionSerializer,
)
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from incidents.views.send_to_department import send_incident_submission_email


# updating the whole incident
@extend_schema(
    request=AdverseDrugReactionUpdateSerializer,
    responses=AdverseDrugReactionUpdateSerializer,
)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON>ars<PERSON>])
def modify_adverse_drug_reaction_incident(request, id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    request_data = request.data.copy()
    report_facility = None
    try:
        incident = AdverseDrugReaction.objects.get(id=id)
        report_facility = incident.report_facility

        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_manager_user(user, incident.department)
        ) and not incident.created_by == user:
            return Response(
                {"error": "You do not have enough rights to update this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )

        facility = incident.report_facility
        if "patient_name" in request.data:
            patient_profile, message = get_patient_profile(
                request.data["patient_name"], facility
            )
            if patient_profile:
                request_data["patient_name"] = patient_profile.id

        if "observers_name" in request.data:
            observers_profile, message = get_patient_profile(
                request.data["observers_name"], facility
            )
            if observers_profile:
                request_data["observers_name"] = observers_profile.id

        if "name_of_physician_notified" in request.data:
            physician_notified_profile, message = get_patient_profile(
                request.data["name_of_physician_notified"], facility
            )
            if physician_notified_profile:
                request_data["name_of_physician_notified"] = (
                    physician_notified_profile.id
                )

        if "name_of_family_notified" in request.data:
            family_notified_profile, message = get_patient_profile(
                request.data["name_of_family_notified"], facility
            )
            if family_notified_profile:
                request_data["name_of_family_notified"] = family_notified_profile.id
        request_data["original_report"] = incident.id
        request_data["report_facility"] = report_facility.id
        request_data["created_by"] = user.id
        request_data["status"] = request.data.get("status", "Draft")
        version_serializer = AdverseDrugReactionUpdateVersionSerializer(
            data=request_data
        )

        if version_serializer.is_valid():
            version_serializer.save()
            incident.is_modified = True
            incident.updated_by = user
            incident.status = request.data.get("status", "Draft")
            incident.save()
            if "status" in request.data and request.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=incident,
                    incident_type="Anaphylaxis/Adverse Drug Reaction",
                )
            return Response(
                {
                    "message": "Incident was successfully updated",
                    "incident": version_serializer.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "failed",
                    "error": f"failed to update incident {str(version_serializer.errors,)}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except AdverseDrugReaction.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
