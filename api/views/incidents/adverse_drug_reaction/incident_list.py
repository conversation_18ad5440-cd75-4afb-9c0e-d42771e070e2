from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from adverse_drug_reaction.services.operations import ADRService
from api.views.incidents.general_incident.incident_list import (
    get_latest_incident_reports,
)
from base.services.auth import verify_user
from adverse_drug_reaction.models import AdverseDrugReaction, ObserversName
from adverse_drug_reaction.serializers import (
    AdverseDrugReactionUpdateSerializer,
    ListAdverseDrugReactionSerializer,
    ObserversSerializer,
)
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser

from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService

adr_service = ADRService()
logging_service = LoggingService()
incidents_service = GetIncidentsService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def adverse_drug_reactions_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    facility_id = request.query_params.get("facility_id")
    department_id = request.query_params.get("department_id")
    try:
        incidents = incidents_service.get_incidents(
            user=request.user,
            model=AdverseDrugReaction,
        )

        if not incidents.success:
            return Response(
                {"error": incidents.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        incident_found, versions, message = get_latest_incident_reports(
            incidents.data, ListAdverseDrugReactionSerializer
        )
        if incident_found:
            return Response(
                versions,
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"message": message}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error getting incidents"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# get adverse drug reaction details


@extend_schema(
    request=ListAdverseDrugReactionSerializer,
    responses=ListAdverseDrugReactionSerializer,
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def adverse_drug_reaction_details_api(request, id):
    incident = adr_service.get_incident_by_id(request.user, incident_id=id)
    if not incident.success:
        return Response(
            {"error": incident.message},
            status=status.HTTP_400_BAD_REQUEST,
        )
    return Response(
        {"data": incident.data},
        status=status.HTTP_200_OK,
    )


@extend_schema(request=ObserversSerializer, responses=ObserversSerializer)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def observers_name_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", " message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    observers_name_data = ObserversName.objects.all()
    observers_name = ObserversSerializer(observers_name_data, many=True).data
    return Response({"data": observers_name}, status=status.HTTP_200_OK)
