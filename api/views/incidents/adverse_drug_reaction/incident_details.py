from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_specific_department,
    is_super_user,
)
from base.services.auth import verify_user
from base.models import Department

from adverse_drug_reaction.serializers import (
    AdverseDrugReactionUpdateSerializer,
    ListAdverseDrugReactionSerializer,
)
from adverse_drug_reaction.models import AdverseDrugReaction
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser

from base.services.incidents.get_incidents import GetIncidentsService

incidents_service = GetIncidentsService()


@extend_schema(
    request=AdverseDrugReactionUpdateSerializer,
    responses=AdverseDrugReactionUpdateSerializer,
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def adverse_drug_reaction_details_api(request, adverse_drug_reaction_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    try:
        incident = AdverseDrugReaction.objects.get(id=adverse_drug_reaction_id)
        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_specific_department(
                user=user, department_name="Pharmacy", facility=incident.report_facility
            )
            and not incident.created_by == user
        ):
            return Response(
                {"error": "You do not have permission to access this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )

        success, incident, modifications, message = (
            incidents_service.get_latest_version(
                incident_data=incident,
                modelName=AdverseDrugReaction,
                incidentSerializer=ListAdverseDrugReactionSerializer,
                incident_id=adverse_drug_reaction_id,
            )
        )
        if success:
            return Response(
                {
                    "incident": incident,
                    "modifications": modifications,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"message": message}, status=status.HTTP_400_BAD_REQUEST)
    except AdverseDrugReaction.DoesNotExist:
        return Response(
            {"error": "data for item not found"}, status=status.HTTP_404_NOT_FOUND
        )
