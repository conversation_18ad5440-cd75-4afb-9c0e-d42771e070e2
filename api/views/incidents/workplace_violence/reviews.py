from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON>SONParser
from api.views.get_reviews import get_incident_reviews
from api.views.new_review import create_review
from base.services.auth import verify_user


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_work_place_violence_review(request, workplace_violence_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"error": "You have no access to perform this action"},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    response = create_review(
        "workplace_violence_reports",
        "WorkplaceViolence",
        workplace_violence_id,
        user,
        request.data.get("content"),
    )

    return response


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_work_place_violence_reviews(request, workplace_violence_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    response = get_incident_reviews(
        user,
        "WorkplaceViolence",
        "workplace_violence_reports",
        workplace_violence_id,
    )

    return response
