from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import JSONParser
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from drf_spectacular.utils import extend_schema

from base.services.incidents.base import IncidentService
from base.services.logging.logger import LoggingService
from workplace_violence_reports.models import (
    IncidentInvolvedParty,
    IncidentPersonInjured,
    IncidentWitness,
    TerminationOfIncident,
    WorkPlaceViolence,
)
from workplace_violence_reports.serializers import (
    IncidentPersonInjuredSerializer,
    IncidentWitnessSerializer,
    WorkPlaceViolenceIncidentInvolvedParty,
    WorkPlaceViolenceObjectSerializer,
    WorkPlaceViolenceSerializer,
    WorkPlaceViolenceStepsSerializer,
)
from incidents.views.send_to_department import (
    send_incident_submission_email,
)
from api.views.incidents.general_incident.new_incident import (
    get_patient_profile,
    generate_username,
)
from accounts.models import Profile
from django.contrib.auth.models import User


incident_service = IncidentService()
logging_service = LoggingService()


@extend_schema(
    request=WorkPlaceViolenceSerializer, responses=WorkPlaceViolenceSerializer
)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def update_workplace_violence_api(request, workplace_violence_id):
    if request.method == "PATCH":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        incident = WorkPlaceViolence.objects.get(id=workplace_violence_id)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        required_fields = []

        missing_fields_response = check_missing_fields(
            required_fields=required_fields, data=request.data
        )
        if missing_fields_response:
            return missing_fields_response

        if "incident_type" in request.data:
            required_fields = ["incident_type"]
        if "date_of_incident" in request.data:
            required_fields = [
                "date_of_incident",
                "time_of_incident",
                "description",
            ]
        if "type_of_contact" in request.data:
            required_fields = [
                "type_of_contact",
                "victim_was_alone",
                "location",
                "there_was_threats_before",
                "staff_member_reported",
                "weapons_were_involved",
                "weapon_used",
            ]
        if "immediate_supervisor" in request.data:
            if request.data.get("immediate_supervisor") == False:
                required_fields = ["immediate_supervisor"]
                request.data["name_of_supervisor"] = None
                request.data["title_of_supervisor"] = None
                request.data["date_notified"] = None
                request.data["time_notified"] = None

            else:
                required_fields = [
                    "immediate_supervisor",
                    "name_of_supervisor",
                    "title_of_supervisor",
                    "date_notified",
                    "notification_time",
                    "action_taken",
                    "prevention_suggestion",
                ]

                if "name_of_supervisor" in request.data:
                    incident, success, message = handle_name_of_supervisor(
                        request.data["name_of_supervisor"],
                        incident,
                    )
                    if not success:
                        # raise
                        return Response(
                            {"error": message}, status=status.HTTP_400_BAD_REQUEST
                        )
                    request.data.pop("name_of_supervisor", None)

        if "initiated_by" in request.data:

            incident, created, message = handle_initiated_by(
                request.data,
                workplace_violence_id,
                incident.report_facility,
                WorkPlaceViolence,
            )
            if created:
                request.data.pop("initiated_by", None)
            else:
                return Response(
                    {"status": "failed", "message": message},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if "incident_witness" in request.data:
            incident, created, message = incident_witness(
                request.data, workplace_violence_id, WorkPlaceViolence
            )
            if created:
                request.data.pop("incident_witness", None)
            else:
                return Response(
                    {"status": "failed", "message": message},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if "persons_injured" in request.data:

            # persons_injured_data = request.data.get("persons_injured", [])
            incident, created, message = handle_person_injured(
                request.data, WorkPlaceViolence, workplace_violence_id
            )
            if created:
                pass
            else:
                return Response(
                    {"status": "failed", "message": message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            request.data.pop("persons_injured")

        if "reported_by" in request.data:

            required_fields = [
                "reported_by",
                "reported_by_title",
                "date_reported",
                "time_reported",
            ]

            incident, created, message = handle_reported_by(
                request.data, workplace_violence_id, WorkPlaceViolence
            )
            if created:
                request.data.pop("reported_by", None)
            else:
                return Response(
                    {"status": "failed", "message": message},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        try:
            incident = WorkPlaceViolence.objects.get(id=workplace_violence_id)

            # trying to avoid serializers
            # saving data with setattr method
            for key, value in request.data.items():
                setattr(incident, key, value)
            incident.updated_by = user
            incident.save()
            # return update incident with a serializer
            incident_data = WorkPlaceViolenceObjectSerializer(incident).data

            return Response(
                {
                    "status": "success",
                    "message": "Workplace violence updated successfully",
                    "workplace_violence": incident_data,
                },
                status=status.HTTP_200_OK,
            )

        except WorkPlaceViolence.DoesNotExist:
            return Response(
                {"error": "Workplace violence not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            raise
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


def handle_name_of_supervisor(data, incident):
    """
    Handle the creation/retrieval and assignment of a supervisor to an incident.

    Args:
        data (dict): Dictionary containing supervisor data with user_data
        incident: WorkPlaceViolence incident object

    Returns:
        tuple: (incident object/None, success boolean, message string)
    """
    try:
        # Validate input data
        if not data or not isinstance(data, dict):
            return None, False, "Invalid supervisor data format"

        user_data = data.get("user_data")
        if not user_data:
            return None, False, "Supervisor user data is missing"

        first_name = user_data.get("first_name")
        last_name = user_data.get("last_name")
        if not first_name or not last_name:
            return None, False, "Supervisor first name or last name is missing"

        try:
            # Get or create user
            user, created = User.objects.get_or_create(
                username=generate_username(f"{first_name}_{last_name}"),
                defaults={"first_name": first_name, "last_name": last_name},
            )

            # Get or create profile
            profile, profile_created = Profile.objects.get_or_create(user=user)

            # Update incident
            incident.name_of_supervisor = profile
            incident.save()

            status_message = "Supervisor {} successfully {}".format(
                f"{first_name} {last_name}",
                "created and assigned" if created else "assigned",
            )

            return incident, True, status_message

        except Exception as e:
            return (
                None,
                False,
                f"Error processing supervisor",
            )

    except Exception as e:
        return None, False, f"Unexpected error"


def handle_initiated_by(data, incident_id, facility, model_name):
    initiators = []
    failed_initiators = []
    # data example:

    try:
        incident = model_name.objects.get(id=incident_id)
        if len(data["initiated_by"]) < 1:
            incident.initiated_by.all().delete()
            incident.save()
            return (incident, True, "Successfully removed incident involved parties")
        for item in data["initiated_by"]:
            # pass
            # get profile or create one with user_data and _profile_data
            # create a new imitated_by with profile found and other data,
            # add the created initiated_by to the list of initiators
            profile, message = get_patient_profile(item, facility)
            if profile:
                party = None
                try:
                    party = IncidentInvolvedParty.objects.get(party=profile)
                except IncidentInvolvedParty.DoesNotExist:

                    party = IncidentInvolvedParty.objects.create(
                        party=profile,
                        party_type=item.get("party_type"),
                        title=item.get("title"),
                        assailant_background=item.get("background"),
                    )
                initiators.append(party)
            else:
                failed_initiators.append(item)
                continue
        incident.initiated_by.set(initiators)
        return (incident, True, "Successfully edited incident")
    except model_name.DoesNotExist:
        return (None, False, "Incident not found")
    except Exception as e:
        raise
        return (None, False, "Internal server error")


def incident_witness(data, incident_id, model_name):

    try:
        incident = model_name.objects.get(id=incident_id)
        incident.notification = data.get("notification", None)
        witnesses = []
        if len(data["incident_witness"]) < 1:
            # remove all incident witness from incident
            incident.incident_witness.all().delete()
            incident.save()
            return (incident, True, "Successfully removed incident witnesses")
        for item in data["incident_witness"]:
            profile, message = get_patient_profile(item, incident.report_facility)
            if profile:
                witness = None
                try:
                    witness = IncidentWitness.objects.get(witness=profile)
                    witnesses.append(witness)
                except IncidentWitness.DoesNotExist:
                    witness = IncidentWitness.objects.create(witness=profile)
                    witnesses.append(witness)
            else:
                continue
        if witnesses:
            incident.incident_witness.set(witnesses)
        return (incident, True, "Successfully edited witness of incident")
    except model_name.DoesNotExist:
        return (None, False, "Incident not found")
    except Exception as e:
        return (None, False, "Internal server error")


def handle_person_injured(data, model_name, incident_id):
    """
    Handle and create records for multiple injured persons in an incident.

    Args:
        persons_injured_data: List of dictionaries containing injured person information
        incident: WorkPlaceViolence incident object

    Returns:
        tuple: (incident/None, success boolean, message string)
    """
    persons = []
    errors = []
    try:
        incident = model_name.objects.get(id=incident_id)
        # Process each person
        if len(data["persons_injured"]) < 1:
            incident.persons_injured.all().delete()

            incident.save()
            return (incident, True, "Successfully removed persons injured")
        for person in data["persons_injured"]:
            try:
                # Validate user data
                user_data = person.get("user_data")
                if not user_data:
                    errors.append("User data is missing for a person")
                    continue

                # Get or create user
                first_name = user_data.get("first_name")
                last_name = user_data.get("last_name")
                username = f"{first_name}_{last_name}"

                try:
                    user, created = User.objects.get_or_create(
                        first_name=first_name,
                        last_name=last_name,
                        username=generate_username(username),
                    )

                    # Get or create profile
                    profile, success = Profile.objects.get_or_create(user=user)

                    # Get or create person injured record
                    person_injured, _ = IncidentPersonInjured.objects.get_or_create(
                        person=profile,
                        injury_description=person.get("injury_description"),
                    )

                    persons.append(person_injured)

                except Exception as e:

                    errors.append(f"Error processing person {first_name} {last_name}")
                    continue

            except Exception as e:

                errors.append(f"Error processing person entry")
                continue

        # Update incident if we have any successful entries
        if persons and len(persons) > 0:
            incident.persons_injured.set(persons)
            incident.there_were_injuries = "Yes"
            incident.save()

            if errors:
                return (
                    incident,
                    True,
                    f"Partially successful. Processed {len(persons)} persons. Errors: {'; '.join(errors)}",
                )
            return incident, True, "Successfully updated all injured persons"
        else:

            return (
                None,
                False,
                f"Failed to process any injured persons. Errors: {'; '.join(errors)}",
            )
    except model_name.DoesNotExist:
        return None, False, "Workplace violence incident not found"
    except Exception as e:
        return None, False, f"Unexpected error"


def handle_reported_by(data, incident_id, model_name):
    try:
        incident = model_name.objects.get(id=incident_id)

        if "reported_by" in data:
            reported_by_profile, message = get_patient_profile(
                data=data["reported_by"], facility=incident.report_facility
            )

            if isinstance(reported_by_profile, Response):
                return None, False, reported_by_profile.data.get("message")
            incident.reported_by = reported_by_profile
            incident.reported_by_title = data.get("reported_by_title")
            incident.date_reported = data.get("date_reported")
            incident.time_reported = data.get("time_reported")
            incident.save()
        return (incident, True, "Incident updated")

    except model_name.DoesNotExist:
        return False, "Workplace violence incident not found."
    except Exception as e:
        return None, False, "Internal server error"


# mark incident as resolved
@extend_schema(request=WorkPlaceViolenceSerializer, responses=WorkPlaceViolence)
@api_view(["PUT"])
@parser_classes([JSONParser])
def mark_workplace_violence_as_resolved(request, workplace_violence_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    data["status"] = "Closed"

    required_fields = ["status"]

    check_missing_fields_response = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_response:
        return check_missing_fields_response

    try:
        incident = WorkPlaceViolence.objects.get(id=workplace_violence_id)

        response = incident_service.mark_as_resolved(
            incident,
            user,
        )
        if not response.success:
            return Response(
                {"status": "failed", "message": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": "Incident was successfully resolved",
                "incident": {
                    "id": incident.id,
                    "is_resolved": incident.is_resolved,
                },
            },
            status=status.HTTP_200_OK,
        )

    except WorkPlaceViolence.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def send_workplace_violence_to_department(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data
    try:
        incident = WorkPlaceViolence.objects.get(id=incident_id)

        if not is_super_user(user) and not is_admin_user(
            user, incident.report_facility
        ):
            return Response(
                {
                    "error": "You do not have permission to send this incident to the department"
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        incident_type = ("Workplace violence report",)
        response = incident_service.send_incident_to_department(
            data, incident, incident_type, user
        )
        if not response.success:
            return Response(
                {"status": "failed", "message": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": response.message,
                "incident": WorkPlaceViolenceSerializer(incident).data,
            },
            status=status.HTTP_200_OK,
        )

    except WorkPlaceViolence.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
