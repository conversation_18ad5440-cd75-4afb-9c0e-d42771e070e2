from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import J<PERSON>NParser
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_specific_department,
    is_super_user,
)
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from drf_spectacular.utils import extend_schema

from base.services.incidents.get_incidents import GetIncidentsService
from workplace_violence_reports.models import WorkPlaceViolence

from workplace_violence_reports.serializers import (
    WorkPlaceViolenceObjectSerializer,
    WorkPlaceViolenceSerializer,
)

incidents_service = GetIncidentsService()


@extend_schema(
    request=WorkPlaceViolenceSerializer, responses=WorkPlaceViolenceSerializer
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def work_place_violence_detail(request, workplace_violence_id):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"error": "Authentication failed"}, status=status.HTTP_401_UNAUTHORIZED
            )

        try:
            incident_data = WorkPlaceViolence.objects.get(id=workplace_violence_id)

            if (
                not is_super_user(user)
                and not is_admin_user(user, incident_data.report_facility)
                and not is_specific_department(
                    user=user,
                    department_name="Human Resources",
                    facility=incident_data.report_facility,
                )
                and not incident_data.created_by == user
            ):
                return Response(
                    {"error": "You do not have permission to access this incident"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            success, incident, modifications, message = (
                incidents_service.get_latest_version(
                    incident_data=incident_data,
                    modelName=WorkPlaceViolence,
                    incidentSerializer=WorkPlaceViolenceObjectSerializer,
                    incident_id=workplace_violence_id,
                )
            )
            if success:
                return Response(
                    {"incident": incident, "modifications": modifications},
                    status=status.HTTP_200_OK,
                )
        except WorkPlaceViolence.DoesNotExist:
            return Response(
                {"error": "Workplace violence not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
