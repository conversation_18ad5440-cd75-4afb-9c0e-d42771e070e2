from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from workplace_violence_reports.models import (
    WorkPlaceViolence,
    WorkPlaceViolenceVersion,
)
from workplace_violence_reports.new_serializers import GetWorkplaceViolenceSerializer
from workplace_violence_reports.serializers import (
    WorkPlaceViolenceListObjectSerializer,
    WorkPlaceViolenceSerializer,
    WorkPlaceViolenceVersionSerializer,
)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def workplace_incident_version(request, incident_id, version_id):
    try:
        incident = WorkPlaceViolenceVersion.objects.get(id=version_id)
        serializer = GetWorkplaceViolenceSerializer(incident)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except WorkPlaceViolenceVersion.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def workplace_incident_original_version(request, incident_id):
    try:
        incident = WorkPlaceViolence.objects.get(id=incident_id)
        serializer = WorkPlaceViolenceListObjectSerializer(incident)
        return Response(serializer.data, status=status.HTTP_200_OK)

    except WorkPlaceViolence.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_404_NOT_FOUND
        )
