from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import J<PERSON>NParser
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.workplace_violence.update_incident import (
    handle_initiated_by,
    handle_name_of_supervisor,
    handle_person_injured,
    handle_reported_by,
    incident_witness,
)
from base.services.auth import verify_user
from drf_spectacular.utils import extend_schema
from workplace_violence_reports.models import (
    WorkPlaceViolence,
    IncidentInvolvedParty,
    TerminationOfIncident,
    IncidentPersonInjured,
    IncidentWitness,
    WorkPlaceViolenceVersion,
)
from workplace_violence_reports.serializers import (
    WorkPlaceViolenceUpdateSerializer,
    WorkPlaceViolenceSerializer,
    WorkPlaceViolenceVersionSerializer,
)
from incidents.views.send_to_department import send_incident_submission_email
from api.views.incidents.general_incident.new_incident import generate_username
from django.contrib.auth.models import User
from accounts.models import Profile


@extend_schema(
    request=WorkPlaceViolenceUpdateSerializer,
    responses=WorkPlaceViolence,
)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def modify_workplace_violence(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    # for i in WorkPlaceViolence.objects.all():
    #     i.delete()
    # for i in WorkPlaceViolenceVersion.objects.all():
    #     i.delete()
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    report_facility = None
    try:
        # Fetch the incident to be updated
        incident = WorkPlaceViolence.objects.get(id=incident_id)
        report_facility = incident.report_facility
        new_version = WorkPlaceViolenceVersion.objects.create(
            original_report=incident, created_by=user
        )
        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_manager_user(user, incident.department)
        ) and not incident.created_by == user:
            return Response(
                {"error": "You do not have permission to update this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )

        if "initiated_by" in request.data:

            incident, created, message = handle_initiated_by(
                request.data,
                new_version.id,
                incident.report_facility,
                WorkPlaceViolenceVersion,
            )
            if created:
                request.data.pop("initiated_by", None)
            else:
                return Response(
                    {"status": "failed", "message": message},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if "incident_witness" in request.data:
            incident, created, message = incident_witness(
                request.data, new_version.id, WorkPlaceViolenceVersion
            )
            if created:
                request.data.pop("incident_witness", None)
            else:
                return Response(
                    {"status": "failed", "message": message},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if "persons_injured" in request.data:
            # persons_injured_data = request.data.get("persons_injured", [])
            incident, created, message = handle_person_injured(
                request.data, WorkPlaceViolenceVersion, new_version.id
            )
            if created:

                request.data.pop("persons_injured")
            else:
                return Response(
                    {"status": "failed", "message": message},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        if "name_of_supervisor" in request.data:
            incident, success, message = handle_name_of_supervisor(
                request.data["name_of_supervisor"], new_version
            )
            if not success:
                # raise
                return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
            request.data.pop("name_of_supervisor", None)

        if "reported_by" in request.data:

            incident, created, message = handle_reported_by(
                request.data, new_version.id, WorkPlaceViolenceVersion
            )

            if created:
                request.data.pop("reported_by", None)
            else:
                return Response(
                    {"status": "failed", "message": message},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        request.data["status"] = request.data.get("status", "Draft")
        incident.is_modified = True
        incident.updated_by = user
        incident.status = request.data.get("status", "Draft")
        incident.save()

        for key, value in request.data.items():
            setattr(new_version, key, value)
        new_version.updated_by = user
        new_version.report_facility = report_facility
        new_version.save()

        if "status" in request.data and request.data.get("status") == "Open":
            send_incident_submission_email(
                incident=incident,
                incident_type="Workplace Violence Report",
            )
        updated_version = WorkPlaceViolenceVersionSerializer(new_version)
        return Response(
            {
                "status": "success",
                "message": "Incident was successfully updated",
                "incident": updated_version.data,
            },
            status=status.HTTP_200_OK,
        )

    except WorkPlaceViolence.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )


# updating initiated_by field
def update_initiated_by(data, incident_id):
    try:
        incident = WorkPlaceViolence.objects.get(id=incident_id)
        if len(data) > 0:
            initiated_by = []
            for party in data:
                new_incident, created = IncidentInvolvedParty.objects.get_or_create(
                    name=party["name"],
                    title=party["title"],
                    phone_number=party["phone_number"],
                    email=party["email"],
                    assailant_relationship_to_patient=party[
                        "assailant_relationship_to_patient"
                    ],
                    assailant_background=party["assailant_background"],
                )
                initiated_by.append(new_incident.id)
            if len(initiated_by) > 0:
                incident.initiated_by.add(*initiated_by)
                incident.save()
                return (True, "Initiated_by field updated successfully")
            else:
                return (False, "Data already exists")
        else:
            return (False, "Data is empty")
    except Exception as e:
        return (False, "Internal server error")


# updating termination of incident field
def update_termination_of_incident(data, incident_id):
    try:
        incident = WorkPlaceViolence.objects.get(id=incident_id)
        if len(data) > 0:
            termination_of_incident = []
            for party in data:
                new_incident, created = TerminationOfIncident.objects.get_or_create(
                    description=party["description"]
                )
                termination_of_incident.append(new_incident.id)
                if len(termination_of_incident) > 0:
                    incident.termination_of_incident.add(*termination_of_incident)
                    incident.save()
                    return (True, "Incident terminated successfully")
                else:
                    return (False, "Data already exists")
        else:
            return (False, "Data is empty")

    except Exception as e:
        return (False, "Internal server error")


# updating person_injured field
def update_person_injured(data, incident_id):
    try:
        incident = WorkPlaceViolence.objects.get(id=incident_id)
        if "person_injured" in data:
            persons_injured_data = data.get("persons_injured", [])

            for person in persons_injured_data:
                user_data = person.get("user_data")
                if not user_data:
                    return Response(
                        {"error": "user data is missing"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                first_name = user_data.get("first_name")
                last_name = user_data.get("last_name")
                username = f"{first_name}_{last_name}"
                user, _ = User.objects.get_or_create(
                    first_name=user_data.get("first_name"),
                    last_name=user_data.get("last_name"),
                    username=generate_username(username),
                )
                profile, _ = Profile.objects.get_or_create(user=user)
                person_injured = IncidentPersonInjured.objects.create(
                    name=profile, injury_description=person.get("injury_description")
                )

                incident.persons_injured.set(person_injured)

            incident.save()
        return incident, True, "Incident data updated successfully"
    except WorkPlaceViolence.DoesNotExist:
        return None, False, "Incident was not found"
    except Exception as e:
        return None, False, "Internal server error"


# updating incident_witness field
def update_incident_witness(data, incident_id):
    try:
        incident = WorkPlaceViolence.objects.get(id=incident_id)
        if len(data) > 0:
            incident_witness = []
            for party in data:
                new_incident, created = IncidentWitness.objects.get_or_create(
                    name=party["name"],
                    phone_number=party["phone_number"],
                    address=party["address"],
                )
                incident_witness.append(new_incident.id)
                if len(incident_witness) > 0:
                    incident.incident_witness.add(*incident_witness)
                    incident.save()
                    return (True, "Person injured updated successfully")
                else:
                    return (False, "Data already exists")
        else:
            return (False, "Data is empty")
    except Exception as e:
        return (False, "Internal server error")
