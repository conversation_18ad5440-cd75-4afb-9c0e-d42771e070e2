from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON>arser
from base.services.auth import verify_user
from base.services.forms import check_missing_fields, check_user_facility
from drf_spectacular.utils import extend_schema

from base.services.notifications import save_notification
from workplace_violence_reports.serializers import WorkPlaceViolenceSerializer


@extend_schema(
    request=WorkPlaceViolenceSerializer, responses=WorkPlaceViolenceSerializer
)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_workplace_violence_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    facility, has_facility = check_user_facility(request.data, user)

    if has_facility:
        request.data["report_facility"] = facility.id
    else:
        return facility
    try:
        request.data["created_by"] = user.id
        new_workplace_violence = WorkPlaceViolenceSerializer(data=request.data)
        if new_workplace_violence.is_valid():
            new_workplace_violence.save()
            save_notification(
                facility=facility,
                group_name="Admin",
                notification_type="info",
                notification_category="incident",
                message="A new incident is submitted",
                item_id=new_workplace_violence.data["id"],
            )
            return Response(
                {
                    "status": "success",
                    "message": "Workplace Violence created successfully",
                    "workplace_violence": new_workplace_violence.data,
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"status": "failed", "message": new_workplace_violence.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Exception as e:
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )
