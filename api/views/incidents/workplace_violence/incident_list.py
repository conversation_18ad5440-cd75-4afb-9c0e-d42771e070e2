# views.py

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from drf_spectacular.utils import extend_schema
from base.services.logging.logger import LoggingService
from workplace_violence_reports.serializers import (
    WorkPlaceViolenceListObjectSerializer,
    WorkPlaceViolenceObjectSerializer,
)
from workplace_violence_reports.services.get_incidents import WorkPlaceViolenceService

workplace_service = WorkPlaceViolenceService()

logging_service = LoggingService()


@extend_schema(
    request=WorkPlaceViolenceObjectSerializer,
    responses=WorkPlaceViolenceObjectSerializer,
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def workplace_violence_api(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        try:
            facility_id = request.query_params.get("facility_id")
            department_id = request.query_params.get("department_id")

            incidents = workplace_service.get_incidents_list(
                user,
                facility_id=facility_id,
                department_id=department_id,
            )

            if not incidents.success:
                return Response(
                    {"error": incidents.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            serializer = WorkPlaceViolenceListObjectSerializer(
                incidents.data, many=True
            )
            return Response({"incidents": serializer.data}, status=status.HTTP_200_OK)
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
            )
