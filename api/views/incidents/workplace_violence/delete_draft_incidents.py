from rest_framework import status
from rest_framework.decorators import api_view
from workplace_violence_reports.models import WorkPlaceViolence
from rest_framework.response import Response
from base.services.auth import verify_user
from incidents.views.delete_drafts import delete_drafts


@api_view(["DELETE"])
def delete_workplace_violence_draft_incidents(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    incident_ids = request.data.get("incident_ids", None)
    user_id = request.data.get("user_id", None)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    return delete_drafts(
        user=user,
        incident_model=WorkPlaceViolence,
        user_id=user_id,
        incident_ids=incident_ids,
    )
