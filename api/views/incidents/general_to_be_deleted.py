# views.py

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from incidents.models.general_patient_visitor import Incident
from general_patient_visitor.serializers import IncidentSerializer
from incidents.models.general_patient_visitor import *


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_incidents(request):
    if request.method == "GET":
        incidents_data = Incident.objects.all()
        incidents = IncidentSerializer(incidents_data).data
        return Response({"incidents": incidents}, status=status.HTTP_200_OK)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_incident(request):
    if request.method == "POST":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        required_fields = [
            "category",
            "patient_visitor_name",
            "incident_date",
            "incident_time",
            "medical_record_number",
            "address",
            "state",
            "zip_code",
            "phone_number",
        ]

        missing_fields_response = check_missing_fields(
            data=request.data, required_fields=required_fields
        )
        if missing_fields_response:
            return missing_fields_response

        try:
            new_incident = IncidentSerializer(data=request.data)
            if new_incident.is_valid():
                new_incident.save()
                return Response(
                    {
                        "status": "success",
                        "message": "Incident created successfully",
                        "incident": new_incident.data,
                    },
                    status=status.HTTP_201_CREATED,
                )
            else:
                return Response(
                    {
                        "message": "Failed to create incident",
                        "errors": new_incident.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            return Response(
                {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
            )


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def update_incident_api(request, incident_id):
    if request.method == "PATCH":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        # getting data from the request
        data = request.data

        # checking missing fields
        required_fields = []

        if "location" in data and data["location"] is not None:
            required_fields = [
                "location",
                "consulting_diagnosis",
                "patient_status",
            ]

        elif "incident_type" in data and data["incident_type"] is not None:
            if data["incident_type"] == "fall":
                required_fields = [
                    "incident_type",
                    "fall_related_type",
                    "fell_from",
                    "morse_fall_score",
                    "fall_type_other",
                    "fall_type_agreement",
                ]
            elif data["incident_type"] == "treatment":
                required_fields = ["treatment_type"]

            elif data["incident_type"] == "equipment":
                required_fields = [
                    "equipment_malfunction",
                    "removed_from_service",
                    "engineering_staff_notified",
                    "equipment_serial_number",
                    "equipment_lot_number",
                ]

            elif data["incident_type"] == "other":
                required_fields = [
                    "fall_type_other_text",
                    "other_type_specimen",
                ]

        elif "outcome" in data and data["outcome"] is not None:
            required_fields = [
                "name_of_physician_notified",
                "reason_for_escalation",
                "date_physician_notified",
                "time_physician_notified",
                "name_of_person_notified",
                "date_family_notified",
                "time_family_notified",
                "notified_by_name",
            ]

        check_missing_fields_response = check_missing_fields(
            required_fields=required_fields, data=data
        )
        if check_missing_fields_response:
            return check_missing_fields_response

        # updating the incident in the database
        try:
            incident = Incident.objects.get(id=incident_id)
            data["updated_by"] = user.id
            updated_incident = IncidentSerializer(
                incident, data=request.data, partial=True
            )

            if updated_incident.is_valid():
                updated_incident.save()

                return Response(
                    {
                        "status": "success",
                        "message": "Incident updated successfully",
                        "incident": updated_incident.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "message": "Failed to update incident",
                        "errors": updated_incident.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Incident.DoesNotExist:
            return Response(
                {"error": "Incident not found"}, status=status.HTTP_404_NOT_FOUND
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def create_witness(request):
    if request.method == "POST":
        data = request.data
        try:
            incident_id = data.get("incident")
            incident = Incident.objects.get(id=incident_id)
            witness = GeneralWitness.objects.create(
                incident=incident, name=data.get("name")
            )
            return Response(
                {"message": "Witness created successfully", "witness_id": witness.id},
                status=status.HTTP_201_CREATED,
            )
        except Incident.DoesNotExist:
            return Response(
                {"error": "Incident not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
            )
