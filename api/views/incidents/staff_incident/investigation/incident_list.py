from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from staff_incident_reports.models import StaffIncidentInvestigation

from staff_incident_reports.serializers import (
    GetHealthInvestigationSerializer,
    HealthInvestigationSerializer,
)
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import <PERSON><PERSON><PERSON>ars<PERSON>


@extend_schema(
    request=HealthInvestigationSerializer, responses=HealthInvestigationSerializer
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def list_incidents(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        incidents = StaffIncidentInvestigation.objects.all().order_by("-created_at")
        serializer = GetHealthInvestigationSerializer(incidents, many=True)
        return Response({"incidents": serializer.data}, status=status.HTTP_200_OK)
    else:
        return Response(
            {"message": "Invalid request"}, status=status.HTTP_400_BAD_REQUEST
        )
