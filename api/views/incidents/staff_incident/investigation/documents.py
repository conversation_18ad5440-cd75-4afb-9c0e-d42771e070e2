from documents.views import (
    delete_incident_document,
    get_incident_documents,
    handle_incident_document,
)
from staff_incident_reports.models import StaffIncidentInvestigation
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.services.auth import verify_user
from documents.models import Document
from api.views.auth.permissions_list import has_permissions


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def employee_health_investigation_new_document_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    try:

        files = request.FILES.getlist("files")
        folder_name = "adverse_drug_reaction"
        incident = StaffIncidentInvestigation.objects.get(id=incident_id)
        response_data = handle_incident_document(files, folder_name, incident, user)
        return Response(response_data, status=status.HTTP_200_OK)
    except StaffIncidentInvestigation.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def employee_health_investigation_document_list_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    documents, found = get_incident_documents(StaffIncidentInvestigation, incident_id)
    if found:
        return Response(documents, status=status.HTTP_200_OK)
    else:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_health_investigation_documents(request, incident_id, document_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident = StaffIncidentInvestigation.objects.get(id=incident_id)

        success, message = delete_incident_document(incident, document_id)

        if success:
            return Response(
                {
                    "message": "Document deleted successfully",
                    "deleted document": document_id,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
    except StaffIncidentInvestigation.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Exception as e:
        return Response(
            {"error": f"Failed to delete document"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
