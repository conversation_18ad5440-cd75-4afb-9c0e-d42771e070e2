from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from staff_incident_reports.models import StaffIncidentInvestigation
from staff_incident_reports.serializers import HealthInvestigationSerializer
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser
from api.views.auth.permissions_list import has_permissions
from api.views.incidents.general_incident.new_incident import get_patient_profile

logging_service = LoggingService()


# updating the whole incident
@extend_schema(
    request=HealthInvestigationSerializer,
    responses=StaffIncidentInvestigation,
)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([J<PERSON><PERSON>arser])
def modify_employee_health_investigation(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    request_data = request.data.copy()

    try:
        incident = StaffIncidentInvestigation.objects.get(id=incident_id)

        if "name_of_injured_staff" in request.data:
            name_of_injured_staff_profile, message = get_patient_profile(
                data=request.data["doctor_info"], facility=incident.report_facility
            )
            if name_of_injured_staff_profile:
                request_data["name_of_injured_staff"] = name_of_injured_staff_profile.id

        if "doctor_info" in request.data:
            doctor_info_profile, message = get_patient_profile(
                data=request.data["doctor_info"], facility=incident.report_facility
            )
            if doctor_info_profile:
                request_data["doctor_info"] = doctor_info_profile.id

        if not has_permissions(user, ["Staff Health Nurse"]):
            return Response(
                {"error": "You do not update this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
        modified_incident = HealthInvestigationSerializer(
            incident, data=request_data, partial=True
        )

        if modified_incident.is_valid():
            modified_incident.save()
            return Response(
                {
                    "status": "success",
                    "message": "Incident was successfully updated",
                    "incident": modified_incident.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            logging_service.log_error(modified_incident.errors)
            return Response(
                {
                    "status": "failed",
                    "error": f"failed to update incident {modified_incident.errors}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except StaffIncidentInvestigation.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
