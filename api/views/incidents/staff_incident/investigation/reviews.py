from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>NParser
from api.views.get_reviews import get_incident_reviews
from api.views.new_review import create_review
from base.services.auth import verify_user
from staff_incident_reports.models import StaffIncidentInvestigation


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_employee_health_investigation_review_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    if request.data.get("content") is None:
        return Response({"message": "content is missing"})

    response = create_review(
        "incidents",
        "StaffIncidentInvestigation",
        incident_id,
        user,
        request.data.get("content"),
    )

    return response


@api_view(["GET"])
def employee_health_investigation_reviews_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    response = get_incident_reviews(
        user, "StaffIncidentInvestigation", "incidents", incident_id
    )

    return response
