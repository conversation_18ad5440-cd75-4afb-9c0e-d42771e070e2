from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from base.services.forms import check_missing_fields, check_user_facility
from base.services.logging.logger import LoggingService
from staff_incident_reports.models import StaffIncidentReport
from staff_incident_reports.models import StaffIncidentInvestigation
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import <PERSON><PERSON><PERSON>ars<PERSON>
from staff_incident_reports.serializers import (
    GetHealthInvestigationSerializer,
    HealthInvestigationSerializer,
)
from api.views.auth.permissions_list import has_permissions
from api.views.incidents.general_incident.new_incident import get_patient_profile
from django.forms.models import model_to_dict

logging_service = LoggingService()


@extend_schema(
    request=HealthInvestigationSerializer, responses=HealthInvestigationSerializer
)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON>ars<PERSON>])
def new_employee_health_incident_investigation(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    if not has_permissions(user, ["Staff Health Nurse"]):
        return Response(
            {
                "status": "failed",
                "error": "You do not have permissions to report Staff health investigation incident",
            },
            status=status.HTTP_403_FORBIDDEN,
        )

    facility, has_facility = check_user_facility(request.data, user)

    if has_facility:
        request.data["report_facility"] = facility
    else:
        return facility

    required_fields = [
        "name_of_injured_staff",
        "date_of_hire",
        "marital_status",
        "part_of_body_injured",
        "nature_of_injury",
    ]

    missing_fields_response = check_missing_fields(
        data=request.data, required_fields=required_fields
    )

    if missing_fields_response:
        return missing_fields_response

    request_data = request.data.copy()

    try:
        incident = StaffIncidentReport.objects.get(id=incident_id)
        if StaffIncidentInvestigation.objects.filter(employee_report=incident):
            return Response(
                {"error": "An investigation incident already exists for this report"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        request.data["created_by"] = user.id

        name_of_injured_staff_profile = None

        if "name_of_injured_staff" in request.data:
            name_of_injured_staff_profile, message = get_patient_profile(
                data=request.data["name_of_injured_staff"], facility=facility
            )

            if not name_of_injured_staff_profile:
                return Response(
                    {"error": "Invalid injured staff data"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        new_incident = {
            "name_of_injured_staff": name_of_injured_staff_profile,
            "date_of_hire": request_data["date_of_hire"],
            "marital_status": request_data["marital_status"],
            "part_of_body_injured": request_data["part_of_body_injured"],
            "nature_of_injury": request_data["nature_of_injury"],
            "report_facility": facility,
            "created_by": user,
            "employee_report": incident,
            "witnesses": request.data.get("witnesses", None),
        }

        incident = StaffIncidentInvestigation.objects.create(**new_incident)

        incident_data = GetHealthInvestigationSerializer(incident).data

        return Response({"status": "success", "incident": incident_data})
    except StaffIncidentReport.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to create incident"}, status=status.HTTP_400_BAD_REQUEST
        )
