from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from staff_incident_reports.models import StaffIncidentInvestigation
from staff_incident_reports.serializers import (
    GetHealthInvestigationSerializer,
    HealthInvestigationSerializer,
)
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import <PERSON><PERSON>NParser
from base.services.forms import check_missing_fields
from incidents.views.send_to_department import send_incident_to_department
from api.views.auth.permissions_list import has_permissions
from api.views.incidents.general_incident.new_incident import get_patient_profile
from django.forms.models import model_to_dict

logging_service = LoggingService()


@extend_schema(
    request=HealthInvestigationSerializer, responses=HealthInvestigationSerializer
)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON>ars<PERSON>])
def update_incident(request, id):
    if request.method == "PATCH":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        request_data = request.data.copy()

        try:
            health = StaffIncidentInvestigation.objects.get(id=id)

            if "doctor_info" in request.data:
                doctor_info_profile, message = get_patient_profile(
                    data=request.data["doctor_info"], facility=health.report_facility
                )
                if doctor_info_profile:
                    request_data["doctor_info"] = doctor_info_profile.id

            serializer = HealthInvestigationSerializer(
                health, data=request_data, partial=True
            )

            if serializer.is_valid():
                serializer.save()
                saved_data = GetHealthInvestigationSerializer(health)
                return Response(
                    {
                        "message": "Health Investigation updated successfully",
                        "data": saved_data.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
            )

    else:
        return Response(
            {"error": "Invalid request"}, status=status.HTTP_400_BAD_REQUEST
        )


# marking incident as resolved
@extend_schema(
    request=HealthInvestigationSerializer, responses=StaffIncidentInvestigation
)
@api_view(["PUT"])
@parser_classes([JSONParser])
def mark_employee_health_investigation_incident_as_resolved(
    request, health_investigation_id
):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    data["status"] = "Closed"

    required_fields = ["status"]

    check_missing_fields_response = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_response:
        return check_missing_fields_response

    try:
        incident = StaffIncidentInvestigation.objects.get(id=health_investigation_id)

        if not has_permissions(
            user,
            ["Manager"],
        ):
            return Response(
                {"error": "You do not have permission to close/resolve this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )

        incident.updated_by = user
        incident.is_resolved = True
        return Response(
            {
                "status": "success",
                "message": "Incident was successfully resolved",
                "incident": {
                    "id": incident.id,
                    "is_resolved": incident.is_resolved,
                },
            },
            status=status.HTTP_200_OK,
        )

    except StaffIncidentInvestigation.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )


# send incident to department
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def send_employee_health_investigation_to_department(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    try:
        incident = StaffIncidentInvestigation.objects.get(id=incident_id)

        if not has_permissions(
            user,
            ["Manager"],
        ):
            return Response(
                {
                    "error": "You do not have permission to send this incident to the department"
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        is_sent, message = send_incident_to_department(data, incident, user)
        if is_sent:
            return Response(
                {
                    "status": "success",
                    "message": message,
                    "incident": HealthInvestigationSerializer(incident).data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except StaffIncidentInvestigation.DoesNotExist:
        return Response(
            {"error": f"incident with id '{incident_id}' is not found"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )
