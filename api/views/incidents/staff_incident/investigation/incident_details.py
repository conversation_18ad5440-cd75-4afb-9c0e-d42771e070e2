from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from staff_incident_reports.models import StaffIncidentInvestigation
from staff_incident_reports.serializers import (
    GetHealthInvestigationSerializer,
    HealthInvestigationSerializer,
)
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser
from api.views.auth.permissions_list import has_permissions


@extend_schema(
    request=HealthInvestigationSerializer, responses=HealthInvestigationSerializer
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON>ars<PERSON>])
def incident_details(request, id):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        try:
            health_incident = StaffIncidentInvestigation.objects.get(id=id)

            if (
                not has_permissions(
                    user,
                    ["Manager", "Department Head", "Admin", "Developer"],
                )
                and not health_incident.created_by == user
            ):
                return Response(
                    {"error": "You do not have permission to access this incident"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            data = request.data
            health_incident.accident_details = data.get("witnesses")
            health_incident.employee_prior_activity = data.get(
                "employee_prior_activity"
            )
            health_incident.equipment_or_tools = data.get("equipment_or_tool")
            health_incident.witnesses = data.get("witness")
            health_incident.incident_date = data.get("date_of_even")
            health_incident.time_of_event = data.get("time_of_event")

            serializer = HealthInvestigationSerializer(
                health_incident, data=request.data, partial=True
            )

            if serializer.is_valid():
                health_incident_update = serializer.save()
                health_incident_data = GetHealthInvestigationSerializer(
                    health_incident_update
                ).data

                return Response(
                    {
                        "message": "Health Investigation updated successfully",
                        "data": health_incident_data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST
                )
        except StaffIncidentInvestigation.DoesNotExist:
            return Response(
                {"error": "Health Investigation not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
            )

    else:
        return Response(
            {"error": "Invalid request"}, status=status.HTTP_400_BAD_REQUEST
        )
