from rest_framework.decorators import api_view, parser_classes, permission_classes
from rest_framework.response import Response
from rest_framework import status
from api.views.incidents.general_incident.new_incident import get_patient_profile
from base.services.forms import check_missing_fields, check_user_facility
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from incidents.serializers.employee_serializers import (
    FullReportSerializer,
    InitialReportSerializer,
    IncidentDescriptionSerializer,
    FinalReportSerializer,
    ReportCompletedSerializer,
    IDResponseSerializer,
)
from staff_incident_reports.models import StaffIncidentReport
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import J<PERSON>NParser
from drf_spectacular.utils import extend_schema
from base.services.auth import verify_user
from rest_framework.permissions import IsAuthenticated
from incidents.views.send_to_department import send_incident_submission_email

logging_service = LoggingService()


@extend_schema(request=InitialReportSerializer, responses=InitialReportSerializer)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([J<PERSON><PERSON>ars<PERSON>])
def initial_report(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    facility, has_facility = check_user_facility(request.data, user)
    if has_facility:
        request.data["report_facility"] = facility
    else:
        return facility

    required_fields = [
        "incident_status",
        "patient_info",
        "job_title",
        "time_of_injury_or_near_miss",
    ]

    missing_fields_response = check_missing_fields(
        data=request.data, required_fields=required_fields
    )
    if missing_fields_response:
        return missing_fields_response
    request_data = request.data.copy()

    try:
        request.data["created_by"] = user.id

        if "patient_info" in request.data:
            patient_info_profile, message = get_patient_profile(
                data=request.data["patient_info"], facility=facility
            )
            if patient_info_profile:
                request_data["patient_info"] = patient_info_profile.id
            request_data.pop("patient_info")

        new_incident = StaffIncidentReport.objects.create(
            created_by=user,
            report_facility=facility,
            incident_status=request.data["incident_status"],
            patient_info=patient_info_profile,
            job_title=request_data["job_title"],
            date_of_injury_or_near_miss=request_data["date_of_injury_or_near_miss"],
            time_of_injury_or_near_miss=request_data["time_of_injury_or_near_miss"],
        )
        save_notification(
            facility=facility,
            group_name="Admin",
            notification_type="info",
            notification_category="incident",
            message="A new incident is submitted",
            item_id=new_incident.id,
        )

        incident_data = FullReportSerializer(new_incident).data
        return Response({"incident": incident_data}, status=status.HTTP_201_CREATED)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


@extend_schema(request=IncidentDescriptionSerializer, responses=IDResponseSerializer)
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def update_incident_description(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    try:
        report = StaffIncidentReport.objects.get(id=request.data["id"])
    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Not Found"}, status=status.HTTP_404_NOT_FOUND)

    serializer = IncidentDescriptionSerializer(report, data=request.data, partial=True)
    if serializer.is_valid():
        instance = serializer.save()
        return Response({"id": instance.id}, status=status.HTTP_200_OK)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(request=FinalReportSerializer, responses=IDResponseSerializer)
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def update_final_report(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    request_data = request.data.copy()
    try:
        report = StaffIncidentReport.objects.get(id=request.data["id"])
        if "doctor_consulted_info" in request.data:
            doctor_consulted_info_profile, message = get_patient_profile(
                data=request.data["doctor_consulted_info"],
                facility=report.report_facility,
            )
            if doctor_consulted_info_profile:
                request_data["doctor_consulted_info"] = doctor_consulted_info_profile.id
            if "patient_info" in request.data:
                patient_info_profile, message = get_patient_profile(
                    data=request.data["patient_info"], facility=report.report_facility
                )

                if patient_info_profile:
                    request_data["patient_info"] = patient_info_profile.id
    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Not Found"}, status=status.HTTP_404_NOT_FOUND)

    serializer = FinalReportSerializer(report, data=request_data, partial=True)
    if serializer.is_valid():
        serializer.save()
        if "status" in request.data and request.data.get("status") == "Open":
            send_incident_submission_email(
                incident=report,
                incident_type="Staff incident report",
            )
        return Response({"incident": serializer.data}, status=status.HTTP_200_OK)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(request=ReportCompletedSerializer, responses=IDResponseSerializer)
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def update_report_completed(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    try:
        report = StaffIncidentReport.objects.get(id=request.data["id"])
    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Report not found"}, status=status.HTTP_404_NOT_FOUND)

    serializer = ReportCompletedSerializer(report, data=request.data, partial=True)
    if serializer.is_valid():
        instance = serializer.save()
        return Response(
            {"id": instance.id, "report_completed": instance.report_completed},
            status=status.HTTP_200_OK,
        )
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
