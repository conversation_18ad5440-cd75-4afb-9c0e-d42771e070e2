from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from documents.models import Document
from documents.views import (
    delete_incident_document,
    get_incident_documents,
    handle_incident_document,
    upload_file,
)
from staff_incident_reports.models import StaffIncidentReport
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)

logging_service = LoggingService()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def employee_incident_new_document_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    files = request.FILES.getlist("files")
    folder_name = "Staff incident"
    try:
        incident = StaffIncidentReport.objects.get(id=incident_id)

        data = handle_incident_document(files, folder_name, incident, user)
        return Response(data, status=status.HTTP_201_CREATED)
    except StaffIncidentReport.DoesNotExist:
        return Response(
            {"error": "Failed to upload file(s)"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to upload file"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def employee_incident_new_documents_list_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    documents, found = get_incident_documents(StaffIncidentReport, incident_id)
    if found:
        return Response(documents, status=status.HTTP_200_OK)
    else:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_employee_incident_documents(request, incident_id, document_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident = StaffIncidentReport.objects.get(id=incident_id)

        success, message = delete_incident_document(incident, document_id)

        if success:
            return Response(
                {
                    "message": "Document deleted successfully",
                    "deleted document": document_id,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
    except StaffIncidentReport.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": f"Failed to delete document"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
