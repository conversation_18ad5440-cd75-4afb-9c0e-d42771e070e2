from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.services.forms import check_user_facility
from reviews.services.review import ReviewsOperations
from staff_incident_reports.models import StaffIncidentReport
from staff_incident_reports.serializers import StaffIncidentReportSerializer
from staff_incident_reports.services.actions import StaffIncidentReportActionService
from staff_incident_reports.services.documents import StaffDocumentService
from staff_incident_reports.services.operations import (
    StaffIncidentInvestigationService,
    StaffIncidentReportService,
)
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService


logging_service = LoggingService()
incidents_service = GetIncidentsService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def staff_incident_report_api(request):
    """
    API view for handling Staff Incident Reports.
    """
    staff_incident_service = StaffIncidentReportService(request.user)
    try:
        if request.method == "GET":

            filters = request.query_params.dict()
            incidents = staff_incident_service.get_incidents_list(filters=filters)

            if not incidents.success:
                return Response(
                    {"error": incidents.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incidents.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            response = staff_incident_service.create_incident(request.data)
            if response.success:
                return Response(response.data, status=status.HTTP_201_CREATED)
            else:
                return Response(response.message, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error getting incidents"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "PATCH"])
@permission_classes([IsAuthenticated])
def staff_incident_report_details_api(request, id):
    """
    API view for handling Staff Incident Report details.
    """
    staff_incident_service = StaffIncidentReportService(request.user)
    try:
        action_services = StaffIncidentReportActionService(
            user=request.user,
            incident_id=id,
            data=request.data,
        )
        if request.method == "GET":
            incident = staff_incident_service.get_incident_by_id(incident_id=id)

            if not incident.success:
                return Response(
                    {"error": incident.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incident.data,
                status=status.HTTP_200_OK,
            )
        elif request.method in ["PUT"]:
            response = staff_incident_service.update_incident(
                id=id,
                data=request.data,
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method in ["PATCH"]:
            action = request.data.get("action", None)
            if not action:
                return Response(
                    {
                        "error": "Action is required",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if action == "send-for-review":
                response = action_services.send_for_review()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "mark-closed":
                response = action_services.mark_closed()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "modify":
                response = action_services.modify_incident()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "delete-draft":
                response = action_services.delete_staff_incident_draft_incidents()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": "Invalid action"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
    except StaffIncidentReport.DoesNotExist:
        return Response(
            {"error": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error getting incident details"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def staff_incident_investigation_api(request, id):
    """
    API view for handling Staff Incident Investigations.
    """
    investigation_service = StaffIncidentInvestigationService(request.user)
    try:
        if request.method == "GET":
            filters = request.query_params.dict()
            response = investigation_service.get_investigations_list(
                report_id=id, filters=filters
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )

        elif request.method == "POST":
            response = investigation_service.create_investigation(
                report_id=id, data=request.data
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )

        return Response(
            {"error": "Method not allowed"},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT"])
@permission_classes([IsAuthenticated])
def staff_incident_detail_investigation_api(request, id, investigation_id):
    """
    API view for handling Staff Incident Investigation Details.
    """
    investigation_service = StaffIncidentInvestigationService(request.user)
    try:
        if request.method == "GET":
            response = investigation_service.get_investigation_by_id(
                report_id=id, investigation_id=investigation_id
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )

        elif request.method == "PUT":
            response = investigation_service.update_investigation(
                investigation_id=investigation_id,
                report_id=id,
                data=request.data,
            )
            if not response.success:
                print(f"update_investigation response: {response.message}")
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )

        return Response(
            {"error": "Method not allowed"},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def staff_incident_documents_api(request, incident_id):
    """
    API view for handling documents related to Staff Incident Reports.
    """
    try:
        service = StaffDocumentService(incident_id=incident_id, user=request.user)
        if request.method == "GET":
            params = request.query_params.dict()
            response = service.get_documents(params=params)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            files = request.FILES.getlist("files")
            response = service.create_document(files=files)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error processing document request"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def staff_incident_reviews_api(request, incident_id):
    """
    API view for handling reviews related to Staff Incident Reports.
    """
    service = ReviewsOperations(
        user=request.user,
        model_name="StaffIncidentReport",
        app_label="staff_incident_reports",
        incident_id=incident_id,
    )
    try:
        if request.method == "GET":
            response = service.get_incident_reviews()
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            content = request.data.get("content")
            if not content:
                return Response(
                    {"error": "Content is required for the review."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            response = service.create_review(content)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
