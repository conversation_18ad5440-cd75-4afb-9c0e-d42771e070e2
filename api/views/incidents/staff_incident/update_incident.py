# add your view here
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.response import Response
from rest_framework import status
from api.views.incidents.general_incident.new_incident import check_anonymous
from api.views.incidents.staff_incident.new_incident import handle_witness
from base.services.incidents.base import IncidentService
from staff_incident_reports.models import StaffIncidentReport
from incidents.serializers.employee_serializers import FullReportSerializer
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import <PERSON><PERSON><PERSON>arser
from base.services.auth import verify_user
from rest_framework.permissions import IsAuthenticated
from base.services.forms import check_missing_fields

from incidents.views.send_to_department import (
    send_incident_submission_email,
)
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import get_patient_profile

incident_service = IncidentService()


@extend_schema(request=FullReportSerializer, responses=FullReportSerializer)
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_report(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    request_data = request.data.copy()

    try:
        report = StaffIncidentReport.objects.get(id=request.data["id"])

        if "doctor_consulted_info" in request.data:
            doctor_consulted_info_profile, message = get_patient_profile(
                data=request.data["doctor_consulted_info"],
                facility=report.report_facility,
            )
            if doctor_consulted_info_profile:
                request_data["doctor_consulted_info"] = doctor_consulted_info_profile.id

        if "patient_info" in request.data:
            patient_info_profile, message = get_patient_profile(
                data=request.data["patient_info"], facility=report.report_facility
            )

            if patient_info_profile:
                request_data["patient_info"] = patient_info_profile.id
                request_data.pop("patient_info")
        if "witnesses" in request.data:
            created, witnesses, message, errors = handle_witness(
                data=request.data["witnesses"],
                facility=report.report_facility,
                user=user,
            )

            if not created:
                return Response({"error": errors}, status=status.HTTP_400_BAD_REQUEST)
            report.witnesses.all().delete()
            report.witnesses.set(witnesses)
            request_data.pop("witnesses")
    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Not Found"}, status=status.HTTP_404_NOT_FOUND)
    data = check_anonymous(request_data, user)
    serializer = FullReportSerializer(report, data=data, partial=True)
    if serializer.is_valid():
        instance = serializer.save()
        if "status" in request.data and request.data.get("status") == "Open":
            send_incident_submission_email(
                incident=report, incident_type="Staff incident report"
            )
        data = FullReportSerializer(instance)
        return Response(data.data, status=status.HTTP_200_OK)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# mark incident as resolved
@extend_schema(request=FullReportSerializer, responses=StaffIncidentReport)
@api_view(["PUT"])
@parser_classes([JSONParser])
def mark_employee_incident_as_resolved(request, employee_incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    data["status"] = "Closed"

    required_fields = ["status"]

    check_missing_fields_response = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_response:
        return check_missing_fields_response

    try:
        incident = StaffIncidentReport.objects.get(id=employee_incident_id)

        response = incident_service.mark_as_resolved(
            incident,
            user,
        )
        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": "Incident was successfully resolved",
                "incident": {
                    "id": incident.id,
                    "is_resolved": incident.is_resolved,
                },
            },
            status=status.HTTP_200_OK,
        )

    except StaffIncidentReport.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def send_employee_report_to_department(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data
    try:
        incident = StaffIncidentReport.objects.get(id=incident_id)

        if not is_super_user(user) and not is_admin_user(
            user, incident.report_facility
        ):
            return Response(
                {
                    "error": "You do not have permission to send this incident to the department"
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        incident_type = ("Staff incident report",)
        response = incident_service.send_incident_to_department(
            data, incident, incident_type, user
        )
        if not response.success:
            return Response(
                {"status": "failed", "message": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": message,
                "incident": FullReportSerializer(incident).data,
            },
            status=status.HTTP_200_OK,
        )
    except StaffIncidentReport.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
