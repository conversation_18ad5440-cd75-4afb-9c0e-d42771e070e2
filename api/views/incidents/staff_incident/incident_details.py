from rest_framework.decorators import api_view, parser_classes, permission_classes
from rest_framework.response import Response
from rest_framework import status
from base.services.incidents.get_incidents import GetIncidentsService
from staff_incident_reports.models import StaffIncidentReport
from staff_incident_reports.models import StaffIncidentInvestigation
from staff_incident_reports.serializers import (
    GetHealthInvestigationSerializer,
)
from incidents.serializers.employee_serializers import (
    FullReportSerializer,
)
from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from rest_framework.parsers import JSONParser
from base.services.auth import verify_user
from rest_framework.permissions import IsAuthenticated
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)

incidents_service = GetIncidentsService()


@extend_schema(
    parameters=[
        OpenApiParameter(
            "id", OpenApiTypes.INT, description="ID parameter", required=True
        ),
    ],
    responses=FullReportSerializer,
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def retrieve_report(request, id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    investigation_data = {}
    try:
        incident = StaffIncidentReport.objects.get(id=id)
        if StaffIncidentInvestigation.objects.filter(employee_report=incident):
            investigation = StaffIncidentInvestigation.objects.filter(
                employee_report=incident
            ).first()

            investigation_data = GetHealthInvestigationSerializer(investigation).data
        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_manager_user(user, incident.department)
            and not incident.created_by == user
        ):
            return Response(
                {"error": "You do not have permission to access this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Report not found"}, status=status.HTTP_404_NOT_FOUND)

    success, incident, modifications, message = incidents_service.get_latest_version(
        incident_data=incident,
        modelName=StaffIncidentReport,
        incidentSerializer=FullReportSerializer,
        incident_id=id,
    )
    if success:
        return Response(
            {
                "incident": incident,
                "investigation": investigation_data,
                "modifications": modifications,
            },
            status=status.HTTP_200_OK,
        )
    else:
        return Response({"message": message}, status=status.HTTP_400_BAD_REQUEST)
