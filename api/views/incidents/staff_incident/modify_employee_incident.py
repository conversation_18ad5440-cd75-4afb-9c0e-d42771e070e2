from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.response import Response
from rest_framework import status
from api.views.incidents.general_incident.new_incident import get_patient_profile
from base.services.logging.logger import LoggingService
from staff_incident_reports.models import StaffIncidentReport
from incidents.serializers.employee_serializers import (
    FullReportSerializer,
    ModifyReportSerializer,
    ModifyReportVersionSerializer,
)
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser
from base.services.auth import verify_user
from rest_framework.permissions import IsAuthenticated
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from incidents.views.send_to_department import send_incident_submission_email


logging_service = LoggingService()


# updating the whole incident
@extend_schema(
    request=FullReportSerializer,
    responses=StaffIncidentReport,
)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON>ars<PERSON>])
def modify_employee_incident(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    request_data = request.data.copy()
    report_facility = None
    try:
        incident = StaffIncidentReport.objects.get(id=incident_id)
        report_facility = incident.report_facility

        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_manager_user(user, incident.department)
        ) and not incident.created_by == user:
            return Response(
                {"error": "You do not have permission to update the incident"},
                status=status.HTTP_403_FORBIDDEN,
            )

        if "patient_info" in request.data:
            patient_info_profile, message = get_patient_profile(
                data=request.data["patient_info"], facility=incident.report_facility
            )
            if patient_info_profile:
                request_data["patient_info"] = patient_info_profile.id

        if "doctor_consulted_info" in request.data:
            doctor_consulted_info_profile, message = get_patient_profile(
                data=request.data["doctor_consulted_info"],
                facility=incident.report_facility,
            )
            if doctor_consulted_info_profile:
                request_data["doctor_consulted_info"] = doctor_consulted_info_profile.id
        if "witnesses" in request.data:
            if not (len(request.data["witnesses"])) > 0:
                incident.witnesses.all().delete()
                request_data.pop("witnesses")
            else:
                incident.witnesses.all().delete()
                request_data.pop("witnesses")

        facility = incident.report_facility
        request_data["original_report"] = incident.id
        request_data["report_facility"] = facility.id
        request_data["created_by"] = user.id
        request_data["status"] = request.data.get("status", "Draft")
        new_version = ModifyReportVersionSerializer(data=request_data)

        if new_version.is_valid():
            new_version.save()
            incident.is_modified = True
            incident.updated_by = user
            incident.status = request.data.get("status", "Draft")
            incident.save()
            if "status" in request.data and request.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=incident,
                    incident_type="Staff incident Report",
                )
            return Response(
                {
                    "status": "success",
                    "message": "Incident was successfully updated",
                    "incident": new_version.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            logging_service.log_error(new_version.errors)
            return Response(
                {
                    "status": "failed",
                    "error": f"failed to update incident {new_version.errors}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except StaffIncidentReport.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
