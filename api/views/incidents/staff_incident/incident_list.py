from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import has_permissions
from api.views.incidents.general_incident.incident_list import (
    get_latest_incident_reports,
    get_limited_access_incident,
)
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService
from staff_incident_reports.models import StaffIncidentReport
from incidents.serializers.employee_serializers import FullReportSerializer
from drf_spectacular.utils import extend_schema
from base.services.auth import verify_user
from rest_framework.permissions import IsAuthenticated

incident_service = GetIncidentsService()
logging_service = LoggingService()


@extend_schema(responses=FullReportSerializer)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def employee_incident_list_api(request):
    incidents_list = []
    facility_id = request.query_params.get("facility_id")
    department_id = request.query_params.get("department_id")

    try:
        incidents = incident_service.get_incidents(
            app_label="staff_incident_reports",
            model_name="StaffIncidentReport",
            user=request.user,
            facility_id=facility_id,
            department_id=department_id,
        )
        if not has_permissions(request.user, ["Admin", "Manager", "Super User"]):
            return Response(
                {"error": "You dont have required permissions"},
                status=status.HTTP_403_FORBIDDEN,
            )
        incidents_list = incidents
        incidents_found, versions, message = get_latest_incident_reports(
            incidents.data, FullReportSerializer
        )
        if incidents_found:
            return Response({"incidents": versions}, status=status.HTTP_200_OK)
        else:
            return Response({"message": message}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logging_service.log_error(e)

        return Response(
            {"an error occurred "}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
