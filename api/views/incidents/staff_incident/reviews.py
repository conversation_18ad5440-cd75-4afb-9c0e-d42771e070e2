from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import JSONParser
from api.views.get_reviews import get_incident_reviews
from api.views.new_review import create_review
from base.services.auth import verify_user
from staff_incident_reports.models import StaffIncidentReport
from reviews.models import Review


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_employee_incident_review_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    if request.data.get("content") is None:
        return Response({"message": "content is missing"})

    response = create_review(
        "staff_incident_reports",
        "StaffIncidentReport",
        incident_id,
        user,
        request.data.get("content"),
    )

    return response


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON>ars<PERSON>])
def employee_incident_reviews_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    response = get_incident_reviews(
        user, "StaffIncidentReport", "staff_incident_reports", incident_id
    )

    return response
