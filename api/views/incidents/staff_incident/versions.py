from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from staff_incident_reports.models import (
    StaffIncidentReport,
    StaffIncidentReportVersion,
)
from incidents.serializers.employee_serializers import (
    FullReportSerializer,
    ModifyReportVersionSerializer,
)
from staff_incident_reports.new_serializers import GetStaffIncidentReportSerializer


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def staff_incident_version(request, incident_id, version_id):
    # ModifyReportVersionSerializer
    # StaffIncidentReportVersion
    try:
        incident = StaffIncidentReportVersion.objects.get(id=version_id)
        serializer = GetStaffIncidentReportSerializer(incident).data
        return Response(serializer, status=status.HTTP_200_OK)
    except StaffIncidentReportVersion.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def staff_incident_original_version(request, incident_id):
    try:
        incident = StaffIncidentReport.objects.get(id=incident_id)
        serializer = FullReportSerializer(incident).data
        return Response(serializer, status=status.HTTP_200_OK)

    except StaffIncidentReport.DoesNotExist:
        return Response({"error": "Report not found"}, status=status.HTTP_404_NOT_FOUND)
