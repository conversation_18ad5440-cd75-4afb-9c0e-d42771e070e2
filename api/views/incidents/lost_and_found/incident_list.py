from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import has_permissions

from api.views.incidents.general_incident.incident_list import (
    get_latest_incident_reports,
    get_limited_access_incident,
)
from base.services.auth import verify_user
from lost_and_found.models import LostAndFound

from lost_and_found.serializers import (
    LostAndFoundLostSerializer,
    LostAndFoundSerializer,
)
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser


@extend_schema(request=LostAndFoundSerializer, responses=LostAndFoundSerializer)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def lost_and_lost_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    incidents_list = []
    lost_and_found_data = LostAndFound.objects.all()

    if not has_permissions(request.user, ["Super User", "Admin", "Manager"]):
        return Response(
            {"error": "You dont have required permissions"},
            status=status.HTTP_403_FORBIDDEN,
        )
    incidents_found, versions, message = get_latest_incident_reports(
        lost_and_found_data, LostAndFoundLostSerializer
    )
    if incidents_found:
        return Response(
            {"status": "success", "lost and found data": versions},
            status=status.HTTP_200_OK,
        )
    else:
        return Response({"message": message}, status=status.HTTP_400_BAD_REQUEST)
