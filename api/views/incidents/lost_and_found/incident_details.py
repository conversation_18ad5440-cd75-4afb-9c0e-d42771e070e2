from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from base.services.auth import verify_user
from base.services.incidents.get_incidents import GetIncidentsService
from lost_and_found.models import LostAndFound
from lost_and_found.serializers import (
    LostAndFoundLostSerializer,
    LostAndFoundSerializer,
)
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser

incidents_service = GetIncidentsService()


@extend_schema(request=LostAndFoundSerializer, responses=LostAndFoundSerializer)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def get_lost_and_found_details_api(request, lost_and_found_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident = LostAndFound.objects.get(id=lost_and_found_id)

        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not incident.created_by == user
        ):
            return Response(
                {"error": "You do not have permission to access this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )

        serializer = LostAndFoundLostSerializer(incident).data
        success, incident, modifications, message = (
            incidents_service.get_latest_version(
                incident_data=incident,
                modelName=LostAndFound,
                incidentSerializer=LostAndFoundLostSerializer,
                incident_id=lost_and_found_id,
            )
        )
        if success:
            return Response(
                {
                    "status": "success",
                    "incident": incident,
                    "modifications": modifications,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except LostAndFound.DoesNotExist:
        return Response(
            {"error": "data was not found"}, status=status.HTTP_404_NOT_FOUND
        )
