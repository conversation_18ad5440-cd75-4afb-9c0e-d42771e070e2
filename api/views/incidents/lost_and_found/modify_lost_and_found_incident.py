from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.services.auth import verify_user
from lost_and_found.serializers import (
    LostAndFoundSerializer,
    LostAndFoundVersionSerializer,
)
from lost_and_found.models import LostAndFound, LostAndFoundVersion
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import JSONParser

from incidents.views.send_to_department import send_incident_submission_email
from api.views.incidents.general_incident.new_incident import get_patient_profile


# updating the whole incident
@extend_schema(request=LostAndFoundSerializer, responses=LostAndFoundSerializer)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def modify_lost_and_found_incident(request, lost_and_found_id):
    # for i in LostAndFound.objects.all():
    #     i.delete()
    # for i in LostAndFoundVersion.objects.all():
    #     i.delete()
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    request_data = request.data.copy()
    report_facility = None
    try:
        incident = LostAndFound.objects.get(id=lost_and_found_id)
        report_facility = incident.report_facility
        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_manager_user(user, incident.department)
        ) and not incident.created_by == user:
            return Response(
                {
                    "status": "failed",
                    "message": "You do not have permission to update this incident",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        request.data["updated_by"] = user.id

        if "person_taking_report_info" in request.data:
            person_taking_report_profile, message = get_patient_profile(
                data=request.data["person_taking_report_info"],
                facility=incident.report_facility,
            )
            if person_taking_report_profile:
                request_data["person_taking_report_info"] = (
                    person_taking_report_profile.id
                )

        if "person_reporting_info" in request.data:
            person_reporting_info_profile, message = get_patient_profile(
                data=request.data["person_reporting_info"],
                facility=incident.report_facility,
            )
            if person_reporting_info_profile:
                request_data["person_reporting_info"] = person_reporting_info_profile.id
        facility = (incident.report_facility,)
        request_data["original_report"] = incident.id
        request_data["report_facility"] = report_facility.id
        request_data["created_by"] = user.id
        request_data["status"] = request.data.get("status", "Draft")
        new_version = LostAndFoundVersionSerializer(data=request_data)

        if new_version.is_valid():
            new_version.save()
            incident.is_modified = True
            incident.updated_by = user
            incident.status = request.data.get("status", "Draft")
            incident.save()

            if "status" in request.data and request.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=incident,
                    incident_type="Lost & Found Property Report",
                )
            return Response(
                {
                    "status": "success",
                    "message": "Incident was successfully resolved",
                    "incident": new_version.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "failed",
                    "error": f"failed to update incident {new_version.errors}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except LostAndFound.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
