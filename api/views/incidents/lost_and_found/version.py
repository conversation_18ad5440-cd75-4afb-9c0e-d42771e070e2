from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from lost_and_found.models import LostAndFound, LostAndFoundVersion
from lost_and_found.new_serializers import GetLostAndFoundSerializer
from lost_and_found.serializers import (
    LostAndFoundLostSerializer,
    LostAndFoundSerializer,
    LostAndFoundVersionSerializer,
)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def lost_and_found_incident_version(request, incident_id, version_id):

    try:
        incident = LostAndFoundVersion.objects.get(id=version_id)
        serializer = GetLostAndFoundSerializer(incident)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except LostAndFoundVersion.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_404_NOT_FOUND
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def lost_and_found_original_version(request, incident_id):
    try:
        incident = LostAndFound.objects.get(id=incident_id)
        serializer = LostAndFoundLostSerializer(incident)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except LostAndFound.DoesNotExist:
        return Response(
            {"error": "Version notdd found"}, status=status.HTTP_404_NOT_FOUND
        )
