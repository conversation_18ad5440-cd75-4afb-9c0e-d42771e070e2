from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.services.auth import verify_user
from base.services.forms import check_user_facility, check_missing_fields
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from lost_and_found.serializers import LostAndFoundSerializer
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import J<PERSON><PERSON>arser
from api.views.incidents.general_incident.new_incident import get_patient_profile

logging_service = LoggingService()


@extend_schema(request=LostAndFoundSerializer, responses=LostAndFoundSerializer)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_lost_and_found_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    facility, has_facility = check_user_facility(request.data, user)
    if has_facility:
        request.data["report_facility"] = facility
    else:
        return facility

    required_fields = [
        "person_taking_report_info",
        "item_description",
    ]

    missing_fields_response = check_missing_fields(
        data=request.data, required_fields=required_fields
    )

    if missing_fields_response:
        return missing_fields_response
    request_data = request.data.copy()

    try:
        request.data["created_by"] = user.id

        if "person_taking_report_info" in request.data:
            person_taking_report_profile, message = get_patient_profile(
                data=request.data["person_taking_report_info"], facility=facility
            )
            if person_taking_report_profile:
                request_data["person_taking_report_info"] = (
                    person_taking_report_profile.id
                )

        if "person_reporting_info" in request.data:
            person_reporting_info_profile, message = get_patient_profile(
                data=request.data["person_reporting_info"], facility=facility
            )
            if person_reporting_info_profile:
                request_data["person_reporting_info"] = person_reporting_info_profile.id

        serializer = LostAndFoundSerializer(data=request_data)

        if serializer.is_valid():
            serializer.save(created_by=user, updated_by=user, status="Draft")
            save_notification(
                facility=facility,
                group_name="Admin",
                notification_type="info",
                notification_category="incident",
                message="A new incident is submitted",
                item_id=serializer.data["id"],
            )
            return Response(
                {
                    "status": "success",
                    "message": "Incident created successfully",
                    "Incident": serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"error": "failed to create lost and data"},
                serializer.errors,
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
