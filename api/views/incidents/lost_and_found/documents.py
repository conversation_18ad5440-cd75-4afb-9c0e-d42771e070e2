from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from accounts.models import Profile
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from lost_and_found.models import LostAndFound
from documents.views import (
    get_incident_documents,
    handle_incident_document,
    delete_incident_document,
)

logging_service = LoggingService()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def lost_and_found_documents_api(request, lost_and_found_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    files = request.FILES.getlist("files")
    folder_name = "lost-and-found-incident"

    try:
        incident = LostAndFound.objects.get(id=lost_and_found_id)

        data = handle_incident_document(files, folder_name, incident, user)
        return Response(data, status=status.HTTP_201_CREATED)
    except LostAndFound.DoesNotExist:
        return Response(
            {"error": "Failed to upload file(s)"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to upload file"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def lost_and_found_documents_list_api(request, lost_and_found_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    documents, found = get_incident_documents(LostAndFound, lost_and_found_id)
    if found:
        return Response(documents, status=status.HTTP_200_OK)
    else:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_lost_and_found_documents(request, lost_and_found_id, document_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident = LostAndFound.objects.get(id=lost_and_found_id)

        success, message = delete_incident_document(incident, document_id)

        if success:
            return Response(
                {
                    "message": "Document deleted successfully",
                    "deleted document": document_id,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
    except LostAndFound.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Exception as e:
        return Response(
            {"error": f"Failed to delete document"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
