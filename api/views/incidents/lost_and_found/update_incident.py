from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.incidents.base import IncidentService
from base.services.logging.logger import LoggingService
from incidents.emails.send_to_department import send_to_department_email
from base.models import Department
from lost_and_found.serializers import LostAndFoundSerializer
from lost_and_found.models import LostAndFound
from drf_spectacular.utils import extend_schema
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
import os
from dotenv import load_dotenv

from incidents.views.send_to_department import (
    send_incident_submission_email,
)
from api.views.incidents.general_incident.new_incident import get_patient_profile

load_dotenv()

incident_service = IncidentService()
logging_service = LoggingService()


@extend_schema(request=LostAndFoundSerializer, responses=LostAndFoundSerializer)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def update_lost_and_found_api(request, lost_and_found_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    required_fields = []

    if "is_found" in data and data["is_found"] is not None:
        required_fields = [
            "date_found",
            "time_found",
            "time_returned",
            "date_returned",
            "location_returned",
            "location_found",
            "found_by",
        ]
    check_missing_fields_response = check_missing_fields(
        required_fields=required_fields, data=data
    )
    if check_missing_fields_response:
        return check_missing_fields_response

    request_data = request.data.copy()

    try:
        lost_and_found_item = LostAndFound.objects.get(id=lost_and_found_id)
        if (
            not has_permissions(user, ["Super User", "Admin", "Manager"])
            and not lost_and_found_item.created_by == user
        ):
            return Response(
                {
                    "status": "failed",
                    "message": "You do not have permission to update the incident",
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        data["updated_by"] = user.id

        if "person_taking_report_info" in request.data:
            person_taking_report_info_profile, message = get_patient_profile(
                data=request.data["person_taking_report_info"],
                facility=lost_and_found_item.report_facility,
            )
            if person_taking_report_info_profile:
                request_data["person_taking_report_info"] = (
                    person_taking_report_info_profile.id
                )

        update_item = LostAndFoundSerializer(
            lost_and_found_item, data=request_data, partial=True
        )
        if update_item.is_valid():
            update_item.save(status="Open")
            if "status" in request.data and request.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=lost_and_found_item,
                    incident_type="Lost & Found Property report",
                )
            return Response(
                {
                    "status": "success",
                    "message": "Incident updated successfully",
                    "incident": update_item.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": f"Failed to update data {update_item.errors}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except LostAndFound.DoesNotExist:
        return Response(
            {"error": "data for item not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# marking incident as resolved
@extend_schema(request=LostAndFoundSerializer, responses=LostAndFoundSerializer)
@api_view(["PUT"])
@parser_classes([JSONParser])
def mark_lost_and_found_incident_as_resolved(request, lost_and_found_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    data["status"] = "Closed"

    required_fields = ["status"]

    check_missing_fields_responses = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_responses:
        return check_missing_fields_responses

    try:
        incident = LostAndFound.objects.get(id=lost_and_found_id)
        response = incident_service.mark_as_resolved(
            incident,
            user,
        )

        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": "Incident was marked as resolved",
                "incident": {
                    "id": incident.id,
                    "is_resolved": incident.is_resolved,
                },
            }
        )

    except LostAndFound.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# send incident to department
@extend_schema(request=LostAndFoundSerializer, responses=LostAndFoundSerializer)
@api_view(["PUT"])
@parser_classes([JSONParser])
def send_lost_and_found_to_department(request, lost_and_found_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    required_fields = ["department"]  # there is an issue here

    check_missing_fields_responses = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_responses:
        return check_missing_fields_responses

    try:
        lost_and_found_incident = LostAndFound.objects.get(id=lost_and_found_id)

        if not is_super_user(user) and not is_admin_user(
            user, lost_and_found_incident.report_facility
        ):
            return Response(
                {
                    "error": "You do not have permission to send this incident to department"
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        incident_type = "Lost & Found Property report"
        response = incident_service.send_incident_to_department(
            data, lost_and_found_incident, incident_type, user
        )

        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )

        return Response(
            {
                "status": "success",
                "message": message,
                "incident": LostAndFoundSerializer(lost_and_found_incident).data,
            }
        )

    except LostAndFound.DoesNotExist:
        return Response(
            {"error": "Incident not found"}, status=status.HTTP_404_NOT_FOUND
        )
