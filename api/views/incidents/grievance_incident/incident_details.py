# views.py

from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.serializers import UserSerializer
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.incidents.get_incidents import GetIncidentsService
from patient_visitor_grievance.models import Grievance
from patient_visitor_grievance.models import GrievanceInvestigation
from patient_visitor_grievance.serializers import (
    GrievanceListSerializer,
)
from patient_visitor_grievance.serializers import (
    RetrieveGrievanceInvestigationSerializer,
)

incidents_service = GetIncidentsService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def grievance_detail(request, grievance_id):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        investigation_data = {}
        try:
            incident = Grievance.objects.get(id=grievance_id)
            if GrievanceInvestigation.objects.filter(
                grievance_report=incident
            ).exists():
                investigation = GrievanceInvestigation.objects.filter(
                    grievance_report=incident
                ).first()
                investigation_data = RetrieveGrievanceInvestigationSerializer(
                    investigation
                ).data
            if (
                not is_super_user(user)
                and not is_admin_user(user, incident.report_facility)
                and not is_manager_user(user, incident.department)
                and not incident.created_by == user
            ):
                return Response(
                    {"error": "You do not have permission to access this incident"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            success, incident, modifications, message = (
                incidents_service.get_latest_version(
                    incident_data=incident,
                    modelName=Grievance,
                    incidentSerializer=GrievanceListSerializer,
                    incident_id=grievance_id,
                )
            )
            if success:
                return Response(
                    {
                        "status": "success",
                        "message": "Grievance retrieved successfully",
                        "incident": incident,
                        "modifications": modifications,
                        "investigation": investigation_data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"status": "failed", "message": message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Grievance.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Grievance not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
