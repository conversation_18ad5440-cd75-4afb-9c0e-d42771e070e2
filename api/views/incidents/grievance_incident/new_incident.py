from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.serializers import UserSerializer
from api.views.incidents.general_incident.new_incident import (
    check_anonymous,
    get_patient_profile,
)
from base.services.auth import verify_user
from base.services.forms import check_missing_fields, check_user_facility
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from patient_visitor_grievance.models import Grievance
from drf_spectacular.utils import extend_schema
from patient_visitor_grievance.serializers import (
    GrievanceListSerializer,
    GrievanceSerializer,
)

logging_service = LoggingService()


@extend_schema(request=GrievanceListSerializer, responses=GrievanceSerializer)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_grievance_api(request):
    if request.method == "POST":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        required_fields = [
            "date",
            "title",
            "form_initiated_by",
            "complaint_made_by",
            "relationship_to_patient",
            "source_of_information",
        ]

        missing_fields_response = check_missing_fields(
            data=request.data, required_fields=required_fields
        )

        if missing_fields_response:
            return missing_fields_response
        facility, has_facility = check_user_facility(request.data, user)
        if has_facility:
            request.data["report_facility"] = facility
        else:
            return facility
        request_data = request.data.copy()
        try:
            request_data["created_by"] = user.id
            if "patient_name" in request.data:
                patient_profile, message = get_patient_profile(
                    data=request.data["patient_name"], facility=facility
                )
                if patient_profile:
                    request_data["patient_name"] = patient_profile.id
            if "form_initiated_by" in request.data:
                form_initiated_by_profile, message = get_patient_profile(
                    data=request.data["form_initiated_by"], facility=facility
                )
                if form_initiated_by_profile:
                    request_data["form_initiated_by"] = form_initiated_by_profile.id
            if "complaint_made_by" in request.data:
                complaint_made_by_profile, message = get_patient_profile(
                    data=request.data["complaint_made_by"], facility=facility
                )
                if complaint_made_by_profile:
                    request_data["complaint_made_by"] = complaint_made_by_profile.id

            data = check_anonymous(request_data, user)
            new_grievance = GrievanceSerializer(data=data)
            if new_grievance.is_valid():
                new_grievance.save()
                save_notification(
                    facility=facility,
                    group_name="Admin",
                    notification_type="info",
                    notification_category="incident",
                    message="A new incident is submitted",
                    item_id=new_grievance.data["id"],
                )
                return Response(
                    {
                        "status": "success",
                        "message": "Grievance created successfully",
                        "grievance": new_grievance.data,
                    },
                    status=status.HTTP_201_CREATED,
                )
            else:
                return Response(
                    {"status": "failed", "message": new_grievance.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {
                    "status": "failed",
                    "message": "An error occurred while creating the grievance",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
