from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_spectacular.utils import extend_schema
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from patient_visitor_grievance.serializers import (
    GrievanceListSerializer,
)
from patient_visitor_grievance.services.get_incidents import GrievanceService

grievance_service = GrievanceService()


@extend_schema(request=GrievanceListSerializer, responses=GrievanceListSerializer)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def grievances_list_api(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        facility_id = request.query_params.get("facility_id")
        department_id = request.query_params.get("department_id")
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        incidents = grievance_service.get_incidents_list(
            user,
            facility_id=facility_id,
            department_id=department_id,
        )

        if not incidents.success:
            return Response(
                {"error": incidents.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = GrievanceListSerializer(incidents.data, many=True)

        return Response(
            {
                "status": "success",
                "message": "Grievances retrieved successfully",
                "grievances": serializer.data,
            }
        )
