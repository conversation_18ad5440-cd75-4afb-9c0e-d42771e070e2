from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import JSONParser
from api.views.auth.permissions_list import has_permissions
from api.views.get_reviews import get_incident_reviews
from api.views.new_review import create_review
from base.services.auth import verify_user
from patient_visitor_grievance.models import Grievance
from reviews.models import Review


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_grievance_review(request, grievance_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)
    if not has_permissions(user, ["Super User", "Admin", "Manager"]):
        return Response(
            {"message": "You do not have the required permissions"},
            status=status.HTTP_403_FORBIDDEN,
        )
    if request.data.get("content") is None:
        return Response({"message": "content is missing"})

    response = create_review(
        "patient_visitor_grievance",
        "Grievance",
        grievance_id,
        user,
        request.data.get("content"),
    )

    return response


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def grievance_reviews_list(request, grievance_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)
    if not has_permissions(user, ["Super User", "Admin", "Manager"]):
        return Response(
            {"message": "You do not have the required permissions"},
            status=status.HTTP_403_FORBIDDEN,
        )
    response = get_incident_reviews(
        user, "Grievance", "patient_visitor_grievance", grievance_id
    )

    return response
