from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.incidents.general_incident.new_incident import get_patient_profile
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.logging.logger import LoggingService
from patient_visitor_grievance.models import Grievance
from patient_visitor_grievance.serializers import (
    GrievanceListSerializer,
    GrievanceSerializer,
    GrievanceSerializerVersion,
)
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_spectacular.utils import extend_schema
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from incidents.views.send_to_department import send_incident_submission_email

logging_service = LoggingService()


@extend_schema(request=GrievanceSerializer, responses=Grievance)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON>ars<PERSON>])
def modify_grievance_incident(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    request_data = request.data.copy()
    report_facility = None
    try:
        incident = Grievance.objects.get(id=incident_id)
        facility = incident.report_facility
        report_facility = incident.report_facility

        if (
            not is_super_user(user)
            and not is_admin_user(user, incident.report_facility)
            and not is_manager_user(user, incident.department)
        ) and not incident.created_by == user:
            return Response(
                {"error": "You do not have permission to update grievance incident"},
                status=status.HTTP_403_FORBIDDEN,
            )

        if "patient_name" in request.data:
            patient_profile, message = get_patient_profile(
                data=request.data["patient_name"], facility=facility
            )
            if patient_profile:
                request_data["patient_name"] = patient_profile.id

        if "form_initiated_by" in request.data:
            form_initiated_by_profile, message = get_patient_profile(
                data=request.data["form_initiated_by"], facility=facility
            )
            if form_initiated_by_profile:
                request_data["form_initiated_by"] = form_initiated_by_profile.id

        if "complaint_made_by" in request.data:
            complaint_made_by_profile, message = get_patient_profile(
                data=request.data["complaint_made_by"], facility=facility
            )
            if complaint_made_by_profile:
                request_data["complaint_made_by"] = complaint_made_by_profile.id

        if "administrator_notified" in request.data:
            administrator_notified_profile, message = get_patient_profile(
                data=request.data["administrator_notified"], facility=facility
            )
            if administrator_notified_profile:
                request_data["administrator_notified"] = (
                    administrator_notified_profile.id
                )
        request_data["original_report"] = incident.id
        request_data["report_facility"] = report_facility.id
        request_data["created_by"] = user.id
        request_data["status"] = request.data.get("status", "Draft")

        new_version = GrievanceSerializerVersion(data=request_data)
        if new_version.is_valid():
            new_version.save()
            incident.is_modified = True
            incident.updated_by = user
            incident.status = request.data.get("status", "Draft")
            incident.save()
            if "status" in request.data and request.data.get("status") == "Open":
                send_incident_submission_email(
                    incident=incident,
                    incident_type="Patient/Visitor Grievance",
                )
            return Response(
                {
                    "status": "success",
                    "message": "Incident was successfully updated",
                    "incident": new_version.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            logging_service.log_error(new_version.errors)
            return Response(
                {
                    "status": "failed",
                    "error": f"failed to update incident {new_version.errors}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Grievance.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )
