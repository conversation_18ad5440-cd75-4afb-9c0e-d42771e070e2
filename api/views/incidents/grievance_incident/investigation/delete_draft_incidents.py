from rest_framework import status
from rest_framework.decorators import api_view
from patient_visitor_grievance.models import GrievanceInvestigation

from rest_framework.response import Response
from base.services.auth import verify_user
from incidents.views.delete_drafts import delete_drafts


@api_view(["DELETE"])
def delete_grievance_investigation_draft_incidents(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    incident_ids = request.data.get("incident_ids", None)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    return delete_drafts(
        user=user, incident_model=GrievanceInvestigation, incident_ids=incident_ids
    )
