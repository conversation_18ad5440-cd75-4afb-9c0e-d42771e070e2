from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ars<PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import has_permissions
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from drf_spectacular.utils import extend_schema

from base.services.logging.logger import LoggingService
from patient_visitor_grievance.models import (
    GrievanceInvestigation,
    GrievanceInvestigationInvolvedParty,
)
from patient_visitor_grievance.serializers import (
    GrievanceInvestigationSerializer,
)
from incidents.views.send_to_department import send_incident_to_department

logging_service = LoggingService()


@extend_schema(
    request=GrievanceInvestigationSerializer, responses=GrievanceInvestigationSerializer
)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>])
def update_grievance_investigation(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    if (
        "parties_involved" in request.data
        and request.data["parties_involved"] is not None
        and len(request.data["parties_involved"])
    ):

        updated, message = update_parties_involved(
            request.data["parties_involved"], incident_id
        )
        if not updated:
            return Response({"message": message}, status=status.HTTP_400_BAD_REQUEST)

    try:
        if "party_involved" in request.data:
            del request.data["parties_involved"]
        grievance_investigation = GrievanceInvestigation.objects.get(id=incident_id)
        if (
            not has_permissions(user, ["Super User", "Admin", "Manager"])
            and not grievance_investigation.created_by == user
        ):
            return Response(
                {
                    "error": "You do not have permission to update this grievance investigation"
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        updated_incident = GrievanceInvestigationSerializer(
            grievance_investigation, request.data, partial=True
        )
        if updated_incident.is_valid():
            updated_incident.save()
            return Response(
                {
                    "status": "success",
                    "message": "Grievance Investigation updated",
                    "incident": updated_incident.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "message": "Failed to update Grievance Investigation",
                    "errors": updated_incident.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Grievance Investigation not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


def update_parties_involved(data, incident_id):
    try:
        incident = GrievanceInvestigation.objects.get(id=incident_id)
        if len(data) > 0:
            involved_parties = []
            for party in data:
                new_incident, created = (
                    GrievanceInvestigationInvolvedParty.objects.get_or_create(
                        name=party["name"],
                        relationship_to_patient=party["relationship_to_patient"],
                    )
                )
                if not incident.involved_parties.filter(id=incident.id).exists():
                    involved_parties.append(new_incident.id)
            if len(involved_parties) > 0:
                incident.involved_parties.add(*involved_parties)
                incident.save()
                return (True, "Parties involved are updated")
            else:
                return (
                    True,
                    "Parties involved already exist",
                )
        else:
            return (
                False,
                "No parties involved in the request",
            )
    except Exception as e:
        logging_service.log_error(e)
        return (
            False,
            "Internal server error",
        )


@extend_schema(
    request=GrievanceInvestigationSerializer, responses=GrievanceInvestigationSerializer
)
@api_view(["PUT"])
@parser_classes([JSONParser])
def mark_grievance_investigation_incident_as_resolved(
    request, grievance_investigation_id
):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    data["status"] = "Closed"

    required_fields = ["status"]

    check_missing_fields_responses = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_responses:
        return check_missing_fields_responses

    try:
        if (
            not has_permissions(user, ["Super User", "Admin"])
            and not incident.created_by == user
        ):
            return Response(
                {"error": "You do not have permission to resolve this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )
        incident = GrievanceInvestigation.objects.get(id=grievance_investigation_id)
        incident.updated_by = user
        resolved_incident = GrievanceInvestigationSerializer(
            incident, data=request.data, partial=True
        )

        if resolved_incident.is_valid():
            resolved_incident.save()
            return Response(
                {
                    "status": "success",
                    "message": "Incident was marked as resolved",
                    "incident": resolved_incident.data,
                }
            )
        else:
            return Response(
                {
                    "error": "Failed update the incident",
                    "error message": resolved_incident.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def send_grievance_investigation_to_department(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    try:
        grievance_investigation = GrievanceInvestigation.objects.get(id=incident_id)
        if not has_permissions(user, ["Super User", "Admin"]):
            return Response(
                {
                    "error": "You do not have permission to send this grievance investigation to a department"
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        is_sent, message = send_incident_to_department(
            data=request.data, incident=grievance_investigation, user=user
        )
        if is_sent:
            return Response(
                {
                    "status": "success",
                    "message": message,
                    "incident": GrievanceInvestigationSerializer(
                        grievance_investigation
                    ).data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Grievance Investigation not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
