from api.views.auth.permissions_list import has_permissions
from api.views.get_reviews import get_incident_reviews
from api.views.new_review import create_review
from base.services.auth import verify_user
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from patient_visitor_grievance.models import GrievanceInvestigation


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_grievance_investigation_review(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)
    if not has_permissions(user, ["Super User", "Admin", "Manager"]):
        return Response(
            {"message": "You do not have the required permissions"},
            status=status.HTTP_403_FORBIDDEN,
        )
    if request.data.get("content") is None:
        return Response({"message": "content is missing"})

    response = create_review(
        "incidents",
        "GrievanceInvestigation",
        incident_id,
        user,
        request.data.get("content"),
    )

    return response


@api_view(["GET"])
def grievance_investigation_reviews(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    response = get_incident_reviews(
        user, "GrievanceInvestigation", "incidents", incident_id
    )
    return response
