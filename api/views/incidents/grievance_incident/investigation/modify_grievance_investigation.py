from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import has_permissions
from base.services.auth import verify_user
from drf_spectacular.utils import extend_schema

from base.services.logging.logger import LoggingService
from patient_visitor_grievance.models import (
    GrievanceInvestigation,
    GrievanceInvestigationInvolvedParty,
)
from patient_visitor_grievance.serializers import (
    GrievanceInvestigationSerializer,
)

from api.views.incidents.general_incident.new_incident import get_patient_profile

logging_service = LoggingService()


@extend_schema(
    request=GrievanceInvestigationSerializer, responses=GrievanceInvestigation
)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def modify_grievance_investigation_incident(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    if "involved_parties" in request.data:
        updated, message = update_parties_involved(
            request.data["involved_parties"], incident_id
        )

        if not updated:
            return Response({"message": message}, status=status.HTTP_400_BAD_REQUEST)

    request_data = request.data.copy()

    try:
        if "involved_parties" in request.data:
            del request.data["involved_parties"]
        incident = GrievanceInvestigation.objects.get(id=incident_id)
        if (
            not has_permissions(user, ["Super User", "Admin", "Manager"])
            and not incident.created_by == user
        ):
            return Response(
                {"error": "You do not have permission to update this incident"},
                status=status.HTTP_403_FORBIDDEN,
            )

        if "conducted_by" in request.data:
            conducted_by_profile, message = get_patient_profile(
                data=request.data["conducted_by"], facility=incident.report_facility
            )
            if conducted_by_profile:
                request_data["conducted_by"] = conducted_by_profile.id

        modified_incident = GrievanceInvestigationSerializer(
            incident, data=request_data, partial=True
        )
        if modified_incident.is_valid():
            modified_incident.save()
            return Response(
                {
                    "status": "success",
                    "message": "Incident was successfully updated",
                    "incident": modified_incident.data,
                },
                status=status.HTTP_200_OK,
            )
        else:
            logging_service.log_error(modified_incident.errors)
            return Response(
                {
                    "status": "failed",
                    "error": f"failed to update incident {modified_incident.errors}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"error": "Incident does not exist"}, status=status.HTTP_404_NOT_FOUND
        )


def update_parties_involved(data, incident_id):
    try:
        incident = GrievanceInvestigation.objects.get(id=incident_id)
        if len(data):
            involved_parties = []
            for party in data:
                new_incident, created = (
                    GrievanceInvestigationInvolvedParty.objects.get_or_create(
                        name=party["name"],
                        relationship_to_patient=party["relationship_to_patient"],
                    )
                )
                if not incident.involved_parties.filter(id=incident.id).exists():
                    involved_parties.append(new_incident.id)
            if len(involved_parties) > 0:
                incident.involved_parties.set(involved_parties)
                incident.save()
                return (True, "Parties involved are updated")
            else:
                return (
                    True,
                    "Parties involved already exist",
                )
        else:
            return (
                False,
                "No parties involved in the request",
            )
    except Exception as e:
        logging_service.log_error(e)
        return (
            False,
            "Internal server error",
        )
