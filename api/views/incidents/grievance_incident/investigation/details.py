from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>art<PERSON><PERSON><PERSON>, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import has_permissions
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from drf_spectacular.utils import extend_schema

from patient_visitor_grievance.models import (
    GrievanceInvestigation,
)
from patient_visitor_grievance.serializers import (
    GrievanceInvestigationSerializer,
)


from patient_visitor_grievance.serializers import (
    RetrieveGrievanceInvestigationSerializer,
)


@extend_schema(
    request=GrievanceInvestigationSerializer, responses=GrievanceInvestigationSerializer
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([J<PERSON>NParser, MultiPartParser, FormParser])
def grievance_investigation_details(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    try:
        grievance_investigation = GrievanceInvestigation.objects.get(id=incident_id)
        if (
            not has_permissions(user, ["Super User", "Admin"])
            and not grievance_investigation.created_by
        ):
            return Response(
                {"message": "You do not have the required permissions"},
                status=status.HTTP_403_FORBIDDEN,
            )
        serializer = RetrieveGrievanceInvestigationSerializer(
            grievance_investigation, many=False
        )
        return Response(serializer.data, status=status.HTTP_200_OK)
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Grievance Investigation not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
