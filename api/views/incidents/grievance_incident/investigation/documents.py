from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from api.views.auth.permissions_list import has_permissions
from base.services.auth import verify_user
import os

from base.services.logging.logger import LoggingService
from documents.views import (
    handle_incident_document,
    handle_single_file,
    delete_incident_document,
)
from patient_visitor_grievance.models import GrievanceInvestigation

logging_service = LoggingService()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def grievance_investigation_extension_letter_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        file = request.FILES["file"]
        folder_name = "adverse_drug_reaction"
        incident = GrievanceInvestigation.objects.get(id=incident_id)
        document, created, message = handle_single_file(file, user)
        if created:
            incident.extension_letter_copy = document
            incident.save()
            return Response(
                {
                    "document": {
                        "id": document.id,
                        "name": document.name,
                        "type": document.file_type,
                        "created_at": document.created_at,
                        "updated_at": document.updated_at,
                        "url": document.file.url,
                    },
                },
                status=status.HTTP_200_OK,
            )
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def grievance_investigation_response_letter_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        file = request.FILES["file"]
        folder_name = "adverse_drug_reaction"
        incident = GrievanceInvestigation.objects.get(id=incident_id)
        document, created, message = handle_single_file(file, user)
        if created:
            incident.response_letter_copy = document
            incident.save()
            return Response(
                {
                    "document": {
                        "id": document.id,
                        "name": document.name,
                        "type": document.file_type,
                        "created_at": document.created_at,
                        "updated_at": document.updated_at,
                        "url": document.file.url,
                    },
                },
                status=status.HTTP_200_OK,
            )
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )


# incident documents
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_grievance_investigation_document_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    files = request.FILES.getlist("files")
    folder_name = "grievance investigation"
    try:
        incident = GrievanceInvestigation.objects.get(id=incident_id)

        if not has_permissions(
            user,
            ["Manager"],
        ):
            return Response(
                {
                    "error": "You do not have permission to upload documents to this incident"
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        data = handle_incident_document(files, folder_name, incident, user)
        return Response(data, status=status.HTTP_201_CREATED)
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"error": "Failed to upload file(s)"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to upload file"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def grievance_investigation_documents_list_api(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    try:
        incident = GrievanceInvestigation.objects.get(id=incident_id)
        documents_list = [
            {
                "id": document.id,
                "name": document.name,
                "type": document.file_type,
                "created_by": f"{document.created_by.first_name} {document.created_by.last_name}",
                "created_at": document.created_at,
            }
            for document in incident.documents.all()
        ]

        return Response(documents_list, status=status.HTTP_200_OK)
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Incident not found"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_grievance_investigation_documents(request, incident_id, document_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident = GrievanceInvestigation.objects.get(id=incident_id)

        success, message = delete_incident_document(incident, document_id)

        if success:
            return Response(
                {
                    "message": "Document deleted successfully",
                    "deleted document": document_id,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"error": message}, status=status.HTTP_400_BAD_REQUEST)
    except GrievanceInvestigation.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Exception as e:
        return Response(
            {"error": f"Failed to delete document"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
