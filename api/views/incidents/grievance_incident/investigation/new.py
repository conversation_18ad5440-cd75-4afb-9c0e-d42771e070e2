from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.logging.logger import LoggingService
from general_patient_visitor.models import GeneralPatientVisitor
from drf_spectacular.utils import extend_schema

from patient_visitor_grievance.models import GrievanceInvestigation
from patient_visitor_grievance.models import Grievance

from patient_visitor_grievance.serializers import (
    GrievanceInvestigationSerializer,
)
from api.views.incidents.general_incident.new_incident import get_patient_profile

logging_service = LoggingService()


@extend_schema(
    request=GrievanceInvestigationSerializer, responses=GrievanceInvestigationSerializer
)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def new_grievance_investigation(request, incident_id):
    if request.method == "POST":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        request_data = request.data.copy()
        try:
            incident = Grievance.objects.get(id=incident_id)

            if GrievanceInvestigation.objects.filter(
                grievance_report=incident
            ).exists():
                return Response(
                    {
                        "status": "failed",
                        "message": "Grievance Investigation already exists for this Incident",
                    },
                    status=status.HTTP_409_CONFLICT,
                )
            request_data["created_by"] = user.id
            request_data["grievance_report"] = incident.id

            if "conducted_by" in request.data:
                conducted_by_profile, message = get_patient_profile(
                    data=request.data["conducted_by"], facility=incident.report_facility
                )
                if conducted_by_profile:
                    request_data["conducted_by"] = conducted_by_profile.id

            new_incident = GrievanceInvestigationSerializer(data=request_data)
            if new_incident.is_valid():
                new_incident.save()
                return Response(
                    {
                        "status": "success",
                        "message": "Grievance Investigation Incident created successfully",
                        "incident": new_incident.data,
                    },
                    status=status.HTTP_201_CREATED,
                )

            else:
                return Response(
                    {
                        "status": "failed",
                        "message": "Failed to create Grievance Investigation Incident",
                        "errors": new_incident.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Grievance.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Grievance Incident not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
