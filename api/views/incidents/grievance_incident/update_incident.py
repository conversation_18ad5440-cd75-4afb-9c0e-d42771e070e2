# views.py

from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.incidents.general_incident.new_incident import (
    check_anonymous,
    get_patient_profile,
)
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.incidents.base import IncidentService
from patient_visitor_grievance.models import Grievance
from patient_visitor_grievance.serializers import (
    GrievanceListSerializer,
    GrievanceSerializer,
)
from rest_framework.parsers import J<PERSON><PERSON>arser
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_super_user,
)
from drf_spectacular.utils import extend_schema

from incidents.views.send_to_department import send_incident_submission_email

incident_service = IncidentService()


@extend_schema(request=GrievanceListSerializer, responses=GrievanceSerializer)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([<PERSON><PERSON><PERSON>ars<PERSON>])
def update_grievance_api(request, grievance_id):
    if request.method == "PATCH":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        required_fields = []

        if "complaint_or_concern" in request.data:

            required_fields = [
                "complaint_or_concern",
                "initial_corrective_actions",
                "adverse_patient_outcome",
            ]
        elif "administrator_notified" in request.data:
            required_fields = [
                "administrator_notified",
                "notification_date",
                "notification_time",
            ]

        missing_fields_response = check_missing_fields(
            data=request.data, required_fields=required_fields
        )

        if missing_fields_response:
            return missing_fields_response

        # updating the incident in the database
        request_data = request.data.copy()
        try:
            grievance = Grievance.objects.get(id=grievance_id)
            if (
                not has_permissions(user, ["Super User", "Admin", "Manager"])
                and not grievance.created_by == user
            ):
                return Response(
                    {
                        "status": "failed",
                        "message": "You have not access to update this",
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )
            facility = grievance.report_facility
            if grievance.created_by != user:
                return Response(
                    {"status": "failed", "message": "Unauthorized"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            if "patient_name" in request.data:
                patient_profile, message = get_patient_profile(
                    data=request.data["patient_name"], facility=facility
                )
                if patient_profile:
                    request_data["patient_name"] = patient_profile.id

            if "form_initiated_by" in request.data:
                form_initiated_by_profile, message = get_patient_profile(
                    data=request.data["form_initiated_by"], facility=facility
                )
                if form_initiated_by_profile:
                    request_data["form_initiated_by"] = form_initiated_by_profile.id

            if "complaint_made_by" in request.data:
                complaint_made_by_profile, message = get_patient_profile(
                    data=request.data["complaint_made_by"], facility=facility
                )
                if complaint_made_by_profile:
                    request_data["complaint_made_by"] = complaint_made_by_profile.id

            if "administrator_notified" in request.data:
                administrator_notified_profile, message = get_patient_profile(
                    data=request.data["administrator_notified"], facility=facility
                )
                if administrator_notified_profile:
                    request_data["administrator_notified"] = (
                        administrator_notified_profile.id
                    )

            data = check_anonymous(request_data, user)

            updated_grievance = GrievanceSerializer(grievance, data=data, partial=True)
            if updated_grievance.is_valid():
                updated_grievance.save()
                if "status" in request.data and request.data.get("status") == "Open":
                    send_incident_submission_email(
                        incident=grievance,
                        incident_type="Patient/Visitor grievance",
                    )
                return Response(
                    {
                        "status": "success",
                        "message": "Grievance updated successfully",
                        "grievance": updated_grievance.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"status": "failed", "message": updated_grievance.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Grievance.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Grievance not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@extend_schema(request=GrievanceListSerializer, responses=GrievanceSerializer)
@api_view(["PUT"])
@parser_classes([JSONParser])
def mark_grievance_incident_as_resolved(request, grievance_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    data = request.data

    data["status"] = "Closed"

    required_fields = ["status"]

    check_missing_fields_responses = check_missing_fields(
        required_fields=required_fields, data=data
    )

    if check_missing_fields_responses:
        return check_missing_fields_responses

    try:
        incident = Grievance.objects.get(id=grievance_id)
        response = incident_service.mark_as_resolved(
            incident,
            user,
        )
        if not response.success:
            return Response(
                {"status": "failed", "message": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": "Incident was marked as resolved",
                "incident": {
                    "id": incident.id,
                    "is_resolved": incident.is_resolved,
                },
            }
        )

    except Grievance.DoesNotExist:
        return Response(
            {"error": "Incident was not found"}, status=status.HTTP_404_NOT_FOUND
        )


@api_view(["PUT"])
def send_grievance_incident_to_department(request, incident_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        incident = Grievance.objects.get(id=incident_id)
        if not is_super_user(user) and not is_admin_user(
            user, incident.report_facility
        ):
            return Response(
                {
                    "status": "failed",
                    "message": "You do not have permission to update the incident",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        response = incident_service.send_incident_to_department(
            data=request.data,
            incident=incident,
            incident_type="Patient/Visitor grievance",
            user=user,
        )
        if not response.success:
            return Response(
                {"status": "failed", "message": response.message},
                status=response.code,
            )
        return Response(
            {
                "status": "success",
                "message": message,
                "grievance": GrievanceSerializer(incident).data,
            },
            status=status.HTTP_200_OK,
        )
    except Grievance.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Grievance not found"},
            status=status.HTTP_404_NOT_FOUND,
        )

    except Exception as e:
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
