from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from base.services.forms import check_user_facility
from base.services.logging.logger import LoggingService
from reviews.services.review import ReviewsOperations
from workplace_violence_reports.services.actions import WorkplaceViolenceActions
from workplace_violence_reports.services.documents import (
    WorkPlaceViolenceDocumentService,
)
from workplace_violence_reports.services.operations import WorkplaceOperations


logging_service = LoggingService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def workplace_violence_list_create(request):
    service = WorkplaceOperations(user=request.user)
    """
    API endpoint to handle GET and POST requests for workplace violence incidents.
    """
    if request.method == "GET":
        # Handle GET request
        filters = request.query_params.dict()
        incidents = service.get_incidents_list(filters)
        if not incidents.success:
            return Response(
                {"error": incidents.message}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(incidents.data, status=status.HTTP_200_OK)
    elif request.method == "POST":
        # Handle POST request
        facility = check_user_facility(request.data, request.user)
        if not facility.success:
            return Response(
                {"error": facility.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        request.data["report_facility"] = facility.data
        incident = service.create_incident(request.data, request.user)
        if not incident.success:
            return Response(
                {"error": incident.message}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(incident.data, status=status.HTTP_201_CREATED)


@api_view(["GET", "POST", "PUT", "PATCH", "DELETE"])
@permission_classes([IsAuthenticated])
def workplace_violence_detail(request, workplace_violence_id):
    service = WorkplaceOperations(user=request.user)
    action_services = WorkplaceViolenceActions(
        user=request.user,
        incident_id=workplace_violence_id,
        data=request.data,
    )
    """
    API endpoint to handle GET, PUT, PATCH, and DELETE requests for a specific workplace violence incident.
    """
    if request.method == "GET":
        # Handle GET request
        incident = service.get_incident_by_id(workplace_violence_id)
        if not incident.success:
            return Response(
                {"error": incident.message}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(incident.data, status=status.HTTP_200_OK)
    elif request.method == "PUT":
        # Handle PUT request
        incident = service.update_incident(workplace_violence_id, request.data)
        if not incident.success:
            return Response(
                {"error": incident.message}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(incident.data, status=status.HTTP_200_OK)

    elif request.method == "PATCH":
        # Handle PATCH request

        # check action
        if not "action" in request.data or not request.data.get("action"):
            return Response(
                {"error": "Action is required for partial update."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        action = request.data.pop("action", None)

        if action == "send-for-review":
            response = action_services.send_for_review()
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )

        elif action == "modify":
            response = action_services.modify_incident(request.data)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif action == "mark-closed":
            response = action_services.mark_closed()
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif action == "delete-draft":
            response = action_services.delete_draft_incident()
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "Invalid action"},
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def workplace_violence_documents_api(request, incident_id):
    """
    This function handles the document retrieval for workplace violence incidents.
    """
    try:
        service = WorkPlaceViolenceDocumentService(
            incident_id=incident_id, user=request.user
        )
        if request.method == "GET":
            params = request.query_params.dict()
            response = service.get_documents(params=params)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            files = request.FILES.getlist("files")
            response = service.create_document(files)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def workplace_violence_reviews_api(request, incident_id):
    """
    This function handles the reviews for workplace violence incidents.
    """
    service = ReviewsOperations(
        user=request.user,
        model_name="WorkplaceViolence",
        app_label="workplace_violence_reports",
        incident_id=incident_id,
    )
    try:
        if request.method == "GET":
            response = service.get_incident_reviews()
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            content = request.data.get("content")
            if not content:
                return Response(
                    {"error": "Content is required for the review."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            response = service.create_review(content)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
