from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from support.models import SupportTicket
from accounts.models import Profile
from incidents.views.send_to_department import send_ticket_email

logging_service = LoggingService()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_support_ticket(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"message": "You are not authenticated"},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    title = request.data.get("title")
    description = request.data.get("description")
    priority = request.data.get("priority")
    user_account = Profile.objects.get(user=user)

    try:
        new_support_ticket = SupportTicket.objects.create(
            created_by=user,
            title=title,
            description=description,
            priority=priority,
            user_account=user_account,
        )

        data = {
            "id": new_support_ticket.id,
            "title": new_support_ticket.title,
            "description": new_support_ticket.description,
            "status": new_support_ticket.status,
            "priority": new_support_ticket.priority,
            "is_resolved": new_support_ticket.is_resolved,
        }

        if data:
            send_ticket_email(new_support_ticket, group="Tech Support")

        return Response(
            {
                "message": "Support Ticket created successfully",
                "data": data,
            },
            status=status.HTTP_201_CREATED,
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {
                "message": "Error creating support ticket",
                "details": "Internal server error",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
