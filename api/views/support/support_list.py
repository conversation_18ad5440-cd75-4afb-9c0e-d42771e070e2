from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from support.models import SupportTicket
from accounts.models import Profile

logging_service = LoggingService()


# tickets submitted by the authenticated user
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def support_ticket_list(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        profile = Profile.objects.get(user=user)
        tickets = SupportTicket.objects.filter(user_account=profile)
        tickets_data = [
            {
                "id": ticket.id,
                "title": ticket.title,
                "status": ticket.status,
                "description": ticket.description,
                "priority": ticket.priority,
                "is_resolved": ticket.is_resolved,
                "created_at": ticket.created_at,
                "updated_at": ticket.updated_at,
                "created_by": {"email": ticket.user_account.user.email},
            }
            for ticket in tickets
        ]
        return Response(tickets_data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while fetching the tickets"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
