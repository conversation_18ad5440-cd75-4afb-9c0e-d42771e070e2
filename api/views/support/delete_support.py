from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from support.models import SupportTicket
from accounts.models import Profile

logging_service = LoggingService()


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_support_ticket(request, ticket_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        profile = Profile.objects.get(user=user)

        try:
            ticket = SupportTicket.objects.get(id=ticket_id)

            if ticket.user_account == profile:
                ticket.delete()
                return Response(
                    {"message": "Support ticket deleted successfully."},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": "You do not have permission to delete this ticket."},
                    status=status.HTTP_403_FORBIDDEN,
                )

        except SupportTicket.DoesNotExist:
            return Response(
                {"error": "Support ticket not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while deleting the ticket."},
            status=status.HTTP_404_NOT_FOUND,
        )
