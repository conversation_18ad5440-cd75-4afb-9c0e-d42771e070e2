"""Title API view module."""

from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated

from accounts.services.title.services import TitleService
from base.services.logging.logger import LoggingService

logging_service = LoggingService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def titles_list(request):
    service = TitleService(user=request.user)
    """
    List all titles or create a new title.
    """
    try:
        if request.method == "GET":
            # Logic to retrieve titles
            titles = service.get_titles(request.query_params)

            if not titles.success:
                return Response(
                    {"error": titles.message}, status=status.HTTP_400_BAD_REQUEST
                )

            return Response(titles.data, status=status.HTTP_200_OK)
        elif request.method == "POST":
            # Logic to create a new title
            title = service.create_title(request.data)

            if not title.success:
                return Response(
                    {"error": title.message}, status=status.HTTP_400_BAD_REQUEST
                )

            return Response(title.data, status=status.HTTP_201_CREATED)
        return Response(status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error occurred."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "DELETE"])
@permission_classes([IsAuthenticated])
def title_detail(request, title_id):
    service = TitleService(user=request.user)
    """
    Retrieve, update or delete a title.
    """
    try:
        if request.method == "GET":
            # Logic to retrieve a title
            title = service.get_title_by_id(title_id)

            if not title.success:
                return Response(
                    {"error": title.message}, status=status.HTTP_400_BAD_REQUEST
                )

            return Response(title.data, status=status.HTTP_200_OK)
        elif request.method == "PUT":
            title = service.update_title(title_id, request.data)
            if not title.success:
                return Response(
                    {"error": title.message}, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(title.data, status=status.HTTP_200_OK)

        elif request.method == "DELETE":
            title = service.delete_title(title_id)
            if not title.success:
                return Response(
                    {"error": title.message}, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(status=status.HTTP_204_NO_CONTENT)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error occurred."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
