from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from base.services.auth import verify_user
from base.models import Department
from base.serializers import DepartmentSerializer
from facilities.services.departments import DepartmentService

department_service = DepartmentService()


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def department_details_api(request, department_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        if request.method == "GET":
            response = department_service.get_department_by_id(department_id)
            if not response.success:
                return Response(
                    {"status": "failed", "message": response.message},
                    status=response.code,
                ) 
            return Response(
                {"status": "success", "data": response.data},
                status=response.code,
            )
        

        # TODO: Implement PUT, DELETE, PATCH methods
        if request.method == "PUT":
            response = department_service.update_department(
                request.data, department_id, request.user
            )
            if not response.success:
                return Response(
                    {"status": "failed", "message": response.message},
                    status=response.code,
                )
            return Response(
                {"status": "success", "data": response.data},
                status=response.code,
            )
        elif request.method == "DELETE":
            response = department_service.delete_department(
                department_id, request.user
            )
            if not response.success:
                return Response(
                    {"status": "failed", "message": response.message},
                    status=response.code,
                )
            return Response(
                {"status": "success", "message": response.message},
                status=response.code,
            )
        elif request.method == "PATCH":
            pass
        else:
            return Response(
                {"status": "failed", "message": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        return Response(
            {"status": "failed", "message": "An error occurred"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
