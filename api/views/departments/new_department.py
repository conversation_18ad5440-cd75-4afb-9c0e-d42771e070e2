from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.models import Profile
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.models import Facility

from base.models import Department
from drf_spectacular.utils import extend_schema
from django.contrib.auth.models import User
from base.serializers import DepartmentSerializer
from base.services.responses import APIResponse


def new_department(data, user) -> APIResponse:

    name = data.get("name")
    parent = data.get("parent")
    description = data.get("description")
    header_of_department_email = data.get("header_of_department")
    facility_id = data.get("facility_id")
    add_to_all = data.get("add_to_all")
    required_fields = [
        "name",
        "header_of_department",
    ]
    missing_fields_response = check_missing_fields(data, required_fields)
    if missing_fields_response:
        return APIResponse(
            status=status.HTTP_400_BAD_REQUEST,
            message=missing_fields_response,
            data=None,
        )
    facilities_to_add = None
    if add_to_all:
        facilities_to_add = Facility.objects.all()
    try:

        # check if header_of_department exists
        header_of_department = User.objects.filter(
            email=header_of_department_email
        ).first()

        # check if parent department exists
        parent_department = None
        if parent:
            parent_department = Department.objects.filter(name=parent).first()

        new_department, created = Department.objects.get_or_create(
            name=name,
            parent=parent_department,
            description=description,
            header_of_department=header_of_department,
            created_by=user,
            facility=(
                Facility.objects.get(id=facility_id)
                if Facility.objects.get(id=facility_id)
                else None
            ),
        )
        if created:
            created_department = DepartmentSerializer(new_department).data
            facilities_added = add_department_to_facilities(
                facilities_to_add=facilities_to_add,
                facility_id=facility_id,
                parent_department=parent_department,
                description=description,
                user=user,
                name=name,
            )
            return APIResponse(
                success=True,
                message="Departments added successfully",
                data=created_department,
                code=201,
            )
        else:
            existing_department = DepartmentSerializer(new_department).data
            facilities_added = add_department_to_facilities(
                facilities_to_add=facilities_to_add,
                facility_id=facility_id,
                parent_department=parent_department,
                description=description,
                user=user,
                name=name,
            )
            return APIResponse(
                success=True,
                message="Department already exists",
                data=existing_department,
                code=200,
            )
    except User.DoesNotExist:
        return APIResponse(
            success=False,
            message="User not found",
            data=None,
            code=400,
        )
    except Exception as e:
        return APIResponse(
            success=False,
            message="Internal server error",
            data=None,
            code=500,
        )


#  a view to add multiple departments
def add_department_to_facilities(
    facilities_to_add, facility_id, name, parent_department, description, user
):
    facilities_added = []
    if facilities_to_add:
        for item in facilities_to_add:
            if not item.id == facility_id:
                department, created = Department.objects.get_or_create(
                    name=name,
                    parent=parent_department,
                    description=description,
                    facility=item,
                )
                if created:
                    department.created_by = user
                    department.save()
                    facilities_added.append(item.name)
                else:
                    continue
            else:
                continue
        return facilities_added


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_departments(request):

    data = data
    not_found_facilities = []
    created_departments = []
    existing_departments = []
    errors = []

    # for facility_id in data["facilities"]:
    for facility in Facility.objects.all():  # data["facilities"]:
        # try:
        #     facility = Facility.objects.get(id=facility_id)
        # except Facility.DoesNotExist:
        #     not_found_facilities.append(facility_id)
        #     continue

        for department_data in data.get("departments"):
            try:
                department_name = department_data.get("name")
                department_parent_name = department_data.get("parent")
                department_description = department_data.get("description")
                department_header_email = department_data.get("header_of_department")

                existing_dep = Department.objects.filter(
                    name=department_name, facility=facility
                ).first()
                if existing_dep:
                    existing_departments.append(f"{department_name} in {facility.name}")
                    continue

                head_of_department = None

                if department_header_email:
                    head_of_department, _ = User.objects.get_or_create(
                        email=department_header_email,
                        defaults={"username": department_header_email},
                    )

                department = Department.objects.create(
                    name=department_name,
                    description=department_description,
                    header_of_department=head_of_department,
                    facility=facility,
                )
                if department:
                    # assign_users
                    for user in User.objects.all():
                        try:
                            user_profile = Profile.objects.get(user=user)
                            user_profile.access_to_department.add(department)

                        except Profile.DoesNotExist:
                            continue
                    created_departments.append(
                        {
                            "id": department.id,
                            "name": department.name,
                            "facility": (
                                department.facility.name
                                if department.facility
                                else None
                            ),
                        }
                    )
                else:
                    continue
            except KeyError as e:
                errors.append(f"Missing required field")
            except Exception as e:
                errors.append(f"Error creating department {department_name}")
    response_data = {
        "created_departments": created_departments,
        "existing_departments": existing_departments,
        "not_found_facilities": not_found_facilities,
        "errors": errors,
    }

    if errors:
        return Response(response_data, status=status.HTTP_400_BAD_REQUEST)

    return Response(response_data, status=status.HTTP_201_CREATED)
