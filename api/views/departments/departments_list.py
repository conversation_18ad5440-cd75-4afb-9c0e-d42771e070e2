from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from adverse_drug_reaction.services.operations import ADRService
from api.views.departments.new_department import new_department
from base.serializers import DepartmentSerializer
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from adverse_drug_reaction.models import AdverseDrugReaction
from base.models import Department
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService
from general_patient_visitor.services.operations import GPVService
from lost_and_found.services.operations import LostAndFoundService
from medication_error.services.operations import MedicationErrorService
from patient_visitor_grievance.services.operations import GrievanceService
from staff_incident_reports.models import StaffIncidentReport
from general_patient_visitor.models import GeneralPatientVisitor
from drf_spectacular.utils import extend_schema

from patient_visitor_grievance.models import Grievance
from patient_visitor_grievance.models import GrievanceInvestigation
from staff_incident_reports.models import StaffIncidentInvestigation
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from staff_incident_reports.services.operations import StaffIncidentReportService
from workplace_violence_reports.models import WorkPlaceViolence
from facilities.services.departments import DepartmentService
from workplace_violence_reports.services.operations import WorkplaceOperations

service = DepartmentService()
logging_service = LoggingService()


@extend_schema(request=DepartmentSerializer, responses=DepartmentSerializer)
@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def departments_api_view(request):
    if request.method == "GET":

        try:
            response = service.get_departments(request.user, request.query_params)

            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    elif request.method == "POST":
        response = service.create_department(request.data, request.user)

        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            response.data,
            status=response.code,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def departments_list(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        try:
            departments = Department.objects.all()
            departments_list = DepartmentSerializer(departments, many=True).data
            return Response({"departments": departments_list})
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
            )

