from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from base.services.auth import verify_user
from base.services.logging.logger import LoggingService
from complaints.models import Complaint
from base.models import Department
from reviews.serializers import ComplaintSerializer

logging_service = LoggingService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def department_complaints_api(request, department_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        department = Department.objects.get(id=department_id)
        complaints = Complaint.objects.filter(department=department)
        # complaints = Complaint.objects.all()
        complaints_list = ComplaintSerializer(complaints, many=True).data
        return Response({"complaints": complaints_list})
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )
