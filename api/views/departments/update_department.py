from rest_framework import status
from django.contrib.auth.models import User
from django.contrib.auth.models import Group
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from api.views.auth.permissions_list import has_permissions, is_super_user
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.models import Department
from drf_spectacular.utils import extend_schema

from base.serializers import (
    DepartmentSerializer,
    UpdateDepartmentSerializer,
)
from base.services.logging.logger import LoggingService

logging_service = LoggingService()


@extend_schema(request=DepartmentSerializer, responses=DepartmentSerializer)
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def updated_department(request, department_id):
    if request.method == "PATCH":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        if not is_super_user(user):
            return Response(
                {
                    "status": "failed",
                    "message": "You do not have the required permissions",
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # Create a copy of request data to avoid mutation
        request_copy = request.data.copy()

        try:
            department = Department.objects.get(id=department_id)

            # Handle parent department
            if "parent" in request_copy:
                try:
                    parent_department = Department.objects.get(
                        id=request_copy["parent"]
                    )
                    request_copy["parent"] = parent_department.id
                except Department.DoesNotExist:
                    return Response(
                        {"status": "failed", "message": "Parent department not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

            # Remove 'members' from request data for now
            request_copy.pop("members", None)

            # Update department details
            updated_department = UpdateDepartmentSerializer(
                department,
                data=request_copy,
                partial=True,
            )
            if updated_department.is_valid():
                updated_department.save()
                # handle head of department if included in the request
                if "header_of_department" in request.data:
                    try:
                        header_id = request.data["header_of_department"]
                        header_id = (
                            int(header_id) if isinstance(header_id, str) else header_id
                        )
                        head_of_department = User.objects.get(id=header_id)

                        department.header_of_department = head_of_department
                        department.save()
                        if (
                            not department.header_of_department
                            in department.members.all()
                        ):
                            department.members.add(head_of_department)
                        department.save()
                    except User.DoesNotExist:
                        return Response(
                            {
                                "status": "failed",
                                "message": "Head of department not found",
                            },
                            status=status.HTTP_404_NOT_FOUND,
                        )
                # Handle members if included in the request
                if "members" in request.data:
                    success, message, failed_users, existing_users = (
                        add_member_to_department(
                            department_id, request.data["members"], user
                        )
                    )
                    if not success:
                        return Response(
                            {"status": "partial_success", "message": message},
                            status=status.HTTP_202_ACCEPTED,
                        )

                return Response(
                    {
                        "status": "success",
                        "message": "Department updated successfully",
                        "department": updated_department.data,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {
                        "status": "failed",
                        "message": "Failed to update department",
                        "errors": updated_department.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Department.DoesNotExist:
            return Response(
                {"status": "failed", "message": "Department not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"status": "failed", "error": "Internal server error"},
                status=status.HTTP_400_BAD_REQUEST,
            )


@extend_schema(request=DepartmentSerializer, responses=DepartmentSerializer)
@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def delete_department(request, department_id):
    if request.method == "DELETE":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        if has_permissions(user, "managers"):

            try:
                department = Department.objects.get(id=department_id)
                department.delete()
                return Response(
                    {"message": "Department deleted successfully"},
                    status=status.HTTP_200_OK,
                )
            except Department.DoesNotExist:
                return Response(
                    {"status": "failed", "message": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
            except Exception as e:
                logging_service.log_error(e)
                return Response(
                    {"error": "Internal server error"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {
                    "status": "failed",
                    "message": "You do not have the required permissions",
                },
                status=status.HTTP_403_FORBIDDEN,
            )


# add user to department
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def add_user_to_department(request, department_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    if not is_super_user(user):
        return Response(
            {
                "status": "failed",
                "message": "You do not have the required permissions",
            },
            status=status.HTTP_403_FORBIDDEN,
        )

    users = request.data.get("users")
    if not users:
        return Response(
            {"status": "failed", "message": "User ID is required"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    success, message, failed_users, add_users = add_user_to_department(
        users, department_id, user
    )
    if success:
        return Response(
            {"message": "Users added successfully"}, status=status.HTTP_200_OK
        )
    else:
        return Response(
            {"message": "Failed to add users", message: failed_users},
            status=status.HTTP_400_BAD_REQUEST,
        )


from django.db import transaction


def add_member_to_department(department_id, users, logged_in_user):
    failed_users = []
    existing_users = []
    try:
        department = Department.objects.get(id=department_id)

        with transaction.atomic():
            for user in users:
                if "id" in user:
                    try:
                        user_obj = User.objects.get(id=user["id"])
                        if user_obj in department.members.all():
                            existing_users.append(user)
                        else:
                            department.members.add(user_obj)
                            if not department.facility.staff_members.filter(
                                id=user_obj.id
                            ).exists():
                                department.facility.staff_members.add(user_obj)
                    except User.DoesNotExist:
                        failed_users.append(user)
                else:
                    failed_users.append(user)

            department.updated_by = logged_in_user
            department.save()

        return (
            True,
            "User(s) added to department successfully",
            failed_users,
            existing_users,
        )
    except Department.DoesNotExist:
        return (False, "Department not found", [], [])
    except Exception as e:
        logging_service.log_error(e)
        return (False, "Internal server error", [], [])


# department members api


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def department_members(request, department_id):
    try:
        department = Department.objects.get(id=department_id)
        head_of_department_email = (
            department.header_of_department.email
            if department.header_of_department
            else None
        )
        members = department.members.all()
        members_list = []
        for member in members:
            members_list.append(
                {
                    "id": member.id,
                    "username": member.username,
                    "email": member.email,
                    "first_name": member.first_name,
                    "last_name": member.last_name,
                    "is_head": (
                        head_of_department_email == member.email
                        if head_of_department_email
                        else False
                    ),
                }
            )
        return Response({"members": members_list}, status=status.HTTP_200_OK)
    except Department.DoesNotExist:
        return Response(
            {"status": "failed", "message": "Department not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )
