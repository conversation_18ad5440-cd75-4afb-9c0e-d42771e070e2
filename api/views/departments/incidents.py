from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from adverse_drug_reaction.services.operations import ADRService
from base.services.auth import verify_user
from base.models import Department
from base.services.logging.logger import LoggingService
from general_patient_visitor.services.operations import GPVService
from lost_and_found.services.operations import LostAndFoundService
from medication_error.services.operations import MedicationErrorService
from patient_visitor_grievance.services.operations import GrievanceService
from patient_visitor_grievance.models import GrievanceInvestigation
from staff_incident_reports.models import StaffIncidentInvestigation
from staff_incident_reports.services.operations import StaffIncidentReportService
from facilities.services.departments import DepartmentService
from workplace_violence_reports.services.operations import WorkplaceOperations

service = DepartmentService()
logging_service = LoggingService()

@api_view(["GET"])
def department_incidents(request, department_id):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_401_UNAUTHORIZED,
            )

        gpv_service = GPVService(request.user)
        staff_incidents_service = StaffIncidentReportService(request.user)
        adr_incidents_service = ADRService(request.user)
        grievance_service = GrievanceService(request.user)
        lost_found_service = LostAndFoundService(request.user)
        workplace_violence_service = WorkplaceOperations(request.user)
        medication_error_service = MedicationErrorService(request.user)
        try:
            department = Department.objects.get(id=department_id)
            
            # Fetch all incident data
            general_incidents = gpv_service.get_incidents_list(
                filters={"department_id": department_id}
            )
            adr_incidents = adr_incidents_service.get_incidents_list(
                filters={"department_id": department_id}
            )
            staff_incidents = staff_incidents_service.get_incidents_list(
                filters={"department_id": department_id}
            )
            grievances_incidents = grievance_service.get_incidents_list(
                filters={"department_id": department_id}
            )
            lost_and_founds = lost_found_service.get_incidents_list(
                filters={"department_id": department_id}
            )
            workplace_violence = workplace_violence_service.get_incidents_list(
                filters={"department_id": department_id}
            )
            medication_error = medication_error_service.get_incidents_list(
                filters={"department_id": department_id}
            )
            
            # Process incident data with safety checks
            general = get_incident_data(general_incidents.data) if general_incidents.data else []
            adverse_drug_reaction_incident = get_incident_data(adr_incidents.data) if adr_incidents.data else []
            employee_incidents_report = get_incident_data(staff_incidents.data) if staff_incidents.data else []
            employee_health_investigation_report = get_incident_data(
                StaffIncidentInvestigation.objects.filter(department=department)
            ) or []
            grievances = get_incident_data(grievances_incidents.data) if grievances_incidents.data else []
            grievance_investigation = get_incident_data(
                GrievanceInvestigation.objects.filter(department=department)
            ) or []
            workplace_violence_incident = get_incident_data(workplace_violence.data) if workplace_violence.data else []
            medication_error_incident = get_incident_data(medication_error.data) if medication_error.data else []
            lost_and_found_incident = get_incident_data(lost_and_founds.data) if lost_and_founds.data else []
            
            incidents_list = {
                "general": general,
                "adverse_drug_reaction": adverse_drug_reaction_incident,
                "employee_incidents": employee_incidents_report,
                "employee_health_investigation": employee_health_investigation_report,
                "lost_and_founds": lost_and_found_incident,
                "grievances": grievances,
                "workplace_violence": workplace_violence_incident,
                "medication_error": medication_error_incident,
                "grievance_investigation": grievance_investigation,
            }
            
            # Combined loop to add categories - single loop with safety checks
            incident_categories = [
                (general, "General Patient Visitor"),
                (adverse_drug_reaction_incident, "Adverse Drug Reaction"),
                (employee_incidents_report, "Staff Incidents Report"),
                (lost_and_found_incident, "Lost & Found"),
                (grievances, "Patient Visitor Grievance"),
                (workplace_violence_incident, "Workplace Violence Reports"),
                (medication_error_incident, "Medication Error"),
            ]
            
            for incident_list, category_name in incident_categories:
                if incident_list:
                    for incident in incident_list:
                        incident["category"] = category_name

            return Response(incidents_list, status=status.HTTP_200_OK)

        except Department.DoesNotExist:
            return Response(
                {"error": f"Department with id '{department_id}' not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response({"error": f"Internal server error: {e}"})


def get_incident_data(incidents):
    if incidents:
        return [
            {
                "id": incident.get("id"),
                "created_at": incident.get("created_at"),
                "created_by": (
                    {
                        "first_name": incident.get("created_by", {}).get("first_name"),
                        "last_name": incident.get("created_by", {}).get("last_name"),
                        "email": incident.get("created_by", {}).get("email"),
                    }
                    if incident.get("created_by")
                    else {}
                ),
                "assignees": [
                    {
                        "id": assignee.get("id"),
                        "first_name": assignee.get("first_name"),
                        "last_name": assignee.get("last_name"),
                        "email": assignee.get("email"),
                    }
                    for assignee in incident.get("assignees", [])
                ],
                "status": incident.get("status"),
            }
            for incident in incidents
        ]
