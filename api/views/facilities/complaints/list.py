from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>NParser

from base.services.auth import verify_user
from complaints.models import Complaint
from base.models import Facility
from reviews.serializers import ComplaintSerializer


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser])
def facility_complaints_list_api(request, facility_id):
    try:
        facility = Facility.objects.get(id=facility_id)
        complaints = Complaint.objects.filter(complain_facility=facility)
        serializer = ComplaintSerializer(complaints, many=True)
        return Response({"complaints": serializer.data}, status=status.HTTP_200_OK)

    except Facility.DoesNotExist:
        return Response(
            {"error": "Facility not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
