from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from base.services.auth import verify_user
from django.contrib.auth.models import User
from base.models import Facility
from facilities.serializers import FacilitySerializer


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def facilities_details_api(request, facility_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"message": "You are not authenticated"},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        facility = Facility.objects.get(id=facility_id)
        data = FacilitySerializer(facility).data
        return Response(
            data,
            status=status.HTTP_200_OK,
        )
    except Facility.DoesNotExist:
        return Response(
            {"message": "Facility not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
