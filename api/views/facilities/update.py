from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from base.services.auth import verify_user
from django.contrib.auth.models import User
from base.models import Facility


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def update_facility_api(request, facility_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"message": "You are not authenticated"},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    name = request.data.get("name")
    address = request.data.get("address")
    phone_number = request.data.get("phone_number")
    email = request.data.get("email")
    facility_type = request.data.get("facility_type")
    ceo = request.data.get("ceo")
    contact_person = request.data.get("contact_person")
    try:
        facility = Facility.objects.get(id=facility_id)
        facility.updated_by = user
        facility.name = name
        facility.address = address
        facility.phone_number = phone_number
        facility.email = email
        facility.facility_type = facility_type
        facility.ceo = User.objects.get(id=ceo) if ceo else None
        facility.contact_person = (
            User.objects.get(id=contact_person) if contact_person else None
        )
        facility.save()
        return Response(
            {
                "message": "Facility updated successfully",
                "data": {
                    "id": facility.id,
                    "name": facility.name,
                    "address": facility.address,
                    "phone_number": facility.phone_number,
                    "email": facility.email,
                    "facility_type": facility.facility_type,
                    "ceo": (
                        {
                            "id": facility.ceo.id,
                            "username": facility.ceo.username,
                            "first_name": facility.ceo.first_name,
                            "last_name": facility.ceo.last_name,
                            "email": facility.ceo.email,
                        }
                        if facility.ceo
                        else None
                    ),
                    "contact_person": (
                        {
                            "id": facility.contact_person.id,
                            "username": facility.contact_person.username,
                            "first_name": facility.contact_person.first_name,
                            "last_name": facility.contact_person.last_name,
                            "email": facility.contact_person.email,
                        }
                        if facility.contact_person
                        else None
                    ),
                },
            }
        )
    except Facility.DoesNotExist:
        return Response(
            {"message": "Facility not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
