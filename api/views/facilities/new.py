from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from base.services.auth import verify_user
from django.contrib.auth.models import User
from base.models import Facility


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_facility_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"message": "You are not authenticated"},
            status=status.HTTP_401_UNAUTHORIZED,
        )
    name = request.data.get("name")
    address = request.data.get("address")
    phone_number = request.data.get("phone_number")
    email = request.data.get("email")
    facility_type = request.data.get("facility_type")
    ceo = request.data.get("ceo")
    contact_person = request.data.get("contact_person")

    new_facility, created = Facility.objects.get_or_create(
        created_by=user,
        name=name,
        address=address,
        phone_number=phone_number,
        email=email,
        facility_type=facility_type,
    )
    data = {
        "id": new_facility.id,
        "name": new_facility.name,
        "address": new_facility.address,
        "phone_number": new_facility.phone_number,
        "email": new_facility.email,
        "facility_type": new_facility.facility_type,
    }
    if created:
        return Response(
            {
                "message": "Facility created successfully",
                "data": data,
            },
            status=status.HTTP_201_CREATED,
        )
    else:
        return Response(
            {
                "message": "Facility already exists",
                "data": data,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
