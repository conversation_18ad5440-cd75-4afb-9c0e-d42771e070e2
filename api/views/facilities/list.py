from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from api.views.auth.permissions_list import has_permissions
from base.services.auth import check_facilities_access, verify_user
from django.contrib.auth.models import User
from base.models import Facility
from facilities.serializers import FacilitySerializer


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def facilities_list_api(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"message": "You are not authenticated"},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    facilities = Facility.objects.all()
    facilities_list = []
    if not has_permissions(user, ["Admin", "Super User", "Corporate"]):

        facilities_ids, response, has_access = check_facilities_access(user)
        if not has_access:
            return Response(response, status=status.HTTP_403_FORBIDDEN)

        facilities_list = [
            facility for facility in facilities if facility.id in facilities_ids
        ]
    else:
        facilities_list = facilities
    data = FacilitySerializer(facilities_list, many=True).data

    return Response(
        data,
        status=status.HTTP_200_OK,
    )
