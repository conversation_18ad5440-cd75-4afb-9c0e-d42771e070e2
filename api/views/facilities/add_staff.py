from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.models import Profile
from base.services.auth import verify_user
from base.models import Facility  # Assuming you have a Facility model


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def add_staff_to_facility_api(request, facility_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"message": "You are not authenticated"},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        facility = Facility.objects.get(id=facility_id)
    except Facility.DoesNotExist:
        return Response(
            {"message": "Facility does not exist"},
            status=status.HTTP_404_NOT_FOUND,
        )

    successful_adds = 0
    failed_adds = 0
    errors = []

    try:
        # Iterate over the staff list in the request
        for staff_member in request.data.get("staff", []):
            try:
                profile = Profile.objects.get(id=staff_member["id"]).id
                add_staff_to_facility(facility, profile)
                successful_adds += 1
            except Profile.DoesNotExist:
                failed_adds += 1
                errors.append(f"Profile for user {staff_member['id']} does not exist")
            except Exception as e:
                failed_adds += 1
                errors.append(f"Error adding user {staff_member['id']}")

        return Response(
            {
                "message": "Process complete",
                "successful_adds": successful_adds,
                "failed_adds": failed_adds,
                "errors": errors,
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


def add_staff_to_facility(facility, profile):
    facility.staff_members.add(profile)
