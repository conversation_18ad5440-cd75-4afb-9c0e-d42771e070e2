from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.models import Profile
from base.services.auth import verify_user
from base.models import Facility
from base.models import Department
from base.services.logging.logger import LoggingService
from django.contrib.auth.models import User

# get staff members in the facility


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def staff_members_in_facility_api_old(request, facility_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"message": "You are not authenticated"},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        facility = Facility.objects.get(id=facility_id)
        staff_members = facility.staff_members.all()

        profiles_with_access = Profile.objects.filter(
            access_to_facilities__id=facility_id
        ).select_related("user")

        users_with_access = [
            profile.user for profile in profiles_with_access if profile.user
        ]

        profiles_belongs_to_facility = Profile.objects.filter(
            facility_id=facility_id
        ).select_related("user")
        profiles_belongs_to_facility_with_access = [
            profile.user for profile in profiles_belongs_to_facility if profile.user
        ]
        all_users = list(
            set(staff_members)
            | set(users_with_access)
            | set(profiles_belongs_to_facility_with_access)
        )

        staff_list = []
        for member in all_users:
            staff_list.append(
                {
                    "id": member.id,
                    "first_name": member.first_name,
                    "last_name": member.last_name,
                    "email": member.email,
                    "department": [
                        {"name": department.name, "id": department.id}
                        for department in Department.objects.filter(members=member)
                    ],
                }
            )
        return Response({"staff": staff_list}, status=status.HTTP_200_OK)
    except Facility.DoesNotExist:
        return Response(
            {"message": "Facility does not exist"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def staff_members_in_facility_api(request, facility_id):
    try:
        facility = Facility.objects.get(id=facility_id)

        staff_members = set(facility.staff_members.values_list("id", flat=True))
        users_with_access = set(
            Profile.objects.filter(access_to_facilities__id=facility_id).values_list(
                "user__id", flat=True
            )
        )
        profiles_belongs_to_facility = set(
            Profile.objects.filter(facility_id=facility_id).values_list(
                "user__id", flat=True
            )
        )

        user_ids = staff_members | users_with_access | profiles_belongs_to_facility

        all_users = User.objects.filter(id__in=user_ids)

        user_departments = Department.objects.filter(members__in=all_users).values(
            "members", "id", "name"
        )

        department_map = {}
        for dept in user_departments:
            if dept["members"] not in department_map:
                department_map[dept["members"]] = []
            department_map[dept["members"]].append(
                {"id": dept["id"], "name": dept["name"]}
            )

        staff_list = []
        for member in all_users:
            access_label = "unknown"
            if member.id in staff_members:
                access_label = "member"
            elif member.id in users_with_access:
                access_label = "access"
            elif member.id in profiles_belongs_to_facility:
                access_label = "member"

            staff_list.append(
                {
                    "id": member.id,
                    "first_name": member.first_name,
                    "last_name": member.last_name,
                    "email": member.email,
                    "department": department_map.get(member.id, []),
                    "access": access_label,
                }
            )

        return Response({"staff": staff_list}, status=status.HTTP_200_OK)

    except Facility.DoesNotExist:
        return Response(
            {"message": "Facility does not exist"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        LoggingService().log_error(e)
        return Response(
            {"error": "Backend error while getting staff"},
            status=status.HTTP_400_BAD_REQUEST,
        )
