from rest_framework.response import Response
from rest_framework import status
from base.services.auth import verify_user
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from base.models import Facility
from base.models import Department
from base.services.facilities.service import FacilitiesService
from base.services.incidents.filter_incients import FilterIncidentsService
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from general_patient_visitor.models import GeneralPatientVisitor
from adverse_drug_reaction.models import AdverseDrugReaction
from patient_visitor_grievance.models import Grievance
from patient_visitor_grievance.models import GrievanceInvestigation
from staff_incident_reports.models import StaffIncidentReport
from staff_incident_reports.models import StaffIncidentInvestigation
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from workplace_violence_reports.models import WorkPlaceViolence
from datetime import datetime

from base.services.incidents.get_incidents import GetIncidentsService

facility_service = FacilitiesService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def facility_overview_incidents(request, facility_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        all_reports = facility_service.get_facility_incidents(
            user=user,
            facility_id=facility_id,
        )
        if not all_reports.success:
            return Response(
                {"error": all_reports.message}, status=status.HTTP_400_BAD_REQUEST
            )
        return Response(all_reports.data, status=status.HTTP_200_OK)
    except Facility.DoesNotExist:
        return Response(
            {"message": "Facility not found"}, status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"status": "failed", "message": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


def format_incidents(drafts, category):

    sorted_reports = sorted(drafts, key=lambda x: x.created_at, reverse=True)

    return [
        {
            "id": incident.id,
            "status": incident.status,
            "created_at": incident.created_at,
            "current_step": incident.current_step,
            "category": category,
        }
        for incident in sorted_reports
    ]


def format_drafts(drafts, category, user):
    user_filtered_reports = drafts.filter(created_by=user)

    sorted_reports = sorted(
        user_filtered_reports, key=lambda x: x.created_at, reverse=True
    )

    return [
        {
            "id": draft.id,
            "status": draft.status,
            "created_at": draft.created_at,
            "current_step": draft.current_step,
            "category": category,
        }
        for draft in sorted_reports
    ]


# def format_incidents(drafts, category, user):
#     user_filtered_reports = drafts.filter(created_by=user)

#     sorted_reports = sorted(drafts, key=lambda x: x.created_at, reverse=True)

#     return [
#         {
#             "id": draft.id,
#             "status": draft.status,
#             "created_at": draft.created_at,
#             "current_step": draft.current_step,
#             "category": category,
#         }
#         for draft in sorted_reports
#     ]


@api_view(["POST"])
def overview_filters(request):
    # implement filter based on facility, and department and date range
    filters = request.data
    genera_incidents = GeneralPatientVisitor.objects.all()
    incidents_by_facility = []
    lost_and_lost_incidents = []
    grievance = []
    adverse_drug_reaction = []
    workplace_violence = []
    employee_incidents = []
    if "facility" in filters:
        facility_id = filters["facility"]
        try:
            facility = Facility.objects.get(id=facility_id)
            genera_incidents = genera_incidents.filter(report_facility=facility)
            lost_and_lost_incidents = LostAndFound.objects.filter(
                report_facility=facility
            )
            grievance = Grievance.objects.filter(report_facility=facility)
            adverse_drug_reaction = AdverseDrugReaction.objects.filter(
                report_facility=facility
            )
            workplace_violence = WorkPlaceViolence.objects.filter(
                report_facility=facility
            )
            employee_incidents = StaffIncidentReport.objects.filter(
                report_facility=facility
            )

            medical_errors = MedicationError.objects.filter(report_facility=facility)

        except Facility.DoesNotExist:
            return Response(
                {"error": "facility does not exists"}, status=status.HTTP_404_NOT_FOUND
            )
    if "department" in filters:
        department_id = filters["department"]
        try:
            department = Department.objects.get(id=department_id)
            genera_incidents = genera_incidents.filter(department=department)
            lost_and_lost_incidents = lost_and_lost_incidents.filter(
                department=department
            )
            grievance = grievance.filter(department=department)
            adverse_drug_reaction = adverse_drug_reaction.filter(department=department)
            workplace_violence = workplace_violence.filter(department=department)
            employee_incidents = employee_incidents.filter(department=department)
            medical_errors = medical_errors.filter(department=department)
        except Department.DoesNotExist:
            return Response(
                {"error": "department does not exists"},
                status=status.HTTP_404_NOT_FOUND,
            )

    # if date in filters, then filter by created_at
    if "date_range" in filters:
        start_date = filters["date_range"]["start_date"]
        end_date = filters["date_range"]["end_date"]

        converted_start_date = str(start_date).split("-")
        start_date = f"{converted_start_date[2]}-{converted_start_date[1]}-{converted_start_date[0]} 00:00:00"

        converted_end_date = str(end_date).split("-")
        end_date = f"{converted_end_date[2]}-{converted_end_date[1]}-{converted_end_date[0]} 00:00:00"

        start_datetime = validate_date_format(start_date, "start_date")
        if start_datetime is None:
            return Response(
                {
                    "error": "start_date has an invalid format. Expected format: YYYY-MM-DD HH:MM:SS."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate end_date
        end_datetime = validate_date_format(end_date, "end_date")
        if end_datetime is None:
            return Response(
                {
                    "error": "end_date has an invalid format. Expected format: YYYY-MM-DD HH:MM:SS."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        genera_incidents = genera_incidents.filter(
            created_at__range=(start_date, end_date)
        )
        lost_and_lost_incidents = lost_and_lost_incidents.filter(
            created_at__range=(start_date, end_date)
        )
        grievance = grievance.filter(created_at__range=(start_date, end_date))
        adverse_drug_reaction = adverse_drug_reaction.filter(
            created_at__range=(start_date, end_date)
        )
        workplace_violence = workplace_violence.filter(
            created_at__range=(start_date, end_date)
        )
        employee_incidents = employee_incidents.filter(
            created_at__range=(start_date, end_date)
        )
        medical_errors = medical_errors.filter(created_at__range=(start_date, end_date))
    if "from_date" in filters:
        from_date = filters["from_date"]
        genera_incidents = genera_incidents.filter(
            created_at__gte=validate_date_format(from_date)
        )
        lost_and_lost_incidents = lost_and_lost_incidents.filter(
            created_at__gte=validate_date_format(from_date)
        )
        grievance = grievance.filter(created_at__gte=validate_date_format(from_date))
        adverse_drug_reaction = adverse_drug_reaction.filter(
            created_at__gte=validate_date_format(from_date)
        )
        workplace_violence = workplace_violence.filter(
            created_at__gte=validate_date_format(from_date)
        )
        employee_incidents = employee_incidents.filter(
            created_at__gte=validate_date_format(from_date)
        )
        medical_errors = medical_errors.filter(
            created_at__gte=validate_date_format(from_date)
        )
    numbers = {
        "general": genera_incidents.count(),
        "lost_and_founds": lost_and_lost_incidents.count(),
        "grievances": grievance.count(),
        "adverse_drug_reactions": adverse_drug_reaction.count(),
        "workplace_violence": workplace_violence.count(),
        "employee_incidents": employee_incidents.count(),
        "medical_errors": medical_errors.count(),
    }
    return Response({"numbers": numbers}, status=status.HTTP_200_OK)


def validate_date_format(date_str, field_name):
    try:
        # Ensure the date is in 'YYYY-MM-DD HH:MM:SS' format
        return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        # Return None or raise an exception
        return None


# get all departments by a name "Quality/Risk Management".
# get objects by departments ids found
