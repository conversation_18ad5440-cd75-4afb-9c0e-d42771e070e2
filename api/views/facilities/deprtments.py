from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from api.views.auth.permissions_list import has_permissions
from base.services.auth import check_departments_access
from base.models import Facility
from base.models import Department
from accounts.models import Profile

# get department in a specific facility


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def facility_department_details_api(request, facility_id):
    user = request.user
    try:
        facility = Facility.objects.get(id=facility_id)
        departments = Department.objects.filter(facility=facility)
        department_list = []
        if not has_permissions(user, ["Admin", "Super User", "Corporate", "Director"]):

            department_ids, response, has_access = check_departments_access(user)
            if not has_access:
                return Response(response, status=status.HTTP_403_FORBIDDEN)
            department_list = [
                {
                    "id": department.id,
                    "name": department.name,
                    "description": department.description,
                    "header_of_department": (
                        department.header_of_department.email
                        if department.header_of_department
                        else None
                    ),
                    "members": department.members.count(),
                }
                for department in departments
                if department.id in department_ids
            ]

        else:
            department_list = [
                {
                    "id": department.id,
                    "name": department.name,
                    "description": department.description,
                    "header_of_department": (
                        department.header_of_department.email
                        if department.header_of_department
                        else None
                    ),
                    "members": department.members.count(),
                }
                for department in departments
            ]

        return Response({"departments": department_list}, status=status.HTTP_200_OK)

    except Facility.DoesNotExist:
        return Response(
            {"message": "Facility not found"}, status=status.HTTP_404_NOT_FOUND
        )
