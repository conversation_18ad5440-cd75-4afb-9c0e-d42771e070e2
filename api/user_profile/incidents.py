from django.contrib.auth.models import User, Group
from api.views.incidents.overview.drafts import format_drafts
from incidents.models.general_patient_visitor import Incident
from adverse_drug_reaction.models import AdverseDrugReaction
from patient_visitor_grievance.models import Grievance
from patient_visitor_grievance.models import GrievanceInvestigation
from staff_incident_reports.models import StaffIncidentReport
from staff_incident_reports.models import StaffIncidentInvestigation
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from workplace_violence_reports.models import WorkPlaceViolence
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from accounts.models import Profile
from base.services.auth import verify_user
from base.models import Department


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_incidents(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response({"error": message}, status=status.HTTP_401_UNAUTHORIZED)

    user_incidents = []
    try:
        general_incidents = format_drafts(
            Incident.objects.all(), "general incident", user
        )

        adr_incidents = format_drafts(
            AdverseDrugReaction.objects.all(), "adverse drug reaction incident", user
        )

        workplace_violence = format_drafts(
            WorkPlaceViolence.objects.all(), "workplace violation", user
        )

        grievance_incidents = format_drafts(
            Grievance.objects.all(), "grievance incident", user
        )

        grievance_investigation_incidents = format_drafts(
            GrievanceInvestigation.objects.all(), "grievance investigation", user
        )

        employee_incidents = format_drafts(
            StaffIncidentReport.objects.all(), "Staff incident", user
        )

        health_investigation_incidents = format_drafts(
            StaffIncidentInvestigation.objects.all(),
            "Staff health investigation",
            user,
        )

        lost_and_found_incidents = format_drafts(
            LostAndFound.objects.all(), "lost and found", user
        )

        medication_error_incidents = format_drafts(
            MedicationError.objects.all(), "medication error", user
        )

        user_incidents = sorted(
            adr_incidents
            + general_incidents
            + workplace_violence
            + grievance_investigation_incidents
            + employee_incidents
            + health_investigation_incidents
            + lost_and_found_incidents
            + medication_error_incidents
            + grievance_incidents,
            key=lambda x: x["created_at"],
            reverse=True,
        )

        return Response({"user_incidents": user_incidents}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
