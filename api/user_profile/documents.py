from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from base.services.auth import verify_user
from documents.models import Document


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_documents(request, user_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response({"error": message}, status=status.HTTP_401_UNAUTHORIZED)
    try:
        user_obj = None  # get the user from the database
        documents = [
            {
                "id": doc.id,
                "name": doc.name,
                "url": doc.document_url,
                "type": doc.file_type,
                "created_at": doc.created_at.isoformat(),
                "updated_at": doc.updated_at.isoformat(),
                "created_by": {
                    "id": user.id,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                },
            }
            for doc in Document.objects.filter(created_by=user_obj)
        ]
        return Response({"documents": documents}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
