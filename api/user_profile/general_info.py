from django.contrib.auth.models import User, Group
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth.hashers import make_password

from accounts.models import Profile
from api.views.auth.users import set_user_permissions
from base.services.auth import verify_user
from base.models import Facility
from base.models import Department
from api.views.auth.permissions_list import (
    has_permissions,
    is_admin_user,
    is_director_user,
    is_manager_user,
    is_super_user,
    is_user_editor,
)
from accounts.serializers import UserSerializer, ProfileSerializer
from base.services.logging.logger import LoggingService

logging_service = LoggingService()


# get other user profile information
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_profile(request, user_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        user_info = User.objects.get(id=user_id)
        department = Department.objects.filter(members=user_info).first()

        user_serializer = UserSerializer(user_info)

        # get user's profile information
        profile = Profile.objects.get(user=user_info)
        if (
            not is_super_user(user)
            and not is_admin_user(user, profile.facility)
            and not is_director_user(user, profile.facility)
            and not is_manager_user(user, department)
            and not is_user_editor(user)
        ):
            return Response(
                {"error": "You are not allowed to edit this user"},
                status=status.HTTP_403_FORBIDDEN,
            )

        profile_data = {
            "id": profile.id,
            "is_corporate": profile.is_corporate,
            "phone_number": profile.phone_number,
            "gender": profile.gender,
            "date_of_birth": profile.date_of_birth,
            "address": profile.address,
            "birth_country": profile.birth_country,
            "facility": (
                {
                    "id": profile.facility.id,
                    "name": profile.facility.name,
                }
                if profile.facility and profile
                else None
            ),
            "city": profile.city,
            "state": profile.state,
            "zip_code": profile.zip_code,
            "medical_record_number": profile.medical_record_number,
            "age": profile.age,
            "is_patient_visitor": profile.is_patient_visitor,
            "department": (
                {
                    "id": department.id,
                    "name": department.name,
                }
                if department
                else None
            ),
            "permissions": {
                "facilities": [
                    {
                        "id": facility.id,
                        "name": facility.name,
                    }
                    for facility in profile.access_to_facilities.all()
                ],
                "departments": [
                    {
                        "id": department.id,
                        "name": department.name,
                    }
                    for department in profile.access_to_department.all()
                ],
            },
        }

        return Response(
            {
                "email": user_info.email,
                "first_name": user_info.first_name,
                "last_name": user_info.last_name,
                "is_active": user_info.is_active,
                "position": user_serializer.data.get("position", None),
                "profile": profile_data,
            }
        )
    except User.DoesNotExist:
        return Response({"error": "User not found"}, status=status.HTTP_400_BAD_REQUEST)
    except Profile.DoesNotExist:
        return Response(
            {"error": "Profile not found"}, status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {
                "error": "Something went wrong",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def update_user_profile(request, user_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        user_info = User.objects.get(id=user_id)
        profile = Profile.objects.get(user=user_info)
        if (
            not is_super_user(user)
            and not is_admin_user(user, profile.facility)
            and not is_user_editor(user)
        ):
            return Response(
                {"error": "You are not allowed to edit this user"},
                status=status.HTTP_403_FORBIDDEN,
            )
        # add request.data.get("role") which returns a group name example 'Admin'
        roles = request.data.get("roles")
        if roles:
            user_roles = []
            for role in roles:
                try:
                    group = Group.objects.get(name=role)
                    user_roles.append(group)
                except Group.DoesNotExist:
                    return Response(
                        {"error": f"Role {role} does not exist"},
                        status=status.HTTP_404_NOT_FOUND,
                    )
            user_info.groups.clear()
            user_info.groups.add(*user_roles)
            user_info.save()

        user_data = request.data
        password = user_data.get("password", None)

        if password:
            user_info.set_password(password)
            user_info.save()

        user_serializer = UserSerializer(user_info, data=user_data, partial=True)

        if user_serializer.is_valid():
            user_serializer.save()
        else:
            return Response(user_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        profile_data = request.data

        facility = profile_data.get("facility", None)
        department_id = profile_data.get("department_id", None)
        if department_id:
            try:
                department = Department.objects.get(id=department_id)
                for dep in Department.objects.filter(members=user_info):
                    dep.members.remove(user_info)
                department.members.add(user_info)

            except Department.DoesNotExist:
                return Response(
                    {"error": f"Department with id {department_id} does not exist"},
                    status=status.HTTP_404_NOT_FOUND,
                )
        if facility:
            try:
                facility_name = Facility.objects.get(name=facility["name"])
                profile_data["facility"] = facility_name.id
            except Facility.DoesNotExist:
                return Response(
                    {"error": f"Facility {facility} does not exist"},
                    status=status.HTTP_404_NOT_FOUND,
                )
        profile_serializer = ProfileSerializer(profile, data=profile_data, partial=True)

        if profile_serializer.is_valid():
            profile_serializer.save()
            request.data.pop("facility", None)
            permissions_added, facilities, departments = set_user_permissions(
                profile=profile,
                data=request.data,
            )
        else:
            return Response(
                profile_serializer.errors, status=status.HTTP_400_BAD_REQUEST
            )

        user_info_data = {
            "email": user_info.email,
            "first_name": user_info.first_name,
            "last_name": user_info.last_name,
            "position": user_serializer.data.get("position", None),
            "profile": {**profile_serializer.data, "facility": facility},
        }

        return Response(user_info_data, status=status.HTTP_200_OK)
    except User.DoesNotExist:
        return Response({"error": "User not found"}, status=status.HTTP_400_BAD_REQUEST)
    except Profile.DoesNotExist:
        return Response(
            {"error": "Profile not found"}, status=status.HTTP_400_BAD_REQUsEST
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": f"Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
