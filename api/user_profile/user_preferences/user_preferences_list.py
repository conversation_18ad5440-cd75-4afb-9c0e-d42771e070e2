from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from accounts.models import Profile, UserPreference
from accounts.serializers import UserPreferenceSerializer
from drf_spectacular.utils import extend_schema

from base.services.logging.logger import LoggingService

logging_service = LoggingService()


@extend_schema(
    request=UserPreferenceSerializer,
    responses=UserPreferenceSerializer,
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_preferences_list(request):
    user = request.user

    try:
        preferences = UserPreference.objects.get(user=user)
        data = {
            "user": preferences.user.email,
            "user_timezone": preferences.user_timezone,
        }

        return Response({"status": "success", "data": data}, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "Failed", "message": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
