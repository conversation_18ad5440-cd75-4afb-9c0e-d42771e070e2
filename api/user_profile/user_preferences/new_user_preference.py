from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from accounts.models import Profile, UserPreference
from accounts.serializers import UserPreferenceSerializer
from drf_spectacular.utils import extend_schema
from base.services.logging.logger import LoggingService

logging_service = LoggingService()


@extend_schema(
    request=UserPreferenceSerializer,
    responses=UserPreferenceSerializer,
)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def set_user_preference(request):
    user = request.user

    try:
        profile = Profile.objects.get(user=user)

        user_preference, created = UserPreference.objects.get_or_create(user=user)

        preference = UserPreferenceSerializer(
            user_preference, data=request.data, partial=True
        )

        if preference.is_valid():
            preference.save()
            message = (
                "User preference created successfully"
                if created
                else "User preference updated successfully"
            )

            return Response(
                {"status": "success", "message": message, "data": preference.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "failed", "error": "Failed to save the preference"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    except Profile.DoesNotExist:
        return Response(
            {"status": "failed", "error": "User does not have a profile"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"status": "failed", "error": "Something went wrong"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
