from django.urls import path

from api.views.departments.complaints import department_complaints_api
from api.views.departments.departments_list import (
    departments_api_view,
)
from api.views.departments.incidents import department_incidents
from api.views.departments.details import department_details_api
from api.views.departments.update_department import (
    add_user_to_department,
    delete_department,
    department_members,
    updated_department,
)
from api.views.facilities.add_staff import add_staff_to_facility_api
from api.views.facilities.complaints.list import facility_complaints_list_api
from api.views.facilities.deprtments import facility_department_details_api
from api.views.facilities.details import facilities_details_api
from api.views.facilities.incidents.oveview import facility_overview_incidents
from api.views.facilities.list import facilities_list_api
from api.views.facilities.new import new_facility_api
from api.views.facilities.staff_members import staff_members_in_facility_api
from api.views.facilities.update import update_facility_api

urlpatterns = [
    path(
        "",
        facilities_list_api,
        name="facilities_list_api",
    ),
    path(
        "<int:facility_id>/",
        facilities_details_api,
        name="facilities_details_api",
    ),
    path(
        "<int:facility_id>/staff/",
        staff_members_in_facility_api,
        name="staff_members_in_facility_api",
    ),
    path("new/", new_facility_api, name="new_facility_api"),
    path(
        "<int:facility_id>/update/",
        update_facility_api,
        name="update_facility_api",
    ),
    path(
        "<int:facility_id>/add-staff/",
        add_staff_to_facility_api,
        name="add_staff_to_facility_api",
    ),
    # incidents
    path(
        "<int:facility_id>/incidents/overview/",
        facility_overview_incidents,
        name="facility_overview_incidents",
    ),
    # complaints
    path(
        "<int:facility_id>/complaints/",
        facility_complaints_list_api,
        name="facility_complaints_list_api",
    ),
    # departments
    path(
        "departments/",
        departments_api_view,
        name="departments",
    ),
    path(
        "<int:department_id>/",
        department_details_api,
        name="departments",
    ),
    path(
        "departments/<int:department_id>/update/",
        updated_department,
        name="departments",
    ),
    path(
        "departments/<int:department_id>/delete/",
        delete_department,
        name="delete_department",
    ),
    path(
        "departments/<int:department_id>/members/",
        department_members,
        name="department_members",
    ),
    path(
        "departments/<int:department_id>/members/new/",
        add_user_to_department,
        name="add_user_to_department",
    ),
    # incidents
    path(
        "departments/<int:department_id>/incidents/",
        department_incidents,
        name="department_incidents",
    ),
    # complaints
    path(
        "departments/<int:department_id>/complaints/",
        department_complaints_api,
        name="department_details_api",
    ),
]
