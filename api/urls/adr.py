from django.urls import path

from api.views.incidents.adverse_drug_reaction.version import (
    adverse_drug_reaction_version,
)
from api.views.incidents.adverse_drug_reaction.views import *

urlpatterns = [
    path(
        "",
        adverse_drug_reactions_api,
        name="new_adverse_drug_reaction_details_api",
    ),
    path(
        "<int:id>/",
        adverse_drug_reactions_details_api,
        name="adverse_drug_reactions_details_api",
    ),
    path(
        "<int:incident_id>/tasks/",
        adverse_drug_reactions_tasks,
        name="adverse_drug_reactions_delete_api",
    ),
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        adverse_drug_reaction_version,
        name="adverse_drug_reactions_versions_api",
    ),
    path(
        "<int:incident_id>/documents/",
        adverse_drug_reactions_documents_api,
        name="adverse_drug_reactions_documents_api",
    ),
    path(
        "<int:incident_id>/reviews/",
        get_adverse_drug_reaction_reviews_api,
        name="adverse_drug_reactions_reviews_api",
    ),
]
