from django.urls import path

from api.views.incidents.general_incident.versions import (
    general_incident_original_version,
)
from api.views.incidents.lost_and_found.incident_list import lost_and_lost_api
from api.views.incidents.lost_and_found.new_incident import new_lost_and_found_api
from api.views.incidents.lost_and_found.reviews import (
    lost_and_found_reviews_api,
    new_lost_and_found_review_api,
)
from api.views.incidents.lost_and_found.update_incident import (
    update_lost_and_found_api,
    mark_lost_and_found_incident_as_resolved,
    send_lost_and_found_to_department,
)
from api.views.incidents.lost_and_found.modify_lost_and_found_incident import (
    modify_lost_and_found_incident,
)
from api.views.incidents.lost_and_found.incident_details import (
    get_lost_and_found_details_api,
)
from api.views.incidents.lost_and_found.documents import (
    lost_and_found_documents_api,
    lost_and_found_documents_list_api,
    delete_lost_and_found_documents,
)

from api.views.incidents.lost_and_found.delete_draft_incidents import (
    delete_lost_and_draft_incidents,
)
from api.views.incidents.lost_and_found.version import (
    lost_and_found_incident_version,
    lost_and_found_original_version,
)

urlpatterns = [
    path("", lost_and_lost_api, name="lost_and_lost_api"),
    path("new/", new_lost_and_found_api, name="new_lost_and_found"),
    path(
        "<int:lost_and_found_id>/",
        get_lost_and_found_details_api,
        name="get_lost_and_found_details",
    ),
    # versions
    # version
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        lost_and_found_incident_version,
        name="lost_and_found_incident_version",
    ),
    path(
        "<int:incident_id>/versions/original/",
        lost_and_found_original_version,
        name="lost_and_found_original_version",
    ),
    # documents
    path(
        "<int:lost_and_found_id>/documents/new/",
        lost_and_found_documents_api,
        name="upload_lost_found_incident_files",
    ),
    path(
        "<int:lost_and_found_id>/documents/",
        lost_and_found_documents_list_api,
        name="lost_and_found_documents_list_api",
    ),
    path(
        "<int:incident_id>/documents/<int:document_id>/delete/",
        delete_lost_and_found_documents,
        name="delete_lost_and_found_documents",
    ),
    # updating urls
    path(
        "<int:lost_and_found_id>/update/",
        update_lost_and_found_api,
        name="update_lost_and_found",
    ),
    path(
        "<int:lost_and_found_id>/resolve/",
        mark_lost_and_found_incident_as_resolved,
        name="mark_lost_and_found_incident_as_resolved",
    ),
    path(
        "<int:lost_and_found_id>/modify/",
        modify_lost_and_found_incident,
        name="modify_lost_and_found_incident",
    ),
    path(
        "<int:lost_and_found_id>/send-to-department/",
        send_lost_and_found_to_department,
        name="send_lost_and_found_to_department",
    ),
    # reviews
    path(
        "<int:incident_id>/reviews/new/",
        new_lost_and_found_review_api,
        name="new_lost_and_found_review",
    ),
    path(
        "<int:incident_id>/reviews/",
        lost_and_found_reviews_api,
        name="lost_and_found_reviews",
    ),
    # end of reviews
    # deleting lost and found drafts
    path(
        "drafts/delete-multiple/",
        delete_lost_and_draft_incidents,
        name="delete_lost_and_draft_incidents",
    ),
]
