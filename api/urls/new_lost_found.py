from django.urls import path

from api.views.incidents.lost_and_found.new_lost_found import *
from api.views.incidents.lost_and_found.version import lost_and_found_incident_version


urlpatterns = [
    path(
        "",
        lost_found_incidents_api,
        name="lost_found_incidents_api",
    ),
    path(
        "<int:id>/",
        lost_found_incident_details_api,
        name="lost_found_incident_details_api",
    ),
    # version
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        lost_and_found_incident_version,
        name="lost_and_found_incident_version",
    ),
    path(
        "<int:incident_id>/documents/",
        lost_found_incident_documents_api,
        name="lost_found_incident_documents_api",
    ),
    path(
        "<int:incident_id>/reviews/",
        lost_found_incident_reviews_api,
        name="lost_found_incident_reviews_api",
    ),
]
