from django.urls import path
from api.views.incidents.overview.drafts import user_incidents_draft_api
from incidents.views.employees_views import *


urlpatterns = [
    # Staff health investigation incident
    # path("new/employee_health_investigation/", new_employee_health_investigation, name = "new_employee_health_investigation"),
    path("new/Staff/", initial_report, name="new_incident_report"),
    path(
        "update/Staff/incident_description/",
        update_incident_description,
        name="update_incident_description",
    ),
    path("update/Staff/final_report/", update_final_report, name="update_final_report"),
    path(
        "update/Staff/complete/",
        update_report_completed,
        name="update_report_complete",
    ),
    path(
        "get/Staff/incident_list/",
        list_reports,
        name="retrieve_employee_incident_list",
    ),
    path(
        "get/Staff/retrieve_report/",
        retrieve_report,
        name="retrieve_employee_incident_report",
    ),
]
