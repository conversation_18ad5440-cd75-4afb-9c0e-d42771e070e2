from django.urls import path

from api.views.permissions import *
from api.views.review_groups import *
from api.views.review_templates import (
    review_template_api_view,
    review_template_details_api_view,
)
from api.views.tasks import (
    review_template_task_api_view,
    review_template_task_details_api_view,
    review_template_task_review_api_view,
)

# from api.views.tasks import (
#     review_template_task_api_view,
#     review_template_task_details_api_view,
#     review_template_task_review_api_view,
# )

urlpatterns = [
    # review groups
    path(
        "review-groups/",
        review_groups_api_view,
        name="review_groups_api_view",
    ),
    path(
        "review-groups/<int:group_id>/",
        review_group_details_api_view,
        name="review_group_details_api_view",
    ),
    path(
        "review-groups/<int:group_id>/members/",
        review_group_members_api_view,
        name="review_group_details_api_view",
    ),
    # review templates
    path(
        "review-templates/",
        review_template_api_view,
        name="review_template_api_view",
    ),
    path(
        "review-templates/<int:template_id>/",
        review_template_details_api_view,
        name="review_template_details_api_view",
    ),
    path(
        "review-templates/<int:template_id>/tasks/",
        review_template_task_api_view,
        name="review_template_task_api_view",
    ),
    path(
        "review-templates/<int:template_id>/tasks/<int:task_id>/",
        review_template_task_details_api_view,
        name="review_template_task_details_api_view",
    ),
    path(
        "review-templates/<int:template_id>/tasks/<int:task_id>/review-groups/",
        review_template_task_review_api_view,
        name="review_template_task_review_api_view",
    ),
    path(
        "",
        permission_groups_api_view,
        name="permission_groups_api_view",
    ),
    path(
        "<group_id>/",
        permission_group_details_api_view,
        name="permission_group_details_api_view",
    ),
    path(
        "<group_id>/users/",
        permission_group_users_api_view,
        name="permission_group_users_api_view",
    ),
    path(
        "<group_id>/remove-permissions/",
        remove_permissions_from_group_api_view,
        name="remove_permissions_from_group_api_view",
    ),
    path(
        "features/<feature>/",
        feature_permission_api_view,
        name="feature_permission_api_view",
    ),
]
