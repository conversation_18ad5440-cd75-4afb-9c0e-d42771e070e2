from django.urls import path
from api.views.incidents.general_incident import *
from api.views.incidents.general_incident.documents import (
    general_incident_documents_api,
    get_general_incident_document_list,
    delete_general_incident_document,
)
from api.views.incidents.general_incident.incident_details import (
    general_incident_detail,
)
from api.views.incidents.general_incident.incident_list import get_incidents
from api.views.incidents.general_incident.modify_general_incident import (
    modify_general_incident,
)
from api.views.incidents.general_incident.reviews import (
    general_incident_reviews,
    new_general_incident_review,
    new_general_incident_review_test,
)

from api.views.incidents.general_incident.versions import (
    general_incident_original_version,
    general_incident_version,
)
from documents.views import upload_uncompressed_files
from incidents.views.employees_views import *
from api.views.incidents.general_incident.new_incident import new_incident
from api.views.incidents.general_incident.update_incident import (
    mark_general_incident_resolved,
    send_general_incident_to_department,
    update_incident_api,
)
from api.views.incidents.general_incident.delete_draft_incidents import (
    delete_general_draft_incidents,
)


urlpatterns = [
    path("", get_incidents, name="incidents_list"),
    path("<int:incident_id>/", general_incident_detail, name="get_incident"),
    
    # version
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        general_incident_version,
        name="general_incident_version",
    ),
    path(
        "<int:incident_id>/versions/original/",
        general_incident_original_version,
        name="general_incident_original_version",
    ),
    path("new/", new_incident, name="new_incident"),
    path(
        "<int:incident_id>/update/",
        update_incident_api,
        name="update_general_incident",
    ),
    # modify incident
    path(
        "<int:incident_id>/modify/",
        modify_general_incident,
        name="update_general_incident",
    ),
    path(
        "<int:incident_id>/reviews/new/",
        new_general_incident_review_test,
        name="new_general_incident_review",
    ),
    path(
        "<int:incident_id>/reviews/",
        general_incident_reviews,
        name="general_incident_reviews",
    ),
    path(
        "<int:incident_id>/send-to-department/",
        send_general_incident_to_department,
        name="send_general_incident_to_department",
    ),
    path(
        "<int:incident_id>/resolve/",
        mark_general_incident_resolved,
        name="mark_general_incident_resolved",
    ),
    path(
        "<int:incident_id>/documents/new/",
        general_incident_documents_api,
        name="upload_files",
    ),
    path(
        "<int:incident_id>/documents/",
        get_general_incident_document_list,
        name="upload_files",
    ),
    path(
        "<int:incident_id>/documents/<int:document_id>/delete/",
        delete_general_incident_document,
        name="delete_general_incident_document",
    ),
    path(
        "drafts/delete-multiple/",
        delete_general_draft_incidents,
        name="delete_general_draft_incidents",
    ),
]
