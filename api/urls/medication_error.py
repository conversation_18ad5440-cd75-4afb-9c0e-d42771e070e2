from django.urls import path


from api.views.incidents.medication_error.new_incident import *
from api.views.incidents.medication_error.reviews import (
    medical_error_reviews_api,
    new_medical_error_review_api,
)
from api.views.incidents.medication_error.update_incident import *
from api.views.incidents.medication_error.incident_list import *
from api.views.incidents.medication_error.incident_details import (
    medical_error_details_api,
)
from api.views.incidents.medication_error.documents import (
    medication_error_documents_api,
    medication_error_documents_list_api,
    delete_medication_error_documents,
)

from api.views.incidents.medication_error.modify_medication_error_incident import (
    modify_medication_error_incident,
)

from api.views.incidents.medication_error.delete_drafts_incidents import (
    delete_medication_error_draft_incidents,
)
from api.views.incidents.medication_error.version import (
    medication_error_incident_version,
    medication_error_original_version,
)


urlpatterns = [
    path("new/", new_medical_error, name="new_medical_error"),
    path("<int:id>/update/", update_medical_error, name="update_medical_error"),
    path("", list_medical_error_incident, name="list_medical_errors"),
    path(
        "<int:medication_error_id>/",
        medical_error_details_api,
        name="medication_error_details",
    ),
    path(
        "<int:medication_error_id>/modify/",
        modify_medication_error_incident,
        name="medication_error_details",
    ),
    path(
        "<int:medication_error_id>/resolve/",
        mark_medical_error_incident_as_resolved,
        name="medication_error_details",
    ),
    # version
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        medication_error_incident_version,
        name="medication_error_incident_version",
    ),
    path(
        "<int:incident_id>/versions/original/",
        medication_error_original_version,
        name="medication_error_original_version",
    ),
    # documents
    path(
        "<int:medication_error_id>/documents/new/",
        medication_error_documents_api,
        name="medication_error_details",
    ),
    path(
        "<int:medication_error_id>/documents/",
        medication_error_documents_list_api,
        name="medication_error_details",
    ),
    path(
        "<int:incident_id>/documents/<int:document_id>/delete/",
        delete_medication_error_documents,
        name="delete_medication_error_documents",
    ),
    # end of documents
    path(
        "<int:incident_id>/send-to-department/",
        send_medical_error_to_department_api,
        name="send_medical_error_to_department_api",
    ),
    # reviews
    path(
        "<int:incident_id>/reviews/new/",
        new_medical_error_review_api,
        name="new_medical_error_review_api",
    ),
    path(
        "<int:incident_id>/reviews/",
        medical_error_reviews_api,
        name="medical_error_reviews_api",
    ),
    # end of reviews
    # deleting incident drafts
    path(
        "drafts/delete-multiple/",
        delete_medication_error_draft_incidents,
        name="delete_medication_error_incidents",
    ),
]
