from django.urls import path

from api.views.facilities.incidents.oveview import overview_filters
from api.views.incidents.overview.drafts import user_incidents_draft_api
from api.views.incidents.overview.overview import *

urlpatterns = [
    path("", incident_overview_data, name="incident_overview_data"),
    path("draft/user/", user_incidents_draft_api, name="user_incidents_draft_api"),
    # general incidents overview
    path(
        "general-incident/",
        general_incidents_overview,
        name="general_incidents_overview",
    ),
    # adr overview
    path(
        "adverse-drug-reaction-incident/",
        adverse_drug_reaction_incidents_overview,
        name="adverse_drug_reaction_incidents_overview",
    ),
    # workplace overview
    path(
        "workplace-violence-incident/",
        workplace_violence_overview,
        name="workplace_violence_overview",
    ),
    # Staff health investigation overview
    path(
        "staff-health-investigation-incident/",
        employee_health_investigation_overview,
        name="employee_health_investigation_overview",
    ),
    # grievance investigation overview
    path(
        "grievance-investigation-incident/",
        grievance_investigation_overview,
        name="grievance_investigation_overview",
    ),
    # lost and found overview
    path(
        "lost-and-found-incident/",
        lost_and_found_overview,
        name="lost_and_found_overview",
    ),
    # medication error overview
    path(
        "medication-error-incident/",
        medication_error_incident_overview,
        name="medication_error_incident_overview",
    ),
    # Staff incident overview
    path(
        "staff-incident/",
        employee_incident_overview,
        name="employee_incident_overview",
    ),
    # grievance incident overview
    path(
        "grievance-incident/",
        grievance_incident_overview,
        name="grievance_incident_overview",
    ),
    # complaints overview
    path("complaints/", complaints_overview, name="complaints_overview"),
    # filters
    path("filter/", overview_filters, name="overview_filters"),
]
