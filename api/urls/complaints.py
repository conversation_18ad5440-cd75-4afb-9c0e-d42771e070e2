from django.urls import path

# New complaints views
from api.views.complaints import complaints_list_create_api, complaints_details_api

# Old complaints views (keeping for backward compatibility)
from api.views.complaint_old.detail import complaint_detail_api
from api.views.complaint_old.list import complaints_list_api
from api.views.complaint_old.new import new_complaint_api
from api.views.complaint_old.send_to_department import send_complaint_to_department_api
from api.views.complaint_old.update import delete_complaint_api, update_complaint_api

urlpatterns = [
    # New REST API endpoints
    path("", complaints_list_create_api, name="complaints_list_create_api"),
    path("<int:complaint_id>/", complaints_details_api, name="complaints_details_api"),
    path(
        "old/<int:complaint_id>/send-to-department/",
        send_complaint_to_department_api,
        name="send_complaint_to_department_api_old",
    ),
]
