from django.urls import path
from api.views.departments.complaints import department_complaints_api
from api.views.departments.departments_list import (
    departments_api_view,
)
from api.views.departments.incidents import department_incidents
from api.views.departments.details import department_details_api
from api.views.departments.new_department import new_department, new_departments
from api.views.departments.update_department import (
    add_user_to_department,
    delete_department,
    department_members,
    updated_department,
)

urlpatterns = [
    path("", departments_api_view, name="departments"),
    path("<int:department_id>/", department_details_api, name="department_details_api"),
    path(
        "<int:department_id>/members/",
        department_members,
        name="department_members",
    ),
    path(
        "<int:department_id>/members/new/",
        add_user_to_department,
        name="add_user_to_department",
    ),
    # incidents
    path(
        "<int:department_id>/incidents/",
        department_incidents,
        name="department_incidents",
    ),
    # complaints
    path(
        "<int:department_id>/complaints/",
        department_complaints_api,
        name="department_details_api",
    ),
    path(
        "<int:department_id>/update/",
        updated_department,
        name="departments",
    )
]
