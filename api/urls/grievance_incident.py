from django.urls import path

from api.views.incidents.grievance_incident.documents import (
    grievance_new_document_api,
    grievance_new_documents_list_api,
    delete_grievance_documents,
)
from api.views.incidents.grievance_incident.incident_details import grievance_detail
from api.views.incidents.grievance_incident.incident_list import grievances_list_api
from api.views.incidents.grievance_incident.investigation.new import (
    new_grievance_investigation,
)
from api.views.incidents.grievance_incident.new_incident import new_grievance_api
from api.views.incidents.grievance_incident.reviews import (
    grievance_reviews_list,
    new_grievance_review,
)
from api.views.incidents.grievance_incident.update_incident import (
    send_grievance_incident_to_department,
    update_grievance_api,
    mark_grievance_incident_as_resolved,
)
from api.views.incidents.grievance_incident.modify_grievance_incident import (
    modify_grievance_incident,
)

from api.views.incidents.grievance_incident.delete_draft_incidents import (
    delete_grievance_draft_incidents,
)
from api.views.incidents.grievance_incident.versions import (
    grievance_original_version,
    grievance_version,
)


urlpatterns = [
    path("", grievances_list_api, name="grievances_list"),
    path("<int:grievance_id>/", grievance_detail, name="grievance_detail"),
    path("new/", new_grievance_api, name="new_grievance"),
    path(
        "<int:grievance_id>/update/",
        update_grievance_api,
        name="update_grievance_api",
    ),
    # version
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        grievance_version,
        name="grievance_version",
    ),
    path(
        "<int:incident_id>/versions/original/",
        grievance_original_version,
        name="grievance_original_version",
    ),
    # reviews
    path(
        "<int:grievance_id>/reviews/new/", new_grievance_review, name="grievance_detail"
    ),
    path(
        "<int:grievance_id>/reviews/", grievance_reviews_list, name="grievance_detail"
    ),
    # end of reviews
    # documents
    path(
        "<int:grievance_id>/documents/new/",
        grievance_new_document_api,
        name="grievance_new_document_api",
    ),
    path(
        "<int:grievance_id>/documents/",
        grievance_new_documents_list_api,
        name="grievance_new_documents_list_api",
    ),
    path(
        "<int:incident_id>/documents/<int:document_id>/delete/",
        delete_grievance_documents,
        name="grievance_new_documents_list_api",
    ),
    # end of documents
    path(
        "<int:grievance_id>/resolve/",
        mark_grievance_incident_as_resolved,
        name="mark_grievance_incident_as_resolved",
    ),
    # add send to department
    path(
        "<int:incident_id>/send-to-department/",
        send_grievance_incident_to_department,
        name="send_grievance_incident_to_department",
    ),
    # modifying the incident
    path(
        "<int:incident_id>/modify/",
        modify_grievance_incident,
        name="modify_grievance_incident",
    ),
    # investigation
    path(
        "<int:incident_id>/investigation/new/",
        new_grievance_investigation,
        name="new_grievance_investigation",
    ),
    # deleting draft incident
    path(
        "drafts/delete-multiple/",
        delete_grievance_draft_incidents,
        name="delete_grievance_draft_incidents",
    ),
]
