from django.urls import path

from api.views.incidents.medication_error.medication_error import *
from api.views.incidents.medication_error.version import (
    medication_error_incident_version,
)


urlpatterns = [
    path(
        "",
        medication_error_incidents_api,
        name="medication_error_incidents_api",
    ),
    path(
        "<int:id>/",
        medication_error_incident_details_api,
        name="medication_error_incident_details_api",
    ),
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        medication_error_incident_version,
        name="medication_error_incident_version",
    ),
    path(
        "<int:incident_id>/documents/",
        medication_error_incident_documents_api,
        name="medication_error_incident_documents_api",
    ),
    path(
        "<int:incident_id>/reviews/",
        medication_error_incident_reviews_api,
        name="medication_error_incident_reviews_api",
    ),
]
