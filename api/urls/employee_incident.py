from django.urls import path
from api.views.incidents.staff_incident.documents import (
    employee_incident_new_document_api,
    employee_incident_new_documents_list_api,
    delete_employee_incident_documents,
)
from api.views.incidents.staff_incident.incident_list import (
    employee_incident_list_api,
)
from api.views.incidents.staff_incident.investigation.new_incident import (
    new_employee_health_incident_investigation,
)
from api.views.incidents.staff_incident.new_incident import *
from api.views.incidents.staff_incident.incident_details import *
from api.views.incidents.staff_incident.reviews import (
    employee_incident_reviews_api,
    new_employee_incident_review_api,
)
from api.views.incidents.staff_incident.update_incident import *
from api.views.incidents.staff_incident.modify_employee_incident import (
    modify_employee_incident,
)

from api.views.incidents.staff_incident.delete_draft_incidents import (
    delete_employee_draft_incidents,
)
from api.views.incidents.staff_incident.versions import (
    staff_incident_original_version,
    staff_incident_version,
)

urlpatterns = [
    # Retrieve Staff Report
    path("", employee_incident_list_api, name="employee_incident_api"),
    path(
        "<int:id>/",
        retrieve_report,
        name="retrieve_employee_incident_report",
    ),
    # versions
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        staff_incident_version,
        name="staff_incident_version",
    ),
    path(
        "<int:incident_id>/versions/original/",
        staff_incident_original_version,
        name="staff_incident_original_version",
    ),
    # Create Staff Report
    path("new/staff/", initial_report, name="new_incident_report"),
    path(
        "update/staff/incident_description/",
        update_incident_description,
        name="update_incident_description",
    ),
    path("update/staff/final_report/", update_final_report, name="update_final_report"),
    path(
        "update/staff/complete/",
        update_report_completed,
        name="update_report_complete",
    ),
    # modify Staff incident
    path(
        "<int:incident_id>/modify/",
        modify_employee_incident,
        name="modify_employee_incident",
    ),
    # Update Staff Report
    path("update/staff/", update_report, name="update_report"),
    # documents
    path(
        "<int:incident_id>/documents/new/",
        employee_incident_new_document_api,
        name="employee_incident_new_document",
    ),
    path(
        "<int:incident_id>/documents/",
        employee_incident_new_documents_list_api,
        name="employee_incident_new_documents_list_api",
    ),
    path(
        "<int:incident_id>/documents/<int:document_id>/delete/",
        delete_employee_incident_documents,
        name="delete_employee_incident_documents",
    ),
    # end of document
    path(
        "<int:employee_incident_id>/resolve/",
        mark_employee_incident_as_resolved,
        name="employee_incident_new_document",
    ),
    path(
        "<int:incident_id>/send-to-department/",
        send_employee_report_to_department,
        name="send_employee_report_to_department",
    ),
    # reviews
    path(
        "<int:incident_id>/reviews/new/",
        new_employee_incident_review_api,
        name="new_employee_incident_review_api",
    ),
    path(
        "<int:incident_id>/reviews/",
        employee_incident_reviews_api,
        name="employee_incident_reviews_api",
    ),
    # end of reviews
    # investigation
    path(
        "<int:incident_id>/investigation/new/",
        new_employee_health_incident_investigation,
        name="new_employee_health_incident_investigation",
    ),
    # deleting drafts
    path(
        "drafts/delete-multiple/",
        delete_employee_draft_incidents,
        name="delete_employee_draft_incidents",
    ),
]
