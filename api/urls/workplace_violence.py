from django.urls import path

from api.views.incidents.workplace_violence.version import workplace_incident_version
from api.views.incidents.worplace_violence import *

urlpatterns = [
    path("", workplace_violence_list_create, name="workplace_violence_list_create"),
    path(
        "<int:workplace_violence_id>/",
        workplace_violence_detail,
        name="workplace_violence_detail",
    ),
    path(
        "<int:incident_id>/documents/",
        workplace_violence_documents_api,
        name="workplace_violence_documents_api",
    ),
    path(
        "<int:incident_id>/reviews/",
        workplace_violence_reviews_api,
        name="workplace_violence_reviews_api",
    ),
    # version
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        workplace_incident_version,
        name="workplace_incident_version",
    ),
]
