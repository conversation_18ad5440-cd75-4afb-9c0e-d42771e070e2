from django.urls import path

from api.views.incidents.adverse_drug_reaction.documents import (
    adverse_drug_reaction_document_api,
    adverse_drug_reaction_document_list,
    delete_adverse_drug_reaction_documents,
)

from api.views.incidents.adverse_drug_reaction.incident_list import (
    adverse_drug_reaction_details_api,
    adverse_drug_reactions_api,
    observers_name_api,
)
from api.views.incidents.adverse_drug_reaction.new_incident import (
    new_adverse_drug_reaction_api,
    new_observer,
)
from api.views.incidents.adverse_drug_reaction.reviews import (
    adverse_drug_reaction_reviews_list,
    new_adverse_drug_reaction_review,
)
from api.views.incidents.adverse_drug_reaction.update_incident import *

from api.views.incidents.adverse_drug_reaction.modify_adverse_drug_reaction import (
    modify_adverse_drug_reaction_incident,
)
from api.views.incidents.adverse_drug_reaction.delete_draft_incidents import (
    delete_adverse_drug_reaction_draft_incidents,
)
from api.views.incidents.adverse_drug_reaction.version import (
    adverse_drug_reaction_original_version,
    adverse_drug_reaction_version,
)

urlpatterns = [
    path("", adverse_drug_reactions_api, name="adverse_drug_reaction_api"),
    path("new/", new_adverse_drug_reaction_api, name="new_adverse_drug_reaction_api"),
    path("new_observer/", new_observer, name="new_observer"),
    path("observers/", observers_name_api, name="observers"),
    # updating urls
    path(
        "<int:id>/update/",
        update_adverse_drug_reaction,
        name="update_adverse_drug_reaction",
    ),
    path(
        "<int:id>/resolve/",
        mark_adverse_drug_reaction_as_resolved,
        name="mark_adverse_drug_reaction_as_resolved",
    ),
    path(
        "<int:id>/",
        adverse_drug_reaction_details_api,
        name="adverse_drug_reaction_details_api",
    ),
    # versions
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        adverse_drug_reaction_version,
        name="adverse_drug_reaction_version",
    ),
    path(
        "<int:incident_id>/versions/original/",
        adverse_drug_reaction_original_version,
        name="adverse_drug_reaction_original_version",
    ),
    # reviews
    path(
        "<int:incident_id>/reviews/new/",
        new_adverse_drug_reaction_review,
        name="adverse_drug_new_reaction_reviews_api",
    ),
    path(
        "<int:incident_id>/reviews/",
        adverse_drug_reaction_reviews_list,
        name="adverse_drug_reaction_reviews_list",
    ),
    # end of reviews
    # documents
    path(
        "<int:adverse_drug_reaction_id>/documents/new/",
        adverse_drug_reaction_document_api,
        name="adverse_drug_reaction_document_api",
    ),
    path(
        "<int:incident_id>/documents/",
        adverse_drug_reaction_document_list,
        name="adverse_drug_reaction_document_list",
    ),
    path(
        "<int:incident_id>/documents/<int:document_id>/delete/",
        delete_adverse_drug_reaction_documents,
        name="delete_adverse_drug_reaction_documents",
    ),
    # end of documents
    path(
        "<int:id>/send-to-department/",
        send_adverse_drug_reaction_to_department,
        name="send_adverse_drug_reaction_to_department",
    ),
    path(
        "<int:id>/modify-incident/",
        modify_adverse_drug_reaction_incident,
        name="modify_adverse_drug_reaction_incident",
    ),
    # deleting draft incidents
    path(
        "drafts/delete-multiple/",
        delete_adverse_drug_reaction_draft_incidents,
        name="delete_adverse_drug_reaction_draft_incidents",
    ),
]
