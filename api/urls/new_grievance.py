from django.urls import path

from api.views.incidents.grievance_incident.new_grievance import *
from api.views.incidents.grievance_incident.versions import grievance_version


urlpatterns = [
    path(
        "",
        grievance_incidents_api,
        name="grievance_incidents_api",
    ),
    path(
        "<int:id>/",
        grievance_incident_detail_api,
        name="grievance_incident_details_api",
    ),
    path(
        "<int:incident_id>/versions/<int:version_id>/",
        grievance_version,
        name="grievance_incident_version",
    ),
    path(
        "<int:id>/investigation/",
        grievance_incident_investigation_api,
        name="grievance_incident_investigation_api",
    ),
    path(
        "<int:id>/investigation/<int:investigation_id>/",
        grievance_incident_detail_investigation_api,
        name="grievance_incident_details_investigation_api",
    ),
    path(
        "<int:incident_id>/documents/",
        grievance_incident_documents_api,
        name="grievance_incident_documents_api",
    ),
    path(
        "<int:incident_id>/reviews/",
        grievance_incident_reviews_api,
        name="grievance_incident_reviews_api",
    ),
    path(
        "<int:id>/investigation/<int:investigation_id>/doc-letter/",
        grievance_investigation_doc_api,
        name="grievance_investigation_special_documents_api",
    ),
]
