from django.urls import path

from api.views.incidents.staff_incident.investigation.documents import (
    employee_health_investigation_document_list_api,
    employee_health_investigation_new_document_api,
    delete_health_investigation_documents,
)
from api.views.incidents.staff_incident.investigation.incident_list import (
    list_incidents,
)
from api.views.incidents.staff_incident.investigation.modify_employee_health_investigation import (
    modify_employee_health_investigation,
)

from api.views.incidents.staff_incident.investigation.reviews import (
    employee_health_investigation_reviews_api,
    new_employee_health_investigation_review_api,
)
from api.views.incidents.staff_incident.investigation.update_incident import (
    mark_employee_health_investigation_incident_as_resolved,
    send_employee_health_investigation_to_department,
    update_incident,
)
from api.views.incidents.staff_incident.investigation.delete_drafts_incidents import (
    delete_employee_health_investigation_draft_incidents,
)


urlpatterns = [
    # path("new/", new_health_incident, name="new_health_incident"),
    # path("<int:id>/", incident_details, name="incident_details"),
    # modifying the incident
    path("<int:id>/update/update_incident/", update_incident, name="update_incident"),
    path(
        "<int:incident_id>/modify-incident/",
        modify_employee_health_investigation,
        name="modify_incident",
    ),
    # end of modifying endpoints
    path("", list_incidents, name="list_incidents"),
    # documents
    path(
        "<int:incident_id>/documents/new/",
        employee_health_investigation_new_document_api,
        name="employee_health_investigation_new_document",
    ),
    path(
        "<int:incident_id>/documents/",
        employee_health_investigation_document_list_api,
        name="employee_health_investigation_new_document_list_api",
    ),
    path(
        "<int:incident_id>/documents/<int:document_id>/delete/",
        delete_health_investigation_documents,
        name="delete_health_investigation_documents",
    ),
    # end of documents
    path(
        "<int:health_investigation_id>/resolve/",
        mark_employee_health_investigation_incident_as_resolved,
        name="update_incident",
    ),
    path(
        "<int:incident_id>/send-to-department/",
        send_employee_health_investigation_to_department,
        name="send_employee_health_investigation_to_department",
    ),
    # reviews
    path(
        "<int:incident_id>/reviews/new/",
        new_employee_health_investigation_review_api,
        name="new_employee_health_investigation_review_api",
    ),
    path(
        "<int:incident_id>/reviews/",
        employee_health_investigation_reviews_api,
        name="employee_health_investigation_reviews_api",
    ),
    # end of reviews
    # deleting health investigation
    path(
        "drafts/delete-multiple/",
        delete_employee_health_investigation_draft_incidents,
        name="delete_employee_health_investigation_draft_incidents",
    ),
]
