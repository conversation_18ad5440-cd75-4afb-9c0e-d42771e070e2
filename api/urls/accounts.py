from django.urls import path
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)
from accounts.views import check_access, login_email_password
from api.user_profile.update_profile import update_multi_profiles
from api.user_profile.user_preferences.new_user_preference import set_user_preference
from api.user_profile.user_preferences.user_preferences_list import (
    user_preferences_list,
)
from api.views.auth.login import login, verify_token_api, verify_user_api
from api.views.auth.mfa import verify_mfa, verify_mfa_code
from api.views.auth.permissions_list import (
    new_permissions,
    permissions_list_api,
    user_permissions,
    user_permissions_by_app,
    all_possible_permissions_by_app,
)
from api.views.auth.users import (
    deactivate_user_api,
    delete_multiple,
    delete_user_api,
    get_users_list_api,
    new_multiple_users_api,
    new_user_api,
    reactivate_user_api,
)

from api.views.auth.reset import change_password_api, request_password_reset
from api.views.auth.reset import reset_password
from api.views.complaint_old.list import user_complaints_list_api
from api.views.patients.list import patients_list_api
from api.views.profile.documents import get_user_documents
from api.views.profile.general_info import get_user_details
from api.views.profile.incidents import get_user_incidents
from api.views.profile.update_profile import delete_profile_api, update_self_profile
from api.user_profile.general_info import get_user_profile, update_user_profile
from api.views.auth.constants import get_constants_data_list
from accounts.views import user_drafts_api_view
from base.services.auth import verify_user


urlpatterns = [
    path("login/", login_email_password, name="login"),
    path("token/access/", check_access, name="check_access"),
    path("token/mfa/<str:user_email>/", verify_mfa, name="verify_mfa_code"),
    path(
        "token/mfa/<str:user_email>/verify/",
        verify_mfa_code,
        name="verify_mfa_code",
    ),
    path("token/verify/", verify_user_api, name="verify_user"),
    path("token/email-password/", login_email_password, name="get_token"),
    path("token/refresh/", TokenRefreshView.as_view, name="refresh"),
    path("permissions/", user_permissions, name="user_permissions"),
    path("user-permissions/", user_permissions_by_app, name="user_permissions_by_app"),
    path(
        "all-permissions/",
        all_possible_permissions_by_app,
        name="all_possible_permissions_by_app",
    ),
    path("permissions/all/", permissions_list_api, name="permissions_list_api"),
    path("permissions/new/", new_permissions, name="permissions_list_api"),
    path("users/list/", get_users_list_api, name="users_list"),
    path("users/new/", new_user_api, name="new_user_api"),
    path("users/new/multiple/", new_multiple_users_api, name="new_user_api"),
    path("users/<int:user_id>/delete/", delete_user_api, name="delete_user_api"),
    path(
        "users/<int:user_id>/deactivate/",
        deactivate_user_api,
        name="deactivate_user_api",
    ),
    path(
        "users/<int:user_id>/reactivate/",
        reactivate_user_api,
        name="reactivate_user_api",
    ),
    path("users/delete-multiple/", delete_multiple, name="delete_user_api"),
    path(
        "password-reset-request/", request_password_reset, name="request_password_reset"
    ),
    path("reset-password/", reset_password, name="reset_password"),
    path("change-password/", change_password_api, name="change_password_api"),
    # profile
    path("profile/", get_user_details, name="get_user_details"),
    path("profile/update/", update_self_profile, name="update_self_profile"),
    path(
        "profile/<int:profile_id>/delete/",
        delete_profile_api,
        name="delete_profile_api",
    ),
    path(
        "profile/<int:user_id>/incidents/",
        get_user_incidents,
        name="get_user_incidents",
    ),
    path("profile/documents/", get_user_documents, name="get_user_documents"),
    path(
        "profile/complaints/",
        user_complaints_list_api,
        name="user_complaints_list_api",
    ),
    # other user info
    path(
        "profile/<int:user_id>/",
        get_user_profile,
        name="get_user_profile",
    ),
    path(
        "profile/<int:user_id>/update/",
        update_user_profile,
        name="update_user_profile",
    ),
    path(
        "profile/update/multiple/", update_multi_profiles, name="update_multi_profiles"
    ),
    # drafts
    path("profile/drafts/", user_drafts_api_view, name="user_drafts_api"),
    # patients
    path("patients/", patients_list_api, name="patients_list_api"),
    # constants
    path("constants/", get_constants_data_list, name="get_constants_data_list"),
    # preferences
    path("preferences/set/", set_user_preference, name="new_user_preference"),
    path("preferences/", user_preferences_list, name="user_preferences_list"),
]
