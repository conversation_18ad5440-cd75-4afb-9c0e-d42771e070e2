from django.urls import path

from api.views.data.clean_data import clean_data_api

urlpatterns = [
    path("clean", clean_data_api, name="clean_data"),
]


"""A function to generate dummy accounts"""

facilities = [
    "Carnegie",
    "Pawhuska",
    "Mangum",
    "Seilling",
    "Prague",
]

# departments in each facility
departments = [
    {
        "name": "Dermatology",
        "parent": "Oncology",
        "description": "Dermatology focuses on conditions related to the skin, hair, and nails, and may include oncology for skin cancer treatment and prevention.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Human Resources",
        "parent": "Operations",
        "description": "Human Resources manages staff-related processes like hiring, payroll, and workplace incident reports.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Quality/Risk Management",
        "parent": "Risk Management",
        "description": "Quality/Risk Management oversees incident reports, lost and found property, medication errors, and ensures quality across hospital services.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Operations",
        "parent": "Executive",
        "description": "Operations ensures the hospital’s services run smoothly, covering day-to-day logistics, staff management, and patient flow.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Pediatrics",
        "parent": "Geriatrics",
        "description": "Pediatrics focuses on the care of children, while Geriatrics manages the health of older adults.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Risk Management",
        "parent": "Executive",
        "description": "Risk Management monitors and mitigates risks within hospital operations, overseeing incident reporting and corrective actions.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Pharmacy",
        "parent": "Medical",
        "description": "Pharmacy manages medication dispensing and reviews reports related to medication errors and near misses.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Radiology",
        "parent": "Psychiatry",
        "description": "Radiology uses imaging techniques for diagnosis and treatment, unrelated to Psychiatry's focus on mental health.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Staff Health",
        "parent": "Human Resources",
        "description": "Staff Health manages the health and safety of hospital staff, including incident reporting and investigations.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Gastroenterology",
        "parent": "Neurology",
        "description": "Gastroenterology focuses on digestive system diseases, while Neurology focuses on the nervous system.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Workplace Violence Response",
        "parent": "Human Resources",
        "description": "This team manages reports and responses to workplace violence incidents in the hospital.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Allergy and Immunology",
        "parent": "Gastroenterology",
        "description": "Allergy and Immunology manages immune system disorders, unrelated to Gastroenterology’s focus on the digestive system.",
        "header_of_department": "<EMAIL>",
    },
    {
        "name": "Case Management",
        "parent": "Medical",
        "description": "Ensures that patients receive coordinated care and services across different departments, focusing on treatment plans and discharge planning.",
    },
    {
        "name": "Nursing",
        "parent": "Medical",
        "description": "Provides comprehensive nursing care across all departments and ensures patient safety and high standards of care.",
    },
    {
        "name": "Dietary",
        "parent": "Operations",
        "description": "Responsible for nutrition services, ensuring that patients receive healthy, well-balanced meals according to dietary needs and medical requirements.",
    },
    {
        "name": "Dialysis",
        "parent": "Medical",
        "description": "Provides dialysis treatment and care for patients with kidney-related issues, ensuring quality treatment and patient safety.",
    },
    {
        "name": "Respiratory Care",
        "parent": "Medical",
        "description": "Specializes in treating patients with respiratory conditions and providing respiratory therapies to improve patient outcomes.",
    },
    {
        "name": "Infection Control",
        "parent": "Medical",
        "description": "Focuses on preventing and controlling infections within the healthcare facility, ensuring compliance with health and safety regulations.",
    },
    {
        "name": "IT",
        "parent": "Operations",
        "description": "Provides technical support and manages IT infrastructure to ensure seamless operation of healthcare technologies and systems.",
    },
    {
        "name": "Therapy",
        "parent": "Medical",
        "description": "Provides a range of therapeutic services, including physical, occupational, and speech therapy, to aid in patient rehabilitation.",
    },
    {
        "name": "Laboratory",
        "parent": "Medical",
        "description": "Conducts clinical tests and diagnostic analyses to assist in the diagnosis and treatment of patient conditions.",
    },
    {
        "name": "Business Office",
        "parent": "Operations",
        "description": "Handles administrative and financial functions, including billing, coding, and overall office management for the facility.",
    },
    {
        "name": "Wound Care",
        "parent": "Medical",
        "description": "Specializes in the treatment and management of chronic wounds, ensuring proper care and faster healing for patients.",
    },
    {
        "name": "Cohesive Management & Consulting",
        "parent": "Executive",
        "description": "Provides strategic consulting services and management oversight for healthcare operations.",
    },
    {
        "name": "Environmental Services",
        "parent": "Operations",
        "description": "Responsible for maintaining cleanliness, safety, and infection control in the hospital environment.",
    },
    {
        "name": "Health Information Management",
        "parent": "Operations",
        "description": "Manages patient records, ensuring accurate documentation and compliance with healthcare regulations.",
    },
    {
        "name": "Materials Management",
        "parent": "Operations",
        "description": "Oversees the procurement, storage, and distribution of medical supplies and equipment throughout the hospital.",
    },
    {
        "name": "Outpatient",
        "parent": "Medical",
        "description": "Manages services for patients who receive treatment without being admitted to the hospital.",
    },
    {
        "name": "Plant Operations",
        "parent": "Operations",
        "description": "Maintains the hospital’s physical infrastructure, including facilities, utilities, and equipment.",
    },
    {
        "name": "Medical Staff",
        "parent": "Medical",
        "description": "Manages the credentialing, privileges, and professional development of doctors and clinical staff.",
    },
]

# user types in each department

department_user_type = ["User", "Manager"]

# user types in each facility

facility_user_type = ["Admin", "Director"]

# user object example

user = {
    "first_name": f"John [Facility name]",
    "last_name": f"Doe",
    "department_name": "[department name]",
    "role": "[role name]",
}



def generate_test_users():
    # loop through facilities
    # create facility
    # create facility user
    # loop through departments
    # create department
    # loop through department_user_type
    # create user
    pass
