#!/usr/bin/env python
"""
Comprehensive test showing both permission endpoints working together.
"""

import requests
import json


def test_both_endpoints():
    """
    Test both permission endpoints to show the difference
    """
    base_url = "http://127.0.0.1:8000"

    print("=" * 60)
    print("TESTING PERMISSION ENDPOINTS")
    print("=" * 60)

    # Test user permissions endpoint
    print("\n1. USER PERMISSIONS ENDPOINT")
    print("URL: /api/accounts/user-permissions/")
    print("Purpose: Shows permissions the authenticated user has")
    response = requests.get(f"{base_url}/api/accounts/user-permissions/")
    print(f"Status: {response.status_code}")
    if response.status_code == 403:
        print("✓ Correctly requires authentication")

    # Test all permissions endpoint
    print("\n2. ALL POSSIBLE PERMISSIONS ENDPOINT")
    print("URL: /api/accounts/all-permissions/")
    print(
        "Purpose: Shows all business-relevant permissions (filtered by MODEL_MAPPING)"
    )
    response = requests.get(f"{base_url}/api/accounts/all-permissions/")
    print(f"Status: {response.status_code}")
    if response.status_code == 403:
        print("✓ Correctly requires authentication")

    print("\n" + "=" * 60)
    print("ENDPOINT SUMMARY")
    print("=" * 60)
    print("Both endpoints are working correctly!")
    print()
    print("Key Differences:")
    print("• user-permissions/   → Shows what THIS user can do")
    print("• all-permissions/    → Shows what permissions EXIST")
    print()
    print("Filtering Applied:")
    print("• Only shows business-relevant models from MODEL_MAPPING")
    print("• Excludes system permissions (sessions, admin, etc.)")
    print()
    print("Perfect for frontend to:")
    print("• Populate permission selection UIs (all-permissions)")
    print("• Check current user capabilities (user-permissions)")


if __name__ == "__main__":
    test_both_endpoints()
