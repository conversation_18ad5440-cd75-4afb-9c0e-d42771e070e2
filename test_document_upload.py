#!/usr/bin/env python3
"""
Temporary test script to verify document upload functionality
"""
import os
import sys
import django
from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from io import BytesIO

# Add the project directory to Python path
sys.path.insert(0, "/Users/<USER>/Desktop/projects/quality-control/q-control-backend")

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
django.setup()

from documents.services.operations import DocumentsOperations
from documents.views import upload_file
from adverse_drug_reaction.models import AdverseDrugReaction
from accounts.models import User
from base.models import Facility


def create_test_file():
    """Create a test file for upload"""
    content = b"This is a test document content for upload testing. It contains some text that simulates a real document."
    return SimpleUploadedFile(
        name="test_document.txt", content=content, content_type="text/plain"
    )


def test_upload_file_function():
    """Test the upload_file function directly"""
    print("Testing upload_file function...")

    test_file = create_test_file()

    try:
        # Test the upload_file function
        result = upload_file(test_file, "test_folder")
        print(f"Upload result: {result}")

        if len(result) == 4:
            success, url, public_id, message = result
            print(f"Success: {success}")
            print(f"URL: {url}")
            print(f"Public ID: {public_id}")
            print(f"Message: {message}")
            return success, url, public_id
        else:
            print(f"Unexpected result format: {result}")
            return False, None, None

    except Exception as e:
        print(f"Error in upload_file: {e}")
        return False, None, None


def test_document_operations():
    """Test the DocumentsOperations.create_document method"""
    print("\nTesting DocumentsOperations.create_document...")

    try:
        # Get or create a test user
        user = User.objects.first()
        if not user:
            print("No users found in database. Creating test user...")
            user = User.objects.create_user(
                username="testuser", email="<EMAIL>", password="testpass123"
            )

        # Get or create a test facility
        facility = Facility.objects.first()
        if not facility:
            print("No facilities found in database. Creating test facility...")
            facility = Facility.objects.create(
                name="Test Facility", location="Test Location"
            )

        # Get or create a test incident
        incident = AdverseDrugReaction.objects.first()
        if not incident:
            print("No ADR incidents found in database. Creating test incident...")
            incident = AdverseDrugReaction.objects.create(
                report_facility=facility, created_by=user, status="draft"
            )

        # Create test files
        test_files = [create_test_file()]

        # Test document creation
        doc_operations = DocumentsOperations()
        result = doc_operations.create_document(
            files=test_files, incident=incident, user=user
        )

        print(f"Document creation result: {result}")

        if hasattr(result, "success"):
            print(f"Success: {result.success}")
            print(f"Message: {result.message}")
            print(f"Data: {result.data}")
            return result.success
        else:
            print(f"Result type: {type(result)}")
            return False

    except Exception as e:
        print(f"Error in DocumentsOperations.create_document: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_cloudinary_config():
    """Test if Cloudinary is properly configured"""
    print("\nTesting Cloudinary configuration...")

    try:
        import cloudinary
        from core.settings import (
            CLOUDINARY_NAME,
            CLOUDINARY_API_KEY,
            CLOUDINARY_SECRET_KEY,
        )

        print(f"Cloudinary Name: {CLOUDINARY_NAME}")
        print(
            f"API Key: {CLOUDINARY_API_KEY[:10]}..."
            if CLOUDINARY_API_KEY
            else "No API Key"
        )
        print(f"Secret Key: {'***' if CLOUDINARY_SECRET_KEY else 'No Secret Key'}")

        # Test configuration
        config = cloudinary.config()
        print(f"Cloudinary config: {config}")

        return True

    except Exception as e:
        print(f"Error testing Cloudinary config: {e}")
        return False


if __name__ == "__main__":
    print("Starting document upload tests...")
    print("=" * 50)

    # Test 1: Cloudinary configuration
    config_ok = test_cloudinary_config()

    # Test 2: Upload file function
    upload_ok, url, public_id = test_upload_file_function()

    # Test 3: Document operations
    doc_ops_ok = test_document_operations()

    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    print(f"Cloudinary Config: {'✓' if config_ok else '✗'}")
    print(f"Upload File Function: {'✓' if upload_ok else '✗'}")
    print(f"Document Operations: {'✓' if doc_ops_ok else '✗'}")

    if all([config_ok, upload_ok, doc_ops_ok]):
        print("\n🎉 All tests passed! Document upload should work correctly.")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
