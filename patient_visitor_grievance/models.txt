from django.db import models
from accounts.models import Profile
from base.models import BaseModel, Department, Facility
from documents.models import Document
from reviews.models import Review


class GrievanceInvestigationInvolvedParty(BaseModel):
    name = models.CharField(max_length=255, null=True, blank=True)
    relationship_to_patient = models.CharField(max_length=255, null=True, blank=True)


class GrievanceBase(BaseModel):
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]

    # Status and Facility
    status = models.CharField(choices=INCIDENT_REVIEW_STATUS_CHOICES, max_length=255, default="Draft")
    report_facility = models.ForeignKey(Facility, blank=True, null=True, on_delete=models.SET_NULL)
    department = models.Foreign<PERSON>ey(Department, blank=True, null=True, on_delete=models.SET_NULL)
    current_step = models.IntegerField(default=1, null=True, blank=True)
    is_resolved = models.BooleanField(default=False)

    # Basic Information
    date = models.DateField(null=True, blank=True)
    title = models.CharField(max_length=255, null=True, blank=True)
    patient_name = models.ForeignKey(Profile, null=True, blank=True, on_delete=models.SET_NULL)

    # Form Details
    form_initiated_by = models.ForeignKey(Profile, blank=True, null=True, on_delete=models.SET_NULL, related_name="form_initiated_by_profile")
    complaint_made_by = models.ForeignKey(Profile, blank=True, null=True, on_delete=models.SET_NULL, related_name="complaint_made_by_profile")
    relationship_to_patient = models.CharField(max_length=255, null=True, blank=True)
    source_of_information = models.CharField(max_length=255, null=True, blank=True)

    # Complaint Information
    complaint_or_concern = models.TextField(null=True, blank=True)
    initial_corrective_actions = models.TextField(null=True, blank=True)
    adverse_patient_outcome = models.BooleanField(default=False)

    # Notification Details
    administrator_notified = models.ForeignKey(Profile, null=True, blank=True, on_delete=models.SET_NULL, related_name="administrator_notified")
    notification_date = models.DateField(null=True, blank=True)
    notification_time = models.TimeField(null=True, blank=True)
    outcome = models.TextField(null=True, blank=True)

    # Related Models
    reviews = models.ManyToManyField(Review, related_name="grievance_reviews_field", blank=True)
    documents = models.ManyToManyField(Document, related_name="grievance_incident_documents_field", blank=True)

    def __str__(self):
        return f"Grievance Form - {self.patient_name or 'Unknown'} - {self.date or 'No Date'}"


class Grievance(GrievanceBase):
    is_modified = models.BooleanField(default=False)


class GrievanceVersion(GrievanceBase):
    original_report = models.ForeignKey(Grievance, on_delete=models.CASCADE, related_name="versions", null=True, blank=True)

    class Meta:
        db_table = "grievance_version"


class GrievanceInvestigation(BaseModel):
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]

    FEEDBACK_CHOICES = [
        ("Meeting", "Meeting"),
        ("Telephone", "Telephone"),
    ]

    # Status and Basic Info
    status = models.CharField(choices=INCIDENT_REVIEW_STATUS_CHOICES, max_length=255, default="Draft")
    report_facility = models.ForeignKey(Facility, blank=True, null=True, on_delete=models.SET_NULL)
    department = models.ForeignKey(Department, blank=True, null=True, on_delete=models.SET_NULL)
    current_step = models.IntegerField(default=1, null=True, blank=True)
    is_resolved = models.BooleanField(default=False)
    grievance_report = models.OneToOneField(Grievance, blank=True, null=True, on_delete=models.SET_NULL)

    # Investigation Details
    findings = models.TextField(max_length=1000, blank=True, null=True)
    interviews_findings = models.TextField(max_length=1000, blank=True, null=True)
    medical_record_findings = models.CharField(max_length=1000, null=True, blank=True)
    conducted_by = models.ForeignKey(Profile, on_delete=models.SET_NULL, null=True, blank=True, related_name="conducted_by")
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)

    # Conclusions and Actions
    conclusion = models.TextField(null=True, blank=True, max_length=1000)
    action_taken = models.TextField(null=True, blank=True, max_length=1000)
    feedback = models.CharField(max_length=100, choices=FEEDBACK_CHOICES, null=True, blank=True)
    date_of_feedback = models.DateField(null=True, blank=True)
    involved_parties = models.ManyToManyField(GrievanceInvestigationInvolvedParty)

    # Extension Management
    extension_letter_sent = models.BooleanField(null=True, blank=True, default=False)
    date_extension_letter_sent = models.DateField(null=True, blank=True)
    extension_letter_copy = models.ForeignKey(Document, null=True, blank=True, on_delete=models.SET_NULL, related_name="grievance_extension_letter_sent")

    # Response Management
    response_letter_sent = models.BooleanField(null=True, blank=True)
    date_response_letter_sent = models.DateField(null=True, blank=True)
    response_letter_copy = models.ForeignKey(Document, null=True, blank=True, on_delete=models.SET_NULL, related_name="grievance_response_letter_sent")

    # Closure Information
    matter_closed = models.BooleanField(null=True, blank=True, default=False)
    date_matter_closed = models.DateField(null=True, blank=True)

    # Related Models
    reviews = models.ManyToManyField(Review, related_name="grievance_investigation_reviews_field")
    documents = models.ManyToManyField(Document, related_name="grievance_investigation_incident_documents_field")