from rest_framework import serializers
from base.serializers import BaseModelSerializer, DepartmentSerializer
from facilities.serializers import FacilitySerializer
from patient_visitor_grievance.models import Grievance, GrievanceInvestigation


class GetPatientVisitorGrievanceSerializer(BaseModelSerializer):
    form_initiated_by = serializers.SerializerMethodField()
    complaint_made_by = serializers.SerializerMethodField()
    patient_name = serializers.SerializerMethodField()
    administrator_notified = serializers.SerializerMethodField()

    class Meta:
        model = Grievance
        fields = "__all__"

    def get_form_initiated_by(self, obj):
        if obj.form_initiated_by:
            return {
                "id": obj.form_initiated_by.id,
                "first_name": obj.form_initiated_by.first_name,
                "last_name": obj.form_initiated_by.last_name,
                "email": obj.form_initiated_by.email,
            }
        return None

    def get_complaint_made_by(self, obj):
        if obj.complaint_made_by:
            return {
                "id": obj.complaint_made_by.id,
                "first_name": obj.complaint_made_by.first_name,
                "last_name": obj.complaint_made_by.last_name,
                "email": obj.complaint_made_by.email,
            }
        return None

    def get_patient_name(self, obj):
        if obj.patient_name:
            return {
                "id": obj.patient_name.id,
                "first_name": obj.patient_name.first_name,
                "last_name": obj.patient_name.last_name,
                "email": obj.patient_name.email,
                "medical_record_number": obj.patient_name.medical_record_number,
            }
        return None

    def get_administrator_notified(self, obj):
        if obj.administrator_notified:
            return {
                "id": obj.administrator_notified.id,
                "first_name": obj.administrator_notified.first_name,
                "last_name": obj.administrator_notified.last_name,
                "email": obj.administrator_notified.email,
            }
        return None


class GetGrievanceInvestigationSerializer(BaseModelSerializer):
    report_facility = FacilitySerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)
    created_by = serializers.SerializerMethodField()
    conducted_by = serializers.SerializerMethodField()
    grievance_report = GetPatientVisitorGrievanceSerializer(read_only=True)
    updated_by = serializers.SerializerMethodField()
    involved_parties = serializers.SerializerMethodField()

    class Meta:
        model = GrievanceInvestigation
        fields = "__all__"

    def get_created_by(self, obj):
        if obj.created_by:
            return {
                "id": obj.created_by.id,
                "first_name": obj.created_by.first_name,
                "last_name": obj.created_by.last_name,
                "email": obj.created_by.email,
            }
        return None

    def get_conducted_by(self, obj):
        if obj.conducted_by:
            return {
                "id": obj.conducted_by.id,
                "first_name": obj.conducted_by.first_name,
                "last_name": obj.conducted_by.last_name,
                "email": obj.conducted_by.email,
            }
        return None

    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                "id": obj.updated_by.id,
                "first_name": obj.updated_by.first_name,
                "last_name": obj.updated_by.last_name,
                "email": obj.updated_by.email,
            }
    
    def get_involved_parties(self, obj):
        if obj.involved_parties:
            return [
                {
                    "id": party.id,
                    "first_name": party.first_name,
                    "last_name": party.last_name,
                    "email": party.email,
                }
                for party in obj.involved_parties.all()
            ]
        return None
