from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserFactory
from patient_visitor_grievance.tests.factory import GrievanceFactory
from reviews.tests.factory import ReviewFactory


class TestGetIncidentReviews(BaseTestSetup):
    """Test getting incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    # cases for patient visitor grievance incident reviews
    def test_get_patient_visitor_grievance_reviews(self):
        """Test getting reviews for a patient visitor grievance incident"""
        incident = GrievanceFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(2, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        self._authenticate_user(self.user)
        response = self.client.get(f"/api/incidents/grievance/{incident.id}/reviews/")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)


class TestCreateIncidentReview(BaseTestSetup):
    """Test creating incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    def test_create_patient_visitor_grievance_review(self):
        """Test creating a review for a patient visitor grievance incident"""
        incident = GrievanceFactory()
        self._authenticate_user(self.user)
        # clear existing reviews
        incident.reviews.clear()
        response = self.client.post(
            f"/api/incidents/grievance/{incident.id}/reviews/",
            data={
                "content": "This is a test review",
            },
            format="json",
        )

        if not response.status_code == 201:
            self.fail(f"Failed to create review: {response.data}")
        # check that the review was created
        self.assertEqual(incident.reviews.first().content, "This is a test review")
