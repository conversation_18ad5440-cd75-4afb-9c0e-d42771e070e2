from django.urls import reverse
from rest_framework import status
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import (
    ProfileFactory,
    UserProfileFactory,
)
from patient_visitor_grievance.tests.factory import (
    GrievanceFactory,
    GrievanceInvestigationFactory,
)
from tasks.tests.factory import ReviewTemplateFactory


class TestGrievanceIncidentsAPIAsSuperUser(BaseTestSetup):
    """Test Grievance API endpoints as Super User"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/grievance/"
        self._authenticate_user(self.super_user)
        self.patient = UserProfileFactory(profile_type="Patient")
        self.complaint_made_by = UserProfileFactory(profile_type="Staff")
        self.valid_data = {
            "status": "Draft",
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "patient": {"user_id": self.patient.id},
            "complaint_made_by": {"user_id": self.complaint_made_by.id},
            "grievance_type": "Service",
            "grievance_category": "Care",
            "grievance_description": "Test grievance description",
            "date_of_incident": "2025-05-21",
            "time_of_incident": "14:30:00",
            "location": "Ward A",
        }

    def test_get_incidents_list(self):
        """Test GET request returns list of incidents"""
        GrievanceFactory.create_batch(3, created_by=self.super_user)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)

    def test_get_incidents_with_filters(self):
        """Test GET request with filters"""
        GrievanceFactory(status="Draft", created_by=self.super_user)
        GrievanceFactory(status="Open", created_by=self.super_user)

        response = self.client.get(f"{self.url}?status=Draft")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_create_incident_success(self):
        """Test POST request creates incident"""
        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_create_incident_invalid_data(self):
        """Test POST request with invalid data fails"""
        invalid_data = self.valid_data.copy()
        invalid_data.get("complaint_made_by")["user_id"] = None

        response = self.client.post(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_incident_unauthenticated(self):
        """Test POST request without authentication fails"""
        self.client.logout()

        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestGrievanceIncidentsAPIAsAdmin(BaseTestSetup):
    """Test Grievance API endpoints as Admin"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/grievance/"
        self._authenticate_user(self.admin_user)

    def test_get_incidents_as_admin(self):
        """Test admin can access incidents from their facility"""
        # Create incidents in admin's facility
        GrievanceFactory.create_batch(
            3,
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )

        # Create incidents in other facility (should not be accessible)
        GrievanceFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)


class TestGrievanceIncidentsAPIAsDirector(BaseTestSetup):
    """Test Grievance API endpoints as Director"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/grievance/"
        self._authenticate_user(self.director_user)

    def test_get_incidents_as_director(self):
        """Test director can access incidents from their facility"""
        # Create incidents in director's facility
        GrievanceFactory.create_batch(
            2,
            created_by=self.director_user,
            department=self.director_user_dept,
            report_facility=self.director_user_fac,
        )

        # Create incidents in other facility (should not be accessible)
        GrievanceFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)


class TestGrievanceIncidentsAPIAsManager(BaseTestSetup):
    """Test Grievance API endpoints as Manager"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/grievance/"
        self._authenticate_user(self.manager_user)

    def test_get_incidents_as_manager(self):
        """Test manager can access incidents from their department"""
        # Create incidents in manager's department
        GrievanceFactory.create_batch(
            2,
            created_by=self.manager_user,
            department=self.manager_user_dept,
            report_facility=self.manager_user_fac,
        )

        # Create incidents in other department (should not be accessible)
        GrievanceFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)


class TestGrievanceIncidentsAPIAsRegularUser(BaseTestSetup):
    """Test Grievance API endpoints as Regular User"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/grievance/"
        self._authenticate_user(self.user_user)

    def test_get_incidents_as_regular_user(self):
        """Test regular user can access their own incidents and assigned reviews"""
        # Create incidents created by the regular user
        GrievanceFactory.create_batch(
            2,
            created_by=self.user_user,
            department=self.user_user_dept,
            report_facility=self.user_user_fac,
        )

        # Create incidents by other users (should not be accessible unless assigned for review)
        GrievanceFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)


class TestGrievanceIncidentDetailsAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = GrievanceFactory(
            created_by=self.super_user,
            department=self.super_user_dept,
            report_facility=self.super_user_fac,
        )
        self.url = f"/api/incidents/grievance/{self.incident.id}/"
        self._authenticate_user(self.super_user)
        self.update_data = {
            "grievance_type": "Updated Type",
            "grievance_description": "Updated description",
        }

    def test_get_incident_details(self):
        """Test GET request returns incident details"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_nonexistent_incident(self):
        """Test GET request for non-existent incident"""
        response = self.client.get("/api/incidents/grievance/99999/")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_incident(self):
        """Test PUT request updates incident"""
        response = self.client.put(self.url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_send_for_review(self):
        """Test PATCH request sends incident for review"""
        review_template = ReviewTemplateFactory()
        data = {
            "action": "send-for-review",
            "review_template": review_template.id,
            "description": "Please review this grievance",
        }

        response = self.client.patch(self.url, data, format="json")
        # test_patch_send_for_review response: {response.data}
        pass

    def test_patch_mark_closed(self):
        """Test PATCH request marks incident as closed"""
        data = {"action": "mark-closed"}

        response = self.client.patch(self.url, data, format="json")
        # test_patch_mark_closed response: {response.data}
        pass

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_modify_incident(self):
        """Test PATCH request modifies incident"""
        data = {"action": "modify", "grievance_type": "Modified Type"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_invalid_action(self):
        """Test PATCH request with invalid action"""
        data = {"action": "invalid-action"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_patch_missing_action(self):
        """Test PATCH request without action"""
        response = self.client.patch(self.url, {}, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class TestGrievanceInvestigationAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.grievance = GrievanceFactory(created_by=self.super_user)
        self.url = f"/api/incidents/grievance/{self.grievance.id}/investigation/"
        self._authenticate_user(self.super_user)
        self.valid_data = {
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "status": "Draft",
            "conclusion": "Test conclusion",
            "conducted_by": {"user_id": self.staff.id},
            "documents": [],
        }

    def test_get_investigations_list(self):
        """Test GET request returns list of investigations"""
        GrievanceInvestigationFactory(
            grievance_report=self.grievance,
            report_facility=self.super_user_fac,
            department=self.super_user_dept,
        )

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_get_investigations_with_filters(self):
        """Test GET request with filters"""
        GrievanceInvestigationFactory(grievance_report=self.grievance, status="Draft")

        response = self.client.get(f"{self.url}?status=Draft")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_create_investigation_success(self):
        """Test POST request creates investigation"""
        response = self.client.post(self.url, self.valid_data, format="json")
        # test_create_investigation_success response: {response.data}
        pass

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("id", response.data)

    def test_create_investigation_invalid_data(self):
        """Test POST request with invalid data fails"""
        invalid_data = self.valid_data.copy()
        invalid_data.get("conducted_by").pop("user_id", None)

        response = self.client.post(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_investigation_unauthenticated(self):
        """Test POST request without authentication fails"""
        self.client.logout()

        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestGrievanceInvestigationDetailAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.grievance = GrievanceFactory(created_by=self.super_user)
        self.investigation = GrievanceInvestigationFactory(
            grievance_report=self.grievance,
            report_facility=self.super_user_fac,
            department=self.super_user_dept,
        )
        self.url = f"/api/incidents/grievance/{self.grievance.id}/investigation/{self.investigation.id}/"
        self._authenticate_user(self.super_user)
        self.update_data = {
            "status": "Open",
            "conclusion": "Updated conclusion",
            "initial_corrective_actions": "Updated actions",
        }

    def test_get_investigation_details(self):
        """Test GET request returns investigation details"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.investigation.id)

    def test_get_nonexistent_investigation(self):
        """Test GET request for non-existent investigation"""
        url = f"/api/incidents/grievance/{self.grievance.id}/investigation/99999/"

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_investigation(self):
        """Test PUT request updates investigation"""
        response = self.client.put(self.url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # test_update_investigation response: {response}
        pass
        self.assertEqual(response.data["status"], "Open")
        self.assertEqual(response.data["conclusion"], "Updated conclusion")

    def test_update_investigation_invalid_data(self):
        """Test PUT request with invalid data fails"""
        invalid_data = self.update_data.copy()
        invalid_data["status"] = "InvalidStatus"

        response = self.client.put(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_investigation_unauthenticated(self):
        """Test PUT request without authentication fails"""
        self.client.logout()

        response = self.client.put(self.url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
