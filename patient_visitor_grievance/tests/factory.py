import factory
from factory.django import DjangoModelFactory
from factory.faker import Faker
from django.utils import timezone

from base.tests.factory import (
    DepartmentFactory,
    DocumentFactory,
    FacilityFactory,
    ProfileFactory,
    ReviewFactory,
    UserFactory,
)
from accounts.tests.factory import UserProfileFactory


class GrievanceInvestigationInvolvedPartyFactory(DjangoModelFactory):
    class Meta:
        model = "patient_visitor_grievance.GrievanceInvestigationInvolvedParty"

    name = Faker("name")
    relationship_to_patient = factory.Iterator(
        ["Family Member", "Friend", "Legal Guardian", "Healthcare Provider", "Other"]
    )


class GrievanceFactory(DjangoModelFactory):
    class Meta:
        model = "patient_visitor_grievance.Grievance"

    report_facility = factory.SubFactory(FacilityFactory)
    status = factory.Iterator(
        [
            "Draft",
            "Open",
            "Pending Assigned",
            "Pending Review",
            "Pending Approval",
            "Resolved",
        ]
    )
    current_step = factory.Sequence(lambda n: n + 1)
    date = factory.LazyFunction(lambda: timezone.now().date())

    patient_name = factory.SubFactory(UserProfileFactory)
    form_initiated_by = factory.SubFactory(UserProfileFactory)
    title = Faker("sentence")
    complaint_made_by = factory.SubFactory(UserProfileFactory)
    relationship_to_patient = factory.Iterator(
        [
            "Family Member",
            "Friend",
            "Legal Guardian",
            "Self",
            "Other",
        ]
    )

    source_of_information = factory.Iterator(
        [
            "Phone Call",
            "Email",
            "In Person",
            "Letter",
            "Other",
        ]
    )
    complaint_or_concern = Faker("text", max_nb_chars=500)
    initial_corrective_actions = Faker("text", max_nb_chars=500)
    adverse_patient_outcome = Faker("boolean")

    administrator_notified = factory.SubFactory(UserProfileFactory)
    notification_date = factory.LazyFunction(lambda: timezone.now().date())
    notification_time = factory.LazyFunction(lambda: timezone.now().time())
    outcome = Faker("text", max_nb_chars=500)

    department = factory.SubFactory(DepartmentFactory)
    is_modified = False
    is_resolved = False
    created_by = factory.SubFactory(UserFactory)

    @factory.post_generation
    def reviews(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for review in extracted:
                self.reviews.add(review)
        else:
            self.reviews.add(ReviewFactory())

    @factory.post_generation
    def documents(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for doc in extracted:
                self.documents.add(doc)
        else:
            self.documents.add(DocumentFactory())


class GrievanceVersionFactory(GrievanceFactory):
    class Meta:
        model = "patient_visitor_grievance.GrievanceVersion"

    original_report = factory.SubFactory(GrievanceFactory)


class GrievanceInvestigationFactory(DjangoModelFactory):
    class Meta:
        model = "patient_visitor_grievance.GrievanceInvestigation"

    report_facility = factory.SubFactory(FacilityFactory)
    status = factory.Iterator(
        [
            "Draft",
            "Open",
            "Pending Assigned",
            "Pending Review",
            "Pending Approval",
            "Resolved",
        ]
    )
    current_step = factory.Sequence(lambda n: n + 1)
    grievance_report = factory.SubFactory(GrievanceFactory)

    findings = Faker("text", max_nb_chars=1000)
    interviews_findings = Faker("text", max_nb_chars=1000)
    medical_record_findings = Faker("text", max_nb_chars=1000)
    conducted_by = factory.SubFactory(UserProfileFactory)

    start_date = factory.LazyFunction(lambda: timezone.now().date())
    end_date = factory.LazyFunction(
        lambda: (timezone.now() + timezone.timedelta(days=7)).date()
    )

    conclusion = Faker("text", max_nb_chars=1000)
    action_taken = Faker("text", max_nb_chars=1000)
    feedback = factory.Iterator(["Meeting", "Telephone"])
    date_of_feedback = factory.LazyFunction(
        lambda: (timezone.now() + timezone.timedelta(days=14)).date()
    )

    extension_letter_sent = Faker("boolean")
    date_extension_letter_sent = factory.LazyFunction(lambda: timezone.now().date())
    extension_letter_copy = factory.SubFactory(DocumentFactory)

    response_letter_sent = Faker("boolean")
    date_response_letter_sent = factory.LazyFunction(
        lambda: (timezone.now() + timezone.timedelta(days=21)).date()
    )
    response_letter_copy = factory.SubFactory(DocumentFactory)

    matter_closed = Faker("boolean")
    date_matter_closed = factory.LazyFunction(
        lambda: (timezone.now() + timezone.timedelta(days=28)).date()
    )

    department = factory.SubFactory(DepartmentFactory)
    is_resolved = False

    @factory.post_generation
    def reviews(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for review in extracted:
                self.reviews.add(review)
        else:
            self.reviews.add(ReviewFactory())

    @factory.post_generation
    def documents(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for doc in extracted:
                self.documents.add(doc)
        else:
            self.documents.add(DocumentFactory())

    @factory.post_generation
    def involved_parties(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for party in extracted:
                self.involved_parties.add(party)
        else:
            self.involved_parties.add(UserProfileFactory())
