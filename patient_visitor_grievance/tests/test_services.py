from base.tests.factory import (
    ProfileFactory,
    UserProfileFactory,
)
from documents.models import Document
from patient_visitor_grievance.services.operations import (
    GrievanceInvestigationService,
    GrievanceService,
)
from patient_visitor_grievance.tests.factory import (
    GrievanceFactory,
    GrievanceInvestigationFactory,
)
from base.tests.base_setup import BaseTestSetup
from patient_visitor_grievance.services.actions import GrievanceActionsService
from tasks.tests.factory import ReviewTemplateFactory


class TestGrievanceServiceModifyIncidentNewUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = GrievanceFactory(created_by=self.super_user)
        self.service = GrievanceActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "form_initiated_by": {
                    "first_name": "<PERSON>",
                    "last_name": "Doe",
                    "email": "<EMAIL>",
                    "profile_type": "Staff",
                },
                "complaint_made_by": {
                    "first_name": "<PERSON>",
                    "last_name": "<PERSON>",
                    "profile_type": "Patient",
                },
                "administrator_notified": {
                    "first_name": "Admin",
                    "last_name": "User",
                    "profile_type": "Staff",
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        # response.message
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_fields(self):
        self.service.data.get("form_initiated_by").pop("profile_type")
        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestGrievanceServiceModifyIncidentExistingUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = GrievanceFactory(created_by=self.super_user)
        self.service = GrievanceActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "form_initiated_by": {
                    "user_id": self.staff.id,
                },
                "complaint_made_by": {
                    "user_id": self.patient.id,
                },
                "administrator_notified": {
                    "user_id": self.staff.id,
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_fields(self):
        self.service.data.get("form_initiated_by").pop("user_id")
        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_modify_incident_invalid_user_id(self):
        self.service.data.get("form_initiated_by")["user_id"] = 9999
        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestGrievanceServiceSendReviewIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = GrievanceFactory(created_by=self.super_user)
        self.review_template = ReviewTemplateFactory()
        self.assignee_1 = ProfileFactory()
        self.assignee_2 = ProfileFactory()
        self.assignee_3 = ProfileFactory()
        self.service = GrievanceActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "review_template": self.review_template.id,
            },
        )

    def test_send_for_review_success(self):
        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_send_for_review_invalid_template(self):
        self.service.data["review_template"] = 9999
        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_missing_template(self):
        self.service.data.pop("review_template")
        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_assignees_and_template(self):
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]
        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_assignees(self):
        self.service.data.pop("review_template")
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]
        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestGrievanceServiceMarkClose(BaseTestSetup):
    def setUp(self):
        super().setUp()
        # Create incident with one user, but close it with another user to avoid the business rule
        self.incident = GrievanceFactory(created_by=self.user_user)
        self.service = GrievanceActionsService(
            user=self.super_user,  # Super user closing incident created by regular user
            incident_id=self.incident.id,
            data={
                "action": "mark-closed",
            },
        )

    def test_mark_close_success(self):
        response = self.service.mark_closed()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestGrievanceServiceGetIncidentById(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = GrievanceService(user=self.super_user)

    def test_get_incident_by_id(self):
        incident = GrievanceFactory(created_by=self.super_user)
        response = self.service.get_incident_by_id(incident.id)

        self.assertTrue(response.success)
        self.assertFalse(response.data["has_investigation"])
        self.assertIsNotNone(response.data)

    def test_get_incident_by_id_not_found(self):
        response = self.service.get_incident_by_id(9999)
        self.assertFalse(response.success)


class TestGrievanceServiceCase(BaseTestSetup):
    """Test Grievance Service operations as Superuser"""

    def setUp(self):
        super().setUp()
        # Use the super_user from BaseTestSetup
        self.service = GrievanceService(user=self.super_user)

    def test_get_incidents_as_superuser(self):
        # create 5 new grievances for new facility
        for _ in range(5):
            GrievanceFactory(
                department=self.super_user_dept,
                report_facility=self.super_user_fac,
                created_by=self.user_user,
            )

        # create 2 grievances for another facility
        for _ in range(2):
            GrievanceFactory()

        response = self.service.get_incidents_list()

        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data), 7)


class TestGrievanceServiceAsAdmin(BaseTestSetup):
    """Test Grievance Service operations as Admin user"""

    def setUp(self):
        super().setUp()
        self.service = GrievanceService(user=self.admin_user)

    def test_get_incidents_as_admin(self):
        """Test getting incidents as an admin user"""
        # Admin should see incidents from their facility
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGrievanceServiceAsDirector(BaseTestSetup):
    """Test Grievance Service operations as Director user"""

    def setUp(self):
        super().setUp()
        self.service = GrievanceService(user=self.director_user)

    def test_get_incidents_as_director(self):
        """Test getting incidents as a director user"""
        # Director should see incidents from their facility
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGrievanceServiceAsManager(BaseTestSetup):
    """Test Grievance Service operations as Manager user"""

    def setUp(self):
        super().setUp()
        self.service = GrievanceService(user=self.manager_user)

    def test_get_incidents_as_manager(self):
        """Test getting incidents as a manager user"""
        # Manager should see incidents from their department
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGrievanceServiceAsRegularUser(BaseTestSetup):
    """Test Grievance Service operations as Regular user"""

    def setUp(self):
        super().setUp()
        self.service = GrievanceService(user=self.user_user)

    def test_get_incidents_as_regular_user(self):
        """Test getting incidents as a regular user"""
        # Regular user should see their own incidents and assigned reviews
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestCreateGrievanceServiceCase(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = GrievanceService(user=self.super_user)

        self.valid_data = {
            "status": "Draft",
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "current_step": 1,
            "is_resolved": False,
            "resolved": False,
            "title": "Patient Complaint",
            "patient_name": {"user_id": self.patient.id},
            "form_initiated_by": {"user_id": self.staff.id},
            "complaint_made_by": {"user_id": self.patient.id},
            "relationship_to_patient": "Self",
            "source_of_information": "Direct Report",
            "complaint_or_concern": "Long wait times in ER",
            "initial_corrective_actions": "Assigned case manager",
            "adverse_patient_outcome": False,
            "administrator_notified": {"user_id": self.staff.id},
            "notification_date": "2025-05-20",
            "notification_time": "14:30:00",
            "outcome": "Patient received apology and explanation",
            "documents": [],
        }

    def test_create_incident_success(self):
        response = self.service.create_incident(self.valid_data)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertIsNotNone(response.data["id"])

        incident = response.data
        self.assertEqual(incident["title"], self.valid_data["title"])
        self.assertEqual(
            incident["complaint_or_concern"], self.valid_data["complaint_or_concern"]
        )

    def test_create_incident_minimum_required_fields(self):
        minimal_data = {
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "title": "Basic Complaint",
            "complaint_or_concern": "General dissatisfaction",
        }

        response = self.service.create_incident(minimal_data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)

    def test_create_incident_missing_required_fields(self):
        invalid_data = self.valid_data.copy()
        invalid_data.get("form_initiated_by").pop("user_id")

        response = self.service.create_incident(invalid_data)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_create_incident_with_new_users(self):
        data = self.valid_data.copy()
        data.update(
            {
                "form_initiated_by": {
                    "first_name": "John",
                    "last_name": "Staff",
                    "email": "<EMAIL>",
                    "profile_type": "Staff",
                },
                "complaint_made_by": {
                    "first_name": "Jane",
                    "last_name": "Patient",
                    "email": "<EMAIL>",
                    "profile_type": "Patient",
                },
            }
        )

        response = self.service.create_incident(data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)

    # def test_create_incident_invalid_facility(self):
    #     invalid_data = self.valid_data.copy()
    #     invalid_data["report_facility"] = 99999

    #     response = self.service.create_incident(invalid_data)
    #     print(response.message)
    #     self.assertFalse(response.success)
    #     self.assertEqual(response.code, 400)


class TestUpdateGrievanceServiceCase(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = GrievanceService(user=self.super_user)
        self.incident = GrievanceFactory(
            created_by=self.super_user,
            department=self.super_user_dept,
            report_facility=self.super_user_fac,
        )
        staff = UserProfileFactory(profile_type="Staff")
        patient = UserProfileFactory(profile_type="Patient")
        admin = UserProfileFactory(profile_type="Admin")

        self.valid_update_data = {
            "status": "Open",
            "department": self.manager_user_dept.id,
            "title": "Updated Complaint Title",
            "complaint_or_concern": "Updated complaint details",
            "outcome": "Updated resolution details",
            "administrator_notified": {"user_id": staff.id},
            "documents": [],
        }

    def test_update_incident_success(self):
        response = self.service.update_incident(
            self.incident.id, self.valid_update_data
        )

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["title"], self.valid_update_data["title"])

    def test_update_incident_with_new_users(self):
        update_data = self.valid_update_data.copy()
        update_data.update(
            {
                "form_initiated_by": {
                    "first_name": "New",
                    "last_name": "Staff",
                    "email": "<EMAIL>",
                    "profile_type": "Staff",
                }
            }
        )

        response = self.service.update_incident(self.incident.id, update_data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_update_nonexistent_incident(self):
        response = self.service.update_incident(99999, self.valid_update_data)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_update_incident_unauthorized(self):
        unauthorized_service = GrievanceService(user=self.user_user)
        response = unauthorized_service.update_incident(
            self.incident.id, self.valid_update_data
        )
        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)

    def test_update_incident_with_status_change(self):
        update_data = {"status": "Open", "title": "Urgent Complaint"}

        response = self.service.update_incident(self.incident.id, update_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data["status"], "Open")

    def test_update_incident_with_documents(self):
        document = Document.objects.create(
            name="Grievance Doc",
            document_url="path/to/doc.pdf",
        )

        update_data = self.valid_update_data.copy()
        update_data["documents"] = [document.id]

        response = self.service.update_incident(self.incident.id, update_data)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["documents"]), 1)


class TestGrievanceInvestigationServiceGetById(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = GrievanceInvestigationService(user=self.super_user)

    def test_get_investigation_by_id_success(self):
        grievance = GrievanceFactory(created_by=self.super_user)
        investigation = GrievanceInvestigationFactory(grievance_report=grievance)
        response = self.service.get_investigation_by_id(grievance.id, investigation.id)

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_investigation_by_id_not_found(self):
        response = self.service.get_investigation_by_id(99999, 88888)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)


class TestGrievanceInvestigationServiceCase(BaseTestSetup):
    """Test Grievance Investigation Service operations as Superuser"""

    def setUp(self):
        super().setUp()
        # Use the super_user from BaseTestSetup
        self.service = GrievanceInvestigationService(user=self.super_user)

    def test_get_investigation_list(self):
        for _ in range(5):
            grievance = GrievanceFactory(created_by=self.user_user)
            GrievanceInvestigationFactory(
                report_facility=self.super_user_fac,
                department=self.super_user_dept,
                grievance_report=grievance,
            )
        GrievanceInvestigationFactory.create_batch(2)

        response = self.service.get_investigations_list(grievance_id=grievance.id)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 1)


class TestGrievanceInvestigationServiceAsAdmin(BaseTestSetup):
    """Test Grievance Investigation Service operations as Admin user"""

    def setUp(self):
        super().setUp()
        self.service = GrievanceInvestigationService(user=self.admin_user)
        # Create a grievance for testing investigations
        self.test_grievance = GrievanceFactory(created_by=self.admin_user)

    def test_get_investigations_as_admin(self):
        """Test getting investigations as an admin user"""
        # Admin should see investigations from their facility
        response = self.service.get_investigations_list(
            grievance_id=self.test_grievance.id
        )

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGrievanceInvestigationServiceAsDirector(BaseTestSetup):
    """Test Grievance Investigation Service operations as Director user"""

    def setUp(self):
        super().setUp()
        self.service = GrievanceInvestigationService(user=self.director_user)
        # Create a grievance for testing investigations
        self.test_grievance = GrievanceFactory(created_by=self.director_user)

    def test_get_investigations_as_director(self):
        """Test getting investigations as a director user"""
        # Director should see investigations from their facility
        response = self.service.get_investigations_list(
            grievance_id=self.test_grievance.id
        )

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGrievanceInvestigationServiceAsManager(BaseTestSetup):
    """Test Grievance Investigation Service operations as Manager user"""

    def setUp(self):
        super().setUp()
        self.service = GrievanceInvestigationService(user=self.manager_user)
        # Create a grievance for testing investigations
        self.test_grievance = GrievanceFactory(created_by=self.manager_user)

    def test_get_investigations_as_manager(self):
        """Test getting investigations as a manager user"""
        # Manager should see investigations from their department
        response = self.service.get_investigations_list(
            grievance_id=self.test_grievance.id
        )

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGrievanceInvestigationServiceAsRegularUser(BaseTestSetup):
    """Test Grievance Investigation Service operations as Regular user"""

    def setUp(self):
        super().setUp()
        self.service = GrievanceInvestigationService(user=self.user_user)
        # Create a grievance for testing investigations
        self.test_grievance = GrievanceFactory(created_by=self.user_user)

    def test_get_investigations_as_regular_user(self):
        """Test getting investigations as a regular user"""
        # Regular user should see their own investigations and assigned reviews
        response = self.service.get_investigations_list(
            grievance_id=self.test_grievance.id
        )

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestCreateGrievanceInvestigationService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = GrievanceInvestigationService(user=self.super_user)
        self.grievance_id = GrievanceFactory(created_by=self.super_user).id

        self.valid_data = {
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "status": "Draft",
            "conclusion": "Findings summary here",
            "feedback": "Meeting",
            "conducted_by": {"user_id": self.staff.id},
            "documents": [],
        }

    def test_create_investigation_success(self):
        response = self.service.create_investigation(self.grievance_id, self.valid_data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertIsNotNone(response.data["id"])

    def test_create_investigation_missing_required_fields(self):
        invalid_data = self.valid_data.copy()
        invalid_data.get("conducted_by").pop("user_id")
        response = self.service.create_investigation(self.grievance_id, invalid_data)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_create_investigation_with_documents(self):
        document = Document.objects.create(name="Evidence", document_url="test.pdf")
        data = self.valid_data.copy()
        data["documents"] = [document.id]

        response = self.service.create_investigation(self.grievance_id, data)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["documents"]), 1)


class TestUpdateGrievanceInvestigationService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = GrievanceInvestigationService(user=self.super_user)
        grievance = GrievanceFactory(
            report_facility=self.super_user_fac,
            department=self.super_user_dept,
            created_by=self.super_user,
        )

        self.investigation = GrievanceInvestigationFactory(grievance_report=grievance)
        self.grievance_id = grievance.id

        self.update_data = {
            "status": "Pending Review",
            "conclusion": "Updated conclusion",
            "action_taken": "New action taken",
            "documents": [],
            "extension_letter_sent": True,
            "response_letter_sent": True,
        }

    def test_update_investigation_success(self):
        response = self.service.update_investigation(
            self.investigation.id, self.grievance_id, self.update_data
        )
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["status"], "Pending Review")

    def test_update_investigation_not_found(self):
        response = self.service.update_investigation(
            99999, self.grievance_id, self.update_data
        )
        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_update_investigation_unauthorized(self):
        unauthorized_service = GrievanceInvestigationService(user=self.user_user)
        response = unauthorized_service.update_investigation(
            self.investigation.id, self.grievance_id, self.update_data
        )
        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)

    def test_update_investigation_with_documents(self):
        doc = Document.objects.create(name="Medical Record", document_url="record.pdf")
        data = self.update_data.copy()
        data["documents"] = [doc.id]

        response = self.service.update_investigation(
            self.investigation.id, self.grievance_id, data
        )
        self.assertTrue(response.success)
        self.assertIn(doc.id, response.data["documents"])
