from accounts.services.user_profile.service import UserProfileService
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.constants import ReviewStatus
from base.services.incidents.base import IncidentService
from base.services.responses import APIResponse
from incidents.services.permissions import IncidentPermissionsService
from patient_visitor_grievance.models import Grievance
from base.services.logging.logger import LoggingService
from patient_visitor_grievance.serializers import GrievanceSerializerVersion
from patient_visitor_grievance.new_serializers import (
    GetPatientVisitorGrievanceSerializer,
)
from incidents.services.workflow import IncidentWorkflow
from rest_framework import status
from django.core.exceptions import ValidationError
from incidents.views.send_to_department import send_incident_submission_email
from activities.services import ActivityLogService
from activities.models import ActivityType
from tasks.models import ReviewTemplates

logging_service = LoggingService()
user_profile_service = UserProfileService()


class GrievanceActionsService:
    """
    Service class for handling Grievance incident actions.
    """

    def __init__(self, user, incident_id, data):
        self.user = user
        self.data = data
        self.incident_id = incident_id
        self.workflow_services = IncidentWorkflow(model=Grievance, user=self.user)
        self.general_incident = IncidentService()
        try:
            self.incident = (
                Grievance.objects.select_related(
                    "report_facility",
                    "department",
                    "created_by",
                    "form_initiated_by",
                    "complaint_made_by",
                    "administrator_notified",
                    "patient_name",
                )
                .prefetch_related(
                    "documents",
                    "reviews",
                )
                .get(id=incident_id)
            )
        except Grievance.DoesNotExist:
            self.incident = None
        self.permissions = IncidentPermissionsService(
            user=self.user,
            app_label=Grievance._meta.app_label,
            incident=self.incident,
        )

    def modify_incident(self) -> APIResponse:
        """Modifies an existing Grievance incident."""
        try:
            # check permissions
            permission_response = self.permissions.can_modify_incident()
            if not permission_response.success:
                return APIResponse(
                    success=False,
                    message=permission_response.message,
                    code=status.HTTP_403_FORBIDDEN,
                    data=None,
                )

            report_facility = self.incident.report_facility
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, self.incident.report_facility)
                and not is_manager_user(self.user, self.incident.department)
            ) and not self.incident.created_by == self.user:
                return APIResponse(
                    success=False,
                    message="You do not have permission to modify this incident",
                    code=status.HTTP_403_FORBIDDEN,
                    data=None,
                )

            # Handle user profile relationships
            for field in [
                "form_initiated_by",
                "complaint_made_by",
                "administrator_notified",
                "conducted_by",
                "patient_name",
            ]:
                if field in self.data:
                    profile = user_profile_service.get_or_create_profile(
                        self.data[field]
                    )
                    if not profile.success:
                        return APIResponse(
                            success=False,
                            message=profile.message,
                            code=status.HTTP_400_BAD_REQUEST,
                            data=None,
                        )
                    self.data[field] = profile.data.id

            self.data["report_facility"] = report_facility.id
            self.data["department"] = (
                self.incident.department.id if self.incident.department else None
            )
            self.data["created_by"] = self.incident.created_by.id
            self.data["original_report"] = self.incident.id

            version_serializer = GrievanceSerializerVersion(data=self.data)
            if version_serializer.is_valid():
                version_serializer.save()
                old_status = self.incident.status
                self.incident.is_modified = True
                self.incident.updated_by = self.user
                self.incident.status = self.data.get("status", ReviewStatus.DRAFT)
                self.incident.save()

                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=self.incident,
                    activity_type=ActivityType.MODIFIED,
                    description="Incident modified"
                )

                new_status = self.incident.status
                if old_status != new_status:
                    ActivityLogService.create_activity(
                        user=self.user,
                        content_object=self.incident,
                        activity_type=ActivityType.STATUS_CHANGED,
                        description=f"Status changed from {old_status} to {new_status}"
                    )

                if self.incident.status == ReviewStatus.OPEN:
                    send_incident_submission_email(
                        incident=self.incident,
                        incident_type="Grievance",
                    )
                return APIResponse(
                    success=True,
                    message="Grievance incident modified successfully",
                    code=status.HTTP_200_OK,
                    data=version_serializer.data,
                )
            else:
                logging_service.log_error(version_serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data provided",
                    code=status.HTTP_400_BAD_REQUEST,
                    data=version_serializer.errors,
                )
        except Grievance.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance incident not found",
                code=status.HTTP_404_NOT_FOUND,
                data=None,
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Validation error",
                code=status.HTTP_400_BAD_REQUEST,
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error modifying incident",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def send_for_review(self) -> APIResponse:
        """Sends the grievance for review."""
        try:
            response = self.workflow_services.send_for_a_review(
                self.incident_id, self.data
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None,
                )

            if self.data.get("assignees") not in [None, [], ""]:
                assignees = list(response.data.assignees.all())
                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=response.data,
                    activity_type=ActivityType.SENT_TO_REVIEWER,
                    highlight_objects=None,
                    destination_objects=assignees
                )
            if self.data.get("review_template") is not None:
                review_template = ReviewTemplates.objects.get(id=self.data["review_template"])
                assignees = list(review_template.assignees.all())
                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=response.data,
                    activity_type=ActivityType.SENT_TO_REVIEWER,
                    highlight_objects=review_template,
                    destination_objects=assignees
                )

            serializer = GetPatientVisitorGrievanceSerializer(response.data)
            return APIResponse(
                success=True,
                message="Grievance sent for review",
                code=status.HTTP_200_OK,
                data=serializer.data,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error sending grievance for review",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def mark_closed(self) -> APIResponse:
        """Marks the grievance as resolved."""
        try:
            response = self.workflow_services.mark_as_resolved(
                incident=self.incident, user=self.user
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None,
                )

            ActivityLogService.create_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.RESOLVED,
                description="Incident marked as resolved"
            )

            return APIResponse(
                success=True,
                message="Grievance marked as resolved",
                code=status.HTTP_200_OK,
                data=None,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error marking grievance as resolved",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def delete_grievance_draft_incidents(self) -> APIResponse:
        """Deletes draft incidents for Grievance."""
        try:
            response = self.workflow_services.delete_drafts(
                user=self.user,
                model=Grievance,
                incident_id=self.data.get("incident_ids", None),
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None,
                )

            ActivityLogService.create_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.DELETED,
                description="Draft incidents deleted"
            )

            return APIResponse(
                success=True,
                message="Draft grievances deleted successfully",
                code=status.HTTP_200_OK,
                data=response.data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error deleting draft grievances",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )
