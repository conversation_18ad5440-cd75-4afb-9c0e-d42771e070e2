from base.services.responses import APIResponse
from documents.models import Document
from django.db.models import Q
from base.validadors.date import DateValidator


class DocumentQuery:
    def __init__(self):
        self.date_validator = DateValidator()

    def get_documents(self, query_params=None) -> APIResponse:
        """
        Document query service to retrieve all documents
        args:
            query_params (dict): Optional parameters to filter documents.
                - page (int): Page number for pagination.
                - page_size (int): Number of documents per page.
                - incident (model): Filter by model_instance.
                - created_by (int): Filter by user ID who created the document.
                - q (str): Search query to filter documents by name or file type.
                - start_date (str): Filter documents created after this date.
                - end_date (str): Filter documents created before this date.
                - sort_by (str): Field to sort the results by.
                - sort_order (str): Order of sorting, either 'asc' or 'desc'.
                - file_type (str): Filter documents by file type.

        returns:
            APIResponse: Response object containing:
            - success (bool): Indicates if the operation was successful.
            - message (str): Message describing the result.
            - data {object}: Contains:
                - results (list): List of documents matching the query.
                - page (int): Current page number.
                - total_pages (int): Total number of pages available.
                - page_size (int): Number of documents per page.
                - has_next (bool): Indicates if there are more pages.
                - has_previous (bool): Indicates if there are previous pages.
        """

        # Defensive: ensure query_params is a dict
        if not query_params:
            query_params = {}

        if "instance" in query_params and query_params.get("instance"):
            model_instance = query_params.get("instance")
            documents = model_instance.documents.all()
        else:
            documents = Document.objects.all()
        if query_params:
            if "incident" in query_params and query_params.get("incident"):
                documents = documents.filter(incident=query_params["incident"])

            if "created_by" in query_params and query_params.get("created_by"):
                documents = documents.filter(created_by=query_params["created_by"])

            if "q" in query_params and query_params.get("q"):
                search_query = query_params["q"]
                documents = documents.filter(Q(name__icontains=search_query))

            if "start_date" in query_params and query_params.get("start_date"):
                is_valid_date = self.date_validator.is_valid_date(
                    query_params["start_date"]
                )
                if not is_valid_date.success:
                    return is_valid_date
                documents = documents.filter(created_at__gte=is_valid_date.data)

            if "end_date" in query_params and query_params.get("end_date"):
                is_valid_date = self.date_validator.is_valid_date(
                    query_params["end_date"]
                )
                if not is_valid_date.success:
                    return is_valid_date
                end_date = is_valid_date.data
                documents = documents.filter(created_at__lte=end_date)

            if "file_type" in query_params and query_params.get("file_type"):
                file_type = query_params["file_type"]
                # validate file_type against allowed types
                allowed_file_types = [".pdf", ".docx", ".txt", ".jpg", ".png"]
                if file_type not in allowed_file_types:
                    return APIResponse(
                        success=False,
                        message=f"Invalid file_type. Allowed types are: {', '.join(allowed_file_types)}",
                        code=400,
                    )
                documents = documents.filter(file_type=file_type)

            if "created_at" in query_params and query_params.get("created_at"):
                from datetime import datetime

                created_at = query_params["created_at"]
                # validate created_at against allowed date formats
                try:
                    created_at = datetime.fromisoformat(created_at)
                except ValueError:
                    return APIResponse(
                        success=False,
                        message="Invalid created_at format. Use ISO format (YYYY-MM-DD).",
                        code=400,
                    )
                documents = documents.filter(created_at__date=created_at.date())

            if "sort_by" in query_params and query_params.get("sort_by"):
                sort_by = query_params["sort_by"]
                # validate sort_by field
                allowed_sort_fields = ["name", "file_type", "created_at", "updated_at"]
                if sort_by not in allowed_sort_fields:
                    return APIResponse(
                        success=False,
                        message=f"Invalid sort_by field. Allowed fields: {', '.join(allowed_sort_fields)}",
                        code=400,
                    )
                sort_order = query_params.get("sort_order", "asc")
                if sort_order == "desc":
                    sort_by = f"-{sort_by}"
                documents = documents.order_by(sort_by)

        # Defensive: handle invalid page/page_size
        try:
            page = int(query_params.get("page", 1))
            page_size = int(query_params.get("page_size", 10))
        except (TypeError, ValueError):
            return APIResponse(
                success=False,
                message="Invalid page or page_size parameter.",
                code=400,
            )

        total_documents = documents.count()
        total_pages = (total_documents + page_size - 1) // page_size
        start_index = (page - 1) * page_size
        end_index = start_index + page_size

        paginated_documents = documents[start_index:end_index]
        has_next = page < total_pages
        has_previous = page > 1
        results = [
            {
                "id": doc.id,
                "name": doc.name,
                "file_type": doc.file_type,
                "created_at": doc.created_at,
                "updated_at": doc.updated_at,
                "url": doc.document_url if doc.document_url else None,
            }
            for doc in paginated_documents
        ]
        return APIResponse(
            success=True,
            message="Documents retrieved successfully",
            data={
                "results": results,
                "page": page,
                "total_pages": total_pages,
                "page_size": page_size,
                "has_next": has_next,
                "has_previous": has_previous,
            },
            code=200,
        )
