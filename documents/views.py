import os
import cloudinary
import cloudinary.uploader
import boto3
from datetime import datetime
import uuid
from core.settings import (
    AWS_S3_ENDPOINT_URL,
    CLOUDINARY_NAME,
    CLOUDINARY_API_KEY,
    CLOUDINARY_SECRET_KEY,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    AWS_STORAGE_BUCKET_NAME,
)
from documents.models import Document
import boto3
from django.conf import settings
from django.core.files.base import ContentFile

cloudinary.config(
    cloud_name=CLOUDINARY_NAME,
    api_key=CLOUDINARY_API_KEY,
    api_secret=CLOUDINARY_SECRET_KEY,
    secure=True,
)

from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.core.exceptions import ObjectDoesNotExist

from base.services.logging.logger import LoggingService

logging_service = LoggingService()

s3_client = boto3.client(
    "s3",
    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
    endpoint_url=os.getenv("AWS_S3_ENDPOINT_URL"),
)


@api_view(["POST"])
def upload_uncompressed_files(request):
    files = request.FILES.getlist("file")
    folder_name = request.data.get("folder_name")

    results = []
    for file in files:
        # get file type, like jpg, pdf, etc
        import os

        base_name, extension = os.path.splitext(str(file.name))

        result, url, message = upload_file_to_aws(file, folder_name)
        # result, url, message = upload_file(file, folder_name)
        results.append(url)

    return Response({"urls": results})


def upload_file_to_aws(file, folder_name):
    try:
        file_path = f"{folder_name}/{datetime.now().year}/{datetime.now().month}/{datetime.now().day}/{file.name}-{uuid.uuid4()}"

        s3_client.upload_fileobj(file, os.getenv("AWS_STORAGE_BUCKET_NAME"), file_path),

        presigned_url = s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": os.getenv("AWS_STORAGE_BUCKET_NAME"), "Key": file_path},
            ExpiresIn=3600,
        )

        file_url = f"{os.getenv('AWS_S3_ENDPOINT_URL')}/{os.getenv('AWS_STORAGE_BUCKET_NAME')}/{file_path}"

        return True, presigned_url, "File uploaded successfully"

    except Exception as e:
        logging_service.log_error(e)
        return False, None, "Internal server error"


def upload_file(file, folder_name):
    try:
        public_id = f"{folder_name}/{datetime.now().year}/{datetime.now().month}/{datetime.now().day}/{file.name}-{uuid.uuid4()}"

        # Determine resource type based on file
        file_extension = file.name.lower().split(".")[-1] if "." in file.name else ""

        # For non-image files, use 'raw' resource type
        if file_extension in [
            "pdf",
            "doc",
            "docx",
            "txt",
            "xls",
            "xlsx",
            "ppt",
            "pptx",
            "zip",
            "rar",
        ]:
            resource_type = "raw"
        else:
            resource_type = "auto"  # Let Cloudinary auto-detect

        response = cloudinary.uploader.upload(
            file,
            public_id=public_id,
            unique_filename=True,
            overwrite=False,
            resource_type=resource_type,
            # Remove preset_name as it might be image-only
            # preset_name="v3x0jd9r",
        )
        return (
            True,
            response["secure_url"],
            response["public_id"],
            "File uploaded successfully",
        )
    except Exception as e:
        logging_service.log_error(e)
        return (False, None, None, "Internal server error")


def delete_file(public_id):
    # delete file from cloudinary
    try:
        cloudinary.uploader.destroy(public_id)
        return True
    except Exception as e:
        logging_service.log_error(e)
        return False


def handle_incident_document(files, folder_name, incident, user):

    try:
        results = []
        documents = []
        incident_documents = []

        if not files or len(files) < 1:
            return ("No files provided", [])

        for file in files:
            unique_filename = f"{uuid.uuid4().hex}_{file.name}"
            wrapped_file = ContentFile(file.read(), name=unique_filename)
            base_name, extension = os.path.splitext(unique_filename)

            if len(base_name) > 200:
                base_name = base_name[:200]

            is_uploaded, document_url, public_id, message = upload_file(
                wrapped_file, folder_name
            )

            if not is_uploaded:
                logging_service.log_error(message)
                raise Exception("File upload failed")
            document = Document.objects.create(
                name=base_name,
                file_type=extension,
                created_by=user,
                document_url=document_url,
                public_id=public_id,
            )

            incident.documents.add(document.id)
            incident.save()

        for doc in incident.documents.all():
            incident_documents.append(
                {
                    "id": doc.id,
                    "name": doc.name,
                    "file_type": doc.file_type,
                    "created_at": doc.created_at,
                    "updated_at": doc.updated_at,
                    "url": doc.document_url,
                }
            )
        return {"files": incident_documents}

    except Exception as e:
        logging_service.log_error(e)
        raise


def handle_single_file(file, user):
    try:
        base_name, extension = os.path.splitext(str(file.name))

        if len(base_name) > 200:
            base_name = base_name[:200]

        wrapped_file = ContentFile(file.read(), name=file.name)
        document = Document.objects.create(
            name=base_name,
            file_type=extension,
            created_by=user,
            file=wrapped_file,
        )
        return (
            document,
            True,
            "Document created",
        )

    except Exception as e:
        logging_service.log_error(e)
        return ({}, False, "Internal server error")


def get_incident_documents(model_name, incident_id):

    try:
        incident = model_name.objects.get(id=incident_id)
        documents = [
            {
                "id": doc.id,
                "name": doc.name,
                "file_type": doc.file_type,
                "url": doc.document_url,
            }
            for doc in incident.documents.all()
        ]

        return (
            documents,
            True,
        )

    except model_name.DoesNotExist:
        return ("Incident not found", False)


def generate_signed_url(file_path):
    s3_client = boto3.client(
        "s3",
        # endpoint_url="https://syd1.digitaloceanspaces.com",
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    )

    signed_url = s3_client.generate_presigned_url(
        "get_object",
        Params={"Bucket": "q-control-testing", "Key": file_path},
        ExpiresIn=300,
    )
    return signed_url


# deleting the document
def delete_file_from_s3(file_path):
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
        endpoint_url=AWS_S3_ENDPOINT_URL,
    )

    try:

        s3_client.delete_object(Bucket=AWS_STORAGE_BUCKET_NAME, Key=file_path)
    except Exception as e:
        logging_service.log_error(e)
        raise


# function handling the delete documents operation
def delete_incident_document(incident, document_id):
    try:

        document = Document.objects.get(id=document_id)
        document_url = document.document_url

        # Delete from Cloudinary if public_id exists
        if document.public_id:
            delete_file(document.public_id)

        incident.documents.remove(document.id)

        document.delete()

        return True, f"Document {document_url} deleted successfully from {incident}"

    except Document.DoesNotExist:
        return False, "Document does not exist"
    except ObjectDoesNotExist:
        return False, "Incident does not exist"
    except Exception as e:
        logging_service.log_error(e)
        return False, f"An error occurred"
