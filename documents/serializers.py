from rest_framework import serializers
from accounts.serializers import UserSerializer
from documents.models import Document


class DocumentSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)

    class Meta:
        model = Document
        fields = ["id", "file_url", "file_type", "created_by"]

    def get_file_url(self, obj):
        if obj:
            return obj.document_url
        return None
