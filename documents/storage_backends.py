# from django.conf import settings
# from storages.backends.s3boto3 import S3Boto3Storage


# class StaticStorage(S3Boto3Storage):
#     location = "static"
#     default_acl = "public-read"


# class PublicMediaStorage(S3Boto3Storage):
#     location = "media"
#     default_acl = "public-read"
#     file_overwrite = False

#     @property
#     def querystring_auth(self):
#         return False


# class PrivateMediaStorage(S3Boto3Storage):
#     location = "private"
#     default_acl = "private"
#     file_overwrite = False
#     custom_domain = False

#     @property
#     def querystring_auth(self):
#         return True

import uuid
from datetime import datetime
from storages.backends.s3boto3 import S3Boto3Storage


class StaticStorage(S3Boto3Storage):
    location = "static"
    default_acl = "public-read"


class PublicMediaStorage(S3Boto3Storage):
    default_acl = "public-read"
    file_overwrite = False

    @property
    def querystring_auth(self):
        return False

    def _save(self, name, content):
        folder_name = "public"
        original_name = name.split(".")[:50]
        extension = name.split(".")[-1]
        unique_filename = f"{uuid.uuid4().hex}_{original_name}.{extension}"
        path = f"{folder_name}/{datetime.now().year}/{datetime.now().month}/{datetime.now().day}/{unique_filename}"[:100]
        return super()._save(path, content)


class PrivateMediaStorage(S3Boto3Storage):
    default_acl = "private"
    file_overwrite = False
    custom_domain = False

    @property
    def querystring_auth(self):
        return True

    def _save(self, name, content):
        folder_name = "private"
        original_name = name.split(".")[:50]
        extension = name.split(".")[-1]
        unique_filename = f"{uuid.uuid4().hex}_{original_name}.{extension}"
        unique_filename = f"{uuid.uuid4().hex}_{name}"
        path = f"{folder_name}/{datetime.now().year}/{datetime.now().month}/{datetime.now().day}/{unique_filename}"[:100]
        return super()._save(path, content)
