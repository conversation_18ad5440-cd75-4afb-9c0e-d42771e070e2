import io
from django.test import TestCase
from django.contrib.auth.models import User
from accounts.models import Profile
from documents.models import Document
from adverse_drug_reaction.models import AdverseDrugReaction
from documents.services.query import DocumentQuery
from django.core.files.uploadedfile import SimpleUploadedFile
from reportlab.pdfgen import canvas
from docx import Document as DocxDocument


def generate_pdf_file(filename="test.pdf"):
    buffer = io.BytesIO()
    p = canvas.Canvas(buffer)
    p.drawString(100, 750, "This is a test PDF file.")
    p.save()
    buffer.seek(0)
    return SimpleUploadedFile(filename, buffer.read(), content_type="application/pdf")


def generate_docx_file(filename="test.docx"):
    buffer = io.BytesIO()
    doc = DocxDocument()
    doc.add_paragraph("This is a test DOCX file.")
    doc.save(buffer)
    buffer.seek(0)
    return SimpleUploadedFile(
        filename,
        buffer.read(),
        content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    )


class TestDocumentQueryWithModelParam(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="testuser")
        self.profile = Profile.objects.create(user=self.user)
        self.adr = AdverseDrugReaction.objects.create()
        self.document1 = Document.objects.create(
            name="doc1",
            file_type=".pdf",
            created_by=self.user,
        )
        self.document2 = Document.objects.create(
            name="doc2",
            file_type=".docx",
            created_by=self.user,
        )
        self.adr.documents.add(self.document1, self.document2)
        self.adr.save()
        self.query_service = DocumentQuery()

    def test_query_with_model_param(self):
        params = {"instance": self.adr}
        response = self.query_service.get_documents(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 2)
        doc_names = [doc["name"] for doc in response.data["results"]]
        self.assertIn("doc1", doc_names)
        self.assertIn("doc2", doc_names)

    def test_query_with_q_param(self):
        params = {"instance": self.adr, "q": "doc1"}
        response = self.query_service.get_documents(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "doc1")

    def test_query_with_created_by_param(self):
        params = {"instance": self.adr, "created_by": self.user.id}
        response = self.query_service.get_documents(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 2)

    def test_query_with_file_type_param(self):
        params = {"instance": self.adr, "file_type": ".pdf"}
        response = self.query_service.get_documents(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["file_type"], ".pdf")

    def test_query_with_start_date_param(self):
        # Set created_at for document1 to a known date
        from datetime import datetime, timedelta

        doc = self.document1
        doc.created_at = datetime.now() - timedelta(days=2)
        doc.save()
        params = {
            "instance": self.adr,
            "start_date": (datetime.now() - timedelta(days=3)).date().isoformat(),
        }
        response = self.query_service.get_documents(params)
        self.assertTrue(response.success)
        self.assertGreaterEqual(len(response.data["results"]), 1)

    def test_query_with_end_date_param(self):
        from datetime import datetime, timedelta

        doc = self.document2
        doc.created_at = datetime.now() - timedelta(days=1)
        doc.save()
        params = {
            "instance": self.adr,
            "end_date": (datetime.now() - timedelta(hours=12)).date().isoformat(),
        }
        response = self.query_service.get_documents(params)
        self.assertTrue(response.success)
        # doc2 should be included if its created_at is before end_date

    def test_query_with_sort_by_param(self):
        params = {"instance": self.adr, "sort_by": "name", "sort_order": "desc"}
        response = self.query_service.get_documents(params)
        self.assertTrue(response.success)
        names = [doc["name"] for doc in response.data["results"]]
        self.assertEqual(names, sorted(names, reverse=True))

    def test_query_with_pagination(self):
        params = {"instance": self.adr, "page": 1, "page_size": 1}
        response = self.query_service.get_documents(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["page"], 1)
        self.assertEqual(response.data["page_size"], 1)
        self.assertTrue(
            response.data["has_next"]
        )  # There are 2 docs, so has_next should be True

    def test_query_with_invalid_file_type(self):
        params = {"instance": self.adr, "file_type": ".exe"}
        response = self.query_service.get_documents(params)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_query_with_invalid_sort_by(self):
        params = {"instance": self.adr, "sort_by": "notafield"}
        response = self.query_service.get_documents(params)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_query_with_invalid_page(self):
        params = {"instance": self.adr, "page": "notanint"}
        response = self.query_service.get_documents(params)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_query_without_model_param(self):
        # Should return all documents in the system
        response = self.query_service.get_documents({})
        self.assertTrue(response.success)
        # At least the two created in setUp should be present
        doc_names = [doc["name"] for doc in response.data["results"]]
        self.assertIn("doc1", doc_names)
        self.assertIn("doc2", doc_names)
