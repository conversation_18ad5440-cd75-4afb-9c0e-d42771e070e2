import io
from django.test import TestCase
from django.contrib.auth.models import User
from accounts.models import Profile
from documents.models import Document
from general_patient_visitor.models import GeneralPatientVisitor
from documents.services.operations import DocumentsOperations
from unittest.mock import patch
from django.core.files.uploadedfile import SimpleUploadedFile
from reportlab.pdfgen import canvas
from docx import Document as DocxDocument


def generate_pdf_file(filename="test.pdf"):
    buffer = io.BytesIO()
    p = canvas.Canvas(buffer)
    p.drawString(100, 750, "This is a test PDF file.")
    p.save()
    buffer.seek(0)
    return SimpleUploadedFile(filename, buffer.read(), content_type="application/pdf")


def generate_docx_file(filename="test.docx"):
    buffer = io.BytesIO()
    doc = DocxDocument()
    doc.add_paragraph("This is a test DOCX file.")
    doc.save(buffer)
    buffer.seek(0)
    return SimpleUploadedFile(
        filename,
        buffer.read(),
        content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    )


class TestGetDocument(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="testuser")
        self.profile = Profile.objects.create(user=self.user)
        self.document = Document.objects.create(
            name="testdoc",
            file_type=".pdf",
            created_by=self.user,
        )
        self.service = DocumentsOperations()

    def test_get_document_success(self):
        response = self.service.get_document(self.document.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data["id"], self.document.id)

    def test_get_document_not_found(self):
        response = self.service.get_document(99999)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)


class TestCreateDocument(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="testuser2")
        self.profile = Profile.objects.create(user=self.user)
        self.incident = GeneralPatientVisitor.objects.create()
        self.service = DocumentsOperations()

    def test_create_document_success(self):
        pdf_file = generate_pdf_file()
        docx_file = generate_docx_file()
        files = [pdf_file, docx_file]
        response = self.service.create_document(
            files, incident=self.incident, user=self.user
        )
        print(response.data)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["files"]), 2)
        self.assertEqual(self.incident.documents.count(), 2)

    def test_create_document_no_files(self):
        response = self.service.create_document(
            [], incident=self.incident, user=self.user
        )
        self.assertEqual(response, ("No files provided", []))

    def test_create_document_exception(self):
        with patch(
            "documents.services.operations.Document.objects.create",
            side_effect=Exception("fail"),
        ):
            pdf_file = generate_pdf_file()
            response = self.service.create_document(
                [pdf_file], incident=self.incident, user=self.user
            )
            self.assertFalse(response.success)
            self.assertEqual(response.code, 500)


class TestUploadDocument(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="testuser_upload")
        self.profile = Profile.objects.create(user=self.user)
        self.service = DocumentsOperations()

    def test_upload_document_success(self):
        pdf_file = generate_pdf_file()
        docx_file = generate_docx_file()
        files = [pdf_file, docx_file]

        response = self.service.upload_document(files=files, user=self.user)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertEqual(len(response.data["files"]), 2)

        self.assertEqual(Document.objects.count(), 2)

    def test_upload_document_no_files(self):
        response = self.service.upload_document([], user=self.user)
        self.assertEqual(response, ("No files provided", []))
        self.assertEqual(Document.objects.count(), 0)

    def test_upload_document_exception(self):
        with patch(
            "documents.services.operations.Document.objects.create",
            side_effect=Exception("fail"),
        ):
            pdf_file = generate_pdf_file()
            response = self.service.upload_document([pdf_file], user=self.user)
            self.assertFalse(response.success)
            self.assertEqual(response.code, 500)
            self.assertEqual(Document.objects.count(), 0)


class TestDeleteDocument(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="testuser3")
        self.profile = Profile.objects.create(user=self.user)
        self.document = Document.objects.create(
            name="todelete",
            file_type=".pdf",
            created_by=self.user,
        )
        self.service = DocumentsOperations()

    @patch("documents.services.operations.cloudinary.uploader.destroy")
    def test_delete_document_success(self, mock_destroy):
        mock_destroy.return_value = {"result": "ok"}
        response = self.service.delete_document(self.document.id)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertFalse(Document.objects.filter(id=self.document.id).exists())

    @patch("documents.services.operations.cloudinary.uploader.destroy")
    def test_delete_document_cloudinary_fail(self, mock_destroy):
        mock_destroy.return_value = {"result": "error"}
        response = self.service.delete_document(self.document.id)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 500)
        self.assertTrue(Document.objects.filter(id=self.document.id).exists())

    def test_delete_document_not_found(self):
        response = self.service.delete_document(99999)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    @patch(
        "documents.services.operations.cloudinary.uploader.destroy",
        side_effect=Exception("fail"),
    )
    def test_delete_document_exception(self, mock_destroy):
        response = self.service.delete_document(self.document.id)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 500)
