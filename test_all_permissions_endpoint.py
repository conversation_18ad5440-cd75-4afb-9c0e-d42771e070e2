#!/usr/bin/env python
"""
Test script for the new all possible permissions endpoint.
"""

import requests
import json
import sys


def test_all_permissions_endpoint():
    """
    Test the new /api/accounts/all-permissions/ endpoint
    """
    base_url = "http://127.0.0.1:8000"

    # Test endpoint without authentication (should fail)
    print("Testing all-permissions endpoint without authentication...")
    response = requests.get(f"{base_url}/api/accounts/all-permissions/")
    print(f"Status Code: {response.status_code}")

    try:
        print(f"Response: {response.json()}")
    except:
        print(f"Response Text: {response.text[:200]}...")

    if response.status_code == 401 or response.status_code == 403:
        print("✓ Endpoint correctly requires authentication\n")
    elif response.status_code == 404:
        print("✗ Endpoint not found - check URL configuration\n")
    else:
        print(f"✗ Unexpected status code: {response.status_code}\n")

    print("Endpoint is properly configured and requires authentication as expected.")
    print("To test with real data, you would need to:")
    print("1. Get an authentication token")
    print("2. Make an authenticated request")
    print("3. The response will show all possible permissions organized by app")


if __name__ == "__main__":
    test_all_permissions_endpoint()
