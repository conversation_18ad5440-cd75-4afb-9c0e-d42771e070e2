name: Django CI

on:
      push:
            branches: [alpha]
      pull_request:
            branches: [alpha]

jobs:
      test:
            runs-on: ubuntu-latest
            strategy:
                  matrix:
                        python-version: ["3.11"]

            steps:
                  - uses: actions/checkout@v4
                  - name: Set up Python ${{ matrix.python-version }}
                    uses: actions/setup-python@v5
                    with:
                          python-version: ${{ matrix.python-version }}
                  - name: Install Dependencies
                    run: |
                          python -m pip install --upgrade pip
                          pip install -r requirements.txt
                  - name: Run Tests
                    env:
                          DJANGO_SETTINGS_MODULE: core.settings
                          ENVIRONMENT: dev
                          SECRET_KEY: ${{ secrets.SECRET_KEY }}
                          DEV_DOMAIN_NAME: localhost
                          TEST_ENCRYPTION_KEY: ${{ secrets.TEST_ENCRYPTION_KEY }}
                          MAIN_EMAIL: ${{ secrets.MAIN_EMAIL }}
                          EMAIL_SERVER_TOKEN: ${{ secrets.EMAIL_SERVER_TOKEN }}
                          CLOUDINARY_NAME: ${{ secrets.CLOUDINARY_NAME }}
                          CLOUDINARY_API_KEY: ${{ secrets.CLOUDINARY_API_KEY }}
                          CLOUDINARY_SECRET_KEY: ${{ secrets.CLOUDINARY_SECRET_KEY }}
                          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
                          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                          AWS_STORAGE_BUCKET_NAME: ${{ secrets.AWS_STORAGE_BUCKET_NAME }}
                          CHMC_DOMAIN: ${{ secrets.CHMC_DOMAIN }}
                          CHMC_API_KEY: ${{ secrets.CHMC_API_KEY }}
                    run: |
                          python manage.py test accounts lost_and_found medication_error general_patient_visitor tasks adverse_drug_reaction patient_visitor_grievance workplace_violence_reports staff_incident_reports facilities
