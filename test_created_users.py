#!/usr/bin/env python
"""
Quick test script to verify created test users work correctly
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
django.setup()

from django.contrib.auth.models import User
from django.test import TransactionTestCase
from rest_framework.test import APIClient
from rest_framework import status


class TestCreatedUsers(TransactionTestCase):
    def setUp(self):
        self.client = APIClient()

    def test_user_login(self):
        """Test that created test users can login"""

        # Test each role type
        test_users = [
            ("user1", "User"),
            ("manager1", "Manager"),
            ("director1", "Director"),
            ("admin1", "Admin"),
            ("quality_manager1", "Quality/Risk Manager"),
            ("superuser1", "Super User"),
            ("user_editor1", "User Editor"),
        ]

        for username, expected_role in test_users:
            with self.subTest(username=username):
                # Check user exists
                try:
                    user = User.objects.get(username=username)
                except User.DoesNotExist:
                    self.fail(f"User {username} does not exist")

                # Check user has correct group
                user_groups = [g.name for g in user.groups.all()]
                self.assertIn(
                    expected_role,
                    user_groups,
                    f"User {username} missing group {expected_role}",
                )

                # Test login
                credentials = {"username": username, "password": "password@1234"}

                response = self.client.post("/api/accounts/login/", credentials)
                self.assertEqual(
                    response.status_code,
                    status.HTTP_200_OK,
                    f"Login failed for {username}: {getattr(response, 'data', 'No data')}",
                )

                # Check response has token
                self.assertIn(
                    "access", response.data, f"No access token for {username}"
                )

                print(f"✅ {username} ({expected_role}): Login successful")

    def test_user_profiles(self):
        """Test that users have proper profiles"""

        test_users = User.objects.filter(email__contains="@email.com")

        for user in test_users:
            with self.subTest(username=user.username):
                # Check profile exists
                self.assertTrue(
                    hasattr(user, "profile"), f"User {user.username} has no profile"
                )

                profile = user.profile

                # Check required fields
                self.assertIsNotNone(
                    profile.facility, f"User {user.username} has no facility"
                )
                self.assertIsNotNone(
                    profile.department, f"User {user.username} has no department"
                )
                self.assertTrue(
                    profile.is_test_account,
                    f"User {user.username} not marked as test account",
                )

                print(
                    f"✅ {user.username}: Profile complete "
                    f"({profile.facility}, {profile.department})"
                )


if __name__ == "__main__":
    import unittest

    unittest.main()
