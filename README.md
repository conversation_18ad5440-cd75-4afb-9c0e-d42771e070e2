# **q-control-backend**

# **Authentication Module**

## **Login with Email and Password**

The authentication module enables users to log into the application using their email and password. It leverages the Django authentication system and uses **JWT** tokens for session management.

### **Login API**

**Endpoint:** `/api/accounts/token/`  
**Method:** `POST`

The login endpoint accepts the user's email and password, authenticates the credentials, and generates both access and refresh tokens. The response also contains the user's basic profile details.

#### **Request Parameters**

- **username**: (string, required) – The email address or username of the user.
- **password**: (string, required) – The user's password.

Example request:

```json
{
	"username": "<EMAIL>",
	"password": "password123"
}
```

## **Notifications Module**

The **Notifications Module** is responsible for handling user notifications within the application, such as sending alerts or updates to the user's browser. The module tracks various events and records notifications with specific attributes. Users can interact with notifications, marking them as read or clearing them once they are no longer needed.

### **Notification Structure**

Each notification includes the following attributes:

- **Type**: Categorizes the notification (e.g., informational, warning, error).
- **Category**: Further classifies notifications into groups, if applicable.
- **Message**: The content of the notification.
- **is_read**: A boolean value indicating whether the notification has been read by the user.

### **Key Features**

#### **1. Marking Notifications as Read**

When a user views their notifications, they can mark specific notifications as read. This is achieved by sending a request to the API with a list of notification IDs to be marked as read.

**Process:**

- The API identifies the user making the request.
- It retrieves the list of notification IDs from the request payload.
- The system compares the provided IDs with notifications in the database.
- Notifications that exist in both the request and the database are updated, setting their `is_read` status to `true`.

#### **2. Clearing Notifications**

Users can also clear notifications that they have marked as read. Clearing a notification removes it from the user’s visible notification list.

**Process:**

- The API identifies the user making the request.
- It checks the list of notification IDs provided in the request.
- The system verifies whether the notifications in the database have been marked as read.
- If a notification has been marked as read, it is cleared from the user's notification list.

---

### **Future Enhancements**

- **Real-time Notifications**: Implement WebSocket functionality for real-time delivery of notifications without needing to refresh the browser.
- **Unread Notifications Count**: Display a badge with the number of unread notifications on the frontend.
- **Notification Preferences**: Allow users to configure preferences for the types of notifications they want to receive.

---

### **How to Contribute**

To contribute to this module:

1. Fork the repository.
2. Make your changes.
3. Create a pull request with a detailed description of the modifications.

---
### **person_injured field on workplace violence**
**Endpoint:** `/api/incidents/workplace_violence/incident_id/update/`  
**Method:** `PATCH`
Example request:

```json
"persons_injured" : [
    {
        "user_data": {
            "first_name": "John",
            "last_name": "Doe"
        },
        "injury_description": "An injured patient in the hospital."

    },
    {
        "user_data": {
            "first_name": "Jane",
            "last_name": "Smith"
        },
        "injury_description": "An injured patient in the hospital."
    },
    {
        "user_data": {
            "first_name": "Michael",
            "last_name": "Johnson"
        },
        "injury_description": "An injured patient in the hospital."
    }
]
```