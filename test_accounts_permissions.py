#!/usr/bin/env python
"""
Quick test script to verify the accounts permissions service is working correctly.
"""
import os
import sys
import django

# Setup Django
sys.path.append("/Users/<USER>/Desktop/projects/quality-control/q-control-backend")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
django.setup()

from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from accounts.models import Profile
from accounts.services.permisions import AccountsPermissionsService


def test_permission_checking():
    """Test the AccountsPermissionsService permission checking"""

    print("🧪 Testing Accounts Permissions Service")
    print("=" * 50)

    # Create a test user
    test_user, created = User.objects.get_or_create(
        username="test_permission_user",
        defaults={
            "email": "<EMAIL>",
            "first_name": "Test",
            "last_name": "User",
            "is_active": True,
        },
    )

    if created:
        print(f"✅ Created test user: {test_user.username}")
    else:
        print(f"📝 Using existing test user: {test_user.username}")

    # Test 1: User without permissions
    print("\n🔒 Test 1: User without any permissions")
    service = AccountsPermissionsService(test_user)
    result = service.can_create_account()
    print(f"   Result: {result.success}")
    print(f"   Message: {result.message}")
    assert (
        not result.success
    ), "User without permissions should not be able to create accounts"
    print("   ✅ PASS: User correctly denied without permissions")

    # Test 2: Inactive user with permissions
    print("\n🔒 Test 2: Inactive user with permissions")
    test_user.is_active = False
    test_user.save()

    # Add permission
    content_type = ContentType.objects.get_for_model(Profile)
    permission, _ = Permission.objects.get_or_create(
        codename="add_profile",
        name="Can add profile",
        content_type=content_type,
    )
    test_user.user_permissions.add(permission)

    service = AccountsPermissionsService(test_user)
    result = service.can_create_account()
    print(f"   Result: {result.success}")
    print(f"   Message: {result.message}")
    assert (
        not result.success
    ), "Inactive user should not be able to create accounts even with permissions"
    print("   ✅ PASS: Inactive user correctly denied")

    # Test 3: Active user with direct permission
    print("\n🔓 Test 3: Active user with direct permission")
    test_user.is_active = True
    test_user.save()

    service = AccountsPermissionsService(test_user)
    result = service.can_create_account()
    print(f"   Result: {result.success}")
    print(f"   Message: {result.message}")
    assert (
        result.success
    ), "Active user with permission should be able to create accounts"
    print("   ✅ PASS: Active user with permission correctly allowed")

    # Test 4: Active user in authorized group
    print("\n🔓 Test 4: Active user in authorized group")

    # Remove direct permission
    test_user.user_permissions.clear()

    # Create and add user to authorized group
    group, _ = Group.objects.get_or_create(name="User editors")
    test_user.groups.add(group)

    service = AccountsPermissionsService(test_user)
    result = service.can_create_account()
    print(f"   Result: {result.success}")
    print(f"   Message: {result.message}")
    assert (
        result.success
    ), "Active user in authorized group should be able to create accounts"
    print("   ✅ PASS: Active user in authorized group correctly allowed")

    # Test 5: User in unauthorized group
    print("\n🔒 Test 5: User in unauthorized group")

    # Remove from authorized group and add to unauthorized group
    test_user.groups.clear()
    unauthorized_group, _ = Group.objects.get_or_create(name="Read Only Users")
    test_user.groups.add(unauthorized_group)

    service = AccountsPermissionsService(test_user)
    result = service.can_create_account()
    print(f"   Result: {result.success}")
    print(f"   Message: {result.message}")
    assert (
        not result.success
    ), "User in unauthorized group should not be able to create accounts"
    print("   ✅ PASS: User in unauthorized group correctly denied")

    # Cleanup
    print("\n🧹 Cleaning up test data...")
    test_user.delete()
    Group.objects.filter(name__in=["User editors", "Read Only Users"]).delete()
    print("   ✅ Cleanup complete")

    print("\n🎉 All tests passed! Permission checking is working correctly.")
    print("\n📋 Summary of fixes made:")
    print(
        "   1. Fixed _user_has_per() to use user.has_perm() instead of checking user.permissions"
    )
    print("   2. Fixed _user_in_group() to use proper QuerySet filtering")
    print(
        "   3. Changed logic to require user to be active AND have permissions/group membership"
    )
    print(
        "   4. Updated permission format to include app label: 'accounts.add_profile'"
    )
    print("   5. Improved error messages and documentation")


if __name__ == "__main__":
    test_permission_checking()
