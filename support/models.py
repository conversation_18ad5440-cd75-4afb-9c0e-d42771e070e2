from django.db import models
from base.models import BaseModel
from accounts.models import Profile

# Create your models here.
class SupportTicket(BaseModel):
    # tickets status choices
    STATUS_CHOICES = (
        ('Open', 'Open'),
        ('Closed', 'Closed'),
        ('Pending', 'Pending'),
    )
    title = models.CharField(max_length=255)
    status = models.CharField(max_length=255, choices= STATUS_CHOICES, default ='Pending')
    description = models.TextField(null=True)
    priority = models.CharField(max_length=255)
    user_account = models.ForeignKey(Profile, on_delete=models.CASCADE)
    is_resolved = models.BooleanField(default=False)
    
    def __str__(self):
        return f"{self.title}"