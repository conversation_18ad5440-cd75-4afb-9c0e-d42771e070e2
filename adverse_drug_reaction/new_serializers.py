from rest_framework import serializers
from adverse_drug_reaction.models import AdverseDrugReaction
from base.serializers import BaseModelSerializer


class GetAdverseDrugReactionSerializer(BaseModelSerializer):
    report_facility = serializers.SerializerMethodField()
    department = serializers.SerializerMethodField()
    patient_name = serializers.SerializerMethodField()
    observers_name = serializers.SerializerMethodField()
    name_of_physician_notified = serializers.SerializerMethodField()
    name_of_family_notified = serializers.SerializerMethodField()
    review_process = serializers.SerializerMethodField()
    notified_by = serializers.SerializerMethodField()

    class Meta:
        model = AdverseDrugReaction
        fields = "__all__"

    def get_report_facility(self, obj):
        if obj.report_facility:
            return {"id": obj.report_facility.id, "name": obj.report_facility.name}
        return None

    def get_department(self, obj):
        if obj.department:
            return {"id": obj.department.id, "name": obj.department.name}
        return None

    def get_patient_name(self, obj):
        if obj.patient_name:
            return {
                "id": obj.patient_name.id,
                "first_name": obj.patient_name.first_name,
                "last_name": obj.patient_name.last_name,
                "email": obj.patient_name.email,
            }
        return None

    def get_observers_name(self, obj):
        if obj.observers_name:
            return {
                "id": obj.observers_name.id,
                "first_name": obj.observers_name.first_name,
                "last_name": obj.observers_name.last_name,
                "email": obj.observers_name.email,
            }
        return None

    def get_name_of_physician_notified(self, obj):
        if obj.name_of_physician_notified:
            return {
                "id": obj.name_of_physician_notified.id,
                "first_name": obj.name_of_physician_notified.first_name,
                "last_name": obj.name_of_physician_notified.last_name,
                "email": obj.name_of_physician_notified.email,
            }
        return None

    def get_name_of_family_notified(self, obj):
        if obj.name_of_family_notified:
            return {
                "id": obj.name_of_family_notified.id,
                "first_name": obj.name_of_family_notified.first_name,
                "last_name": obj.name_of_family_notified.last_name,
                "email": obj.name_of_family_notified.email,
            }
        return None

    def get_review_process(self, obj):
        if obj.review_process:
            return {
                "id": obj.review_process.id,
                "name": obj.review_process.name,
                "status": obj.review_process.status,
            }
        return None

    def get_notified_by(self, obj):
        if obj.notified_by:
            return {
                "id": obj.notified_by.id,
                "first_name": obj.notified_by.first_name,
                "last_name": obj.notified_by.last_name,
                "email": obj.notified_by.email,
            }
        return None