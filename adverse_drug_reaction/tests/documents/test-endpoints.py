import io
from django.test import TestCase
from django.contrib.auth.models import User
from accounts.models import Profile
from adverse_drug_reaction.services.documents import ADRDocumentService
from base.tests.base_setup import BaseTestSetup
from documents.models import Document
from adverse_drug_reaction.models import AdverseDrugReaction
from documents.services.query import DocumentQuery
from django.core.files.uploadedfile import SimpleUploadedFile
from reportlab.pdfgen import canvas
from docx import Document as DocxDocument
from unittest.mock import patch


def generate_pdf_file(filename="test.pdf"):
    buffer = io.BytesIO()
    p = canvas.Canvas(buffer)
    p.drawString(100, 750, "This is a test PDF file.")
    p.save()
    buffer.seek(0)
    return SimpleUploadedFile(filename, buffer.read(), content_type="application/pdf")


def generate_docx_file(filename="test.docx"):
    buffer = io.BytesIO()
    doc = DocxDocument()
    doc.add_paragraph("This is a test DOCX file.")
    doc.save(buffer)
    buffer.seek(0)
    return SimpleUploadedFile(
        filename,
        buffer.read(),
        content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    )


class TestDocumentQueryWithModelParam(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = User.objects.create(username="testuser")
        self.profile = Profile.objects.create(user=self.user)
        self.adr = AdverseDrugReaction.objects.create()
        self.document1 = Document.objects.create(
            name="doc1",
            file_type=".pdf",
            created_by=self.user,
        )
        self.document2 = Document.objects.create(
            name="doc2",
            file_type=".docx",
            created_by=self.user,
        )
        self.adr.documents.add(self.document1, self.document2)

        self.adr.save()
        self.query_service = ADRDocumentService(incident_id=self.adr.id, user=self.user)
        self._authenticate_user(self.user)

    def test_query_with_model_param(self):
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/",
            data={},
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 2)
        doc_names = [doc["name"] for doc in response.data["results"]]
        self.assertIn("doc1", doc_names)
        self.assertIn("doc2", doc_names)

    def test_query_with_q_param(self):
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?q=doc1",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "doc1")

    def test_query_with_created_by_param(self):
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?created_by={self.user.id}",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 2)

    def test_query_with_file_type_param(self):
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?file_type=.pdf",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["file_type"], ".pdf")

    def test_query_with_start_date_param(self):
        # Set created_at for document1 to a known date
        from datetime import datetime, timedelta

        doc = self.document1
        doc.created_at = datetime.now() - timedelta(days=2)
        doc.save()
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?start_date={(datetime.now() - timedelta(days=3)).date().isoformat()}",
        )
        self.assertEqual(response.status_code, 200)
        self.assertGreaterEqual(len(response.data["results"]), 1)

    def test_query_with_end_date_param(self):
        from datetime import datetime, timedelta

        doc = self.document2
        doc.created_at = datetime.now() - timedelta(days=1)
        doc.save()
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?end_date={(datetime.now() - timedelta(hours=12)).date().isoformat()}",
        )
        self.assertEqual(response.status_code, 200)
        # doc2 should be included if its created_at is before end_date

    def test_query_with_sort_by_param(self):
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?sort_by=name&sort_order=desc",
        )
        self.assertEqual(response.status_code, 200)
        names = [doc["name"] for doc in response.data["results"]]
        self.assertEqual(names, sorted(names, reverse=True))

    def test_query_with_pagination(self):
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?page=1&page_size=1",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["page"], 1)
        self.assertEqual(response.data["page_size"], 1)
        self.assertTrue(
            response.data["has_next"]
        )  # There are 2 docs, so has_next should be True

    def test_query_with_invalid_file_type(self):
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?file_type=.exe",
        )
        self.assertEqual(response.status_code, 400)

    def test_query_with_invalid_sort_by(self):
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?sort_by=notafield",
        )
        self.assertFalse(response.status_code == 200)
        self.assertEqual(response.status_code, 400)

    def test_query_with_invalid_page(self):
        response = self.client.get(
            f"{self.adr_endpoint}/{self.adr.id}/documents/?page=notanint",
        )
        self.assertEqual(response.status_code, 400)


class TestCreateDocument(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = User.objects.create(username="testuser2")
        self.profile = Profile.objects.create(user=self.user)
        self.incident = AdverseDrugReaction.objects.create()
        self.service = ADRDocumentService(self.incident.id, user=self.user)
        self._authenticate_user(self.user)

    def test_create_document_success(self):
        pdf_file = generate_pdf_file()
        docx_file = generate_docx_file()
        files = [pdf_file, docx_file]

        response = self.client.post(
            f"{self.adr_endpoint}/{self.incident.id}/documents/",
            data={"files": files},
            format="multipart",
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(len(response.data["files"]), 2)
        self.assertEqual(self.incident.documents.count(), 2)

    def test_create_document_no_files(self):
        response = self.client.post(
            f"{self.adr_endpoint}/{self.incident.id}/documents/",
            data={},
            format="multipart",
        )
        self.assertEqual(response.status_code, 400)

    def test_create_document_exception(self):
        with patch(
            "documents.services.operations.Document.objects.create",
            side_effect=Exception("fail"),
        ):
            pdf_file = generate_pdf_file()
            response = self.client.post(
                f"{self.adr_endpoint}/{self.incident.id}/documents/",
                data={"files": [pdf_file]},
                format="multipart",
            )
            self.assertEqual(response.status_code, 500)
