import factory
from datetime import date, time
from factory.django import DjangoModelFactory
from factory.faker import Faker
from django.utils import timezone

from accounts.tests.factory import UserProfileFactory
from base.tests.factory import (
    DepartmentFactory,
    DocumentFactory,
    FacilityFactory,
    ReviewFactory,
    UserFactory,
)


class AdverseDrugReactionClassificationFactory(DjangoModelFactory):
    class Meta:
        model = "adverse_drug_reaction.AdverseDrugReactionClassification"

    description = Faker("sentence")


class AdverseDrugReactionOutcomeFactory(DjangoModelFactory):
    class Meta:
        model = "adverse_drug_reaction.AdverseDrugReactionOutcome"

    outcome_type = Faker("word")
    description = Faker("text", max_nb_chars=255)
    anaphylaxis_outcome = factory.Iterator(["Mild", "Moderate", "Severe"])
    adverse_event_to_be_reported_to_FDA = factory.Faker("boolean")
    name_of_physician_notified = factory.SubFactory(UserProfileFactory)
    date_physician_was_notified = factory.LazyFunction(lambda: timezone.now().date())
    time_physician_was_notified = factory.LazyFunction(lambda: timezone.now().time())
    name_of_family_notified = Faker("name")
    date_family_was_notified = factory.LazyFunction(lambda: timezone.now().date())
    time_family_was_notified = factory.LazyFunction(lambda: timezone.now().time())
    notified_by = factory.SubFactory(UserProfileFactory)


class AdverseDrugReactionFactory(DjangoModelFactory):
    class Meta:
        model = "adverse_drug_reaction.AdverseDrugReaction"

    status = factory.Iterator(
        [
            "Draft",
            "Open",
            "Pending Assigned",
            "Pending Review",
            "Pending Approval",
            "Resolved",
        ]
    )
    created_by = factory.SubFactory(UserFactory)
    report_facility = factory.SubFactory(FacilityFactory)
    department = factory.SubFactory(DepartmentFactory)
    current_step = factory.Sequence(lambda n: n + 1)
    is_resolved = factory.Faker("boolean")
    patient_type = factory.Iterator(["Outpatient", "Inpatient"])
    patient_name = factory.SubFactory(UserProfileFactory)
    incident_date = factory.LazyFunction(lambda: timezone.now().date())
    incident_time = factory.LazyFunction(lambda: timezone.now().time())
    provider = Faker("name")
    observers_name = factory.SubFactory(UserProfileFactory)
    date_of_report = factory.LazyFunction(lambda: timezone.now().date())
    time_of_report = factory.LazyFunction(lambda: timezone.now().time())
    event_detail = Faker("sentence")
    suspected_medication = Faker("word")
    dose = Faker("word")
    route = factory.Iterator(["Oral", "Intravenous", "Intramuscular", "Subcutaneous"])
    frequency = Faker("word")
    rate_of_administration = Faker("word")
    date_of_medication_order = factory.LazyFunction(lambda: timezone.now().date())
    other_route_description = Faker("text", max_nb_chars=255)
    date_of_information = factory.LazyFunction(lambda: timezone.now().date())
    nurse_note = factory.Faker("boolean")
    progress_note = factory.Faker("boolean")
    other_information_can_be_found_in = factory.Faker("boolean")
    other_information_description = Faker("text", max_nb_chars=255)
    information_reaction = Faker("sentence")
    date_of_adverse_reaction = factory.LazyFunction(lambda: timezone.now().date())
    reaction_on_settime = factory.LazyFunction(lambda: timezone.now().time())
    reaction_was_treated = factory.Faker("boolean")
    treatment_description = Faker("text", max_nb_chars=255)
    incident_type_classification = Faker("text", max_nb_chars=1000)
    outcome_type = Faker("word")
    description = Faker("text", max_nb_chars=255)
    anaphylaxis_outcome = factory.Iterator(["Mild", "Moderate", "Severe"])
    adverse_event_to_be_reported_to_FDA = factory.Faker("boolean")
    name_of_physician_notified = factory.SubFactory(UserProfileFactory)
    date_physician_was_notified = factory.LazyFunction(lambda: timezone.now().date())
    time_physician_was_notified = factory.LazyFunction(lambda: timezone.now().time())
    name_of_family_notified = factory.SubFactory(UserProfileFactory)
    date_family_was_notified = factory.LazyFunction(lambda: timezone.now().date())
    time_family_was_notified = factory.LazyFunction(lambda: timezone.now().time())
    notified_by = factory.SubFactory(UserProfileFactory)
    brief_summary_incident = Faker("text")
    immediate_actions_taken = Faker("text")
    severity_rating = factory.Faker(
        "pydecimal", left_digits=2, right_digits=2, positive=True, max_value=5
    )
    is_modified = False

    @factory.post_generation
    def reviews(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for review in extracted:
                self.reviews.add(review)
        else:
            self.reviews.add(ReviewFactory())

    @factory.post_generation
    def documents(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for doc in extracted:
                self.documents.add(doc)
        else:
            self.documents.add(DocumentFactory())


class AdverseDrugReactionVisitorVersionFactory(AdverseDrugReactionFactory):
    class Meta:
        model = "adverse_drug_reaction.AdverseDrugReactionVisitorVersion"

    original_report = factory.SubFactory(AdverseDrugReactionFactory)
