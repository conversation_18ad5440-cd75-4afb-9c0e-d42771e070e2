from django.test import TransactionTestCase

from adverse_drug_reaction.tests.factory import AdverseDrugReactionFactory
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserFactory
from reviews.tests.factory import ReviewFactory


class TestGetIncidentReviews(BaseTestSetup):
    """Test getting incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    # cases for adverse drug reaction incident reviews
    def test_get_adverse_drug_reaction_reviews(self):
        """Test getting reviews for an adverse drug reaction incident"""
        incident = AdverseDrugReactionFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(3, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        self._authenticate_user(self.user)
        response = self.client.get(
            f"/api/incidents/adverse-drug-reaction/{incident.id}/reviews/",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to get reviews: {response.data}")
        # check number of reviews returned
        self.assertEqual(len(response.data), 3)


class TestCreateIncidentReview(BaseTestSetup):
    """Test creating incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    # cases for adverse drug reaction incident reviews
    def test_create_adverse_drug_reaction_review(self):
        """Test creating a review for an adverse drug reaction incident"""
        incident = AdverseDrugReactionFactory()
        # clear any existing reviews
        incident.reviews.clear()
        self._authenticate_user(self.user)
        response = self.client.post(
            f"/api/incidents/adverse-drug-reaction/{incident.id}/reviews/",
            {"content": "This is a test review"},
        )

        if not response.status_code == 201:
            self.fail(f"Failed to create review: {response.data}")
        # check that the review was created
        self.assertEqual(incident.reviews.first().content, "This is a test review")
