from django.db import models
from base.models import BaseModel
from documents.models import Document
from base.models import Facility
from base.models import Department
from reviews.models import Review
from accounts.models import Profile


class AdverseDrugReactionClassification(BaseModel):
    description = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.description


class AdverseDrugReactionOutcome(BaseModel):

    outcome_type = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(max_length=255, null=True, blank=True)
    anaphylaxis_outcome = models.CharField(max_length=255, null=True, blank=True)
    adverse_event_to_be_reported_to_FDA = models.BooleanField(null=True, blank=True)
    # step 2
    name_of_physician_notified = models.ForeignKey(
        Profile, on_delete=models.SET_NULL, null=True, blank=True
    )
    date_physician_was_notified = models.DateField(null=True, blank=True)
    time_physician_was_notified = models.TimeField(null=True, blank=True)
    name_of_family_notified = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    date_family_was_notified = models.DateField(null=True, blank=True)
    time_family_was_notified = models.TimeField(null=True, blank=True)
    notified_by = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.outcome_type


class ObserversName(BaseModel):
    name = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.name


# we need to add search indexes to optimize performance during searching and querying
class AdverseDrugReactionBase(BaseModel):
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES,
        max_length=255,
        default="Draft",
    )
    report_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    current_step = models.IntegerField(default=1, null=True, blank=True)
    patient_type = models.CharField(max_length=255, null=True, blank=True)
    patient_name = models.ForeignKey(
        Profile, null=True, blank=True, on_delete=models.SET_NULL
    )
    incident_date = models.DateField(null=True, blank=True)
    incident_time = models.TimeField(null=True, blank=True)

    provider = models.CharField(max_length=255, null=True, blank=True)
    observers_name = models.ForeignKey(
        Profile,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="observers_name",
    )
    date_of_report = models.DateField(null=True, blank=True)
    time_of_report = models.TimeField(null=True, blank=True)
    event_detail = models.CharField(max_length=255, null=True, blank=True)
    suspected_medication = models.CharField(max_length=255, null=True, blank=True)
    dose = models.CharField(max_length=255, null=True, blank=True)
    route = models.CharField(max_length=255, null=True, blank=True)
    frequency = models.CharField(max_length=255, null=True, blank=True)
    rate_of_administration = models.CharField(max_length=255, null=True, blank=True)
    date_of_medication_order = models.DateField(null=True, blank=True)
    other_route_description = models.TextField(max_length=255, null=True, blank=True)
    date_of_information = models.DateField(null=True, blank=True)
    nurse_note = models.BooleanField(null=True, blank=True)
    progress_note = models.CharField(max_length=500, null=True, blank=True)
    other_information_can_be_found_in = models.BooleanField(null=True, blank=True)
    other_information_description = models.TextField(
        max_length=255, null=True, blank=True
    )
    information_reaction = models.CharField(max_length=255, null=True, blank=True)
    date_of_adverse_reaction = models.DateField(null=True, blank=True)
    reaction_on_settime = models.TimeField(null=True, blank=True)
    reaction_was_treated = models.BooleanField(null=True, blank=True)
    treatment_description = models.TextField(max_length=255, null=True, blank=True)
    # step 5
    incident_type_classification = models.TextField(
        max_length=1000, null=True, blank=True
    )

    # step 6
    outcome_type = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(max_length=255, null=True, blank=True)
    anaphylaxis_outcome = models.CharField(max_length=255, null=True, blank=True)
    adverse_event_to_be_reported_to_FDA = models.BooleanField(null=True, blank=True)
    name_of_physician_notified = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="physician_notified_name",
    )
    date_physician_was_notified = models.DateField(null=True, blank=True)
    time_physician_was_notified = models.TimeField(null=True, blank=True)
    name_of_family_notified = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="family_notified_name",
    )
    date_family_was_notified = models.DateField(null=True, blank=True)
    time_family_was_notified = models.TimeField(null=True, blank=True)
    notified_by = models.CharField(max_length=255, null=True, blank=True)
    # step 7
    brief_summary_incident = models.TextField(null=True, blank=True)
    immediate_actions_taken = models.TextField(null=True, blank=True)
    severity_rating = models.DecimalField(
        decimal_places=2, null=True, blank=True, max_digits=9
    )

    reviews = models.ManyToManyField(
        Review, related_name="adverse_drug_reviews_field", blank=True
    )

    documents = models.ManyToManyField(
        Document, related_name="adverse_drug_incident_documents_field", blank=True
    )

    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )
    is_resolved = models.BooleanField(default=False)

    def __str__(self):
        return f"adverse drug report: {self.patient_name} - {self.incident_date}"


class AdverseDrugReaction(AdverseDrugReactionBase):
    is_modified = models.BooleanField(default=False)


class AdverseDrugReactionVisitorVersion(AdverseDrugReactionBase):
    original_report = models.ForeignKey(
        AdverseDrugReaction,
        on_delete=models.CASCADE,
        related_name="versions",
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "adr_version"
