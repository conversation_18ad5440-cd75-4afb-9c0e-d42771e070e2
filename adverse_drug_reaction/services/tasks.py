# A class to manage task on Averse drug reaction


from adverse_drug_reaction.models import AdverseDrugReaction
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from tasks.services.review_process_tasks import IncidentTasks

logging_service = LoggingService()


class ADRTasksService:
    def __init__(self, incident_id, user, filters=None):
        self.service = IncidentTasks(
            instance=AdverseDrugReaction.objects.get(id=incident_id),
            logged_in_user=user,
            filters=filters,
        )

    def get_tasks(self) -> APIResponse:
        try:
            incidents = self.service.get_tasks()
            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                    code=400,
                )
            return APIResponse(
                success=True,
                message=incidents.message,
                data=incidents.data,
                code=200,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error while getting incident tasks",
                code=500,
            )
