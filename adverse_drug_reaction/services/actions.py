from accounts.models import Profile
from accounts.services.user_profile.service import UserProfileService
from adverse_drug_reaction.models import AdverseDrugReaction
from adverse_drug_reaction.serializers import (
    AdverseDrugReactionUpdateVersionSerializer,
    ListAdverseDrugReactionSerializer,
)
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import get_patient_profile
from base.constants import ReviewStatus
from base.services.incidents.base import IncidentService
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from rest_framework import status
from rest_framework.response import Response
from django.core.exceptions import ValidationError

from incidents.services.permissions import IncidentPermissionsService
from incidents.services.workflow import IncidentWorkflow
from incidents.views.send_to_department import send_incident_submission_email
from activities.services import ActivityLogService
from activities.models import ActivityType
from tasks.models import ReviewTemplates

logging_service = LoggingService()
user_profile_service = UserProfileService()


class ADRActionsService:
    """
    This class contains methods to handle actions related to adverse drug reactions (ADRs).
    """

    def __init__(self, user, incident_id, data):
        self.user = user
        self.data = data
        self.incident_id = incident_id
        self.workflow_services = IncidentWorkflow(
            model=AdverseDrugReaction, user=self.user
        )
        self.general_incident = IncidentService()
        try:
            self.incident = (
                AdverseDrugReaction.objects.select_related(
                    "review_process",
                    "name_of_physician_notified",
                    "name_of_family_notified",
                    "patient_name",
                    "observers_name",
                    "report_facility",
                    "department",
                    "created_by",
                )
                .prefetch_related(
                    "documents",
                    "reviews",
                    "review_tasks",
                )
                .get(id=incident_id)
            )
        except AdverseDrugReaction.DoesNotExist:
            self.incident = None
        self.permissions = IncidentPermissionsService(
            user=self.user,
            app_label=AdverseDrugReaction._meta.app_label,
            incident=self.incident,
        )

    def modify_incident(self) -> APIResponse:
        """
        Modifies an existing ADR incident with the provided self.data.
        """
        # Check if the user has permission to modify the incident
        report_facility = None
        try:
            # check permissions
            permission_response = self.permissions.can_modify_incident()
            if not permission_response.success:
                return APIResponse(
                    success=False,
                    message=permission_response.message,
                    code=status.HTTP_403_FORBIDDEN,
                    data=None,
                )

            report_facility = self.incident.report_facility

            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, self.incident.report_facility)
                and not is_manager_user(self.user, self.incident.department)
            ) and not self.incident.created_by == self.user:
                return APIResponse(
                    success=False,
                    message="You do not have enough rights to update this incident",
                    code=status.HTTP_403_FORBIDDEN,
                    data=None,
                )
            # updating related fields

            # patient name
            if "patient_name" in self.data:
                patient_profile = user_profile_service.get_or_create_profile(
                    self.data["patient_name"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["patient_name"] = patient_profile.data.id

            # observers name
            if "observers_name" in self.data:
                observers_profile = user_profile_service.get_or_create_profile(
                    self.data["observers_name"]
                )
                if not observers_profile.success:
                    return APIResponse(
                        success=False,
                        message=observers_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["observers_name"] = observers_profile.data.id

            # name of physician notified
            if "name_of_physician_notified" in self.data:
                physician_notified_profile = user_profile_service.get_or_create_profile(
                    self.data["name_of_physician_notified"]
                )
                if not physician_notified_profile.success:
                    return APIResponse(
                        success=False,
                        message=physician_notified_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["name_of_physician_notified"] = (
                    physician_notified_profile.data.id
                )
            # name of family notified
            if "name_of_family_notified" in self.data:
                family_notified_profile = user_profile_service.get_or_create_profile(
                    self.data["name_of_family_notified"]
                )
                if not family_notified_profile.success:
                    return APIResponse(
                        success=False,
                        message=family_notified_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["name_of_family_notified"] = family_notified_profile.data.id

            if "notified_by" in self.data:
                notified_by_profile = user_profile_service.get_or_create_profile(
                    self.data["notified_by"]
                )
                if not notified_by_profile.success:
                    return APIResponse(
                        success=False,
                        message=notified_by_profile.message,
                        data=None,
                        code=400,
                    )
                self.data["notified_by"] = notified_by_profile.data.id

            self.data["original_report"] = self.incident.id
            self.data["report_facility"] = report_facility
            self.data["created_by"] = self.user.id
            self.data["status"] = self.data.get("status", ReviewStatus.DRAFT)

            version_serializer = AdverseDrugReactionUpdateVersionSerializer(
                data=self.data
            )

            if version_serializer.is_valid():
                version_serializer.save()
                old_status = self.incident.status
                self.incident.is_modified = True
                self.incident.updated_by = self.user
                self.incident.status = self.data.get("status", ReviewStatus.DRAFT)
                self.incident.save()

                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=self.incident,
                    activity_type=ActivityType.MODIFIED,
                    description="Incident modified"
                )

                new_status = self.incident.status
                if old_status != new_status:
                    ActivityLogService.create_activity(
                        user=self.user,
                        content_object=self.incident,
                        activity_type=ActivityType.STATUS_CHANGED,
                        description=f"Status changed from {old_status} to {new_status}"
                    )

                if self.incident.status == ReviewStatus.OPEN:
                    send_incident_submission_email(
                        incident=self.incident,
                        incident_type="Anaphylaxis/Adverse Drug Reaction",
                    )
                return APIResponse(
                    success=True,
                    message="Incident modified successfully",
                    code=status.HTTP_200_OK,
                    data=version_serializer.data,
                )
            else:
                logging_service.log_error(version_serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data",
                    code=status.HTTP_400_BAD_REQUEST,
                    data=version_serializer.errors,
                )
        except AdverseDrugReaction.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                code=status.HTTP_404_NOT_FOUND,
                data=None,
            )

        except ValidationError as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Validation error",
                code=status.HTTP_400_BAD_REQUEST,
                data=None,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while modifying the incident",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def send_for_review(self) -> APIResponse:

        response = self.workflow_services.send_for_a_review(
            self.incident_id,
            self.data,
        )

        if not response.success:
            return APIResponse(
                success=False,
                message=response.message,
                code=response.code,
                data=None,
            )
        
        if self.data.get("assignees") not in [None, [], ""]:
            assignees = list(response.data.assignees.all())
            ActivityLogService.create_activity(
                user=self.user,
                content_object=response.data,
                activity_type=ActivityType.SENT_TO_REVIEWER,
                highlight_objects=None,
                destination_objects=assignees
            )
        if self.data.get("review_template") is not None:
            review_template = ReviewTemplates.objects.get(id=self.data["review_template"])
            assignees = list(review_template.assignees.all())
            ActivityLogService.create_activity(
                user=self.user,
                content_object=response.data,
                activity_type=ActivityType.SENT_TO_REVIEWER,
                highlight_objects=review_template,
                destination_objects=assignees
            )

        serializer = ListAdverseDrugReactionSerializer(response.data)
        return APIResponse(
            success=True,
            message="Incident sent for review successfully",
            code=status.HTTP_200_OK,
            data=serializer.data,
        )

    def mark_closed(self) -> APIResponse:

        try:
            response = self.workflow_services.mark_as_resolved(
                incident=self.incident,
                user=self.user,
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=400,
                    data=None,
                )
            response = self.workflow_services.mark_as_resolved(
                incident=self.incident,
                user=self.user,
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=400,
                    data=None,
                )

            ActivityLogService.create_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.RESOLVED,
                description="Incident marked as resolved"
            )

            return APIResponse(
                success=True,
                message="Incident marked as closed successfully",
                code=status.HTTP_200_OK,
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while marking the incident as closed",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def delete_adverse_drug_reaction_draft_incidents(self) -> APIResponse:
        try:
            response = self.workflow_services.delete_drafts(
                model=AdverseDrugReaction,
                user=self.user,
                incident_ids=self.data.get("incident_ids", None),
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None,
                )

            
            ActivityLogService.create_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.DELETED,
                description="Draft incidents deleted"
            )

            return APIResponse(
                success=True,
                message="Draft incidents deleted successfully",
                code=status.HTTP_200_OK,
                data=response.data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while deleting draft incidents",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )
