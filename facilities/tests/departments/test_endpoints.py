from adverse_drug_reaction.tests.factory import AdverseDrugReactionFactory
from base.models import Department
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import (
    DepartmentFactory,
    FacilityFactory,
    GeneralPatientVisitorFactory,
)
from facilities.services.departments import DepartmentService
from general_patient_visitor.models import GeneralPatientVisitor
from lost_and_found.tests.factory import LostAndFoundFactory
from medication_error.tests.factory import MedicationErrorFactory
from patient_visitor_grievance.tests.factory import GrievanceFactory
from staff_incident_reports.tests.factory import StaffIncidentReportFactory
from workplace_violence_reports.tests.factory import WorkPlaceViolenceFactory
from django.urls import reverse
from rest_framework import status


class TestGetIncidents(BaseTestSetup):

    def test_get_general_incidents(self):

        # create 3 new incidents for new department
        for _ in range(3):
            GeneralPatientVisitorFactory(
                department=self.manager_user_dept,
                report_facility=self.manager_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            GeneralPatientVisitorFactory()

        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/departments/{self.manager_user_dept.id}/incidents/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["general"]), 3)

    def test_get_adr_incidents(self):

        # create 3 new incidents for new department
        for _ in range(3):
            AdverseDrugReactionFactory(
                department=self.pharmacy_manager_user_dept,
                report_facility=self.manager_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            AdverseDrugReactionFactory()

        self.manager_user_profile.access_to_department.add(
            self.pharmacy_manager_user_dept
        )
        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/departments/{self.pharmacy_manager_user_dept.id}/incidents/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["adverse_drug_reaction"]), 3)

    def test_get_staff_incidents(self):
        # create 3 new incidents for new department
        for _ in range(3):
            StaffIncidentReportFactory(
                department=self.manager_user_dept,
                report_facility=self.manager_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            StaffIncidentReportFactory()

        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/departments/{self.manager_user_dept.id}/incidents/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["employee_incidents"]), 3)

    def test_get_grievance_incidents(self):

        # create 3 new incidents for new department
        for _ in range(3):
            GrievanceFactory(
                department=self.pharmacy_manager_user_dept,
                report_facility=self.manager_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            GrievanceFactory()

        self.manager_user_profile.access_to_department.add(
            self.pharmacy_manager_user_dept
        )
        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/departments/{self.pharmacy_manager_user_dept.id}/incidents/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["grievances"]), 3)

    def test_get_medication_error_incidents(self):

        # create 3 new incidents for new department
        for _ in range(3):
            MedicationErrorFactory(
                department=self.pharmacy_manager_user_dept,
                report_facility=self.manager_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            MedicationErrorFactory()

        self.manager_user_profile.access_to_department.add(
            self.pharmacy_manager_user_dept
        )
        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/departments/{self.pharmacy_manager_user_dept.id}/incidents/"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["medication_error"]), 3)

    def test_get_lost_and_found_incidents(self):
        # create 3 new incidents for new department
        for _ in range(3):
            LostAndFoundFactory(
                department=self.manager_user_dept,
                report_facility=self.manager_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            LostAndFoundFactory()

        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/departments/{self.manager_user_dept.id}/incidents/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["lost_and_founds"]), 3)

    def test_get_workplace_violence_incident_incidents(self):
        # create 3 new incidents for new department
        for _ in range(3):
            WorkPlaceViolenceFactory(
                department=self.manager_user_dept,
                report_facility=self.manager_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            WorkPlaceViolenceFactory()

        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/departments/{self.manager_user_dept.id}/incidents/"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["workplace_violence"]), 3)


class TestCreateDepartment(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.valid_data = {
            "name": "Quality Control Test 2",
            "description": "Software development team",
            "header_of_department": self.manager_user.email,
            "facility_id": self.manager_user_fac.id,
            "add_to_all": False,
        }

    def test_create_department(self):
        self._authenticate_user(self.manager_user)
        response = self.client.post(
            "/api/facilities/departments/", self.valid_data, format="json"
        )

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["name"], self.valid_data["name"])

    def test_add_to_all(self):
        facility_1 = FacilityFactory()
        facility_2 = FacilityFactory()
        self.valid_data["add_to_all"] = True
        self._authenticate_user(self.super_user)

        response = self.client.post(
            "/api/facilities/departments/", self.valid_data, format="json"
        )

        self.assertEqual(response.status_code, 201)

        facility_1.refresh_from_db()
        facility_2.refresh_from_db()

        self.assertIsNotNone(
            Department.objects.get(
                facility=facility_1,
                name=response.data["name"],
            )
        )
        self.assertIsNotNone(
            Department.objects.get(
                facility=facility_2,
                name=response.data["name"],
            )
        )
        self.assertIsNotNone(
            Department.objects.get(
                facility=self.manager_user_fac,
                name=response.data["name"],
            )
        )


class TestGetDepartments(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = DepartmentService()

        for _ in range(4):
            DepartmentFactory(
                facility=self.manager_user_fac,
            )

    def test_get_departments(self):
        params = {
            "facility_id": self.manager_user_fac.id,
        }
        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/facilities/departments/?facility_id={self.manager_user_fac.id}",
        )
        self.assertEqual(response.status_code, 200)
        self.assertGreater(len(response.data), 3)

    def test_departments_no_facility_id(self):

        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/facilities/departments/",
        )
        self.assertNotEqual(response.status_code, 200)

    def test_get_departments_search(self):
        # Add departments with unique names and descriptions
        Department.objects.create(
            name="Alpha Department",
            facility=self.manager_user_fac,
            description="Alpha Desc",
        )
        Department.objects.create(
            name="Beta Department",
            facility=self.manager_user_fac,
            description="Beta Desc",
        )
        Department.objects.create(
            name="Gamma Department",
            facility=self.manager_user_fac,
            description="Gamma Desc",
        )
        self._authenticate_user(self.manager_user)
        # Search by name
        response = self.client.get(
            f"/api/facilities/departments/?facility_id={self.manager_user_fac.id}&q=Alpha"
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            any("Alpha Department" in d["name"] for d in response.data["results"])
        )
        # Search by description
        response = self.client.get(
            f"/api/facilities/departments/?facility_id={self.manager_user_fac.id}&q=Beta Desc"
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            any("Beta Department" in d["name"] for d in response.data["results"])
        )

    def test_get_departments_sorting(self):
        Department.objects.create(name="A Dept", facility=self.manager_user_fac)
        Department.objects.create(name="B Dept", facility=self.manager_user_fac)
        Department.objects.create(name="C Dept", facility=self.manager_user_fac)
        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/facilities/departments/?facility_id={self.manager_user_fac.id}&sort_by=name"
        )
        self.assertEqual(response.status_code, 200)
        names = [d["name"] for d in response.data["results"]]
        self.assertEqual(names, sorted(names))

    def test_get_departments_pagination(self):
        # Create 15 departments for pagination
        for i in range(15):
            Department.objects.create(name=f"Dept {i}", facility=self.manager_user_fac)
        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/facilities/departments/?facility_id={self.manager_user_fac.id}&page=2&page_size=5"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["page"], 2)
        self.assertEqual(response.data["page_size"], 5)
        self.assertEqual(len(response.data["results"]), 5)
        self.assertTrue(response.data["has_next"])
        self.assertTrue(response.data["has_previous"])

    def test_get_departments_filtering_by_facility(self):
        # Create a department in a different facility
        other_facility = FacilityFactory()
        Department.objects.create(name="Other Facility Dept", facility=other_facility)
        self._authenticate_user(self.manager_user)
        response = self.client.get(
            f"/api/facilities/departments/?facility_id={self.manager_user_fac.id}"
        )
        self.assertEqual(response.status_code, 200)
        # Should not include department from other facility
        self.assertFalse(
            any(d["name"] == "Other Facility Dept" for d in response.data["results"])
        )


class TestDepartmentDetailsEndpoint(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.department = self.super_user_dept
        self.dept_url = f"/api/departments/{self.department.id}/"
        self._authenticate_user(user=self.super_user)

    def test_get_department_success(self):
        """Test GET request for department details"""
        response = self.client.get(self.dept_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], "success")
        self.assertEqual(response.data["data"]["id"], self.department.id)
        self.assertEqual(response.data["data"]["name"], self.department.name)

    def test_get_nonexistent_department(self):
        """Test GET request for non-existent department"""
        non_existent_url = f"/api/departments/99999/"
        response = self.client.get(non_existent_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data["status"], "failed")

    def test_update_department_success(self):
        """Test PUT request to update department"""
        update_data = {
            "name": "Updated Department",
            "description": "Updated description",
        }
        response = self.client.put(self.dept_url, update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], "success")
        self.assertEqual(response.data["data"]["name"], update_data["name"])
        self.assertEqual(
            response.data["data"]["description"], update_data["description"]
        )

    def test_update_nonexistent_department(self):
        """Test PUT request for non-existent department"""
        non_existent_url = f"/api/departments/99999/"
        update_data = {"name": "Updated Department"}
        response = self.client.put(non_existent_url, update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data["status"], "failed")

    def test_delete_department_success(self):
        """Test DELETE request for department"""
        response = self.client.delete(self.dept_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], "success")

    def test_delete_nonexistent_department(self):
        """Test DELETE request for non-existent department"""
        non_existent_url = f"/api/departments/99999/"
        response = self.client.delete(non_existent_url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data["status"], "failed")

    def test_unauthenticated_access(self):
        """Test accessing endpoint without authentication"""
        self.client.logout()

        # Test all methods
        get_response = self.client.get(self.dept_url)
        put_response = self.client.put(self.dept_url, {"name": "test"})
        delete_response = self.client.delete(self.dept_url)

        self.assertEqual(get_response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(put_response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(delete_response.status_code, status.HTTP_403_FORBIDDEN)
