from django.test import TestCase
from base.models import Department
from base.tests.base_setup import BaseTestSetup

from base.tests.factory import *
from facilities.services.departments import DepartmentService


class TestCreateService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = DepartmentService()
        self.valid_data = {
            "name": "Quality Control Test 2",
            "description": "Software development team",
            "header_of_department": self.manager_user.email,
            "facility_id": self.manager_user_fac.id,
            "add_to_all": False,
        }

    def test_create_department(self):
        response = self.service.create_department(self.valid_data, self.super_user)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data["id"])
        self.assertEqual(response.data["name"], self.valid_data["name"])

    def test_create_department_missing_fields(self):
        # Test with missing required fields
        invalid_data = {
            "description": "Software development team",
            "header_of_department": self.manager_user.email,
            "facility_id": self.manager_user_fac.id,
        }
        response = self.service.create_department(invalid_data, self.super_user)
        self.assertFalse(response.success)

    def test_create_department_members(self):
        # Test with members
        self.valid_data["members"] = [self.manager_user.id, self.super_user.id]
        response = self.service.create_department(self.valid_data, self.super_user)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["members"]), 2)

    def test_create_department_nonexistent_members(self):
        """Test creating department with non-existent member IDs fails appropriately"""
        self.valid_data["members"] = [99999, 88888]

        response = self.service.create_department(self.valid_data, self.super_user)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_add_to_all(self):
        facility_1 = FacilityFactory()
        facility_2 = FacilityFactory()
        self.valid_data["add_to_all"] = True

        response = self.service.create_department(self.valid_data, self.super_user)

        # get facilities form the database
        facility_1.refresh_from_db()
        facility_2.refresh_from_db()

        self.assertTrue(response.success)
        self.assertIsNotNone(
            Department.objects.get(
                facility=facility_1,
                name=response.data["name"],
            )
        )
        self.assertIsNotNone(
            Department.objects.get(
                facility=facility_2,
                name=response.data["name"],
            )
        )
        self.assertIsNotNone(
            Department.objects.get(
                facility=self.manager_user_fac,
                name=response.data["name"],
            )
        )


class TestGetDepartments(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = DepartmentService()

        for _ in range(4):
            DepartmentFactory(
                facility=self.manager_user_fac,
            )

    def test_get_departments(self):
        params = {
            "facility_id": self.manager_user_fac.id,
        }
        response = self.service.get_departments(self.super_user, params)
        self.assertTrue(response.success)
        self.assertGreater(len(response.data), 3)

    def test_departments_no_facility_id(self):
        params = {}
        response = self.service.get_departments(self.super_user, params)
        self.assertFalse(response.success)


class TestHandleDepartmentMembers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = DepartmentService()
        # Create a test department
        self.department = DepartmentFactory(
            facility=self.manager_user_fac, created_by=self.super_user
        )

    def test_handle_empty_members(self):
        """Test handling when no members are provided"""
        response = self.service.handle_department_members(self.department, [])

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_handle_valid_members(self):
        """Test adding valid members to department"""
        valid_members = [self.manager_user.id, self.super_user.id]

        response = self.service.handle_department_members(
            self.department, valid_members
        )

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(self.department.members.count(), 2)

    def test_handle_nonexistent_members(self):
        """Test handling non-existent member IDs"""
        invalid_members = [99999, 88888]

        response = self.service.handle_department_members(
            self.department, invalid_members
        )

        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)
        self.assertEqual(self.department.members.count(), 0)

    def test_handle_mixed_valid_invalid_members(self):
        """Test handling mix of valid and invalid member IDs"""
        mixed_members = [self.manager_user.id, 99999]

        response = self.service.handle_department_members(
            self.department, mixed_members
        )

        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)
        self.assertEqual(self.department.members.count(), 0)


class TestGetDepartmentById(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = DepartmentService()
        # Create a test department
        self.department = DepartmentFactory(
            facility=self.manager_user_fac,
            created_by=self.super_user,
            header_of_department=self.manager_user,
        )

    def test_get_department_success(self):
        """Test retrieving an existing department"""
        response = self.service.get_department_by_id(self.department.id)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["id"], self.department.id)
        self.assertEqual(response.data["name"], self.department.name)

    def test_get_nonexistent_department(self):
        """Test retrieving a non-existent department"""
        non_existent_id = 99999
        response = self.service.get_department_by_id(non_existent_id)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)
        self.assertIsNone(response.data)

    def test_get_department_with_invalid_id(self):
        """Test retrieving department with invalid ID type"""
        response = self.service.get_department_by_id("invalid_id")

        self.assertFalse(response.success)
        self.assertEqual(response.code, 500)
        self.assertIsNone(response.data)


class TestUpdateDepartment(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = DepartmentService()
        self.department = DepartmentFactory(
            facility=self.manager_user_fac,
            created_by=self.super_user,
            header_of_department=self.manager_user,
        )
        self.valid_update_data = {
            "name": "Updated Department",
            "description": "Updated description",
        }

    def test_update_department_success(self):
        """Test successfully updating a department"""
        response = self.service.update_department(
            self.valid_update_data, self.department.id, self.super_user
        )

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["name"], self.valid_update_data["name"])
        self.assertEqual(
            response.data["description"], self.valid_update_data["description"]
        )

    def test_update_department_with_new_head(self):
        """Test updating department's head"""
        update_data = {"header_of_department": self.super_user.id}

        response = self.service.update_department(
            update_data, self.department.id, self.super_user
        )

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        # Verify new head is also added as member
        self.assertIn(self.super_user, self.department.members.all())

    def test_update_department_with_members(self):
        """Test updating department members"""
        update_data = {"members": [self.super_user.id, self.manager_user.id]}

        response = self.service.update_department(
            update_data, self.department.id, self.super_user
        )

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(self.department.members.count(), 2)

    def test_update_nonexistent_department(self):
        """Test updating a non-existent department"""
        non_existent_id = 99999

        response = self.service.update_department(
            self.valid_update_data, non_existent_id, self.super_user
        )

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_update_with_invalid_parent(self):
        """Test updating with non-existent parent department"""
        update_data = {"parent": 99999}  # Non-existent parent ID

        response = self.service.update_department(
            update_data, self.department.id, self.super_user
        )

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_update_with_nonexistent_head(self):
        """Test updating with non-existent head of department"""
        update_data = {"header_of_department": 99999}  # Non-existent user ID

        response = self.service.update_department(
            update_data, self.department.id, self.super_user
        )
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestDeleteDepartment(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = DepartmentService()
        self.department = DepartmentFactory(
            facility=self.manager_user_fac,
            created_by=self.super_user,
            header_of_department=self.manager_user,
        )

    def test_delete_department_success(self):
        """Test successfully deleting a department"""
        response = self.service.delete_department(self.department.id, self.super_user)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

        # Verify department no longer exists in database
        with self.assertRaises(Department.DoesNotExist):
            Department.objects.get(id=self.department.id)

    def test_delete_nonexistent_department(self):
        """Test deleting a non-existent department"""
        non_existent_id = 99999

        response = self.service.delete_department(non_existent_id, self.super_user)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_delete_department_with_invalid_id(self):
        """Test deleting department with invalid ID type"""
        response = self.service.delete_department("invalid_id", self.super_user)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 500)
