from base.models import Department, Facility
from base.serializers import DepartmentSerializer, UpdateDepartmentSerializer
from base.services.forms import check_missing_fields
from base.services.responses import APIResponse
from django.contrib.auth.models import User
from django.db import transaction
from base.services.logging.logger import LoggingService
from django.db.models import Q

logging_service = LoggingService()


class DepartmentService:
    """Service class to handle department-related operations."""

    def create_department(self, data, user) -> APIResponse:
        """
        Create a new department based on provided data.

        Args:
            data (dict): Department data including name, parent, description, etc.
            user (User): The user creating the department

        Returns:
            APIResponse: Response object with success status, message, and data
        """
        name = data.get("name")
        parent = data.get("parent")
        description = data.get("description")
        header_of_department_email = data.get("header_of_department")
        facility_id = data.get("facility_id")
        add_to_all = data.get("add_to_all")
        members = data.get("members", [])

        # Validate required fields
        required_fields = ["name", "header_of_department"]
        missing_fields_response = check_missing_fields(data, required_fields)
        if missing_fields_response:
            return APIResponse(
                success=False,
                message=missing_fields_response,
                data=None,
            )

        # Determine which facilities to add the department to
        facilities_to_add = Facility.objects.all() if add_to_all else None

        try:
            # Get header of department user
            header_of_department = User.objects.filter(
                email=header_of_department_email
            ).first()

            if not header_of_department:
                return APIResponse(
                    success=False,
                    message="User with email {header_of_department_email} not found",
                    data=None,
                    code=404,
                )

            # add head of department as member
            members.append(header_of_department.id)

            # Get parent department if specified
            parent_department = None
            if parent:
                parent_department = Department.objects.filter(name=parent).first()

            # Get facility if specified
            facility = None
            if facility_id:
                try:
                    facility = Facility.objects.get(id=facility_id)
                except Facility.DoesNotExist:
                    return APIResponse(
                        success=False,
                        message="Facility with id {facility_id} not found",
                        data=None,
                        code=404,
                    )

            # Create or get department with transaction to ensure atomicity
            with transaction.atomic():
                new_department, created = Department.objects.get_or_create(
                    name=name,
                    parent=parent_department,
                    description=description,
                    header_of_department=header_of_department,
                    created_by=user,
                    facility=facility,
                )

                if created:
                    member_response = self.handle_department_members(
                        new_department, members
                    )
                    if not member_response.success:
                        new_department.delete()
                        return APIResponse(
                            success=False,
                            message=member_response.message,
                            data=None,
                            code=member_response.code,
                        )

                # Serialize the department
                department_data = DepartmentSerializer(new_department).data

                # Serialize the department
                department_data = DepartmentSerializer(new_department).data

                # Add department to other facilities if needed
                facilities_added = []
                if facilities_to_add and facility_id:
                    facilities_added = self.add_department_to_facilities(
                        facilities_to_add=facilities_to_add,
                        facility_id=facility_id,
                        name=name,
                        parent_department=parent_department,
                        description=description,
                        user=user,
                    )

            # Determine response based on whether department was created or already existed
            message = (
                "Departments added successfully"
                if created
                else "Department already exists"
            )
            code = 201 if created else 200

            return APIResponse(
                success=True,
                message=message,
                data=department_data,
                code=code,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error creating department",
                data=None,
                code=500,
            )

    def get_department_by_id(self, department_id):
        """Move logic from api view to here"""
        try:
            department = Department.objects.get(id=department_id)
            serializer = DepartmentSerializer(department)
            return APIResponse(
                success=True,
                message="Department retrieved successfully",
                data=serializer.data,
                code=200,
            )
        except Department.DoesNotExist:
            return APIResponse(
                success=False,
                message="Department not found",
                data=None,
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error retrieving department",
                data=None,
                code=500,
            )

    def add_department_to_facilities(
        self,
        facilities_to_add,
        facility_id,
        name,
        parent_department,
        description,
        user,
    ):
        """
        Add a department to multiple facilities.

        Args:
            facilities_to_add (QuerySet): Facilities to add the department to
            facility_id (int): ID of the original facility (to avoid duplication)
            name (str): Department name
            parent_department (Department): Parent department object
            description (str): Department description
            user (User): The user creating the departments

        Returns:
            list: Names of facilities where the department was added
        """
        facilities_added = []

        try:
            with transaction.atomic():
                for facility in facilities_to_add:
                    # Skip the original facility
                    if facility.id == facility_id:
                        continue

                    department, created = Department.objects.get_or_create(
                        name=name,
                        parent=parent_department,
                        description=description,
                        facility=facility,
                        defaults={"created_by": user},
                    )

                    if created:
                        facilities_added.append(facility.name)
        except Exception as e:
            # Log the error or handle it as needed
            pass

        return facilities_added

    def update_department(self, data, department_id, user) -> APIResponse:
        request_copy = data.copy()
        try:
            department = Department.objects.get(id=department_id)

            if "parent" in request_copy:
                try:
                    parent_department = Department.objects.get(
                        id=request_copy["parent"]
                    )
                    request_copy["parent"] = parent_department.id
                except Department.DoesNotExist:
                    return APIResponse(
                        success=False,
                        message="Parent department not found",
                        data=None,
                        code=404,
                    )

            request_copy.pop("members", None)

            updated_department = UpdateDepartmentSerializer(
                department,
                data=request_copy,
                partial=True,
            )
            if updated_department.is_valid():
                updated_department.save()
                # handle head of department if included in the request
                if "header_of_department" in data:
                    try:
                        header_id = data["header_of_department"]
                        header_id = (
                            int(header_id) if isinstance(header_id, str) else header_id
                        )
                        head_of_department = User.objects.get(id=header_id)

                        department.header_of_department = head_of_department
                        department.save()
                        if (
                            not department.header_of_department
                            in department.members.all()
                        ):
                            department.members.add(head_of_department)
                        department.save()
                    except User.DoesNotExist:
                        return APIResponse(
                            success=False,
                            message="Head of department not found",
                            data=None,
                            code=404,
                        )
                # Handle members if included in the request
                if "members" in data:
                    member_response = self.handle_department_members(
                        department, data["members"]
                    )
                    if not member_response.success:
                        # Rollback the transaction if adding members fails
                        department.delete()
                        return APIResponse(
                            success=False,
                            message=member_response.message,
                            data=None,
                            code=member_response.code,
                        )
                # Serialize the updated department
                updated_department = DepartmentSerializer(department).data

                return APIResponse(
                    success=True,
                    message="Department updated successfully",
                    data=updated_department,
                    code=200,
                )
            else:
                return APIResponse(
                    success=False,
                    message="Invalid data",
                    data=updated_department.errors,
                    code=400,
                )
        except Department.DoesNotExist:
            return APIResponse(
                success=False,
                message="Department not found",
                data=None,
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error updating department",
                data=None,
                code=500,
            )

    def delete_department(self, department_id, user) -> APIResponse:
        try:
            department = Department.objects.get(id=department_id)
            # TODO: Check if the department does not have any members or other dependencies
            department.delete()
            return APIResponse(
                success=True,
                message="Department deleted successfully",
                data=None,
                code=200,
            )
        except Department.DoesNotExist:
            return APIResponse(
                success=False,
                message="Department not found",
                data=None,
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error deleting department",
                data=None,
                code=500,
            )

    def get_departments(self, user, params) -> APIResponse:
        """
        Retrieves a paginated, searchable, and sortable list of departments for a given facility.
        Args:
            user: The user making the request.
            params (dict): Query parameters containing:
                - facility_id (int, required): The ID of the facility to filter departments.
                - q (str, optional): Search query to filter departments by name, description, or header email.
                - sort_by (str, optional): Field to sort by ('name', 'created_at', or 'id').
                - sort_order (str, optional): Order of sorting ('asc' or 'desc', default is 'asc').
                - page (int, optional): Page number for pagination (default: 1).
                - page_size (int, optional): Number of items per page (default: 10).
        Returns:
            APIResponse: An object containing:
                - success (bool): Indicates if the operation was successful.
                - message (str): A message describing the result.
                - data (dict or None): Contains paginated department data if successful, otherwise None.
                - code (int): HTTP status code.
        Raises:
            Returns an error response if required parameters are missing or an exception occurs.
        """

        if not params.get("facility_id"):
            return APIResponse(
                success=False,
                message="Facility id is required in query parameters",
                data=None,
                code=400,
            )
        try:

            departments = (
                Department.objects.all()
                .select_related(
                    "header_of_department",
                    "created_by",
                    "updated_by",
                    "facility",
                )
                .prefetch_related("members")
                .filter(facility__id=params.get("facility_id"))
            )

            # Searching (OR logic for name, description, header email)
            search_query = params.get("q")
            if search_query:
                departments = departments.filter(
                    Q(name__icontains=search_query)
                    | Q(description__icontains=search_query)
                    | Q(header_of_department__email__icontains=search_query)
                )

            # Sorting
            sort_by = params.get("sort_by")
            if sort_by:
                if sort_by in ["name", "created_at", "id"]:
                    departments = departments.order_by(sort_by)
                else:
                    return APIResponse(
                        success=False,
                        message=f"Invalid sort_by parameter: {sort_by}",
                        data=None,
                        code=400,
                    )

            # Pagination
            page = int(params.get("page", 1))
            page_size = int(params.get("page_size", 10))
            total_count = departments.count()
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            paginated_departments = departments[start_index:end_index]
            departments_list = DepartmentSerializer(paginated_departments, many=True)

            total_pages = (total_count + page_size - 1) // page_size
            has_next = page < total_pages
            has_previous = page > 1

            data = {
                "results": departments_list.data,
                "count": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous,
            }

            return APIResponse(
                data=data,
                success=True,
                message="Departments retrieved successfully",
                code=200,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                data=None,
                success=False,
                message="Error retrieving departments",
            )

    def handle_department_members(self, department, member_ids) -> APIResponse:
        """
        Service method to handle department member operations.
        """
        if not member_ids:
            return APIResponse(
                success=True, message="No members to add", data=None, code=200
            )

        # Verify all users exist
        users = User.objects.filter(id__in=member_ids)
        if len(users) != len(member_ids):
            found_ids = set(users.values_list("id", flat=True))
            missing_ids = set(member_ids) - found_ids
            if missing_ids:
                return APIResponse(
                    success=False,
                    message=f"Users with IDs {missing_ids} not found",
                    data=None,
                    code=400,
                )

        try:
            department.members.set(users)
            return APIResponse(
                success=True, message="Members added successfully", data=None, code=200
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False, message="Error adding members", data=None, code=500
            )
