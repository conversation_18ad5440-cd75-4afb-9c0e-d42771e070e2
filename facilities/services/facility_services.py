from base.models import Facility
from base.services.responses import RepositoryResponse


class FacilityService:

    def get_facility_by_id(self, facility_id):
        try:
            facility = Facility.objects.get(id=facility_id)
            return RepositoryResponse(
                data=facility,
                success=True,
                message="Facility retrieved successfully",
            )
        except Facility.DoesNotExist:
            return RepositoryResponse(
                data=None,
                success=False,
                message="Facility not found",
            )
        except Exception as e:
            return RepositoryResponse(
                data=None,
                success=False,
                message="Internal server error",
            )
