# from django.db import models
# from django.contrib.auth.models import User
# from base.models import BaseModel


# # Create your models here.
# class Facility(BaseModel):
#     name = models.CharField(max_length=500, null=True, blank=True)
#     address = models.Char<PERSON>ield(max_length=500, null=True, blank=True)
#     phone_number = models.CharField(max_length=255, null=True, blank=True)
#     email = models.EmailField(null=True, blank=True)
#     facility_type = models.CharField(max_length=255, null=True, blank=True)
#     ceo = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
#     contact_person = models.ForeignKey(
#         User,
#         null=True,
#         blank=True,
#         on_delete=models.SET_NULL,
#         related_name="facility_contact_person",
#     )
#     staff_members = models.ManyToManyField(User, related_name="facility_employees")

#     def __str__(self):
#         return self.name if self.name else "No Facility Name Available"
