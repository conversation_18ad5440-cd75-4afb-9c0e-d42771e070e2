from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserFactory
from reviews.tests.factory import ReviewFactory
from workplace_violence_reports.tests.factory import WorkPlaceViolenceFactory


class TestGetIncidentReviews(BaseTestSetup):
    """Test getting incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    # cases for workplace violence incident reviews
    def test_get_workplace_violence_reviews(self):
        """Test getting reviews for a workplace violence incident"""
        incident = WorkPlaceViolenceFactory()

        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(2, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        self._authenticate_user(self.user)
        response = self.client.get(
            f"/api/incidents/workplace-violence/{incident.id}/reviews/"
        )

        if not response.status_code == 200:
            self.fail(f"Failed to get reviews: {response.data}")
        # check number of reviews returned
        self.assertEqual(len(response.data), 2)


class TestCreateIncidentReview(BaseTestSetup):
    """Test creating incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    def test_create_workplace_violence_review(self):
        """Test creating a review for a workplace violence incident"""
        incident = WorkPlaceViolenceFactory()
        incident.reviews.clear()
        #  clear existing reviews
        self._authenticate_user(self.user)

        response = self.client.post(
            f"/api/incidents/workplace-violence/{incident.id}/reviews/",
            data={
                "content": "This is a test review",
            },
            format="json",
        )

        if not response.status_code == 201:
            self.fail(f"Failed to create review: {response.data}")
        # check that the review was created
        self.assertEqual(incident.reviews.first().content, "This is a test review")
