from rest_framework import status
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import (
    DepartmentFactory,
    FacilityFactory,
    ProfileFactory,
)
from tasks.tests.factory import (
    ReviewGroupFactory,
    ReviewTemplateFactory,
    ReviewTemplateTaskFactory,
)
from workplace_violence_reports.models import (
    WorkPlaceViolence,
    WorkPlaceViolenceVersion,
)
from workplace_violence_reports.services.actions import WorkplaceViolenceActions
from workplace_violence_reports.services.operations import WorkplaceOperations
from workplace_violence_reports.tests.factory import WorkPlaceViolenceFactory
from datetime import datetime, timedelta


class TestCreateReport(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self.facility = FacilityFactory()
        self.department = DepartmentFactory()
        self.service = WorkplaceOperations(user=self.user_user)
        self.data = {
            "status": "Draft",
            "facility": self.facility.id,
            "department": self.department.id,
            "current_step": 1,
            "is_resolved": False,
            "type_of_incident": "Physical Assault",
            "incident_type": "Type 2",
            "physical_injury_description": "Bruising on left arm",
            "date_of_incident": "2025-05-15",
            "time_of_incident": "14:30:00",
            "description": "An altercation occurred between a patient and a staff member in the ER.",
            "involved_parties": [
                {
                    "profile_type": "Assailant",
                    "first_name": "John",
                    "last_name": "Doe",
                    "title": "Patient",
                    "assailant_relationship_to_patient": "Self",
                    "assailant_background": "History of aggressive behavior",
                },
                {
                    "profile_type": "Victim",
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "title": "Nurse",
                    "assailant_relationship_to_patient": None,
                    "assailant_background": None,
                },
            ],
            "type_of_contact": "In-person",
            "victim_was_alone": True,
            "location": "ER Triage Area",
            "there_was_threats_before": "Verbal threats reported prior",
            "staff_member_reported": "Jane Smith",
            "weapons_were_involved": False,
            "weapon_used": None,
            "there_were_injuries": "Minor injuries reported",
            "persons_injured": [
                {
                    "profile_type": "Victim",
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "injury_description": "Bruising on left arm",
                }
            ],
            "incident_witnesses": [
                {
                    "profile_type": "Witness",
                    "first_name": "Mike",
                    "last_name": "Johnson",
                }
            ],
            "notification": "Supervisor notified immediately",
            "termination_of_incident": "Patient was restrained and removed by security.",
            "victim_has_contact_with_assailant": "No further contact",
            "immediate_supervisor": True,
            "name_of_supervisor": {
                "first_name": "Sarah",
                "last_name": "Brown",
                "profile_type": "Supervisor",
            },
            "title_of_supervisor": "Head Nurse",
            "date_notified": "2025-05-15",
            "time_notified": "14:45:00",
            "action_taken": "Incident reported to security and HR",
            "prevention_suggestion": "Increase security presence in ER",
            "reported_by": {
                "first_name": "Jane",
                "last_name": "Smith",
                "profile_type": "Reporter",
            },
            "reported_by_title": "Nurse",
            "date_reported": "2025-05-15",
            "time_reported": "15:00:00",
            "is_modified": False,
        }

    def test_create_report(self):
        self._authenticate_user(self.user_user)
        response = self.client.post(
            f"{self.incidents_endpoint}/workplace-violence/",
            data=self.data,
            format="json",
        )

        if not response.status_code == 201:
            self.fail(f"Failed to create report: {response.data}")
        # incident should be created
        self.assertEqual(response.data["status"], "Draft")
        self.assertEqual(response.data["type_of_incident"], "Physical Assault")
        self.assertEqual(response.data["incident_type"], "Type 2")
        self.assertEqual(
            response.data["physical_injury_description"], "Bruising on left arm"
        )
        self.assertEqual(response.data["date_of_incident"], "2025-05-15")
        # return
        # check involved parties
        created_incident = WorkPlaceViolence.objects.get(id=response.data["id"])
        involved_parties = created_incident.involved_parties.all()
        self.assertEqual(len(involved_parties), 2)

        # check persons injured
        persons_injured = created_incident.persons_injured.all()
        self.assertEqual(len(persons_injured), 1)

        # check incident witnesses
        incident_witnesses = created_incident.incident_witness.all()
        self.assertEqual(len(incident_witnesses), 1)

        # check name of supervisor
        name_of_supervisor = created_incident.name_of_supervisor
        self.assertIsNotNone(name_of_supervisor)
        self.assertEqual(name_of_supervisor.first_name, "Sarah")
        self.assertEqual(name_of_supervisor.last_name, "Brown")

        # check reported_by
        reported_by = created_incident.reported_by
        self.assertIsNotNone(reported_by)
        self.assertEqual(reported_by.first_name, "Jane")
        self.assertEqual(reported_by.last_name, "Smith")


class TestUpdateReport(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.facility = FacilityFactory()
        self.department = DepartmentFactory()
        self.data = {
            "status": "Draft",
            "facility": self.facility.id,
            "department": self.department.id,
            "current_step": 1,
            "is_resolved": False,
            "type_of_incident": "Physical Assault",
            "incident_type": "Type 2",
            "physical_injury_description": "Bruising on left arm",
            "date_of_incident": "2025-05-15",
            "time_of_incident": "14:30:00",
            "description": "An altercation occurred between a patient and a staff member in the ER.",
            "involved_parties": [
                {
                    "profile_type": "Assailant",
                    "first_name": "John",
                    "last_name": "Doe",
                    "title": "Patient",
                    "assailant_relationship_to_patient": "Self",
                    "assailant_background": "History of aggressive behavior",
                },
                {
                    "profile_type": "Victim",
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "title": "Nurse",
                    "assailant_relationship_to_patient": None,
                    "assailant_background": None,
                },
            ],
            "type_of_contact": "In-person",
            "victim_was_alone": True,
            "location": "ER Triage Area",
            "there_was_threats_before": "Verbal threats reported prior",
            "staff_member_reported": "Jane Smith",
            "weapons_were_involved": False,
            "weapon_used": None,
            "there_were_injuries": "Minor injuries reported",
            "persons_injured": [
                {
                    "profile_type": "Victim",
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "injury_description": "Bruising on left arm",
                }
            ],
            "incident_witnesses": [
                {
                    "profile_type": "Witness",
                    "first_name": "Mike",
                    "last_name": "Johnson",
                }
            ],
            "notification": "Supervisor notified immediately",
            "termination_of_incident": "Patient was restrained and removed by security.",
            "victim_has_contact_with_assailant": "No further contact",
            "immediate_supervisor": True,
            "name_of_supervisor": {
                "first_name": "Sarah",
                "last_name": "Brown",
                "profile_type": "Supervisor",
            },
            "title_of_supervisor": "Head Nurse",
            "date_notified": "2025-05-15",
            "time_notified": "14:45:00",
            "action_taken": "Incident reported to security and HR",
            "prevention_suggestion": "Increase security presence in ER",
            "reported_by": {
                "first_name": "Jane",
                "last_name": "Smith",
                "profile_type": "Reporter",
            },
            "reported_by_title": "Nurse",
            "date_reported": "2025-05-15",
            "time_reported": "15:00:00",
            "is_modified": False,
        }

        self.incident = WorkPlaceViolenceFactory(created_by=self.user_user)

    def test_update_report(self):
        self._authenticate_user(self.user_user)
        response = self.client.put(
            f"{self.incidents_endpoint}/workplace-violence/{self.incident.id}/",
            data=self.data,
            format="json",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to update report: {response.data}")
        # incident should be updated
        updated_incident = WorkPlaceViolence.objects.get(id=self.incident.id)
        self.assertEqual(updated_incident.status, "Draft")
        self.assertEqual(updated_incident.type_of_incident, "Physical Assault")
        self.assertEqual(updated_incident.incident_type, "Type 2")
        self.assertEqual(
            updated_incident.physical_injury_description, "Bruising on left arm"
        )
        self.assertEqual(
            updated_incident.date_of_incident.strftime("%Y-%m-%d"), "2025-05-15"
        )

        # check involved parties

        involved_parties = updated_incident.involved_parties.all()
        self.assertEqual(len(involved_parties), 2)

        # check persons injured
        persons_injured = updated_incident.persons_injured.all()
        self.assertEqual(len(persons_injured), 1)

        # check incident witnesses
        incident_witnesses = updated_incident.incident_witness.all()
        self.assertEqual(len(incident_witnesses), 1)

        # check name of supervisor
        name_of_supervisor = updated_incident.name_of_supervisor
        self.assertIsNotNone(name_of_supervisor)
        self.assertEqual(name_of_supervisor.first_name, "Sarah")
        self.assertEqual(name_of_supervisor.last_name, "Brown")

        # check reported by
        reported_by = updated_incident.reported_by
        self.assertIsNotNone(reported_by)
        self.assertEqual(reported_by.first_name, "Jane")
        self.assertEqual(reported_by.last_name, "Smith")


class TestGetReport(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self.service = WorkplaceOperations(user=self.super_user)
        self.incident = WorkPlaceViolenceFactory(created_by=self.super_user)

    def test_get_report(self):
        self._authenticate_user(self.super_user)
        response = self.client.get(
            f"{self.incidents_endpoint}/workplace-violence/{self.incident.id}/"
        )

        if not response.status_code == 200:
            self.fail(f"Failed to get report: {response.data}")

        # Check if the incident data is correct
        incident_data = response.data
        self.assertEqual(incident_data["incident"]["id"], self.incident.id)
        self.assertEqual(incident_data["incident"]["status"], self.incident.status)
        self.assertEqual(
            incident_data["incident"]["type_of_incident"],
            self.incident.type_of_incident,
        )
        self.assertEqual(
            incident_data["incident"]["incident_type"], self.incident.incident_type
        )


class TestModifyReport(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.facility = FacilityFactory()
        self.department = DepartmentFactory()
        self.data = {
            "status": "Draft",
            "facility": self.facility.id,
            "department": self.department.id,
            "current_step": 1,
            "is_resolved": False,
            "type_of_incident": "Physical Assault",
            "incident_type": "Type 2",
            "physical_injury_description": "Bruising on left arm",
            "date_of_incident": "2025-05-15",
            "time_of_incident": "14:30:00",
            "description": "An altercation occurred between a patient and a staff member in the ER.",
            "involved_parties": [
                {
                    "profile_type": "Assailant",
                    "first_name": "John",
                    "last_name": "Doe",
                    "title": "Patient",
                    "assailant_relationship_to_patient": "Self",
                    "assailant_background": "History of aggressive behavior",
                },
                {
                    "profile_type": "Victim",
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "title": "Nurse",
                    "assailant_relationship_to_patient": None,
                    "assailant_background": None,
                },
            ],
            "type_of_contact": "In-person",
            "victim_was_alone": True,
            "location": "ER Triage Area",
            "there_was_threats_before": "Verbal threats reported prior",
            "staff_member_reported": "Jane Smith",
            "weapons_were_involved": False,
            "weapon_used": None,
            "there_were_injuries": "Minor injuries reported",
            "persons_injured": [
                {
                    "profile_type": "Victim",
                    "first_name": "Jane",
                    "last_name": "Smith",
                    "injury_description": "Bruising on left arm",
                }
            ],
            "incident_witnesses": [
                {
                    "profile_type": "Witness",
                    "first_name": "Mike",
                    "last_name": "Johnson",
                }
            ],
            "notification": "Supervisor notified immediately",
            "termination_of_incident": "Patient was restrained and removed by security.",
            "victim_has_contact_with_assailant": "No further contact",
            "immediate_supervisor": True,
            "name_of_supervisor": {
                "first_name": "Sarah",
                "last_name": "Brown",
                "profile_type": "Supervisor",
            },
            "title_of_supervisor": "Head Nurse",
            "date_notified": "2025-05-15",
            "time_notified": "14:45:00",
            "action_taken": "Incident reported to security and HR",
            "prevention_suggestion": "Increase security presence in ER",
            "reported_by": {
                "first_name": "Jane",
                "last_name": "Smith",
                "profile_type": "Reporter",
            },
            "reported_by_title": "Nurse",
            "date_reported": "2025-05-15",
            "time_reported": "15:00:00",
            "is_modified": False,
        }

        self.incident = WorkPlaceViolenceFactory(created_by=self.user_user)

    def test_modify_report(self):
        self.data["action"] = "modify"
        self._authenticate_user(self.super_user)
        response = self.client.patch(
            f"{self.incidents_endpoint}/workplace-violence/{self.incident.id}/",
            self.data,
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to modify report: {response.data}")

        # incident should be modified
        self.incident.refresh_from_db()
        self.assertEqual(self.incident.is_modified, True)

        # there should be a new version of the incident
        self.assertIsNotNone(
            WorkPlaceViolenceVersion.objects.filter(
                original_report=self.incident
            ).first()
        )

        # this new version should have the updated data
        updated_incident = WorkPlaceViolenceVersion.objects.filter(
            original_report=self.incident
        ).first()
        self.assertEqual(updated_incident.status, "Draft")
        self.assertEqual(updated_incident.type_of_incident, "Physical Assault")
        self.assertEqual(updated_incident.incident_type, "Type 2")
        self.assertEqual(
            updated_incident.physical_injury_description, "Bruising on left arm"
        )
        self.assertEqual(
            updated_incident.date_of_incident.strftime("%Y-%m-%d"), "2025-05-15"
        )

        # check involved parties

        involved_parties = updated_incident.involved_parties.all()
        self.assertEqual(len(involved_parties), 2)

        # check persons injured
        persons_injured = updated_incident.persons_injured.all()
        self.assertEqual(len(persons_injured), 1)

        # check incident witnesses
        incident_witnesses = updated_incident.incident_witness.all()
        self.assertEqual(len(incident_witnesses), 1)

        # check name of supervisor
        name_of_supervisor = updated_incident.name_of_supervisor
        self.assertIsNotNone(name_of_supervisor)
        self.assertEqual(name_of_supervisor.first_name, "Sarah")
        self.assertEqual(name_of_supervisor.last_name, "Brown")

        # check reported by
        reported_by = updated_incident.reported_by
        self.assertIsNotNone(reported_by)
        self.assertEqual(reported_by.first_name, "Jane")
        self.assertEqual(reported_by.last_name, "Smith")


class TestMarkAsClosed(BaseTestSetup):
    """Testing  marking workplace violence report as closed"""

    def setUp(self):
        super().setUp()
        self.incident = WorkPlaceViolenceFactory(created_by=self.user_user)
        self.service = WorkplaceViolenceActions(
            user=self.super_user,
            incident_id=self.incident.id,
            data=None,
        )

    def test_mark_as_closed(self):
        self._authenticate_user(self.super_user)
        response = self.client.patch(
            f"{self.incidents_endpoint}/workplace-violence/{self.incident.id}/",
            {"action": "mark-closed"},
            format="json",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to mark as closed: {response.data}")

        # incident should be marked as closed
        self.incident.refresh_from_db()
        self.assertEqual(self.incident.status, "Closed")


class TestSendForReview(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.template = ReviewTemplateFactory(
            name="Test Template", description="Test Desc"
        )
        self.incident = WorkPlaceViolenceFactory()

        # Create profiles and review group
        self.template_profiles = [ProfileFactory() for _ in range(3)]
        self.group = ReviewGroupFactory()
        self.group.members.add(*self.template_profiles)

        # Create a ReviewTemplateTasks with number_of_days_to_complete
        self.task = ReviewTemplateTaskFactory(
            review_template=self.template,
            require_approval_for_all_groups=True,
        )
        self.task.review_groups.add(self.group)

        # Incident reviewers (optional, depending on model)
        self.incident_profiles = [ProfileFactory() for _ in range(3)]

        self.service = WorkplaceViolenceActions(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "action": "send-for-review",
                "review_template": self.template.id,
            },
        )

    def test_send_for_success(self):
        self._authenticate_user(self.super_user)
        response = self.client.patch(
            f"{self.incidents_endpoint}/workplace-violence/{self.incident.id}/",
            {"action": "send-for-review", "review_template": self.template.id},
            format="json",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to send for review: {response.data}")

        # incident should be sent for review
        self.incident.refresh_from_db()

        self.assertIsNotNone(self.incident.review_process)
        self.assertEqual(self.incident.review_process.name, self.template.name)
        self.assertEqual(
            self.incident.review_process.description, self.template.description
        )

        # Verify ReviewProcessTasks
        process_tasks = self.incident.review_process.review_process_tasks.all()
        self.assertEqual(process_tasks.count(), 1)
        process_task = process_tasks.first()

        # Check task fields
        self.assertEqual(process_task.name, self.task.name)
        self.assertEqual(process_task.description, self.task.description)
        self.assertEqual(process_task.task_priority, self.task.task_priority)
        self.assertEqual(
            process_task.require_approval_for_all_groups,
            self.task.require_approval_for_all_groups,
        )
        self.assertEqual(process_task.status, "Pending")

        # Check deadline
        expected_deadline = datetime.now().date() + timedelta(
            days=self.task.number_of_days_to_complete
        )
        self.assertEqual(process_task.deadline, expected_deadline)

        # Check review_groups
        self.assertIn(self.group, process_task.review_groups.all())


class TestDeleteDrafts(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = WorkPlaceViolenceFactory(
            created_by=self.super_user, status="Draft"
        )
        self.service = WorkplaceViolenceActions(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "action": "delete-draft",
                "incident_ids": [
                    self.incident.id,
                ],
            },
        )

    def test_delete_drafts(self):
        self._authenticate_user(self.super_user)
        response = self.client.patch(
            f"{self.incidents_endpoint}/workplace-violence/{self.incident.id}/",
            {"action": "delete-draft", "incident_ids": [self.incident.id]},
            format="json",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to delete drafts: {response.data}")

        # incident should be deleted
        with self.assertRaises(WorkPlaceViolence.DoesNotExist):
            WorkPlaceViolence.objects.get(id=self.incident.id)


class TestWorkplaceViolenceAPIAsSuperUser(BaseTestSetup):
    """Test Workplace Violence API endpoints as Super User"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/workplace-violence/"
        self._authenticate_user(self.super_user)

    def test_get_incidents_as_superuser(self):
        """Test superuser can access all incidents"""
        # Create incidents in super user's facility
        WorkPlaceViolenceFactory.create_batch(
            3, created_by=self.super_user, report_facility=self.super_user_fac
        )

        # Create incidents in other facilities (should be accessible to superuser)
        WorkPlaceViolenceFactory.create_batch(2)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 5)

    def test_get_incidents_with_filters(self):
        """Test getting filtered incidents"""
        WorkPlaceViolenceFactory(
            status="Draft",
            created_by=self.super_user,
            report_facility=self.super_user_fac,
        )
        WorkPlaceViolenceFactory(
            status="Open",
            created_by=self.super_user,
            report_facility=self.super_user_fac,
        )

        response = self.client.get(f"{self.url}?status=Draft")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)


class TestWorkplaceViolenceAPIAsAdmin(BaseTestSetup):
    """Test Workplace Violence API endpoints as Admin"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/workplace-violence/"
        self._authenticate_user(self.admin_user)

    def test_get_incidents_as_admin(self):
        """Test admin can access incidents from their facility"""
        # Create incidents in admin's facility
        WorkPlaceViolenceFactory.create_batch(
            3,
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )

        # Create incidents in other facility (should not be accessible)
        WorkPlaceViolenceFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)


class TestWorkplaceViolenceAPIAsDirector(BaseTestSetup):
    """Test Workplace Violence API endpoints as Director"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/workplace-violence/"
        self._authenticate_user(self.director_user)

    def test_get_incidents_as_director(self):
        """Test director can access incidents from their facility"""
        # Create incidents in director's facility
        WorkPlaceViolenceFactory.create_batch(
            4,
            created_by=self.director_user,
            department=self.director_user_dept,
            report_facility=self.director_user_fac,
        )

        # Create incidents in other facility (should not be accessible)
        WorkPlaceViolenceFactory.create_batch(2)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 4)


class TestWorkplaceViolenceAPIAsManager(BaseTestSetup):
    """Test Workplace Violence API endpoints as Manager"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/workplace-violence/"
        self._authenticate_user(self.manager_user)

    def test_get_incidents_as_manager(self):
        """Test manager can access incidents from their department"""
        # Create incidents in manager's department
        WorkPlaceViolenceFactory.create_batch(
            3,
            created_by=self.manager_user,
            department=self.manager_user_dept,
            report_facility=self.manager_user_fac,
        )

        # Create incidents in other department (should not be accessible)
        WorkPlaceViolenceFactory.create_batch(2)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)


class TestWorkplaceViolenceAPIAsRegularUser(BaseTestSetup):
    """Test Workplace Violence API endpoints as Regular User"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/workplace-violence/"
        self._authenticate_user(self.user_user)

    def test_get_incidents_as_regular_user(self):
        """Test regular user can access incidents they created or are assigned to review"""
        # Create incidents created by the regular user
        WorkPlaceViolenceFactory.create_batch(
            2,
            created_by=self.user_user,
            department=self.user_user_dept,
            report_facility=self.user_user_fac,
        )

        # Create incidents by other users (should not be visible)
        WorkPlaceViolenceFactory.create_batch(
            3,
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)


class TestWorkplaceViolenceDetailsAPIAsSuperUser(BaseTestSetup):
    """Test Workplace Violence detail API endpoints as Super User"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.super_user)
        self.incident = WorkPlaceViolenceFactory(
            created_by=self.super_user,
            department=self.super_user_dept,
            report_facility=self.super_user_fac,
        )
        self.url = f"/api/incidents/workplace-violence/{self.incident.id}/"

    def test_get_incident_details_as_superuser(self):
        """Test superuser can view any incident details"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_superuser(self):
        """Test superuser can update any incident"""
        update_data = {"type_of_incident": "Verbal Abuse"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )


class TestWorkplaceViolenceDetailsAPIAsAdmin(BaseTestSetup):
    """Test Workplace Violence detail API endpoints as Admin"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.admin_user)
        self.incident = WorkPlaceViolenceFactory(
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )
        self.url = f"/api/incidents/workplace-violence/{self.incident.id}/"

    def test_get_incident_details_as_admin(self):
        """Test admin can view incident details in their facility"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_admin(self):
        """Test admin can update incident in their facility"""
        update_data = {"type_of_incident": "Verbal Abuse"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )


class TestWorkplaceViolenceDetailsAPIAsDirector(BaseTestSetup):
    """Test Workplace Violence detail API endpoints as Director"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.director_user)
        self.incident = WorkPlaceViolenceFactory(
            created_by=self.director_user,
            department=self.director_user_dept,
            report_facility=self.director_user_fac,
        )
        self.url = f"/api/incidents/workplace-violence/{self.incident.id}/"

    def test_get_incident_details_as_director(self):
        """Test director can view incident details in their facility"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_director(self):
        """Test director can update incident in their facility"""
        update_data = {"type_of_incident": "Verbal Abuse"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )


class TestWorkplaceViolenceDetailsAPIAsManager(BaseTestSetup):
    """Test Workplace Violence detail API endpoints as Manager"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.manager_user)
        self.incident = WorkPlaceViolenceFactory(
            created_by=self.manager_user,
            department=self.manager_user_dept,
            report_facility=self.manager_user_fac,
        )
        self.url = f"/api/incidents/workplace-violence/{self.incident.id}/"

    def test_get_incident_details_as_manager(self):
        """Test manager can view incident details in their department"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_manager(self):
        """Test manager can update incident in their department"""
        update_data = {"type_of_incident": "Verbal Abuse"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )


class TestWorkplaceViolenceDetailsAPIAsRegularUser(BaseTestSetup):
    """Test Workplace Violence detail API endpoints as Regular User"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.user_user)
        self.incident = WorkPlaceViolenceFactory(
            created_by=self.user_user,
            department=self.user_user_dept,
            report_facility=self.user_user_fac,
        )
        self.url = f"/api/incidents/workplace-violence/{self.incident.id}/"

    def test_get_incident_details_as_regular_user(self):
        """Test regular user can view incident details they created"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_regular_user(self):
        """Test regular user can update incident they created"""
        update_data = {"type_of_incident": "Verbal Abuse"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )
