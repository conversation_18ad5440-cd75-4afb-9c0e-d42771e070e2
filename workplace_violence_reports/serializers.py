from rest_framework import serializers
from accounts.models import Profile
from accounts.serializers import GetProfileSerializer, UserProfileSerializer, UserSerializer
from workplace_violence_reports.models import (
    IncidentInvolvedParty,
    IncidentPersonInjured,
    IncidentWitness,
    WorkPlaceViolence,
    TerminationOfIncident,
    WorkPlaceViolenceVersion,
)
from general_patient_visitor.serializers import FacilitySerializer
from accounts.serializers import ProfileSerializer
from api.views.incidents.general_incident.new_incident import (
    generate_username,
    get_patient_profile,
)
from django.contrib.auth.models import User


class IncidentWitnessSerializer(serializers.ModelSerializer):
    witness = UserProfileSerializer()

    class Meta:
        model = IncidentWitness
        fields = "__all__"


class WorkPlaceViolenceIncidentInvolvedParty(serializers.ModelSerializer):
    party = UserProfileSerializer()

    class Meta:
        model = IncidentInvolvedParty
        fields = "__all__"


class IncidentPersonInjuredSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncidentPersonInjured
        fields = "__all__"


class TerminationOfIncidentSerializer(serializers.ModelSerializer):
    class Meta:
        model = TerminationOfIncident
        fields = "__all__"


class WorkPlaceViolenceSerializer(serializers.ModelSerializer):

    class Meta:
        model = WorkPlaceViolence
        fields = "__all__"


class UpdatePersonInjuredSerializer(serializers.ModelSerializer):
    person = GetProfileSerializer()

    class Meta:
        model = IncidentPersonInjured
        fields = "__all__"


class WorkPlaceViolenceVersionSerializer(serializers.ModelSerializer):
    incident_witness = IncidentWitnessSerializer(many=True, read_only=True)
    persons_injured = IncidentPersonInjuredSerializer(many=True, read_only=True)

    class Meta:
        model = WorkPlaceViolenceVersion
        fields = '__all__'


class WorkPlaceViolenceObjectSerializer(serializers.ModelSerializer):
    initiated_by = WorkPlaceViolenceIncidentInvolvedParty(many=True)
    incident_witness = IncidentWitnessSerializer(many=True)
    persons_injured = UpdatePersonInjuredSerializer(many=True, required=False)
    reported_by = GetProfileSerializer()
    name_of_supervisor = GetProfileSerializer()

    class Meta:
        model = WorkPlaceViolence
        fields = "__all__"


class PersonInjuredSerializer(serializers.ModelSerializer):
    name = ProfileSerializer()

    class Meta:
        model = IncidentPersonInjured
        fields = "__all__"


class WorkPlaceViolenceListObjectSerializer(serializers.ModelSerializer):
    initiated_by = WorkPlaceViolenceIncidentInvolvedParty(many=True)
    termination_of_incident = TerminationOfIncidentSerializer(many=True)
    incident_witness = IncidentWitnessSerializer(many=True)
    persons_injured = PersonInjuredSerializer()
    report_facility = FacilitySerializer()

    class Meta:
        model = WorkPlaceViolence
        fields = "__all__"


class WorkPlaceViolenceIncidentInvolvedPartySerializer(serializers.ModelSerializer):
    class Meta:
        model = IncidentInvolvedParty
        fields = "__all__"


class TerminationOfIncidentSerializer(serializers.ModelSerializer):
    class Meta:
        model = TerminationOfIncident
        fields = "__all__"


class WorkPlaceViolenceUpdateSerializer(serializers.ModelSerializer):
    initiated_by = WorkPlaceViolenceIncidentInvolvedParty(many=True, required=False)
    termination_of_incident = TerminationOfIncidentSerializer(many=True, required=False)
    persons_injured = UpdatePersonInjuredSerializer(many=True, required=False)
    incident_witness = IncidentWitnessSerializer(many=True, required=False)

    class Meta:
        model = WorkPlaceViolence
        fields = "__all__"

    def update(self, instance, validated_data):
        initiated_by_data = validated_data.pop("initiated_by", None)
        termination_of_incident_data = validated_data.pop(
            "termination_of_incident", None
        )
        persons_injured_data = validated_data.pop("persons_injured", None)
        incident_witness_data = validated_data.pop("incident_witness", None)

        # Update simple fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Save instance with updated simple fields
        instance.save()

        if initiated_by_data is not None:
            instance.initiated_by.clear()
            for party_data in initiated_by_data:
                party, _ = IncidentInvolvedParty.objects.get_or_create(**party_data)
                instance.initiated_by.add(party)

        if termination_of_incident_data is not None:
            instance.termination_of_incident.clear()
            for termination_data in termination_of_incident_data:
                termination, _ = TerminationOfIncident.objects.get_or_create(
                    **termination_data
                )
                instance.termination_of_incident.add(termination)

        if persons_injured_data:

            for injured_data in persons_injured_data:
                user_data = injured_data.get("user_data")

                if not user_data:
                    continue

                injury_description = injured_data.get("injury_description", "")

                first_name = user_data.get("first_name", "Unknown")
                last_name = user_data.get("last_name", "Unknown")
                username = f"{first_name}_{last_name}"

                user, _ = User.objects.get_or_create(
                    first_name=first_name,
                    last_name=last_name,
                    defaults={"username": generate_username(username)},
                )
                profile, _ = Profile.objects.get_or_create(user=user)
                person_injured = IncidentPersonInjured.objects.create(
                    name=profile, injury_description=injury_description
                )

                instance.persons_injured.add(person_injured)

        if incident_witness_data is not None:
            instance.incident_witness.clear()
            for witness_data in incident_witness_data:
                witness, _ = IncidentWitness.objects.get_or_create(**witness_data)
                instance.incident_witness.add(witness)

        instance.save()
        return instance


# serializers for updating/moving to the next steps
class WorkPlaceViolenceStepsSerializer(serializers.ModelSerializer):
    person_injured = GetProfileSerializer()
    reported_by = GetProfileSerializer()
    name_of_supervisor = GetProfileSerializer()

    class Meta:
        model = WorkPlaceViolence
        fields = "__all__"

    def update(self, instance, validated_data):
        if "person_injured" in validated_data:
            person_injured_data = validated_data.pop("person_injured")
            person_injured_profile, message = get_patient_profile(
                data=person_injured_data, facility=instance.report_facility
            )
            if person_injured_profile:
                instance.person_injured = person_injured_profile

        if "reported_by" in validated_data:
            reported_by_data = validated_data.pop("reported_by")
            reported_by_profile, message = get_patient_profile(
                data=reported_by_data, facility=instance.report_facility
            )
            if reported_by_profile:
                instance.reported_by = reported_by_profile

        if "name_of_supervisor" in validated_data:
            name_of_supervisor_data = validated_data.pop("name_of_supervisor")
            name_of_supervisor_profile, message = get_patient_profile(
                data=name_of_supervisor_data, facility=instance.report_facility
            )
            if name_of_supervisor_profile:
                instance.name_of_supervisor = name_of_supervisor_profile

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()

        return instance
