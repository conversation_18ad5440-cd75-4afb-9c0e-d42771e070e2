from accounts.services.user_profile.service import UserProfileService
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from workplace_violence_reports.models import (
    IncidentPersonInjured,
    WorkPlaceViolence,
    WorkPlaceViolenceVersion,
)

user_profile_service = UserProfileService()
logging_service = LoggingService()


class WorkplaceViolenceUtils:

    def handle_person_injured(
        self, data, incident: WorkPlaceViolenceVersion
    ) -> RepositoryResponse:
        """
        Handle and create records for multiple injured persons in an incident.

        Args:
            data: Dict with key 'persons_injured', a list of dicts with injured person info
            incident: WorkPlaceViolence incident object

        Returns:
            RepositoryResponse: with incident, success boolean, and message string
        """
        persons = []

        try:
            # Process each person
            if len(data["persons_injured"]) < 1:
                incident.persons_injured.all().delete()
                incident.there_were_injuries = "No"
                incident.save()
                return RepositoryResponse(
                    data=incident,
                    success=True,
                    message="Successfully deleted all injured persons",
                )
            for person_dict in data["persons_injured"]:
                # Validate user data
                injury_description = person_dict.pop("injury_description", None)
                person_profile = user_profile_service.get_or_create_profile(person_dict)
                if not person_profile.success:
                    return RepositoryResponse(
                        data=None,
                        success=False,
                        message=person_profile.message,
                    )
                # Get or create person injured record
                person_injured, _ = IncidentPersonInjured.objects.get_or_create(
                    person=person_profile.data,
                    injury_description=injury_description,
                )
                persons.append(person_injured)
            # Update incident if we have any successful entries
            if persons:
                incident.persons_injured.set(persons)
                incident.there_were_injuries = "Yes"
                incident.save()
                return RepositoryResponse(
                    data=incident,
                    success=True,
                    message="Successfully updated all injured persons",
                )
            else:
                return RepositoryResponse(
                    data=None,
                    success=False,
                    message="Failed to process any injured persons.",
                )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message=f"Unexpected error",
            )

    def handle_involved_parties(
        self, data, incident: WorkPlaceViolenceVersion
    ) -> RepositoryResponse:
        """
        Handle and create records for multiple involved parties (initiated_by) in an incident.

        Args:
            data: Dict with key 'initiated_by', a list of dicts with party info
            incident: WorkPlaceViolence incident object

        Returns:
            RepositoryResponse: with incident, success boolean, and message string
        """
        from workplace_violence_reports.models import IncidentInvolvedParty

        if isinstance(data, dict):
            initiated_by_list = data.get("involved_parties", [])
        else:
            initiated_by_list = data
        initiators = []
        failed_initiators = []
        try:
            if len(initiated_by_list) < 1:
                incident.involved_party.clear()
                incident.save()
                return RepositoryResponse(
                    data=incident,
                    success=True,
                    message="Successfully removed incident involved parties",
                )
            for item in initiated_by_list:
                party_data = item.get("party", {})
                party_type = item.pop("party_type", "Victim")
                title = item.pop("title", "")
                assailant_relationship_to_patient = item.pop(
                    "assailant_relationship_to_patient", ""
                )
                assailant_background = item.pop("assailant_background", "")
                # Get or create profile

                profile_response = user_profile_service.get_or_create_profile(item)
                if not profile_response.success:
                    failed_initiators.append(profile_response.message)
                    continue
                profile = profile_response.data
                # Get or create IncidentInvolvedParty
                involved_party, _ = IncidentInvolvedParty.objects.get_or_create(
                    party=profile,
                    party_type=party_type,
                    defaults={
                        "title": title,
                        "assailant_relationship_to_patient": assailant_relationship_to_patient,
                        "assailant_background": assailant_background,
                    },
                )
                initiators.append(involved_party)
            if initiators:
                incident.involved_parties.set(initiators)
                incident.save()
            if failed_initiators and initiators:
                return RepositoryResponse(
                    data=incident,
                    success=True,
                    message=f"Some parties failed: {'; '.join(failed_initiators)}",
                )
            elif failed_initiators:
                return RepositoryResponse(
                    data=None,
                    success=False,
                    message=f"All parties failed: {'; '.join(failed_initiators)}",
                )
            return RepositoryResponse(
                data=incident,
                success=True,
                message="Successfully edited incident involved parties",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Unexpected error while handling initiated_by",
            )

    def handle_incident_witnesses(
        self, data, incident: WorkPlaceViolenceVersion
    ) -> RepositoryResponse:
        """
        Handle and create records for multiple witnesses in an incident.

        Args:
            data: Dict with key 'incident_witnesses', a list of dicts with witness info
            incident: WorkPlaceViolence incident object

        Returns:
            RepositoryResponse: with incident, success boolean, and message string
        """
        from workplace_violence_reports.models import IncidentWitness

        if isinstance(data, dict):
            witnesses_list = data.get("incident_witnesses", [])
        else:
            witnesses_list = data
        witnesses = []
        failed_witnesses = []
        try:
            if len(witnesses_list) < 1:
                incident.incident_witness.clear()
                incident.save()
                return RepositoryResponse(
                    data=incident,
                    success=True,
                    message="Successfully removed all incident witnesses",
                )
            for item in witnesses_list:
                witness_data = item.get("witness", {})
                # Get or create profile
                profile_response = user_profile_service.get_or_create_profile(item)
                if not profile_response.success:
                    failed_witnesses.append(profile_response.message)
                    continue
                profile = profile_response.data
                # Get or create IncidentWitness
                witness, _ = IncidentWitness.objects.get_or_create(
                    witness=profile,
                )
                witnesses.append(witness)
            if witnesses:
                incident.incident_witness.set(witnesses)
                incident.save()
            if failed_witnesses and witnesses:
                return RepositoryResponse(
                    data=incident,
                    success=True,
                    message=f"Some witnesses failed: {'; '.join(failed_witnesses)}",
                )
            elif failed_witnesses:
                return RepositoryResponse(
                    data=None,
                    success=False,
                    message=f"All witnesses failed: {'; '.join(failed_witnesses)}",
                )
            return RepositoryResponse(
                data=incident,
                success=True,
                message="Successfully edited incident witnesses",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Unexpected error while handling incident witnesses",
            )
