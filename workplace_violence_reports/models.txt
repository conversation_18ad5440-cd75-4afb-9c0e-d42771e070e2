from django.db import models

from accounts.models import Profile
from base.models import BaseModel
from documents.models import Document
from base.models import Facility
from base.models import Department
from reviews.models import Review


class TerminationOfIncident(BaseModel):
    description = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.description


class IncidentInvolvedParty(BaseModel):
    PARTY_TYPE_CHOICES = [
        ("Assailant", "Assailant"),
        ("<PERSON>ti<PERSON>", "Victim"),
        ("Witness", "Witness"),
    ]

    party_type = models.Char<PERSON>ield(
        max_length=244, choices=PARTY_TYPE_CHOICES, default="Victim"
    )
    party = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="person_injured",
    )
    title = models.CharField(max_length=255, null=True, blank=True)
    assailant_relationship_to_patient = models.Char<PERSON>ield(
        max_length=255, null=True, blank=True
    )
    assailant_background = models.Cha<PERSON><PERSON><PERSON>(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.name


class IncidentPersonInjured(BaseModel):
    person = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    injury_description = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.name


class IncidentWitness(BaseModel):
    witness = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    def __str__(self):
        return self.name


# we need to add search indexes to optimize performance during searching and querying
class WorkPlaceViolenceBase(BaseModel):
    INCIDENT_TYPE_CHOICES = [
        ("Type 1", "Type 1"),
        ("Type 2", "Type 2"),
        ("Type 3", "Type 3"),
        ("Type 4", "Type 4"),
        ("Type 5", "Type 5"),
    ]
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES,
        max_length=255,
        default="Draft",
    )
    report_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    type_of_incident = models.CharField(max_length=244, null=True, blank=True)
    physical_injury_description = models.CharField(
        max_length=255, null=True, blank=True
    )

    incident_type = models.CharField(
        max_length=255, choices=INCIDENT_TYPE_CHOICES, default="Type 1"
    )

    date_of_incident = models.DateField(null=True, blank=True)
    time_of_incident = models.TimeField(null=True, blank=True)
    description = models.TextField(null=True, blank=True, max_length=1000)

    # part 2
    initiated_by = models.ManyToManyField(IncidentInvolvedParty, blank=True)

    # part 3
    type_of_contact = models.CharField(max_length=255, null=True, blank=True)
    victim_was_alone = models.BooleanField(default=False)
    location = models.CharField(max_length=255, blank=True, null=True)
    there_was_threats_before = models.CharField(max_length=255, blank=True, null=True)
    staff_member_reported = models.CharField(max_length=255, blank=True, null=True)
    weapons_were_involved = models.BooleanField(default=False)
    weapon_used = models.CharField(max_length=255, null=True, blank=True)
    there_were_injuries = models.CharField(max_length=255, blank=True, null=True)
    persons_injured = models.ManyToManyField(IncidentPersonInjured, blank=True)
    incident_witness = models.ManyToManyField(IncidentWitness, blank=True)
    notification = models.CharField(max_length=255, null=True, blank=True)

    # part 4
    termination_of_incident = models.CharField(max_length=10000, null=True, blank=True)
    victim_has_contact_with_assailant = models.CharField(
        max_length=255, null=True, blank=True
    )
    # part 6
    immediate_supervisor = models.BooleanField(default=False)
    name_of_supervisor = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="name_of_supervisor",
    )
    title_of_supervisor = models.CharField(max_length=255, null=True, blank=True)
    date_notified = models.DateField(null=True, blank=True)
    time_notified = models.TimeField(null=True, blank=True)
    action_taken = models.CharField(max_length=255, null=True, blank=True)
    prevention_suggestion = models.CharField(max_length=255, null=True, blank=True)

    # part 7
    reported_by = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="reported_by",
    )
    reported_by_title = models.CharField(max_length=255, null=True, blank=True)
    date_reported = models.DateField(null=True, blank=True)
    time_reported = models.TimeField(null=True, blank=True)
    reviews = models.ManyToManyField(
        Review, related_name="workplace_violence_reviews_field", blank=True
    )
    documents = models.ManyToManyField(
        Document, related_name="workplace_violence_incident_documents_field", blank=True
    )
    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )

    is_resolved = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.initiated_by} - {self.type_of_incident}"


class WorkPlaceViolence(WorkPlaceViolenceBase):
    is_modified = models.BooleanField(default=False)


class WorkPlaceViolenceVersion(WorkPlaceViolenceBase):
    original_report = models.ForeignKey(
        WorkPlaceViolence,
        on_delete=models.CASCADE,
        related_name="versions",
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "workplace_violence_version"
