from rest_framework import serializers
from accounts.serializers import UserSerializer
from facilities.serializers import FacilitySerializer
from base.models import Department, BaseModel
from django.contrib.auth.models import Permission, Group, User


class DepartmentNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ("name",)


class DepartmentSerializer(serializers.ModelSerializer):
    parent = DepartmentNestedSerializer()
    header_of_department = UserSerializer()
    updated_by = UserSerializer()
    facility = FacilitySerializer()
    members = UserSerializer(many=True)

    class Meta:
        model = Department
        fields = "__all__"


class UpdateDepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = "__all__"


class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = ["id", "name", "codename"]


class GroupSerializer(serializers.ModelSerializer):
    permissions = PermissionSerializer(many=True, read_only=True)

    class Meta:
        model = Group
        fields = ["id", "name", "permissions"]


class UserNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "first_name", "last_name", "email"]


class BaseModelSerializer(serializers.ModelSerializer):
    created_by = serializers.SerializerMethodField()
    updated_by = serializers.SerializerMethodField()
    assignees = serializers.SerializerMethodField()

    class Meta:
        model = BaseModel
        fields = [
            "id",
            "created_by",
            "updated_by",
            "created_at",
            "updated_at",
            "assignees",
        ]

    def get_created_by(self, obj):
        if obj and hasattr(obj, 'created_by') and obj.created_by:
            return {
                "id": obj.created_by.id,
                "first_name": obj.created_by.first_name,
                "last_name": obj.created_by.last_name,
                "email": obj.created_by.email,
            }
        return None

    def get_updated_by(self, obj):
        if obj and hasattr(obj, 'updated_by') and obj.updated_by:
            return {
                "id": obj.updated_by.id,
                "first_name": obj.updated_by.first_name,
                "last_name": obj.updated_by.last_name,
                "email": obj.updated_by.email,
            }
        return None

    def get_assignees(self, obj):
        if obj and hasattr(obj, 'assignees'):
            return [
                {
                    "id": assignee.id,
                    "first_name": assignee.first_name,
                    "last_name": assignee.last_name,
                    "email": assignee.email,
                }
                for assignee in obj.assignees.all()
            ]
        return []


class IncidentSerializer(BaseModelSerializer):
    review_process = serializers.SerializerMethodField()
