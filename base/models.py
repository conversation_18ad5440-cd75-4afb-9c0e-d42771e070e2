import uuid
from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify

from django.db import models
from django.contrib.auth.models import User, Group

from base.services.permissions.mixins import (
    BasePermissionsMixin,
    IncidentsPermissionsMixin,
)


class BaseModel(models.Model):
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="created_by_who"
    )
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="updated_by_who"
    )
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    assignees = models.ManyToManyField(
        User, related_name="incident_assignees", blank=True
    )

    def __str__(self) -> str:
        if self.created_by and self.created_by.first_name:
            return self.created_by.first_name
        return "No name available"


class GeneralWitness(BaseModel):
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name


class UserNotification(BaseModel):
    NOTIFICATION_TYPE_CHOICES = (
        ("info", "Information"),
        ("warning", "Warning"),
        ("error", "Error"),
    )
    NOTIFICATION_CATEGORY_CHOICES = (
        ("incident", "Incident"),
        ("complaint", "Complaint"),
        ("system", "System"),
        ("other", "Other"),
    )
    notification_type = models.CharField(
        max_length=10, choices=NOTIFICATION_TYPE_CHOICES, default="info"
    )
    notification_category = models.CharField(
        max_length=15, choices=NOTIFICATION_CATEGORY_CHOICES, default="incident"
    )
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    item_id = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return f"{self.notification_type} {self.notification_category}"

    class Meta:
        permissions = IncidentsPermissionsMixin.custom_permissions


# Create your models here.
class Facility(BaseModel):
    name = models.CharField(max_length=500, null=True, blank=True)
    address = models.CharField(max_length=500, null=True, blank=True)
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    facility_type = models.CharField(max_length=255, null=True, blank=True)
    ceo = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
    contact_person = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="facility_contact_person",
    )
    staff_members = models.ManyToManyField(User, related_name="facility_employees")

    def __str__(self):
        return self.name if self.name else "No Facility Name Available"

    class Meta:
        permissions = BasePermissionsMixin.custom_permissions


class Department(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    parent = models.ForeignKey("self", on_delete=models.CASCADE, null=True, blank=True)
    header_of_department = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True
    )
    members = models.ManyToManyField(User, related_name="incident_members")

    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="department_created_by"
    )
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="department_updated_by"
    )
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    facility = models.ForeignKey(
        Facility,
        related_name="department_facility",
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )

    def __str__(self):
        return self.name

    class Meta:
        permissions = BasePermissionsMixin.custom_permissions


class APIKey(BaseModel):
    key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    user = models.ForeignKey(
        "auth.User", on_delete=models.CASCADE, related_name="api_keys"
    )
    is_active = models.BooleanField(default=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.key}"
