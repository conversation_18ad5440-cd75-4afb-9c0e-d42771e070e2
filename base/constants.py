class ReviewStatus:
    """
    Centralized constants for review statuses used across incidents and ADRs.

    Attributes:
        DRAFT: Represents a draft status.
        OPEN: Represents an open status.
        ...
    """

    DRAFT = "Draft"
    OPEN = "Open"
    PENDING_ASSIGNED = "Pending Assigned"
    PENDING_REVIEW = "Pending Review"
    PENDING_APPROVAL = "Pending Approval"
    CLOSED = "Closed"

    CHOICES = [
        (DRAFT, "Draft"),
        (OPEN, "Open"),
        (PENDING_ASSIGNED, "Pending Assigned"),
        (PENDING_REVIEW, "Pending Review"),
        (PENDING_APPROVAL, "Pending Approval"),
        (CLOSED, "Closed"),
    ]

    @classmethod
    def is_valid(cls, status):
        return status in dict(cls.CHOICES)


class TEST_DEFAULTS:
    ACTIVE_EMAIL = "<EMAIL>"
    INACTIVE_EMAIL = "<EMAIL>"
