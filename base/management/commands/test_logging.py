"""
Django management command to test the logging configuration.

Usage: python manage.py test_logging
"""

from django.core.management.base import BaseCommand
from base.services.logging.logger import LoggingService
from base.services.logging.utils import (
    log_info,
    log_error,
    log_warning,
    log_debug,
    log_success,
    log_critical,
)


class Command(BaseCommand):
    help = "Test the logging configuration"

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Testing logging configuration..."))

        # Test LoggingService directly
        logger = LoggingService(logger_name="test_logging")

        # Test all logging levels
        logger.log_info("This is an info message from LoggingService")
        logger.log_warning("This is a warning message from LoggingService")
        logger.log_debug("This is a debug message from LoggingService")
        logger.log_success("This is a success message from LoggingService")
        logger.log_critical("This is a critical message from LoggingService")

        # Test error logging
        try:
            raise ValueError("This is a test exception")
        except Exception as e:
            logger.log_error(e, error_type="TEST_ERROR")

        # Test service operation logging
        logger.log_service_operation(
            service_name="TestService",
            operation="test_operation",
            status="success",
            details="This is a test service operation",
        )

        # Test database operation logging
        logger.log_database_operation(
            model="TestModel", operation="create", record_id=123, status="success"
        )

        # Test utility functions
        log_info(
            "This is an info message from utility function", logger_name="test_utils"
        )
        log_warning(
            "This is a warning message from utility function", logger_name="test_utils"
        )
        log_success(
            "This is a success message from utility function", logger_name="test_utils"
        )

        try:
            raise RuntimeError("This is another test exception")
        except Exception as e:
            log_error(e, error_type="UTIL_TEST_ERROR", logger_name="test_utils")

        self.stdout.write(
            self.style.SUCCESS(
                "Logging test completed! Check the following log files:\n"
                "- logs/application.log (all application logs)\n"
                "- logs/error.log (error logs only)\n"
                "- logs/debug.log (debug logs - if DEBUG=True)\n"
                "- logs/django.log (Django framework logs)\n"
                "Also check the console output above."
            )
        )
