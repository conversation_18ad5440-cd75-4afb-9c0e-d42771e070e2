from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.apps import apps


class Command(BaseCommand):
    help = "Create user groups as defined in userFlow.md if they don't already exist"

    def add_arguments(self, parser):
        parser.add_argument(
            "--list-groups",
            action="store_true",
            help="List all existing groups without creating new ones",
        )
        parser.add_argument(
            "--force-recreate",
            action="store_true",
            help="Delete and recreate all groups (WARNING: This will remove all existing group memberships)",
        )
        parser.add_argument(
            "--show-permissions",
            action="store_true",
            help="Show what each group can do according to userFlow.md",
        )
        parser.add_argument(
            "--check-duplicates",
            action="store_true",
            help="Check for duplicate group names and suggest cleanup",
        )
        parser.add_argument(
            "--assign-permissions",
            action="store_true",
            help="Assign permissions to existing groups based on userGroupPermissions.md",
        )

    def handle(self, *args, **options):
        # Groups defined in userFlow.md with their descriptions
        groups_to_create = [
            {
                "name": "User",
                "description": "Regular users - Draft/submit incident reports, upload documents, view drafts and own reports",
            },
            {
                "name": "Manager",
                "description": "Access unrestricted incidents in assigned dept, add reviews, send to Quality/Risk, list logs (limited), export dept logs",
            },
            {
                "name": "Director",
                "description": "Read-only access to facility/dept incidents, view dashboards/logs, export data",
            },
            {
                "name": "Admin",
                "description": "Full access to facility incidents, modify reports, send to dept, export logs, manage user accounts",
            },
            {
                "name": "Quality/Risk Manager",
                "description": "Full access to all facilities' incidents, modify, add severity, close (except own), assign restricted access",
            },
            {
                "name": "Super user",
                "description": "Full access across all facilities, delete/modify reports/users, configure settings/backups",
            },
            {
                "name": "User Editor",
                "description": "Create/edit/deactivate user accounts, submit own reports (no access to reports/logs/dashboards)",
            },
        ]

        if options["list_groups"]:
            self.list_existing_groups()
            return

        if options["show_permissions"]:
            self.get_group_permissions_info()
            return

        if options["check_duplicates"]:
            self.check_duplicate_groups(groups_to_create)
            return

        if options["assign_permissions"]:
            self.assign_permissions_to_groups()
            return

        if options["force_recreate"]:
            self.force_recreate_groups(groups_to_create)
        else:
            self.create_groups_if_not_exists(groups_to_create)

    def list_existing_groups(self):
        """List all existing groups"""
        self.stdout.write(self.style.HTTP_INFO("Existing Groups:"))
        self.stdout.write(self.style.HTTP_INFO("=" * 50))

        groups = Group.objects.all().order_by("name")
        if not groups:
            self.stdout.write(self.style.WARNING("No groups found in the database."))
            return

        for group in groups:
            user_count = group.user_set.count()
            self.stdout.write(f"• {group.name} ({user_count} users)")

        self.stdout.write(self.style.HTTP_INFO("=" * 50))
        self.stdout.write(f"Total groups: {groups.count()}")

    def create_groups_if_not_exists(self, groups_to_create):
        """Create groups if they don't already exist"""
        self.stdout.write(
            self.style.HTTP_INFO("Creating user groups from userFlow.md...")
        )
        self.stdout.write(self.style.HTTP_INFO("=" * 60))

        created_count = 0
        existing_count = 0

        with transaction.atomic():
            for group_data in groups_to_create:
                group_name = group_data["name"]
                group_description = group_data["description"]

                group, created = Group.objects.get_or_create(name=group_name)

                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f"✓ Created group: '{group_name}'")
                    )
                    self.stdout.write(
                        self.style.HTTP_INFO(f"  Description: {group_description}")
                    )
                else:
                    existing_count += 1
                    user_count = group.user_set.count()
                    self.stdout.write(
                        self.style.WARNING(
                            f"• Group already exists: '{group_name}' ({user_count} users)"
                        )
                    )

        self.stdout.write(self.style.HTTP_INFO("=" * 60))
        self.stdout.write(self.style.SUCCESS(f"✓ Created {created_count} new groups"))
        self.stdout.write(
            self.style.WARNING(f"• Found {existing_count} existing groups")
        )
        self.stdout.write(
            self.style.HTTP_INFO(f"Total groups: {created_count + existing_count}")
        )

        self.stdout.write("\n")
        self.assign_permissions_to_groups()

        if created_count > 0:
            self.stdout.write(
                "\n" + self.style.HTTP_INFO("Groups successfully created! You can now:")
            )
            self.stdout.write(
                "• Assign users to appropriate groups through Django admin"
            )
            self.stdout.write(
                "• Use the permissions system with role-based access control"
            )
            self.stdout.write("• Test permissions with the test commands")

    def force_recreate_groups(self, groups_to_create):
        """Delete all existing groups and recreate them (WARNING: removes all group memberships)"""
        self.stdout.write(
            self.style.WARNING(
                "⚠️  WARNING: This will delete ALL existing groups and group memberships!"
            )
        )
        self.stdout.write(
            self.style.WARNING(
                "⚠️  All users will be removed from their current groups!"
            )
        )

        confirm = input(
            "Are you sure you want to continue? Type 'DELETE ALL GROUPS' to confirm: "
        )

        if confirm != "DELETE ALL GROUPS":
            self.stdout.write(self.style.ERROR("Operation cancelled."))
            return

        with transaction.atomic():
            # Delete all existing groups
            deleted_count = Group.objects.count()
            Group.objects.all().delete()

            self.stdout.write(
                self.style.WARNING(f"🗑️  Deleted {deleted_count} existing groups")
            )

            # Create all groups fresh
            self.stdout.write(self.style.HTTP_INFO("Creating fresh groups..."))

            for group_data in groups_to_create:
                group_name = group_data["name"]
                group_description = group_data["description"]

                Group.objects.create(name=group_name)
                self.stdout.write(
                    self.style.SUCCESS(f"✓ Created group: '{group_name}'")
                )
                self.stdout.write(
                    self.style.HTTP_INFO(f"  Description: {group_description}")
                )

        self.stdout.write(
            "\n" + self.style.SUCCESS("✓ All groups recreated successfully!")
        )

        self.stdout.write("\n")
        self.assign_permissions_to_groups()

        self.stdout.write(
            "\n" + self.style.WARNING(
                "⚠️  Remember to reassign users to their appropriate groups."
            )
        )

    def get_group_permissions_info(self):
        """Display information about what each group can do"""
        self.stdout.write(
            "\n" + self.style.HTTP_INFO("Group Permissions Summary (from userFlow.md):")
        )
        self.stdout.write(self.style.HTTP_INFO("=" * 70))

        permissions_info = {
            "User": [
                "• Draft/submit incident reports",
                "• Upload documents",
                "• View drafts and own reports",
                "• No access to others' reports",
                "• No modification/deletion rights",
            ],
            "Manager": [
                "• Access unrestricted incidents in assigned dept",
                "• Add reviews, send to Quality/Risk",
                "• List logs (limited), export dept logs",
                "• Cannot close incidents",
                "• No restricted info without approval",
            ],
            "Director": [
                "• Read-only access to facility/dept incidents",
                "• View dashboards/logs, export data",
                "• No editing/modification rights",
                "• Restricted info requires approval",
            ],
            "Admin": [
                "• Full access to facility incidents",
                "• Modify reports, send to dept",
                "• Export logs, manage user accounts",
                "• Cannot close own incidents",
                "• Facility-bound",
            ],
            "Quality/Risk Manager": [
                "• Full access to all facilities' incidents",
                "• Modify, add severity, close (except own)",
                "• Assign restricted access",
                "• Cannot close own incidents",
            ],
            "Super User": [
                "• Full access across all facilities",
                "• Delete/modify reports/users",
                "• Configure settings/backups",
                "• No restrictions",
            ],
            "User Editor": [
                "• Create/edit/deactivate user accounts",
                "• Submit own reports",
                "• No access to reports/logs/dashboards",
            ],
        }

        for group_name, permissions in permissions_info.items():
            self.stdout.write(f"\n{self.style.HTTP_REDIRECT(group_name)}:")
            for permission in permissions:
                self.stdout.write(f"  {permission}")

    def check_duplicate_groups(self, groups_to_create):
        """Check for duplicate or similar group names and suggest cleanup"""
        self.stdout.write(
            self.style.HTTP_INFO("Checking for duplicate or similar groups...")
        )
        self.stdout.write(self.style.HTTP_INFO("=" * 60))

        # Get all existing groups
        existing_groups = list(Group.objects.all().values_list("name", flat=True))
        required_groups = [group["name"] for group in groups_to_create]

        # Check for exact duplicates
        duplicates_found = False
        potential_renames = []

        for required_group in required_groups:
            matches = [
                group
                for group in existing_groups
                if group.lower() == required_group.lower() and group != required_group
            ]
            if matches:
                duplicates_found = True
                for match in matches:
                    user_count = Group.objects.get(name=match).user_set.count()
                    self.stdout.write(
                        self.style.WARNING(
                            f"⚠️  Similar group found: '{match}' ({user_count} users)"
                        )
                    )
                    self.stdout.write(
                        self.style.HTTP_INFO(f"   Required group: '{required_group}'")
                    )
                    if user_count > 0:
                        potential_renames.append((match, required_group, user_count))

        if not duplicates_found:
            self.stdout.write(self.style.SUCCESS("✓ No duplicate groups found!"))
            return

        self.stdout.write("\n" + self.style.HTTP_INFO("Suggested Actions:"))
        self.stdout.write(self.style.HTTP_INFO("-" * 40))

        if potential_renames:
            self.stdout.write(
                "\n" + self.style.WARNING("Groups with users that could be renamed:")
            )
            for old_name, new_name, user_count in potential_renames:
                self.stdout.write(
                    f"• Rename '{old_name}' to '{new_name}' ({user_count} users)"
                )
                self.stdout.write(
                    f"  SQL: UPDATE auth_group SET name='{new_name}' WHERE name='{old_name}';"
                )

        empty_groups = [
            group
            for group in existing_groups
            if group not in required_groups
            and Group.objects.get(name=group).user_set.count() == 0
        ]

        if empty_groups:
            self.stdout.write(
                "\n" + self.style.HTTP_INFO("Empty groups that could be deleted:")
            )
            for group in empty_groups:
                self.stdout.write(f"• '{group}' (0 users)")

        self.stdout.write(
            "\n"
            + self.style.WARNING(
                "⚠️  Always backup your database before making changes!"
            )
        )
        self.stdout.write(
            "💡 Consider using Django admin or a data migration for these changes."
        )

    def get_group_permissions_mapping(self):
        """
        Returns the permission mapping for each group based on userGroupPermissions.md
        """
        return {
            "User": [
                'view_list',   
                'view_details',
            ],
            "Manager": [
                'view_list', 
                'view_details',
                'export_list',       
                'export',            
                'add_review',        
                'assign_incident',     
                'change_incident',
                'send_for_review',   
                'send_to_department', 
                'view_users_incidents',
            ],
            "Director": [
                'view_list',   
                'view_details',
                'export_list',        
                'export',             
                'view_users_incidents', 
            ],
            "Admin": [
                'view_list',             
                'view_details',          
                'export_list',      
                'export',           
                'add_review',         
                'assign_incident',
                'close_incident',
                'rate_severity',       
                'change_incident',      
                'send_for_review',     
                'mark_as_resolved',     
                'send_to_department',     
                'view_users_incidents',
            ],
            "Quality/Risk Manager": [
                'view_list',    
                'view_details', 
                'export_list'
                'export',    
                'add_review',   
                'assign_incident', 
                'close_incidint',
                'rate_severity', 
                'change_incident',   
                'send_for_review'
                'mark_as_resolved',
                'send_to_department',
                'view_users_incidents',   
            ],
            "Super user": [
                'view_list',    
                'view_details', 
                'export_list',
                'export',     
                'add_review',    
                'assign_incident',  
                'close_incident'
                'rate_severity',  
                'change_incident',    
                'change_incident',    
                'change_incident',    
                'change_incident',    
                'send_for_review',
                'mark_as_resolved',
                'send_to_department',
                'delete_incident',
                'view_users_incidents',
            ],
            "User Editor": [
                'view_list',
                'view_details',
            ],
        }

    def assign_permissions_to_groups(self):
        """
        Assign permissions to groups based on the permission mapping
        """
        self.stdout.write(
            self.style.HTTP_INFO("Assigning permissions to groups...")
        )
        self.stdout.write(self.style.HTTP_INFO("=" * 60))

        permissions_mapping = self.get_group_permissions_mapping()

        models_with_permissions = []
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                if hasattr(model._meta, 'permissions') and model._meta.permissions:
                    permission_codes = [perm[0] for perm in model._meta.permissions]
                    if any(code in ['view_list', 'view_details', 'export_list', 'export',
                                   'add_review', 'assign_incident', 'close_incident',
                                   'rate_severity', 'change_incident', 'send_for_review',
                                   'delete_incident', 'view_users_incidents', 'mark_as_resolved',
                                   'send_to_department']
                           for code in permission_codes):
                        models_with_permissions.append(model)

        total_assigned = 0
        total_skipped = 0

        for group_name, permission_codes in permissions_mapping.items():
            try:
                group = Group.objects.get(name=group_name)
                self.stdout.write(f"\nAssigning permissions to '{group_name}':")

                group_assigned = 0
                group_skipped = 0

                for model in models_with_permissions:
                    content_type = ContentType.objects.get_for_model(model)
                    model_name = f"{model._meta.app_label}.{model._meta.model_name}"

                    for permission_code in permission_codes:
                        try:
                            permission = Permission.objects.get(
                                codename=permission_code,
                                content_type=content_type
                            )

                            if not group.permissions.filter(id=permission.id).exists():
                                group.permissions.add(permission)
                                group_assigned += 1
                                self.stdout.write(
                                    f"  ✓ Added '{permission_code}' for {model_name}"
                                )
                            else:
                                group_skipped += 1

                        except Permission.DoesNotExist:
                            continue

                total_assigned += group_assigned
                total_skipped += group_skipped

                self.stdout.write(
                    f"  Summary: {group_assigned} permissions assigned, {group_skipped} already existed"
                )

            except Group.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"Group '{group_name}' not found. Create groups first.")
                )

        self.stdout.write(self.style.HTTP_INFO("=" * 60))
        self.stdout.write(
            self.style.SUCCESS(f"✓ Total permissions assigned: {total_assigned}")
        )
        self.stdout.write(
            self.style.WARNING(f"• Total permissions already existed: {total_skipped}")
        )
