from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group
from django.db import transaction
from accounts.models import Profile, Title
from base.models import Facility, Department
from datetime import date
import random


class Command(BaseCommand):
    help = "Create comprehensive test users for all roles defined in userFlow.md"

    def add_arguments(self, parser):
        parser.add_argument(
            "--delete-existing",
            action="store_true",
            help="Delete existing test users before creating new ones",
        )
        parser.add_argument(
            "--list-users",
            action="store_true",
            help="List all existing test users",
        )

    def handle(self, *args, **options):
        if options["list_users"]:
            self.list_existing_users()
            return

        if options["delete_existing"]:
            self.delete_existing_test_users()

        self.stdout.write(
            self.style.HTTP_INFO(
                "Creating comprehensive test users based on userFlow.md..."
            )
        )
        self.stdout.write(self.style.HTTP_INFO("=" * 70))

        with transaction.atomic():
            # Create facilities and departments first
            self.create_test_infrastructure()

            # Create all user types
            self.create_test_users()

        self.stdout.write(
            self.style.SUCCESS("\n✅ All test users created successfully!")
        )
        self.stdout.write(
            self.style.HTTP_INFO("📋 Check testUser.md for complete user details")
        )

    def delete_existing_test_users(self):
        """Delete existing test users"""
        self.stdout.write(self.style.WARNING("Deleting existing test users..."))

        # Delete test users (those with test emails)
        test_users = User.objects.filter(email__contains="@email.com")
        count = test_users.count()
        test_users.delete()

        self.stdout.write(self.style.SUCCESS(f"✓ Deleted {count} existing test users"))

    def list_existing_users(self):
        """List all existing test users"""
        self.stdout.write(self.style.HTTP_INFO("Existing Test Users:"))
        self.stdout.write(self.style.HTTP_INFO("=" * 50))

        test_users = User.objects.filter(email__contains="@email.com").order_by(
            "username"
        )

        if not test_users:
            self.stdout.write(self.style.WARNING("No test users found"))
            return

        for user in test_users:
            groups = ", ".join([g.name for g in user.groups.all()])
            profile = getattr(user, "profile", None)
            facility = profile.facility.name if profile and profile.facility else "N/A"
            department = (
                profile.department.name if profile and profile.department else "N/A"
            )

            self.stdout.write(f"• {user.username} ({user.email})")
            self.stdout.write(f"  Groups: {groups}")
            self.stdout.write(f"  Facility: {facility}")
            self.stdout.write(f"  Department: {department}")
            self.stdout.write("")

    def create_test_infrastructure(self):
        """Create test facilities and departments"""
        self.stdout.write("Creating test infrastructure...")

        # Create test facilities
        facilities_data = [
            {
                "name": "Central Hospital",
                "facility_type": "Hospital",
                "address": "123 Main St, City Center",
            },
            {
                "name": "North Clinic",
                "facility_type": "Clinic",
                "address": "456 North Ave, Northside",
            },
            {
                "name": "South Medical Center",
                "facility_type": "Medical Center",
                "address": "789 South Blvd, Southside",
            },
        ]

        self.facilities = []
        for fac_data in facilities_data:
            facility, created = Facility.objects.get_or_create(
                name=fac_data["name"],
                defaults={
                    "facility_type": fac_data["facility_type"],
                    "address": fac_data["address"],
                    "phone_number": f"555-{random.randint(1000, 9999)}",
                    "email": f"{fac_data['name'].lower().replace(' ', '_')}@hospital.com",
                },
            )
            self.facilities.append(facility)
            if created:
                self.stdout.write(f"  ✓ Created facility: {facility.name}")

        # Create test departments
        departments_data = [
            {"name": "Emergency Department", "description": "Emergency care services"},
            {"name": "Pharmacy", "description": "Medication management and dispensing"},
            {"name": "Human Resources", "description": "HR and employee services"},
            {"name": "Nursing", "description": "Patient care and nursing services"},
            {"name": "Administration", "description": "Hospital administration"},
            {
                "name": "Quality Assurance",
                "description": "Quality control and risk management",
            },
            {"name": "IT Department", "description": "Information technology services"},
        ]

        self.departments = []
        for dept_data in departments_data:
            for facility in self.facilities:
                department, created = Department.objects.get_or_create(
                    name=dept_data["name"],
                    facility=facility,
                    defaults={"description": dept_data["description"]},
                )
                self.departments.append(department)
                if created:
                    self.stdout.write(
                        f"  ✓ Created department: {department.name} at {facility.name}"
                    )

        # Create test titles
        titles_data = [
            {"name": "Chief Executive Officer", "description": "Hospital CEO"},
            {"name": "Department Manager", "description": "Department head"},
            {
                "name": "Registered Nurse",
                "description": "Licensed nursing professional",
            },
            {"name": "Staff Member", "description": "General staff position"},
            {"name": "Quality Manager", "description": "Quality assurance manager"},
            {"name": "Risk Manager", "description": "Risk management professional"},
        ]

        for title_data in titles_data:
            title, created = Title.objects.get_or_create(
                name=title_data["name"],
                defaults={"description": title_data["description"]},
            )
            if created:
                self.stdout.write(f"  ✓ Created title: {title.name}")

    def create_test_users(self):
        """Create test users for all roles"""

        # User data based on userFlow.md roles
        user_roles_data = [
            {
                "role": "User",
                "users": [
                    {
                        "username": "user1",
                        "first_name": "John",
                        "last_name": "Doe",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "user2",
                        "first_name": "Jane",
                        "last_name": "Smith",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "user3",
                        "first_name": "Mike",
                        "last_name": "Johnson",
                        "email": "<EMAIL>",
                    },
                ],
            },
            {
                "role": "Manager",
                "users": [
                    {
                        "username": "manager1",
                        "first_name": "Sarah",
                        "last_name": "Wilson",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "manager2",
                        "first_name": "David",
                        "last_name": "Brown",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "pharmacy_manager",
                        "first_name": "Lisa",
                        "last_name": "Garcia",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "hr_manager",
                        "first_name": "Robert",
                        "last_name": "Taylor",
                        "email": "<EMAIL>",
                    },
                ],
            },
            {
                "role": "Director",
                "users": [
                    {
                        "username": "director1",
                        "first_name": "Michael",
                        "last_name": "Davis",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "director2",
                        "first_name": "Jennifer",
                        "last_name": "Miller",
                        "email": "<EMAIL>",
                    },
                ],
            },
            {
                "role": "Admin",
                "users": [
                    {
                        "username": "admin1",
                        "first_name": "Christopher",
                        "last_name": "Anderson",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "admin2",
                        "first_name": "Amanda",
                        "last_name": "Thomas",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "admin3",
                        "first_name": "Kevin",
                        "last_name": "Jackson",
                        "email": "<EMAIL>",
                    },
                ],
            },
            {
                "role": "Quality/Risk Manager",
                "users": [
                    {
                        "username": "quality_manager1",
                        "first_name": "Jessica",
                        "last_name": "White",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "risk_manager1",
                        "first_name": "Daniel",
                        "last_name": "Harris",
                        "email": "<EMAIL>",
                    },
                ],
            },
            {
                "role": "Super User",
                "users": [
                    {
                        "username": "superuser1",
                        "first_name": "Elizabeth",
                        "last_name": "Martin",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "superuser2",
                        "first_name": "William",
                        "last_name": "Thompson",
                        "email": "<EMAIL>",
                    },
                ],
            },
            {
                "role": "User Editor",
                "users": [
                    {
                        "username": "user_editor1",
                        "first_name": "Ashley",
                        "last_name": "Garcia",
                        "email": "<EMAIL>",
                    },
                    {
                        "username": "user_editor2",
                        "first_name": "Ryan",
                        "last_name": "Martinez",
                        "email": "<EMAIL>",
                    },
                ],
            },
        ]

        self.created_users = []

        for role_data in user_roles_data:
            role_name = role_data["role"]
            self.stdout.write(f"\nCreating {role_name} users...")

            # Ensure the group exists
            group, created = Group.objects.get_or_create(name=role_name)
            if created:
                self.stdout.write(f"  ✓ Created group: {role_name}")

            for user_data in role_data["users"]:
                user = self.create_user_with_profile(user_data, role_name)
                if user:
                    # Add user to the appropriate group
                    user.groups.add(group)
                    self.created_users.append(
                        {
                            "user": user,
                            "role": role_name,
                            "profile": getattr(user, "profile", None),
                        }
                    )
                    self.stdout.write(
                        f"  ✓ Created {role_name}: {user.username} ({user.email})"
                    )

        self.stdout.write(
            f"\n📊 Summary: Created {len(self.created_users)} test users across {len(user_roles_data)} roles"
        )

    def create_user_with_profile(self, user_data, role):
        """Create a user with associated profile"""

        # Create User
        user, created = User.objects.get_or_create(
            username=user_data["username"],
            defaults={
                "first_name": user_data["first_name"],
                "last_name": user_data["last_name"],
                "email": user_data["email"],
                "is_active": True,
            },
        )

        if not created:
            return user  # User already exists

        # Set password
        user.set_password("password@1234")
        user.save()

        # Assign facility and department based on role
        facility = self.assign_facility_by_role(role)
        department = self.assign_department_by_role(role, facility)
        title = self.assign_title_by_role(role)

        # Create Profile
        profile, profile_created = Profile.objects.get_or_create(
            user=user,
            defaults={
                "facility": facility,
                "department": department,
                "title": title,
                "is_test_account": True,
                "address": f"{random.randint(100, 999)} Test St",
                "city": "Test City",
                "state": "TS",
                "zip_code": f"{random.randint(10000, 99999)}",
                "phone_number": f"555-{random.randint(1000, 9999)}",
                "birth_country": "United States",
                "gender": random.choice(["Male", "Female", "Other"]),
                "date_of_birth": date(
                    random.randint(1970, 2000),
                    random.randint(1, 12),
                    random.randint(1, 28),
                ),
                "age": random.randint(25, 55),
                "created_by": user,
            },
        )

        # Set access permissions
        if role in ["Admin", "Quality/Risk Manager", "Super User"]:
            # Full facility access
            profile.access_to_facilities.set(self.facilities)
        elif role in ["Director"]:
            # Single facility access
            profile.access_to_facilities.add(facility)
        elif role in ["Manager"]:
            # Department access
            profile.access_to_department.add(department)

        return user

    def assign_facility_by_role(self, role):
        """Assign facility based on role"""
        if role == "Super User":
            return self.facilities[0]  # Central Hospital for super users
        elif role in ["Quality/Risk Manager", "Admin"]:
            return self.facilities[0]  # Central Hospital for high-level roles
        else:
            return random.choice(self.facilities)

    def assign_department_by_role(self, role, facility):
        """Assign department based on role and facility"""
        facility_departments = [d for d in self.departments if d.facility == facility]

        if role == "Manager":
            # Assign specific departments to managers
            if "pharmacy" in role.lower():
                return next(
                    (d for d in facility_departments if "Pharmacy" in d.name),
                    facility_departments[0],
                )
            elif "hr" in role.lower():
                return next(
                    (d for d in facility_departments if "Human Resources" in d.name),
                    facility_departments[0],
                )
            else:
                return random.choice(facility_departments)
        elif role in ["Quality/Risk Manager"]:
            return next(
                (d for d in facility_departments if "Quality" in d.name),
                facility_departments[0],
            )
        elif role in ["Admin", "Super User"]:
            return next(
                (d for d in facility_departments if "Administration" in d.name),
                facility_departments[0],
            )
        else:
            return random.choice(facility_departments)

    def assign_title_by_role(self, role):
        """Assign title based on role"""
        title_mapping = {
            "Super User": "Chief Executive Officer",
            "Quality/Risk Manager": "Quality Manager",
            "Admin": "Department Manager",
            "Director": "Department Manager",
            "Manager": "Department Manager",
            "User": "Staff Member",
            "User Editor": "Staff Member",
        }

        title_name = title_mapping.get(role, "Staff Member")
        return Title.objects.filter(name=title_name).first()
