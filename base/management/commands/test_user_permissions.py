from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from base.services.permissions.manage_permissions import PermissionsManagement
from accounts.models import Profile
from tasks.models import ReviewProcessTasks


class Command(BaseCommand):
    help = "Test the user permissions by app endpoint"

    def add_arguments(self, parser):
        parser.add_argument(
            "--create-test-user",
            action="store_true",
            help="Create a test user with permissions",
        )
        parser.add_argument(
            "--test-user-id",
            type=int,
            help="Test user ID to get permissions for",
        )
        parser.add_argument(
            "--test-all-permissions",
            action="store_true",
            help="Test the all possible permissions endpoint",
        )

    def handle(self, *args, **options):
        permissions_management = PermissionsManagement()

        if options["create_test_user"]:
            self.create_test_user()

        if options["test_all_permissions"]:
            self.test_all_permissions(permissions_management)

        if options["test_user_id"]:
            self.test_user_permissions(options["test_user_id"], permissions_management)
        elif not options["test_all_permissions"]:
            # If no specific user ID and not testing all permissions, test with any existing user
            users = User.objects.all()[:1]
            if users:
                self.test_user_permissions(users[0].id, permissions_management)
            else:
                self.stdout.write(
                    self.style.WARNING(
                        "No users found. Use --create-test-user to create one."
                    )
                )

    def create_test_user(self):
        """Create a test user with some permissions"""
        self.stdout.write("Creating test user...")

        # Create test user
        user, created = User.objects.get_or_create(
            username="testuser",
            defaults={
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "User",
            },
        )

        if created:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Created test user: {user.username} (ID: {user.id})"
                )
            )
        else:
            self.stdout.write(
                f"Test user already exists: {user.username} (ID: {user.id})"
            )

        # Create a test group with some permissions
        group, created = Group.objects.get_or_create(name="Test Group")

        # Add some permissions to the group
        content_types = [
            ContentType.objects.get_for_model(Profile),
            ContentType.objects.get_for_model(ReviewProcessTasks),
        ]

        for content_type in content_types:
            permissions = Permission.objects.filter(content_type=content_type)[:2]
            for perm in permissions:
                group.permissions.add(perm)

        # Add user to group
        user.groups.add(group)

        self.stdout.write(
            self.style.SUCCESS(f"Added user {user.username} to group {group.name}")
        )

        return user

    def test_all_permissions(self, permissions_management):
        """Test getting all possible permissions by app"""
        self.stdout.write("Testing all possible permissions endpoint...")

        response = permissions_management.get_all_possible_permissions_by_app()

        if response.success:
            self.stdout.write(
                self.style.SUCCESS(
                    "✓ Successfully retrieved all possible permissions by app!"
                )
            )
            self.stdout.write("All possible permissions by app:")

            for app_name, permissions in response.data.items():
                self.stdout.write(f"  {app_name}: ({len(permissions)} permissions)")
                for perm in permissions[:3]:  # Show first 3 for brevity
                    self.stdout.write(
                        f"    - {perm['codename']}: {perm['name']} ({perm['model']})"
                    )
                if len(permissions) > 3:
                    self.stdout.write(f"    ... and {len(permissions) - 3} more")

            total_permissions = sum(len(perms) for perms in response.data.values())
            self.stdout.write(
                self.style.SUCCESS(f"Total permissions found: {total_permissions}")
            )
        else:
            self.stdout.write(
                self.style.ERROR(f"Failed to get all permissions: {response.message}")
            )

    def test_user_permissions(self, user_id, permissions_management):
        """Test getting user permissions by app"""
        self.stdout.write(f"Testing permissions for user ID: {user_id}")

        try:
            user = User.objects.get(id=user_id)
            self.stdout.write(
                f"User: {user.username} ({user.first_name} {user.last_name})"
            )

            # Test our new method
            response = permissions_management.get_user_permissions_by_app(user_id)

            if response.success:
                self.stdout.write(
                    self.style.SUCCESS(
                        "✓ Successfully retrieved user permissions by app!"
                    )
                )
                self.stdout.write("Permissions by app:")

                for app_name, permissions in response.data.items():
                    self.stdout.write(f"  {app_name}:")
                    for perm in permissions:
                        self.stdout.write(f"    - {perm}")

                if not response.data:
                    self.stdout.write(
                        self.style.WARNING("No permissions found for this user.")
                    )
            else:
                self.stdout.write(
                    self.style.ERROR(f"Failed to get permissions: {response.message}")
                )

        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"User with ID {user_id} not found."))
