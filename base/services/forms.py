from rest_framework.response import Response
from rest_framework import status

from accounts.models import Profile
from base.models import Facility
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse

logging_service = LoggingService()


# Function to check if required fields are missing in the request data
def check_missing_fields(data, required_fields):
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        return Response(
            {
                "status": "failed",
                "message": f"Missing required fields: {', '.join(missing_fields)}",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
    return None


def check_user_facility(data, user) -> RepositoryResponse:
    if "facility" in data:
        facility_id = data["facility"]
        try:
            facility = Facility.objects.get(id=facility_id)
            return RepositoryResponse(
                success=True,
                message="Facility found",
                data=facility,
            )
        except Facility.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Facility not found",
                data=None,
            )
    else:
        try:
            user_profile = Profile.objects.get(user=user)
            user_facility = user_profile.facility
            if user_facility is None:
                return RepositoryResponse(
                    success=False,
                    message="User facility not found",
                    data=None,
                )
            return RepositoryResponse(
                success=True,
                message="User facility found",
                data=user_facility,
            )

        except Profile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="User profile not found",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Internal server error",
                data=None,
            )
