"""
Logging utilities for the Q-Control Backend application.

This module provides convenient logging functions and decorators that can be used
throughout the application without needing to instantiate LoggingService.
"""

import logging
import functools
from typing import Any, Callable
from base.services.logging.logger import LoggingService


def get_logger(name: str = None) -> LoggingService:
    """
    Get a LoggingService instance for the specified name.

    Args:
        name: Logger name (defaults to caller's module name)

    Returns:
        LoggingService instance
    """
    return LoggingService(logger_name=name)


def log_service_method(service_name: str = None, operation_name: str = None):
    """
    Decorator to automatically log service method entry and exit.

    Args:
        service_name: Name of the service (defaults to class name)
        operation_name: Name of the operation (defaults to method name)

    Usage:
        @log_service_method(service_name="ReviewsService")
        def create_review(self, data):
            # method implementation
            pass
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            logger = get_logger()

            # Determine service and operation names
            svc_name = service_name or self.__class__.__name__
            op_name = operation_name or func.__name__

            # Log method entry
            logger.log_service_operation(
                service_name=svc_name, operation=op_name, status="started"
            )

            try:
                result = func(self, *args, **kwargs)

                # Log successful completion
                logger.log_service_operation(
                    service_name=svc_name, operation=op_name, status="completed"
                )

                return result

            except Exception as e:
                # Log error
                logger.log_service_operation(
                    service_name=svc_name,
                    operation=op_name,
                    status="failed",
                    details=str(e),
                )
                logger.log_error(e, error_type=f"{svc_name}_{op_name}")
                raise

        return wrapper

    return decorator


def log_database_operation_decorator(model_name: str = None, operation: str = None):
    """
    Decorator to automatically log database operations.

    Args:
        model_name: Name of the model being operated on
        operation: Type of operation (create, read, update, delete)

    Usage:
        @log_database_operation_decorator(model_name="Review", operation="create")
        def create_review_in_db(self, data):
            # method implementation
            return review
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger()

            try:
                result = func(*args, **kwargs)

                # Log successful database operation
                record_id = None
                if hasattr(result, "id"):
                    record_id = result.id
                elif hasattr(result, "pk"):
                    record_id = result.pk

                logger.log_database_operation(
                    model=model_name or "Unknown",
                    operation=operation or func.__name__,
                    record_id=record_id,
                    status="success",
                )

                return result

            except Exception as e:
                # Log failed database operation
                logger.log_database_operation(
                    model=model_name or "Unknown",
                    operation=operation or func.__name__,
                    status="failed",
                )
                raise

        return wrapper

    return decorator


# Convenience functions for quick logging
def log_info(message: str, logger_name: str = None):
    """Log an info message."""
    logger = get_logger(logger_name)
    logger.log_info(message)


def log_error(error: Exception, error_type: str = "ERROR", logger_name: str = None):
    """Log an error."""
    logger = get_logger(logger_name)
    logger.log_error(error, error_type)


def log_warning(message: str, logger_name: str = None):
    """Log a warning message."""
    logger = get_logger(logger_name)
    logger.log_warning(message)


def log_debug(message: str, logger_name: str = None):
    """Log a debug message."""
    logger = get_logger(logger_name)
    logger.log_debug(message)


def log_success(message: str, logger_name: str = None):
    """Log a success message."""
    logger = get_logger(logger_name)
    logger.log_success(message)


def log_critical(message: str, logger_name: str = None):
    """Log a critical message."""
    logger = get_logger(logger_name)
    logger.log_critical(message)
