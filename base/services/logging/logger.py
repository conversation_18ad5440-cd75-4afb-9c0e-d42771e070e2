import traceback
import datetime
import logging
from typing import Union, List, Dict, Any
from django.conf import settings


class LoggingService:
    def __init__(self, logger_name: str = None):
        """
        Initialize the logging service with an optional logger name.
        If no logger_name is provided, it will use the module name of the calling code.
        """
        if logger_name:
            self.logger = logging.getLogger(logger_name)
        else:
            # Get the caller's module name for logger
            import inspect

            frame = inspect.currentframe()
            try:
                caller_frame = frame.f_back
                module = inspect.getmodule(caller_frame)
                if module and hasattr(module, "__name__"):
                    self.logger = logging.getLogger(module.__name__)
                else:
                    self.logger = logging.getLogger(__name__)
            finally:
                del frame

    def get_first_error(self, errors):

        if isinstance(errors, dict):
            first_field_errors = next(iter(errors.values()))
            if isinstance(first_field_errors, list):
                return first_field_errors[0]
            return self.get_first_error(first_field_errors)

        elif isinstance(errors, list):
            if settings.ENVIRONMENT == "production":
                # In production, return a generic error message
                return errors[0]
            else:  # print errors[0]
                print(errors[0])
                return errors[0]

        if settings.ENVIRONMENT == "production":
            # In production, return a generic error message
            return str(errors)
        else:
            # In development, print the error for debugging
            print(errors)
            return str(errors)

    def get_serializer_error_message(self, serializer):
        """
        Extract meaningful error messages from serializer validation errors.

        Args:
            serializer: The Django REST framework serializer instance that failed validation
        Returns:
            str: A formatted error message string
        """
        errors = serializer.errors

        # Initializing messages for different error types
        missing_fields = []
        invalid_fields = []

        # Processing each error to categorize them
        for field, error_list in errors.items():
            if isinstance(error_list, list):
                error_codes = [getattr(error, "code", "") for error in error_list]

                if any(code in ["required", "blank", "null"] for code in error_codes):
                    missing_fields.append(field)
                else:
                    invalid_fields.append(
                        f"{field}: {', '.join(str(error) for error in error_list)}"
                    )
            else:
                # non-list error values (like dict errors from nested serializers)
                invalid_fields.append(f"{field}: {error_list}")

        # a meaningful error message
        error_message = ""
        if missing_fields:
            error_message += f"Missing required fields: {', '.join(missing_fields)}. "
        if invalid_fields:
            error_message += f"Invalid data: {'; '.join(invalid_fields)}"

        return error_message.strip()

    def check_required_fields(self, data, required_fields):
        """
        This gets the data, checks the required fields. it returns something like this:
        "field: name, email are required"
        """
        missing_fields = [field for field in required_fields if not data.get(field)]

        if missing_fields:
            if len(missing_fields) == 1:
                return f"Field: {missing_fields[0]} is required"
            else:
                formatted_fields = ", ".join(missing_fields)
                return f"Fields: {formatted_fields} are required"

        return None

    def log_error(self, error, error_type="message"):
        """
        Log error messages. Maintains backward compatibility while using proper logging.
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if isinstance(error, Exception):
            # Use proper logging for exceptions
            self.logger.error(
                f"{error_type.upper()} ERROR: {str(error)}",
                exc_info=True,
                extra={"error_type": error_type.upper()},
            )

            # Print to console only in development
            if settings.ENVIRONMENT != "production":
                print(f"[{timestamp}] {error_type.upper()} ERROR")
                print(f"Error message: {str(error)}")
                print("Traceback")
                print(traceback.format_exc())
        else:
            # Use proper logging for error messages
            self.logger.error(
                f"{error_type.upper()}: {error}",
                extra={"error_type": error_type.upper()},
            )

            # Print to console only in development
            if settings.ENVIRONMENT != "production":
                print(f"[{timestamp}] {error_type.upper()}: {error}")

    def log_info(self, message):
        """
        Log info messages. Maintains backward compatibility while using proper logging.
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Use proper logging
        self.logger.info(message)

        # Print to console only in development
        if settings.ENVIRONMENT != "production":
            print(f"[{timestamp}] INFO: {message}")

    def log_warning(self, message):
        """
        Log warning messages. Maintains backward compatibility while using proper logging.
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Use proper logging
        self.logger.warning(message)

        # Print to console only in development
        if settings.ENVIRONMENT != "production":
            print(f"[{timestamp}] WARNING: {message}")

    def log_debug(self, message):
        """
        Log debug messages. Maintains backward compatibility while using proper logging.
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Use proper logging
        self.logger.debug(message)

        # Print to console only in development
        if settings.ENVIRONMENT != "production":
            print(f"[{timestamp}] DEBUG: {message}")

    def log_success(self, message):
        """
        Log success messages. Maintains backward compatibility while using proper logging.
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Use proper logging (info level for success messages)
        self.logger.info(f"SUCCESS: {message}", extra={"message_type": "SUCCESS"})

        # Print to console only in development
        if settings.ENVIRONMENT != "production":
            print(f"[{timestamp}] SUCCESS: {message}")

    def log_critical(self, message):
        """
        Log critical messages. Maintains backward compatibility while using proper logging.
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Use proper logging
        self.logger.critical(message)

        # Print to console only in development (with trace for critical messages)
        if settings.ENVIRONMENT != "production":
            print(f"[{timestamp}] CRITICAL: {message}")
            # Print stack trace for critical messages in development
            print("Stack trace:")
            traceback.print_stack()

    def log_request(self, request, message: str = "Request received"):
        """
        Log HTTP request information.
        """
        if hasattr(request, "method") and hasattr(request, "path"):
            log_message = f"{message} - {request.method} {request.path}"
            if hasattr(request, "user") and hasattr(request.user, "username"):
                log_message += f" - User: {request.user.username}"
            self.logger.info(
                log_message,
                extra={
                    "request_method": request.method,
                    "request_path": request.path,
                    "user": (
                        getattr(request.user, "username", "Anonymous")
                        if hasattr(request, "user")
                        else "Unknown"
                    ),
                },
            )

            # Print to console only in development
            if settings.ENVIRONMENT != "production":
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{timestamp}] REQUEST: {log_message}")
        else:
            self.logger.info(message)

            # Print to console only in development
            if settings.ENVIRONMENT != "production":
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{timestamp}] REQUEST: {message}")

    def log_response(self, response, message: str = "Response sent"):
        """
        Log HTTP response information.
        """
        if hasattr(response, "status_code"):
            log_message = f"{message} - Status: {response.status_code}"
            self.logger.info(
                log_message, extra={"response_status": response.status_code}
            )

            # Print to console only in development
            if settings.ENVIRONMENT != "production":
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{timestamp}] RESPONSE: {log_message}")
        else:
            self.logger.info(message)

            # Print to console only in development
            if settings.ENVIRONMENT != "production":
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{timestamp}] RESPONSE: {message}")

    def log_service_operation(
        self, service_name: str, operation: str, status: str, details: str = None
    ):
        """
        Log service operations with structured information.
        """
        message = f"Service: {service_name} - Operation: {operation} - Status: {status}"
        if details:
            message += f" - Details: {details}"

        extra_data = {
            "service_name": service_name,
            "operation": operation,
            "status": status,
        }
        if details:
            extra_data["details"] = details

        if status.lower() in ["success", "completed"]:
            self.logger.info(message, extra=extra_data)
        elif status.lower() in ["error", "failed"]:
            # Log error with trace
            self.logger.error(message, extra=extra_data, exc_info=True)
        else:
            self.logger.info(message, extra=extra_data)

        # Print to console only in development
        if settings.ENVIRONMENT != "production":
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] SERVICE: {message}")
            if status.lower() in ["error", "failed"]:
                print("Stack trace:")
                traceback.print_stack()

    def log_database_operation(
        self,
        model: str,
        operation: str,
        record_id: Union[int, str] = None,
        status: str = "success",
    ):
        """
        Log database operations.
        """
        message = f"DB Operation: {operation} on {model}"
        if record_id:
            message += f" (ID: {record_id})"
        message += f" - Status: {status}"

        extra_data = {"db_operation": operation, "model": model, "status": status}
        if record_id:
            extra_data["record_id"] = str(record_id)

        if status.lower() == "success":
            self.logger.info(message, extra=extra_data)
        else:
            # Log error with trace for failed database operations
            self.logger.error(message, extra=extra_data, exc_info=True)

        # Print to console only in development
        if settings.ENVIRONMENT != "production":
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] DB: {message}")
            if status.lower() != "success":
                print("Stack trace:")
                traceback.print_stack()
