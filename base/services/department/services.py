from base.models import Department
from base.services.responses import RepositoryResponse


class DepartmentService:
    def get_department_by_id(self, id):
        try:
            department = Department.objects.get(id=id)
            return RepositoryResponse(
                data=department,
                success=True,
                message="Department retrieved successfully",
            )
        except Department.DoesNotExist:
            return RepositoryResponse(
                data=None,
                success=False,
                message="Department not found",
            )
        except Exception as e:
            return RepositoryResponse(
                data=None,
                success=False,
                message="Internal server error",
            )
