import pytz
from django.utils import timezone
from accounts.models import UserPreference
from base.services.logging.logger import LoggingService

logging_service = LoggingService()


def convert_created_at_to_user_timezone(created_at, user):
    """
    Convert a 'created_at' datetime to the user's preferred timezone.

    :param created_at: Datetime (in UTC) to be converted
    :param user: User object to retrieve the user's timezone preference
    :return: Datetime formatted as mm/dd/yyyy %H:%M in user's timezone
    """
    has_error = False
    try:
        # Fetch the user's timezone from their preferences
        user_preference = UserPreference.objects.filter(user=user).first()
        if user_preference:

            user_timezone = user_preference.user_timezone  # e.g., 'America/Chicago'
        else:
            user_timezone = "UTC"
        user_tz = pytz.timezone(user_timezone)

    except (UserPreference.DoesNotExist, pytz.UnknownTimeZoneError, Exception) as e:
        logging_service.log_error(e)
        # Default to UTC if any error occurs
        user_tz = pytz.UTC
        has_error = True

    # Convert the 'created_at' field to the user's timezone (or UTC if fallback)
    local_time = created_at.astimezone(user_tz)

    # Format the converted time as mm/dd/yyyy %H:%M
    formatted_time = local_time.strftime("%m/%d/%Y %H:%M")

    return f"{formatted_time} UTC" if has_error else formatted_time
