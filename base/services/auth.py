import requests
from django.contrib.auth.models import User
import jwt
from django.conf import settings
from accounts.models import Profile
from core.settings import SECRET_KEY
import jwt
from django.conf import settings
from core.settings import SECRET_KEY
from rest_framework.response import Response
from rest_framework import status
import secrets
import string


def verify_user(access_token_with_bearer):

    if not isinstance(access_token_with_bearer, str):
        user = None
        message = "Token has expired"
        return (user, message)

    try:
        access_token = access_token_with_bearer.split()[1]
        decoded_token = jwt.decode(access_token, SECRET_KEY, algorithms=["HS256"])
        user_id = decoded_token.get("user_id")
        user = User.objects.get(pk=user_id)
        message = "User is authenticated"
        return (user, message)
    except jwt.ExpiredSignatureError:
        user = None
        message = "Token has expired"
        return (user, message)
    except jwt.InvalidTokenError:
        user = None
        message = "Invalid token"
        return (user, message)
    except User.DoesNotExist:
        user = None
        message = "User not found"
        return (user, message)


def check_departments_access(user):
    try:
        user_profile = Profile.objects.get(user=user)
    except Profile.DoesNotExist:
        return (
            [],
            {
                "message": "Profile not found",
                "departments": [],
            },
            False,
        )

    can_access_departments = user_profile.access_to_department.all()

    if not can_access_departments.exists():
        return (
            [],
            {
                "message": "You have access to 0 departments. Contact admin",
                "departments": [],
            },
            False,
        )

    department_ids = [department.id for department in can_access_departments]

    return (
        department_ids,
        True,
        {
            "message": f"You have access to {len(department_ids)} department(s)",
            "department_ids": department_ids,
        },
    )


def check_facilities_access(user):
    try:
        user_profile = Profile.objects.get(user=user)
    except Profile.DoesNotExist:
        return [], False, {"message": "Profile not found", "facilities": []}

    can_access_facilities = user_profile.access_to_facilities.all()

    if not can_access_facilities.exists():
        return (
            [],
            {
                "message": "You have access to 0 facilities. Contact admin",
                "facilities": [],
            },
            False,
        )

    facility_ids = [facility.id for facility in can_access_facilities]

    return (
        facility_ids,
        {
            "message": f"You have access to {len(facility_ids)} facility(ies)",
        },
        True,
    )


def generate_random_password(length=12):
    alphabet = string.ascii_letters + string.digits + string.punctuation
    password = "".join(secrets.choice(alphabet) for i in range(length))
    return password


import random


def generate_email(first_name, last_name, existing_users=[]):
    first = first_name.lower().strip()
    last = last_name.lower().strip()

    base_email = f"{first}.{last}@example.com"

    if base_email not in existing_users:
        return base_email

    counter = 1
    while counter < 1000:
        new_email = f"{first}.{last}{counter}@example.com"
        if new_email not in existing_users:
            return new_email
        counter += 1

    return f"{first}.{last}{random.randint(10000, 99999)}@example.com"
