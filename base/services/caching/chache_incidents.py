import json
from typing import Optional, List, Any, Dict
import redis
from django.conf import settings
from dataclasses import dataclass

from base.services.logging.logger import LoggingService

logging_service = LoggingService()


@dataclass
class CachingResponse:
    success: bool
    message: Optional[str] = None
    data: Optional[Any] = None


class IncidentCacheService:
    """Service class for handling incident caching operations using Redis"""

    INCIDENT_KEY_PREFIX = "incident"
    INCIDENT_LIST_KEY = "incidents:list"
    CACHE_TTL = 3600  # 1 hour in seconds

    def __init__(self):
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            decode_responses=True,
        )

    def _get_incident_key(self, incident_id: int) -> str:
        """Generates a unique identifier for the incident"""
        return f"{self.INCIDENT_KEY_PREFIX}{incident_id}"

    def cache_incident(self, incident_data: Dict[str, Any]) -> CachingResponse:
        """
        Cache an incident and add it to the cached list
        """

        try:
            incident_id = incident_data.get("id")
            if not incident_id:
                return CachingResponse(
                    success=False,
                    message="Missing incident ID",
                )
            incident_key = self._get_incident_key(incident_id)
            self.redis_client.setex(
                incident_key,
                self.CACHE_TTL,
                json.dumps(incident_data),
            )

            self.redis_client.lpush(self.INCIDENT_LIST_KEY, incident_id)
            self.redis_client.expire(self.INCIDENT_LIST_KEY, self.CACHE_TTL)
            return CachingResponse(
                success=True,
                data=incident_data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return CachingResponse(
                success=False,
                message=f"Error caching",
            )

    def get_cached_incident(self, incident_id: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve an incident from the cache by its ID
        """
        try:
            incident_key = self._get_incident_key(incident_id)
            incident_data = self.redis_client.get(incident_key)
            if incident_data:
                return json.loads(incident_data)
            return None
        except Exception as e:
            logging_service.log_error(e)
            return None

    def update_cached_incident(self, incident_data: Dict[str, Any]) -> bool:
        """
        Update an incident in the cache by its ID
        """
        try:
            incident_id = incident_data.get("id")
            if not incident_id:
                return False
            incident_key = self._get_incident_key(incident_id)
            if not self.redis_client.exists(incident_key):
                return False
            self.redis_client.setex(
                incident_key,
                self.CACHE_TTL,
                json.dumps(incident_data),
            )
            return True
        except Exception as e:
            logging_service.log_error(e)
            return False

    def delete_cached_incident(self, incident_id: int) -> bool:
        """
        Delete an incident from the cache by its ID
        """
        try:
            incident_key = self._get_incident_key(incident_id)

            self.redis_client.lrem(self.INCIDENT_LIST_KEY, 0, incident_id)

            return bool(self.redis_client.delete(incident_key))
        except Exception as e:
            logging_service.log_error(e)
            return False

    def get_cached_incident_list(
        self, start: int = 0, end: int = -1
    ) -> List[Dict[str, Any]]:
        """
        Retrieve a list of all incident IDs from the cache
        """
        try:
            incident_ids = self.redis_client.lrange(self.INCIDENT_LIST_KEY, start, end)

            incidents = []
            for incident_id in incident_ids:
                incident_data = self.get_incident(int(incident_id))
                if incident_data:
                    incidents.append(incident_data)

            return incidents
        except Exception as e:
            logging_service.log_error(e)
            return []

    def clear_cache(self) -> bool:
        """
        Clear all cached incidents and the incidents list
        """
        try:
            incident_keys = self.redis_client.keys(f"{self.INCIDENT_KEY_PREFIX}*")

            keys_to_delete = [*incident_keys, self.INCIDENT_LIST_KEY]
            if keys_to_delete:
                self.redis_client.delete(*keys_to_delete)

            return True
        except Exception as e:
            logging_service.log_error(e)
            return False
