from accounts.models import Profile
from accounts.services.profile.services import ProfileService
from api.views.auth.permissions_list import (
    is_admin_user,
    is_director_user,
    is_manager_user,
    is_specific_department,
    is_super_user,
)

from base.models import Department
from base.services.department.services import DepartmentService
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.services.permissions.permissions import PermissionService
from base.services.responses import APIResponse, RepositoryResponse
from django.apps import apps
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
from facilities.services.facility_services import FacilityService

facility_service = FacilityService()
department_service = DepartmentService()
profile_service = ProfileService()
permissions_management = PermissionsManagement()
logging_service = LoggingService()


class GetIncidentsService:

    def get_incident_by_id(
        self,
        user,
        app_label,
        model_name,
        serializer,
        incident_id,
        department=None,
    ):

        try:
            if not model_name:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No model name specified",
                )
            model_response = get_model_by_name(app_label, model_name)

            if not model_response.success:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="User profile does not have a facility",
                )
            Model = model_response.data
            incident_data = Model.objects.get(id=incident_id)
            if department:
                # check is is_specific_department and created_by
                permission_service = PermissionService(user)
                if (
                    not is_specific_department(
                        user=user,
                        department_name=department,
                        facility=incident_data.report_facility,
                    )
                    and not is_super_user(user)
                    and not is_admin_user(user, incident_data.report_facility)
                    and not incident_data.created_by == user
                ):
                    return RepositoryResponse(
                        success=False,
                        data=None,
                        message="You do not have permission to access this incident",
                    )
            else:
                # check the admin, super user and created by
                if (
                    not is_super_user(user)
                    and not is_admin_user(user, incident_data.report_facility)
                    and not incident_data.created_by == user
                ):

                    return RepositoryResponse(
                        success=False,
                        message="You do not have permission to access this incident",
                        data=None,
                    )
            success, incident, modifications, message = self.get_latest_version(
                incident_data=incident_data,
                modelName=Model,
                incidentSerializer=serializer,
                incident_id=incident_id,
            )

            if not success:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=message,
                )
            return RepositoryResponse(
                data={"incident": incident, "modifications": modifications},
                success=True,
                message="Incident retrieved successfully",
            )

        except Model.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Incident not found",
            )

    def get_incidents(
        self,
        app_label,
        model_name,
        user,
        facility_id=None,
        department_id=None,
        accessible_to="",
    ) -> RepositoryResponse:
        if not model_name:
            return RepositoryResponse(
                success=False,
                data=None,
                message="No model name specified",
            )
        model_response = get_model_by_name(app_label, model_name)

        if not model_response.success:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User profile does not have a facility",
            )

        Model = model_response.data
        facility = None
        department = None
        if department_id:
            department_response = department_service.get_department_by_id(department_id)
            if not department_response.success:
                return department_response
            department = department_response.data
        if facility_id:
            facility_response = facility_service.get_facility_by_id(facility_id)
            if not facility_response.success:
                return facility_response
            facility = facility_response.data

        if is_super_user(user):
            return self.get_incidents_by_superuser(Model, user)

        elif is_admin_user(user, facility):
            return self.get_incidents_by_admin(Model, user)

        elif is_director_user(user, facility):

            return self.get_incidents_by_director(Model, facility, accessible_to)

        elif is_manager_user(user, department):

            return self.get_incidents_by_manager(Model, user, department, accessible_to)
        else:
            return self.get_incidents_by_user(Model, user)

    # filter incidents by permissions(superuser)
    def get_incidents_by_superuser(self, Model, user) -> RepositoryResponse:
        try:

            profile_response = profile_service.get_profile_by_user(user)
            if not profile_response.success:
                return profile_response

            if not profile_response.data.facility:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="User profile does not have a facility",
                )
            profile_ids = profile_response.data.access_to_facilities.values_list(
                "id", flat=True
            )

            incidents = Model.objects.filter(report_facility__id__in=profile_ids)

            return RepositoryResponse(
                success=True,
                data=incidents,
                message="Incidents are retrieved successfully",
            )
        except Profile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User profile not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error",
            )

    # filter incidents by permissions(admin)

    def get_incidents_by_admin(self, Model, user):
        try:

            profile_response = profile_service.get_profile_by_user(user)
            if not profile_response.success:
                return profile_response

            if not profile_response.data.facility:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="User profile does not have a facility",
                )

            access_to_facility_ids = (
                profile_response.data.access_to_facilities.values_list("id", flat=True)
            )
            # Filter incidents using report_facility__id
            incidents = Model.objects.filter(
                report_facility__id__in=access_to_facility_ids
            )

            return RepositoryResponse(
                data=incidents,
                success=True,
                message="Incidents retrieved successfully",
            )

        except Profile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User profile not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error",
            )

    # filter incidents by permissions(manager)
    def get_incidents_by_manager(self, Model, user, department, accessible_to=""):
        """
        Filter incidents by departments a manager has access to

        Args:
            app_label: Django app label
            model_name: Model name to query
            department: Department instance
            user: User instance requesting the data
        """
        try:
            if not department:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Department not found",
                )

            profile_response = profile_service.get_profile_by_user(user)
            if not profile_response.success:
                return profile_response

            if not profile_response.data.facility:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="User profile does not have a facility",
                )
            # Get accessible department IDs more efficiently
            department_ids = profile_response.data.access_to_department.values_list(
                "id", flat=True
            )
            incidents = Model.objects.filter(department__id__in=department_ids)
            if accessible_to:
                incidents_response = self.filter_incidents_by_department(
                    user=user,
                    incidents=incidents,
                    accessible_to=accessible_to,
                    facility=profile_response.data.facility,
                )
                if not incidents_response.success:
                    return incidents_response
                else:
                    incidents = incidents_response.data
            return RepositoryResponse(
                data=incidents,
                success=True,
                message="Incidents retrieved successfully",
            )

        except Profile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Profile not found for user",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error",
            )

    # filter incidents by permissions(director)
    def get_incidents_by_director(
        self, Model, facility, user=None, accessible_to=""
    ) -> RepositoryResponse:
        try:
            if not facility:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Facility not found",
                )
            incidents = Model.objects.filter(report_facility=facility)
            if accessible_to and user:
                incidents_response = self.filter_incidents_by_department(
                    user,
                    incidents,
                    accessible_to,
                    facility,
                )
                if not incidents_response.success:
                    return incidents_response
                incidents = incidents_response.data
            return RepositoryResponse(
                data=incidents,
                success=True,
                message="Incidents retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=True,
                data=None,
                message="Internal server error",
            )

    def filter_incidents_by_department(self, user, incidents, accessible_to, facility):

        try:
            department = Department.objects.filter(
                header_of_department=user,
                name=accessible_to,
                facility=facility,
            ).first()
            if department:
                incidents = incidents.filter(department=department)
                return RepositoryResponse(
                    success=True,
                    data=incidents,
                    message="Incidents retrieved successfully for department",
                )
            else:
                return RepositoryResponse(
                    success=None,
                    data=[],
                    message=f"Insufficient access to {accessible_to} department ",
                )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Error filtering incidents by department",
            )

    # filter incidents by permissions(user)
    def get_incidents_by_user(self, Model, user) -> RepositoryResponse:
        try:

            incidents = Model.objects.filter(created_by=user)
            return RepositoryResponse(
                data=incidents,
                success=True,
                message="Incidents retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Internal server error",
            )

    def get_latest_version(
        self, incident_data, modelName, incidentSerializer, incident_id
    ):
        modifications = {}
        incident_latest_version = None
        try:
            if incident_data.versions.count() > 0:
                incident_latest_version = (
                    modelName.objects.get(id=incident_id)
                    .versions.order_by("-created_at")
                    .first()
                )
            else:
                incident_latest_version = incident_data
            incident = incidentSerializer(incident_latest_version).data
            incident["status"] = incident_data.status
            modifications = incident_data.versions.all().order_by("created_at")

            modifications = {
                "count": modifications.count(),
                "versions": [
                    {
                        "date": incident_data.created_at,
                        "id": incident_data.id,
                        "original": True,
                        "latest": incident_data.id == incident_latest_version.id,
                    }
                ]
                + [
                    {
                        "date": modification.created_at,
                        "id": modification.id,
                        "latest": modification.id == incident_latest_version.id,
                        "original": False,
                    }
                    for modification in modifications
                    if modification.id
                ],
            }

            return True, incident, modifications, "success"

        except Exception as e:
            logging_service.log_error(e)
            return False, None, None, "Internal server error"


def get_model_by_name(app_label, model_name) -> RepositoryResponse:
    try:
        model = apps.get_model(app_label, model_name)
        if not model:
            return RepositoryResponse(
                data=None,
                success=False,
                message=f"Model {model_name} not found in app {app_label}",
            )
        return RepositoryResponse(
            data=model,
            success=True,
            message=None,
        )
    except LookupError:
        return RepositoryResponse(
            data=None,
            success=False,
            message=f"Model {model_name} not found in app {app_label}",
        )
    except Exception as e:
        logging_service.log_error(e)
        return RepositoryResponse(
            data=None,
            success=False,
            message="Internal server error",
        )


# def get incidents by super user, roles, and permission levels
class GetIncidentsService:

    from django.db.models import Model
    from django.contrib.auth.models import User

    def get_incidents(self, user: User, model: Model, filters={}) -> RepositoryResponse:
        """
        This is a helper function to get incidents
        User it to retrieve incidents, filter them and paginate them
        Filters include:
            1. page: Page number
            2. page_size: Number of incidents to retrieve per page
            3. status: Status of incidents to retrieve
            4. department_id: Department to retrieve incidents for
            5. user_id: User id to retrieve incidents for a specific user
            6. facility_id: Facility to retrieve incidents for specific user
        """
        try:
            # case super user
            if user.is_superuser:
                """
                Get all incidents
                """
                incidents = model.objects.all()
                return RepositoryResponse(
                    data=incidents,
                    success=True,
                    message="Incidents retrieved successfully",
                )
            else:
                permissions = permissions_management.check_permissions(
                    user=user,
                    model=model,
                    # group_name=group.name,
                    permissions=["view_list"],
                )
                if not permissions.success:
                    return permissions

                profile = Profile.objects.get(user=user)

                # case corporate
                if profile.permission_level == "Corporate":
                    """
                    Filter incidents by access to facilities
                    """
                    incidents = self._filter_incidents_by_access_to_facilities(
                        profile, model
                    )
                    return incidents

                # case facility
                elif profile.permission_level == "Facility":
                    """
                    Filter incidents by facility
                    """
                    incidents = self._filter_incidents_by_facility(profile, model)
                    return incidents

                # case department
                elif profile.permission_level == "Department":
                    """
                    Filter incidents by department
                    """
                    incidents = self._filter_incidents_by_access_to_departments(
                        profile, model
                    )
                    return incidents

                # default
                else:
                    """
                    Filter incidents by created
                    """
                    incidents = model.objects.filter(created_by=user)

                    return RepositoryResponse(
                        data=incidents,
                        success=True,
                        message="Incidents retrieved successfully",
                    )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                data=None,
                success=False,
                message="Internal server error",
            )

    def get_incident_by_id(
        self,
        user: User,
        model,
        incident_id: int,
    ) -> RepositoryResponse:
        """
        Get incident by id:
        Permissions:
            1. incident created by user
            2. If user is super user, get incident
            3. If permissions level is corporate, check view details for incident
            4. If permissions level is facility, check view details for incident, and if incident facility is same as user facility
            5. If permissions level is department, check view details for incident, and if incident department is same user department
        """

        try:
            profile = profile_service.get_user_profile(user)
            if not profile.success:
                return profile

            try:
                incident = model.objects.get(id=incident_id)
                if incident.created_by == user or user.is_superuser:
                    return RepositoryResponse(
                        success=True,
                        message="Incident retrieved successfully",
                        data=incident,
                    )
                # check view permission
                permission_check = permissions_management.check_permissions(
                    user, model, ["view_details"]
                )

                if not permission_check.success:
                    return permission_check

                # handle corporate
                if profile.permission_level == "Corporate":
                    return RepositoryResponse(
                        success=True,
                        message="Incident retrieved successfully",
                        data=incident,
                    )
                # handle facility
                elif profile.permission_level == "Facility":
                    if not profile.facility.id == incident.report_facility.id:
                        return RepositoryResponse(
                            success=False,
                            message="Unauthorized to view incident",
                            data=None,
                        )
                    return RepositoryResponse(
                        success=True,
                        message="Incident retrieved successfully",
                        data=incident,
                    )
                # handle department
                elif profile.permission_level == "Department":
                    if not incident.department in profile.success_to_department.all():
                        return RepositoryResponse(
                            success=False,
                            message="Unauthorized to view incident",
                            data=None,
                        )
                    return RepositoryResponse(
                        success=True,
                        message="Incident retrieved successfully",
                        data=incident,
                    )
                else:
                    return RepositoryResponse(
                        success=False,
                        message="Invalid permission level",
                        data=None,
                    )

            except model.DoesNotExist:
                return RepositoryResponse(
                    success=False,
                    message="Incident not found",
                    data=None,
                )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,  # Changed from True to False
                message="Unknown error getting incident details",
                data=None,
            )

    def _filter_incidents_by_access_to_facilities(
        self, profile: Profile, model: Model
    ) -> RepositoryResponse:
        """
        Filter incidents by access to facilities
        """

        access_ids = profile.access_to_facilities.values_list("id", flat=True)

        incidents = model.objects.filter(report_facility__id__in=access_ids)
        return RepositoryResponse(
            data=incidents,
            success=True,
            message="Incidents retrieved successfully for facilities",
        )

    def _filter_incidents_by_access_to_departments(
        self, profile: Profile, model: Model
    ):
        """
        Filter incidents by access to departments
        """
        access_ids = profile.access_to_department.values_list("id", flat=True)
        incidents = model.objects.filter(department__id__in=access_ids)
        return RepositoryResponse(
            data=incidents,
            success=True,
            message="Incidents retrieved successfully for departments",
        )

    def _filter_incidents_by_facility(
        self, profile: Profile, model: Model
    ) -> RepositoryResponse:
        """
        Filter incidents by facility
        """

        incidents = model.objects.filter(report_facility__id=profile.facility.id)
        return RepositoryResponse(
            data=incidents,
            success=True,
            message="Incidents retrieved successfully for facility",
        )

    def _filter_incidents_by_department(
        self, department_id: int, model: Model
    ) -> RepositoryResponse:
        """
        Filter incidents by department
        """

        incidents = model.objects.filter(department_id=department_id)

        return RepositoryResponse(
            data=incidents,
            success=True,
            message="Incidents retrieved successfully for department",
        )

    def _apply_filters(self, incidents, filters):
        """
        Apply filters to incidents queryset
        """
        if filters.get("status"):
            incidents = incidents.filter(status=filters["status"])

        if filters.get("department_id"):
            incidents = incidents.filter(department_id=filters["department_id"])

        if filters.get("user_id"):
            incidents = incidents.filter(created_by_id=filters["user_id"])

        if filters.get("facility_id"):
            incidents = incidents.filter(report_facility_id=filters["facility_id"])

        return incidents

    def _paginate(self, incidents, page, page_size):
        """
        Paginate incidents queryset
        """
        paginator = Paginator(incidents, page_size)
        try:
            incidents = paginator.page(page)
        except PageNotAnInteger:
            incidents = paginator.page(1)
        except EmptyPage:
            incidents = paginator.page(paginator.num_pages)

        return incidents

    def get_latest_version(
        self, incident_data, modelName, incidentSerializer, incident_id
    ):
        modifications = {}
        incident_latest_version = None
        try:
            if incident_data.versions.count() > 0:
                incident_latest_version = (
                    modelName.objects.get(id=incident_id)
                    .versions.order_by("-created_at")
                    .first()
                )
            else:
                incident_latest_version = incident_data
            incident = incidentSerializer(incident_latest_version).data
            incident["status"] = incident_data.status
            modifications = incident_data.versions.all().order_by("created_at")

            modifications = {
                "count": modifications.count(),
                "versions": [
                    {
                        "date": incident_data.created_at,
                        "id": incident_data.id,
                        "original": True,
                        "latest": incident_data.id == incident_latest_version.id,
                    }
                ]
                + [
                    {
                        "date": modification.created_at,
                        "id": modification.id,
                        "latest": modification.id == incident_latest_version.id,
                        "original": False,
                    }
                    for modification in modifications
                    if modification.id
                ],
            }

            return True, incident, modifications, "success"

        except Exception as e:
            logging_service.log_error(e)
            return False, None, None, "Internal server error"
