from base.services.responses import RepositoryResponse


class FilterIncidentsService:
    # filter incidents by facility
    def filter_incidents_by_facility(self, incidents, facility) -> RepositoryResponse:
        """
        Filter incidents queryset by facility

        Args:
            incidents: QuerySet of incidents to filter
            facility: Facility instance to filter by

        Returns:
            RepositoryResponse: Contains filtered incidents or error details
        """
        try:
            # Validate inputs
            if not incidents:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No incidents provided to filter",
                )

            if not facility:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No facility provided for filtering",
                )

            if not hasattr(facility, "id"):
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Invalid facility object provided",
                )

            filtered_incidents = incidents.filter(report_facility__id=facility.id)

            return RepositoryResponse(
                success=True,
                data=filtered_incidents,
                message="Incidents are retrieved successfully",
            )

        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Incidents are not retrieved successfully",
            )

    def filter_incidents_by_department(
        self, incidents, department
    ) -> RepositoryResponse:
        """
        Filter incidents queryset by single department

        Args:
            incidents: QuerySet of incidents to filter
            department: Department instance to filter by
        """
        try:
            if not incidents:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No incidents provided to filter",
                )

            if not department:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No department provided for filtering",
                )

            if not hasattr(department, "id"):
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Invalid department object provided",
                )

            filtered_incidents = incidents.filter(department__id=department.id)

            return RepositoryResponse(
                success=True,
                data=filtered_incidents,
                message="Incidents filtered by department successfully",
            )

        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to filter incidents by department",
            )

    def filter_incidents_by_facilities(
        self, incidents, facilities
    ) -> RepositoryResponse:
        """
        Filter incidents queryset by multiple facilities

        Args:
            incidents: QuerySet of incidents to filter
            facilities: List/QuerySet of Facility instances
        """
        try:
            if not incidents:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No incidents provided to filter",
                )

            if not facilities:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No facilities provided for filtering",
                )

            facility_ids = [f.id for f in facilities if hasattr(f, "id")]
            if not facility_ids:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No valid facility IDs found",
                )

            filtered_incidents = incidents.filter(report_facility__id__in=facility_ids)

            return RepositoryResponse(
                success=True,
                data=filtered_incidents,
                message="Incidents filtered by facilities successfully",
            )

        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to filter incidents by facilities",
            )

    def filter_incidents_by_departments(
        self, incidents, departments
    ) -> RepositoryResponse:
        """
        Filter incidents queryset by multiple departments

        Args:
            incidents: QuerySet of incidents to filter
            departments: List/QuerySet of Department instances
        """
        try:
            if not incidents:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No incidents provided to filter",
                )

            if not departments:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No departments provided for filtering",
                )

            department_ids = [d.id for d in departments if hasattr(d, "id")]
            if not department_ids:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No valid department IDs found",
                )

            filtered_incidents = incidents.filter(department__id__in=department_ids)

            return RepositoryResponse(
                success=True,
                data=filtered_incidents,
                message="Incidents filtered by departments successfully",
            )

        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to filter incidents by departments",
            )

    def filter_incidents_by_date_range(
        self, incidents, start_date, end_date
    ) -> RepositoryResponse:
        """
        Filter incidents queryset by date range

        Args:
            incidents: QuerySet of incidents to filter
            start_date: datetime/date object for range start
            end_date: datetime/date object for range end
        """
        try:
            if not incidents:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No incidents provided to filter",
                )

            if not start_date or not end_date:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Both start_date and end_date are required",
                )

            if start_date > end_date:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Start date must be before end date",
                )

            filtered_incidents = incidents.filter(
                created_at__date__gte=start_date, created_at__date__lte=end_date
            )

            return RepositoryResponse(
                success=True,
                data=filtered_incidents,
                message="Incidents filtered by date range successfully",
            )

        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to filter incidents by date range",
            )

    def filter_incidents_by_status(self, incidents, status) -> RepositoryResponse:
        """
        Filter incidents queryset by status

        Args:
            incidents: QuerySet of incidents to filter
            status: Status value to filter by
        """
        try:
            if not incidents:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No incidents provided to filter",
                )

            if not status:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No status provided for filtering",
                )

            filtered_incidents = incidents.filter(status=status)

            return RepositoryResponse(
                success=True,
                data=filtered_incidents,
                message="Incidents filtered by status successfully",
            )

        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to filter incidents by status",
            )

    def filter_incidents_by_type(self, incidents, incident_type) -> RepositoryResponse:
        """
        Filter incidents queryset by type

        Args:
            incidents: QuerySet of incidents to filter
            incident_type: Type value to filter by
        """
        try:
            if not incidents:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No incidents provided to filter",
                )

            if not incident_type:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="No incident type provided for filtering",
                )

            filtered_incidents = incidents.filter(incident_type=incident_type)

            return RepositoryResponse(
                success=True,
                data=filtered_incidents,
                message="Incidents filtered by type successfully",
            )

        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to filter incidents by type",
            )
