from accounts.models import Profile, ReviewGroups, ReviewProcessTasks
from base.services.responses import RepositoryResponse


class TaskManager:

    def handle_task_submission(
        self, task: ReviewProcessTasks, group: ReviewGroups, reviewer: Profile
    ) -> RepositoryResponse:
        """
        This is called when
        1. task.require_approval_for_all_groups == true, and not all groups have submitted the task
        2. incident.require_approval_for_all_reviewers == true and not all reviewers have submitted
        """
        if group and group not in task.review_groups.all():
            return RepositoryResponse(
                message="Group is not assigned to this task",
                success=False,
                data=None,
            )
        if reviewer and reviewer not in task.reviewers.all():
            return RepositoryResponse(
                message="Reviewer is not assigned to this task",
                success=False,
                data=None,
            )

        # handle task

        # 1. get task
        if group:
            task.groups_completed.add(group)
            if not task.require_approval_for_all_groups:
                response = self.handle_task_completion(
                    task=task,
                    group=group,
                )
                return response

            else:
                if task.require_approval_for_all_groups and set(
                    task.review_groups.all()
                ) == set(task.groups_completed.all()):
                    response = self.handle_task_completion(
                        task=task,
                        group=group,
                    )
                    return response

        if reviewer:
            task.reviewers_completed.add(reviewer)
            if not task.require_all_members_to_complete:
                response = self.handle_task_completion(
                    task,
                    task.reviewers,
                )
                return response

            else:
                if (
                    task.require_all_members_to_complete
                    and task.reviewers.count() == task.reviewers_completed.count()
                    and set(task.reviewers.all()) == set(task.reviewers_completed.all())
                ):
                    response = self.handle_task_completion(
                        task,
                        reviewers=task.reviewers,
                    )

    def handle_task_completion(
        self,
        task: ReviewProcessTasks,
        group=None,
        reviewers=[],
    ) -> RepositoryResponse:
        """
        This is called when
        1. task.require_approval_for_all_groups == false,
        2. task.require_approval_for_all_groups == true, and all groups have submitted the task
        3. incident.require_approval_for_all_reviewers == false
        4. incident.require_approval_for_all_reviewers == true and all reviewers have submitted
        """
        if group:
            print("Handling group")
        elif reviewers:
            print("Handling reviewers")
