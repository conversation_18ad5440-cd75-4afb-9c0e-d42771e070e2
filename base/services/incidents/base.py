from api.views.auth.permissions_list import is_admin_user, is_super_user
from base.constants import ReviewStatus
from base.models import Department
from base.services.format_date import convert_created_at_to_user_timezone
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from django.contrib.auth.models import User
import os

from incidents.emails.send_to_department import send_to_department_email

logging_service = LoggingService()


class IncidentService:

    def mark_as_resolved(self, incident, user) -> APIResponse:
        try:
            if not is_super_user(user) and not is_admin_user(
                user, incident.report_facility
            ):
                return APIResponse(
                    success=False,
                    message="You do not have permission to mark incidents as resolved",
                    data=None,
                    code=403,
                )
            incident.updated_by = user
            incident.is_resolved = True
            incident.status = ReviewStatus.CLOSED
            incident.save()
            # get all incident versions and mark them as resolved
            versions = incident.versions.all()
            for version in versions:
                version.is_resolved = True
                version.status = ReviewStatus.CLOSED
                version.updated_by = user
                version.save()

            return APIResponse(
                success=True,
                message="Incident marked as resolved",
                data=None,
                code=200,
            )
        except Exception as e:
            LoggingService.log_error(e)

            return APIResponse(
                status="failed",
                message="Failed to mark incident as resolved",
                data=None,
                code=500,
            )

    def send_incident_to_department(self, data, incident, incident_type, user):

        assignees = []

        if not "department" in data:
            return (
                False,
                "Department id is required",
            )

        required_fields = ["department", "comment"]

        missing_fields = logging_service.check_required_fields(data, required_fields)
        if missing_fields:
            return APIResponse(
                success=False,
                message=missing_fields,
                data=None,
                code=400,
            )

        if "assignees" in data:
            for assignee in data.get("assignees"):
                user = User.objects.filter(
                    id=assignee["user_id"]
                ).first()  # Use .first() to get a single user
                if user:
                    assignees.append(user)
        try:
            department = Department.objects.get(id=data["department"])

            incident.department = department
            incident.assignees.add(*assignees)
            incident.save()

            incident.department = department
            incident.assignees.add(*assignees)
            incident.save()

            incident_preview = data["comment"]
            incident_url = f"{os.getenv('MAIN_DOMAIN_NAME')}/"
            # send email to department head with the incident details
            for assignee in assignees:
                send_to_department_email(
                    recipient_email=assignee.email,
                    sender_department=department.name,
                    incident_datetime=convert_created_at_to_user_timezone(
                        incident.created_at, assignee
                    ),
                    incident_preview=incident_preview,
                    incident_id=incident.id,
                    incident_type=incident_type,
                    incident_link=incident_url,
                    first_name=assignee.first_name,
                )
            return APIResponse(
                success=True,
                message="Incident sent to department successfully",
                code=200,
            )

        except Department.DoesNotExist:
            return APIResponse(
                success=False,
                message=f"Department with id {data['department']}' not found",
                data=None,
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return (False, "Internal server error")
