from django.contrib.auth.models import Group

from accounts.models import Profile
from base.models import UserNotification
from base.services.logging.logger import LoggingService

logging_service = LoggingService()


def save_notification(
    facility, group_name, message, notification_type, notification_category, item_id
):
    try:
        group = Group.objects.get(name=group_name)
        assignees_ids = Profile.objects.filter(
            access_to_facilities=facility, user__groups=group
        )

        notifications = []
        for assignee_id in assignees_ids:
            notification = UserNotification.objects.create(
                message=message,
                to=assignee_id.user,
                notification_type=notification_type,
                notification_category=notification_category,
                item_id=item_id,
            )
            notifications.append(notification)

        return (notifications, True)
    except Group.DoesNotExist:
        return None, False

    except Exception as e:
        logging_service.log_error(e)
        return None, False
