from api.views.auth.permissions_list import is_admin_user, is_super_user
from base.models import Department, Facility
from base.services.incidents.filter_incients import FilterIncidentsService
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse


incident_service = GetIncidentsService()
filter_service = FilterIncidentsService()
logging_service = LoggingService()

INCIDENT_TYPES = [
    {
        "name": "General Patient Visitor",
        "model": "GeneralPatientVisitor",
        "model_name": "GeneralPatientVisitor",
        "accessible_to": "",
        "app_label": "general_patient_visitor",
    },
    {
        "name": "Lost and Found",
        "model": "LostAndFound",
        "model_name": "LostAndFound",
        "accessible_to": "",
        "app_label": "lost_and_found",
    },
    {
        "name": "Grievance",
        "model": "Grievance",
        "model_name": "Grievance",
        "accessible_to": "",
        "app_label": "patient_visitor_grievance",
    },
    {
        "name": "Adverse Drug Reaction",
        "model": "AdverseDrugReaction",
        "model_name": "AdverseDrugReaction",
        "accessible_to": "Pharmacy",
        "app_label": "adverse_drug_reaction",
    },
    {
        "name": "Medication Error",
        "model": "MedicationError",
        "model_name": "MedicationError",
        "accessible_to": "Pharmacy",
        "app_label": "medication_error",
    },
    {
        "name": "Workplace Violence",
        "model": "WorkPlaceViolence",
        "model_name": "WorkPlaceViolence",
        "accessible_to": "Human Resources",
        "app_label": "workplace_violence_reports",
    },
    {
        "name": "Staff Incident Report",
        "model": "StaffIncidentReport",
        "model_name": "StaffIncidentReport",
        "accessible_to": "Human Resources",
        "app_label": "staff_incident_reports",
    },
]


class FacilitiesService:
    pass


# I commenting out the method to avoid confusion, as it is not currently used.

# def get_facility_incidents(self, user, facility_id):
#     all_reports = []

#     try:
#         facility = Facility.objects.get(id=facility_id)

#         for incident_type in INCIDENT_TYPES:
#             incidents_response = incident_service.get_incidents(
#                 app_label=incident_type["app_label"],
#                 model_name=incident_type["model_name"],
#                 user=user,
#                 facility_id=facility.id,
#             )

#             if not incidents_response.success or not incidents_response.data:
#                 continue

#             filtered_reports = incidents_response.data

#             # Skip department access checks for super users or admin users
#             if not is_super_user(user) and not is_admin_user(user, facility):
#                 if incident_type["accessible_to"]:
#                     department = Department.objects.filter(
#                         header_of_department=user,
#                         name=incident_type["accessible_to"],
#                         facility=facility,
#                     ).first()

#                     if department:
#                         filtered_reports = filtered_reports.filter(
#                             department=department
#                         )
#                     else:
#                         # Exclude incidents from the inaccessible department and those without a department
#                         filtered_reports = filtered_reports.exclude(
#                             department__name=incident_type["accessible_to"]
#                         ).exclude(department__isnull=True)

#             facility_filtered_reports = filter_service.filter_incidents_by_facility(
#                 filtered_reports, facility
#             )

#             if (
#                 not facility_filtered_reports.success
#                 or not facility_filtered_reports.data
#             ):
#                 continue

#             formatted_reports = self.format_incidents(
#                 facility_filtered_reports.data,
#                 incident_type["name"],
#             )

#             all_reports.extend(formatted_reports)

#         return RepositoryResponse(success=True, data=all_reports)

#     except Facility.DoesNotExist:
#         return RepositoryResponse(
#             success=False, message="Facility not found.", data=None
#         )
#     except Exception as e:
#         logging_service.log_error(e)
#         return RepositoryResponse(
#             success=False, message="Internal server error", data=None
#         )

# def format_incidents(self, incidents, category):
#     sorted_reports = sorted(incidents, key=lambda x: x.created_at, reverse=True)
#     return [
#         {
#             "id": incident.id,
#             "status": incident.status,
#             "created_at": incident.created_at,
#             "current_step": incident.current_step,
#             "category": category,
#         }
#         for incident in sorted_reports
#     ]
