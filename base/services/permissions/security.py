from base64 import b64encode, b64decode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import hashlib
import json
import os
from django.conf import settings

from base.services.logging.logger import LoggingService

logging_service = LoggingService()


class SecurityService:
    def __init__(self):
        self.key = hashlib.sha256(settings.ENCRYPTION_KEY.encode()).digest()

    def encrypt_data(self, data):
        try:
            data_string = json.dumps(data)

            iv = os.urandom(16)

            cipher = AES.new(self.key, AES.MODE_CBC, iv)

            encrypted_bytes = cipher.encrypt(
                pad(data_string.encode("utf-8"), AES.block_size)
            )

            encrypted_base64 = b64encode(iv + encrypted_bytes).decode("utf-8")

            return encrypted_base64

        except Exception as e:
            logging_service.log_error(e)
            return None

    def decrypt_data(self, encrypted_data):
        try:
            enc = b64decode(encrypted_data)

            iv = enc[:16]
            ct = enc[16:]

            cipher = AES.new(self.key, AES.MODE_CBC, iv=iv)

            pt = unpad(cipher.decrypt(ct), AES.block_size)

            return json.loads(pt.decode("utf-8"))

        except Exception as e:
            logging_service.log_error(e)
            return None
