import uuid
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from rest_framework import authentication
from rest_framework import exceptions
from django.utils import timezone
from django.contrib.auth.models import User
from typing import List

from base.models import APIKey
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from django.db.models import Model
from django.contrib.auth.models import User, Group

logging_service = LoggingService()


class PermissionService:
    def __init__(self, user, user_profile=None):
        self.user = user
        self.user_profile = user_profile or self.user.profile

    def check_group(self, groups: List[str]) -> bool:
        """
        Check if user belongs to any of the given groups
        """
        return self.user.groups.filter(name__in=groups).exists()

    def check_facility_access(self, facility) -> bool:
        """
        Check if user has access to given facility
        """
        return (
            self.user_profile.facility == facility
            or facility in self.user_profile.access_to_facilities.all()
        )

    def check_department_access(self, department) -> bool:
        """
        Check if user has access to given department
        """
        return (
            department.header_of_department == self.user
            or department in self.user_profile.access_to_department.all()
        )

    def can_view_draft(self, incident) -> bool:
        """
        Super User, Quality/Risk Manager or incident creator can view drafts
        """
        if not incident:
            return False

        return (
            self.check_group(["Super User"])
            or self.check_group(["Quality/Risk Manager"])
            or incident.created_by == self.user
        )

    def can_edit_draft(self, incident) -> bool:
        """
        Only incident creator can edit drafts
        """
        if not incident:
            return False

        return incident.created_by == self.user

    def can_create_incident(self) -> bool:
        """
        Any authenticated user can create incidents
        """
        return self.user.is_authenticated

    def can_modify_incident(self, facility, department, incident=None) -> bool:
        """
        Check modification permissions based on user role and access
        """
        # Super User with facility access
        if self.check_group(["Super User"]) and self.check_facility_access(facility):
            return True

        # Admin with facility access
        if self.check_group(["Admin"]) and self.check_facility_access(facility):
            return True

        # Quality/Risk Manager with facility access
        if self.check_group(["Quality/Risk Manager"]) and self.check_facility_access(
            facility
        ):
            return True

        # Manager with department access
        if self.check_group(["Manager"]) and self.check_department_access(department):
            return True

        # Incident creator
        if incident and incident.created_by == self.user:
            return True

        return False

    def can_assign_to_department(self, facility, department) -> bool:
        """
        Permission to assign incidents to departments
        """
        return (
            (self.check_group(["Super User"]) and self.check_facility_access(facility))
            or (self.check_group(["Admin"]) and self.check_facility_access(facility))
            or (
                self.check_group(["Quality/Risk Manager"])
                and self.check_facility_access(facility)
            )
        )

    def can_add_review(self, facility, department, incident=None) -> bool:
        """
        Permission to add reviews to incidents
        """
        base_permission = (
            (self.check_group(["Super User"]) and self.check_facility_access(facility))
            or (self.check_group(["Admin"]) and self.check_facility_access(facility))
            or (
                self.check_group(["Quality/Risk Manager"])
                and self.check_facility_access(facility)
            )
            or (
                self.check_group(["Manager"])
                and self.check_department_access(department)
            )
        )

        if incident and incident.created_by == self.user:
            return True

        return base_permission

    def can_view_list_and_export(self, facility, department) -> bool:
        """
        Permission to view and export incident lists
        """
        return (
            (self.check_group(["Super User"]) and self.check_facility_access(facility))
            or (self.check_group(["Admin"]) and self.check_facility_access(facility))
            or (
                self.check_group(["Quality/Risk Manager"])
                and self.check_facility_access(facility)
            )
            or (
                self.check_group(["Manager"])
                and self.check_department_access(department)
            )
        )

    def can_view_and_export_details(self, incident):
        """
        Permission to view and export incident details
        """
        return (
            (
                self.check_group(["Super User"])
                and self.check_facility_access(incident.facility)
            )
            or (
                self.check_group(["Admin"])
                and self.check_facility_access(incident.facility)
            )
            or (
                self.check_group(["Quality/Risk Manager"])
                and self.check_facility_access(incident.facility)
            )
            or (
                self.check_group(["Manager"])
                and self.check_department_access(incident.department)
            )
            or incident.created_by == self.user
        )

    def can_assign_severity_rating(self, facility, department) -> bool:
        """
        Permission to assign severity ratings
        """
        return (
            (self.check_group(["Super User"]) and self.check_facility_access(facility))
            or (self.check_group(["Admin"]) and self.check_facility_access(facility))
            or (
                self.check_group(["Quality/Risk Manager"])
                and self.check_facility_access(facility)
            )
            or (
                self.check_group(["Manager"])
                and self.check_department_access(department)
            )
        )


# Usage example:
def handle_incident_view(request, incident):
    permission_service = PermissionService(request.user)

    if permission_service.can_modify_incident(
        facility=incident.facility, department=incident.department, incident=incident
    ):
        # Handle modification
        pass


class APIKeyAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        api_key = request.META.get("HTTP_X_API_KEY")
        if not api_key:
            raise exceptions.AuthenticationFailed("API key not provided")

        try:
            uuid_obj = uuid.UUID(api_key)
        except ValueError:
            raise exceptions.AuthenticationFailed("Invalid API key format")

        try:
            api_key_obj = APIKey.objects.select_related("user").get(
                key=uuid_obj, is_active=True
            )
            if api_key_obj.expires_at and api_key_obj.expires_at < timezone.now():
                raise exceptions.AuthenticationFailed("API key has expired")
            if not api_key_obj.user or not api_key_obj.user.is_active:
                raise exceptions.AuthenticationFailed("User inactive or deleted")
            return (api_key_obj.user, api_key_obj)
        except APIKey.DoesNotExist:
            raise exceptions.AuthenticationFailed("Invalid API key")
        except User.DoesNotExist:
            raise exceptions.AuthenticationFailed("User does not exist")
        except Exception as e:
            logging_service.log_error(e)
            raise exceptions.AuthenticationFailed("Error authenticating with backend")
