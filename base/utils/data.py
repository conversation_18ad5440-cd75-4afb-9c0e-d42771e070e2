class DataCleaner:
    """
    A utility class for cleaning data by removing keys with empty values.
    """

    @staticmethod
    def clean_data(data):
        """
        Recursively removes keys with empty values (None, '', [], {}, ()).

        Args:
            data (dict, list, or tuple): The data structure to clean.

        Returns:
            The cleaned data structure.
        """
        if isinstance(data, dict):
            return {
                k: DataCleaner.clean_data(v)
                for k, v in data.items()
                if v not in [None, "", [], {}, ()]
            }
        elif isinstance(data, list):
            return [
                DataCleaner.clean_data(item)
                for item in data
                if item not in [None, "", [], {}, ()]
            ]
        elif isinstance(data, tuple):
            return tuple(
                DataCleaner.clean_data(item)
                for item in data
                if item not in [None, "", [], {}, ()]
            )
        else:
            return data
