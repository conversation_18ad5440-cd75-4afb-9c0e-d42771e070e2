from accounts.models import Profile, Title
from adverse_drug_reaction.models import AdverseDrugReaction
from base.models import Department, Facility
from complaints.models import Complaint
from general_patient_visitor.models import (
    GeneralPatientVisitor,
    GeneralPatientVisitorVersion,
)
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from patient_visitor_grievance.models import Grievance, GrievanceInvestigation
from staff_incident_reports.models import (
    StaffIncidentInvestigation,
    StaffIncidentReport,
)
from workplace_violence_reports.models import WorkPlaceViolence
from tasks.models import (
    ReviewProcessTasks,
    ReviewGroups,
    ReviewTemplateTasks,
    ReviewTemplates,
    ReviewProcess,
)


MODEL_MAPPING = {
    "general": GeneralPatientVisitor,
    "general_patient_visitor": GeneralPatientVisitor,
    "adverse_drug_reaction": AdverseDrugReaction,
    "staff_incident_reports": StaffIncidentReport,
    "staff_incident_investigation": StaffIncidentInvestigation,
    "patient_visitor_grievance": Grievance,
    "patient_visitor_grievance_investigation": GrievanceInvestigation,
    "medication_error": MedicationError,
    "workplace_violence_reports": WorkPlaceViolence,
    "lost_and_found": LostAndFound,
    "profiles": Profile,
    "facilities": Facility,
    "departments": Department,
    "complaints": Complaint,
    "complaint": Complaint,
    "review_group": ReviewGroups,
    "review_template": ReviewTemplates,
    "review_process": ReviewProcess,
    "titles": Title,
    "review_process_tasks": ReviewProcessTasks,
    "review_template": ReviewTemplates,
    "review_template_tasks": ReviewTemplateTasks,
}

REVERSE_MODEL_MAPPING = {
    GeneralPatientVisitor: "general_patient_visitor",
    AdverseDrugReaction: "adverse_drug_reaction",
    StaffIncidentReport: "staff_incident_reports",
    StaffIncidentInvestigation: "staff_incident_investigation",
    Grievance: "patient_visitor_grievance",
    GrievanceInvestigation: "patient_visitor_grievance_investigation",
    MedicationError: "medication_error",
    WorkPlaceViolence: "workplace_violence_reports",
    LostAndFound: "lost_and_found",
    Complaint: "complaints",
    Profile: "profiles",
    Facility: "facilities",
    Department: "departments",
    ReviewGroups: "review_group",
    ReviewTemplates: "review_template",
    ReviewProcess: "review_process",
    ReviewProcessTasks: "review_process_tasks",
    ReviewTemplateTasks: "review_template_tasks",
    Title: "titles",
}

INCIDENT_TYPE_NAMES = {
    "general_patient_visitor": "General Patient/Visitor",
    "adverse_drug_reaction": "Adverse Drug Reaction",
    "staff_incident_reports": "Staff Incident Report",
    "staff_incident_investigation": "Staff Incident Investigation",
    "patient_visitor_grievance": "Patient/Visitor Grievance",
    "patient_visitor_grievance_investigation": "Grievance Investigation",
    "medication_error": "Medication Error",
    "workplace_violence_reports": "Workplace Violence Report",
    "lost_and_found": "Lost and Found",
    "complaints": "Complaint",
}


def get_model_class(incident_type: str):
    """Get the model class for a given incident type string."""
    return MODEL_MAPPING.get(incident_type)


def get_incident_type(model_class):
    """Get the incident type string for a given model class."""
    return REVERSE_MODEL_MAPPING.get(model_class)


def get_incident_type_name(incident_type: str):
    """Get the human-readable name for an incident type."""
    return INCIDENT_TYPE_NAMES.get(
        incident_type, incident_type.replace("_", " ").title()
    )


def get_all_incident_types():
    """Get all available incident types."""
    return list(MODEL_MAPPING.keys())


def get_all_model_classes():
    """Get all incident model classes."""
    return list(MODEL_MAPPING.values())
