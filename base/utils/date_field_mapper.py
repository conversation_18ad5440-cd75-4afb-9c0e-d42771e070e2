"""
Utility function to map model classes to their respective date fields
"""


def get_date_field_for_model(model_class):
    """
    Returns the name of the date field to use for filtering by date range
    for each incident model.
    """
    model_map = {
        'GeneralPatientVisitor': 'incident_date',
        'AdverseDrugReaction': 'incident_date',
        'MedicationError': 'date_of_error',
        'LostAndFound': 'date_reported',
        'Grievance': 'date',
        'StaffIncidentReport': 'date_of_injury_or_near_miss',
        'WorkPlaceViolence': 'date_of_incident',
        'Complaint': 'date_of_complaint',
    }
    return model_map.get(model_class.__name__, None)
