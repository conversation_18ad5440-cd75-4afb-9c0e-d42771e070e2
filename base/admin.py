from django.contrib import admin
from base.models import APIKey, UserNotification

# Register your models here.


class NotificationAdmin(admin.ModelAdmin):
    list_display = (
        "to",
        "id",
        "notification_type",
        "notification_category",
        "message",
        "is_read",
        "created_at",
    )
    search_fields = ["message"]
    ordering = ["-created_at"]
    list_filter = ["is_read", "created_at"]


admin.site.register(UserNotification, NotificationAdmin)


@admin.register(APIKey)
class APIKeyAdmin(admin.ModelAdmin):
    list_display = ("key", "user", "is_active", "expires_at", "created_at")
    list_filter = ("is_active", "expires_at")
    search_fields = ("user__username", "user__email", "key")
    raw_id_fields = ("user",)
    readonly_fields = ("key", "created_at", "updated_at")
    list_per_page = 25

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields + ("user",)
        return self.readonly_fields
