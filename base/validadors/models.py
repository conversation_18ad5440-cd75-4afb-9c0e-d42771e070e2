from base.services.responses import RepositoryResponse


def validate_model_data(model_class, data: dict) -> RepositoryResponse:
    """
    Validates data against a model by removing fields that don't exist in the model.

    Args:
        model_class: The Django model class to validate against
        data: Dictionary containing the data to validate

    Returns:
        RepositoryResponse with:
        - success: True if validation passed with no invalid fields, False otherwise
        - message: Success message or error message listing invalid fields
        - data: Dictionary with only the fields that exist in the model
    """
    # Get all field names from the model
    model_fields = set([field.name for field in model_class._meta.get_fields()])

    # Create a new dict with only valid fields
    valid_data = {}
    invalid_fields = []

    for field_name, value in data.items():
        if field_name in model_fields:
            valid_data[field_name] = value
        else:
            invalid_fields.append(field_name)

    # Check if we have any invalid fields
    if invalid_fields:
        invalid_fields_str = ", ".join(invalid_fields)
        message = f"Fields: {invalid_fields_str} are not allowed"
        return RepositoryResponse(success=False, message=message, data=valid_data)
    else:
        return RepositoryResponse(
            success=True, message="All fields are valid", data=valid_data
        )
