import re


def validate_zipcode(zipcode, country_code):
    patterns = {
        "USA": r"^[0-9]{5}(?:-[0-9]{4})?$",
        "Canada": r"^[ABCEGHJKLMNPRSTVXY][0-9][ABCEGHJKLMNPRSTVWXYZ] [0-9][ABCEGHJKLMNPRSTVWXYZ][0-9]$",
        "UK": r"^([A-Z]{1,2}[0-9]{1,2}[A-Z]? [0-9][A-Z]{2})$",
    }
    generic_pattern = r"^\d{3,10}(-\d{1,4})?$"

    if country_code not in patterns:
        return "Invalid country code"

    if country_code and str(country_code).upper() in patterns:
        pattern = patterns[str(country_code).upper()]
    else:
        pattern = generic_pattern

    return bool(re.match(pattern, zipcode))
