import re

from base.services.responses import RepositoryResponse


def validate_phone_number(phone_number):
    cleaned_number = re.sub(r"\D", "", phone_number)

    if len(cleaned_number) == 10:
        pattern = r"^(\d{3})(\d{3})(\d{4})$"

    elif len(cleaned_number) == 11 and cleaned_number.startswith("1"):
        pattern = r"^1(\d{3})(\d{3})(\d{4})$"

    else:
        return False

    return bool(re.match(pattern, cleaned_number))
