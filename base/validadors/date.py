# a function to validate if a string is a valid date in the format YYYY-MM-DD
from datetime import datetime
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse

logging_service = LoggingService()


class DateValidator:

    def is_valid_date(self, date_string: str) -> APIResponse:
        """
        Validates if the input is a string representing a valid date in ISO format (YYYY-MM-DD or similar).
        Returns True if valid, False otherwise.
        """
        if not isinstance(date_string, str):
            logging_service.log_info("Invalid date format. Expected a string.")
            return APIResponse(
                success=False,
                message="Invalid date format. Expected a string.",
            )
        try:
            start_date = datetime.fromisoformat(date_string)
            return APIResponse(
                success=True,
                message="Valid date format.",
                data=start_date,
                code=200,
            )
        except ValueError as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Invalid date format. Use ISO format (YYYY-MM-DD).",
            )

    def is_valid_date_range(self, start_date: str, end_date: str) -> bool:
        """
        Validates if the input strings represent a valid date range in ISO format (YYYY-MM-DD).
        Returns True if valid, False otherwise.
        """
        if not (isinstance(start_date, str) and isinstance(end_date, str)):
            return False
        try:
            start = datetime.fromisoformat(start_date)
            end = datetime.fromisoformat(end_date)
            return start <= end
        except ValueError:
            return False
