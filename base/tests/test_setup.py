from django.test import TransactionTestCase

from django.contrib.auth.models import User

from base.constants import TEST_DEFAULTS
from base.tests.base_setup import CleanUpDatabase
from base.tests.factory import UserFactory


class TestCleanupDatabase(TransactionTestCase):

    def test_cleanup_database(self):
        user = UserFactory(email=TEST_DEFAULTS.ACTIVE_EMAIL)

        response = CleanUpDatabase({"email": user.email}).delete_user()

        self.assertIsNone(User.objects.filter(email=user.email).first())
