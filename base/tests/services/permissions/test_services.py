from adverse_drug_reaction.models import AdverseDrugReaction
from base.models import Facility
from base.services.permissions.manage_permissions import PermissionsManagement
from django.contrib.auth.models import User, Group, Permission
from django.test import TestCase

from base.services.permissions.mixins import (
    BasePermissionsMixin,
    IncidentsPermissionsMixin,
)
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import GroupFactory, UserFactory
from general_patient_visitor.models import GeneralPatientVisitor
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from patient_visitor_grievance.models import Grievance
from staff_incident_reports.models import StaffIncidentReport
from workplace_violence_reports.models import WorkPlaceViolence
from django.contrib.contenttypes.models import ContentType

permissions_management = PermissionsManagement()


class TestCheckGroup(TestCase):
    def test_create_group(self):
        group = permissions_management.check_group("Test Group")
        self.assertTrue(group.success)
        self.assertEqual(group.data.name, "Test Group")

    def test_group_already_exists(self):
        permissions_management.check_group("Test Group")
        group = permissions_management.check_group("Test Group")
        self.assertFalse(group.success)


class TestPermissionsToGroup(TestCase):
    def setUp(self):
        self.permissions = [
            {
                "code_name": "001",
                "name": "Can create",
            },
            {
                "code_name": "002",
                "name": "Can delete",
            },
        ]
        self.model = GeneralPatientVisitor
        self.group = permissions_management.check_group("Test Group 2")
        content_type = ContentType.objects.get_for_model(self.model)
        for perm in self.permissions:
            Permission.objects.get_or_create(
                codename=perm["code_name"],
                name=perm["name"],
                content_type=content_type,
            )

    def test_add_permissions_to_group(self):
        response = permissions_management.add_permissions_to_group(
            self.group.data,
            permissions=self.permissions,
            model=self.model,
        )
        self.assertTrue(response.success)
        self.assertEqual(len(response.data.permissions.all()), 2)
        self.assertEqual(
            [p.codename for p in response.data.permissions.all()],
            ["001", "002"],
        )

    def test_remove_permissions_from_group(self):
        self.test_add_permissions_to_group()
        response = permissions_management.remove_permissions_from_group(
            self.group.data,
            permissions=self.permissions,
            model=self.model,
        )
        self.assertTrue(response.success)
        self.assertEqual(len(response.data.permissions.all()), 0)

    def test_add_permissions_to_user(self):
        user = UserFactory()
        response = permissions_management.add_permissions_to_user(
            user,
            self.permissions,
            model=self.model,
        )
        self.assertTrue(response.success)
        self.assertEqual(len(user.user_permissions.all()), 2)
        self.assertEqual(
            [p.codename for p in user.user_permissions.all()],
            ["001", "002"],
        )

    def test_remove_permissions_from_user(self):
        user = UserFactory()
        self.test_add_permissions_to_user()
        response = permissions_management.remove_permissions_from_user(
            user,
            self.permissions,
            model=self.model,
        )
        self.assertTrue(response.success)
        self.assertEqual(len(user.user_permissions.all()), 0)


class TestAssignUserToGroup(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="testuser")
        self.group = permissions_management.check_group("Test Group 3")

    def test_assign_user_to_group(self):
        response = permissions_management.assign_user_to_group(
            self.user, self.group.data
        )
        self.assertTrue(response.success)
        self.assertIn(self.user, self.group.data.user_set.all())

    def test_remove_user_from_group(self):
        self.test_assign_user_to_group()
        response = permissions_management.remove_user_from_group(
            self.user, self.group.data
        )
        self.assertTrue(response.success)
        self.assertNotIn(self.user, self.group.data.user_set.all())


class TestPermissions(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.data = {
            "group.name": "User Group",  # changed from group_name to group.name
            "permissions": [
                {
                    "code_name": "001",
                    "name": "Can create",
                },
                {
                    "code_name": "003",
                    "name": "Can 003",
                },
            ],
        }
        self.model = GeneralPatientVisitor
        # Ensure permissions exist in the DB for the model
        from django.contrib.contenttypes.models import ContentType
        from django.contrib.auth.models import Permission

        content_type = ContentType.objects.get_for_model(self.model)
        for perm in self.data["permissions"]:
            Permission.objects.get_or_create(
                codename=perm["code_name"],
                name=perm["name"],
                content_type=content_type,
            )

    def test_create_group_with_permissions(self):
        input_data = self.data.copy()
        response = permissions_management.create_permission(
            self.user,
            self.data,
            model=self.model,
        )
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, input_data["group.name"])  # changed key
        self.assertEqual(len(response.data.permissions.all()), 2)
        self.assertEqual(
            [p.codename for p in response.data.permissions.all()],
            ["001", "003"],
        )

    def test_get_incident_model_permissions(self):
        response = permissions_management.get_incident_model_permissions(
            self.model, type="incident"
        )
        self.assertTrue(response.success)
        # Compare with actual permissions in DB for the model
        from django.contrib.contenttypes.models import ContentType
        from django.contrib.auth.models import Permission

        content_type = ContentType.objects.get_for_model(self.model)
        permissions = Permission.objects.filter(content_type=content_type)
        expected_permissions = [
            {"code_name": p.codename, "name": p.name} for p in permissions
        ]
        self.assertEqual(response.data, expected_permissions)

    def test_get_model_permissions(self):
        response = permissions_management.get_incident_model_permissions(
            self.model, type="base"
        )
        self.assertTrue(response.success)
        # Compare with actual permissions in DB for the model
        from django.contrib.contenttypes.models import ContentType
        from django.contrib.auth.models import Permission

        content_type = ContentType.objects.get_for_model(self.model)
        permissions = Permission.objects.filter(content_type=content_type)
        expected_permissions = [
            {"code_name": p.codename, "name": p.name} for p in permissions
        ]
        self.assertEqual(response.data, expected_permissions)

    """
    Test check permissions
    models:
        1. MedicationError
        2. AdverseDrugReaction
        3. Complaint
        4. Facility
        5. GeneralPatientVisitor
        6. LostAndFound
        6. Grievance
        7. StaffIncidentReport
        8. WorkPlaceViolence
    """


class TestCheckPermissionsMedicationError(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.admin = UserFactory()
        self.group = GroupFactory()
        self.admin.groups.add(self.group)

    def test_check_permissions_medication_error_success(self):
        response = permissions_management.add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_medicationerror",
                    "name": "Can create medication error",
                },
                {
                    "code_name": "change_medicationerror",
                    "name": "Can update medication error",
                },
            ],
            model=MedicationError,
        )

        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                MedicationError,
                [
                    "add_medicationerror",
                    "change_medicationerror",
                ],
            )

            self.assertTrue(response.success)
        else:
            self.fail(response.message)

    def test_check_permissions_medication_error_fail(self):
        response = permissions_management.add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_medicationerror",
                    "name": "Can create medication error",
                },
                {
                    "code_name": "change_medicationerror",
                    "name": "Can update medication error",
                },
            ],
            model=MedicationError,
        )

        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                MedicationError,
                [
                    "add_medicationerror",
                    "change_medicationerror",
                    "delete_medicationerror",
                ],
            )

            self.assertFalse(response.success)
        else:
            self.fail(response.message)


class TestCheckPermissionsAdverseDrugReaction(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self.admin = UserFactory()
        self.group = GroupFactory()
        self.admin.groups.add(self.group)

    def test_check_permissions_adverse_drug_reaction_success(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_adversedrugreaction",
                    "name": "Can create adverse drug reaction",
                },
                {
                    "code_name": "change_adversedrugreaction",
                    "name": "Can update adverse drug reaction",
                },
            ],
            model=AdverseDrugReaction,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                AdverseDrugReaction,
                [
                    "add_adversedrugreaction",
                    "change_adversedrugreaction",
                ],
            )
            self.assertTrue(response.success)

    def test_check_permissions_adverse_drug_reaction_fail(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_adversedrugreaction",
                    "name": "Can create adverse drug reaction",
                },
                {
                    "code_name": "change_adversedrugreaction",
                    "name": "Can update adverse drug reaction",
                },
            ],
            model=AdverseDrugReaction,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                AdverseDrugReaction,
                [
                    "add_adversedrugreaction",
                    "change_adversedrugreaction",
                    "delete_adversedrugreaction",
                ],
            )

            self.assertFalse(response.success)


class TestPermissionsFacility(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.admin = UserFactory()
        self.group = GroupFactory()
        self.admin.groups.add(self.group)

    def test_check_permissions_facility_success(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_facility",
                    "name": "Can create facility",
                },
                {
                    "code_name": "change_facility",
                    "name": "Can update facility",
                },
            ],
            model=Facility,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                Facility,
                [
                    "add_facility",
                    "change_facility",
                ],
            )
            self.assertTrue(response.success)

    def test_check_permissions_facility_fail(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_facility",
                    "name": "Can create facility",
                },
                {
                    "code_name": "change_facility",
                    "name": "Can update facility",
                },
            ],
            model=Facility,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                Facility,
                [
                    "add_facility",
                    "change_facility",
                    "delete_facility",
                ],
            )

            self.assertFalse(response.success)


class TestPermissionsGeneralPatientVisitor(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.admin = UserFactory()
        self.group = GroupFactory()
        self.admin.groups.add(self.group)

    def test_check_permissions_general_patient_visitor_success(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_generalpatientvisitor",
                    "name": "Can create general patient visitor",
                },
                {
                    "code_name": "change_generalpatientvisitor",
                    "name": "Can update general patient visitor",
                },
            ],
            model=GeneralPatientVisitor,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                GeneralPatientVisitor,
                [
                    "add_generalpatientvisitor",
                    "change_generalpatientvisitor",
                ],
            )

            self.assertTrue(response.success)

    def test_check_permissions_general_patient_visitor_fail(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_generalpatientvisitor",
                    "name": "Can create general patient visitor",
                },
                {
                    "code_name": "change_generalpatientvisitor",
                    "name": "Can update general patient visitor",
                },
            ],
            model=GeneralPatientVisitor,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                GeneralPatientVisitor,
                [
                    "add_generalpatientvisitor",
                    "change_generalpatientvisitor",
                    "delete_generalpatientvisitor",
                ],
            )

            self.assertFalse(response.success)


class TestPermissionsLostAndFound(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.admin = UserFactory()
        self.group = GroupFactory()
        self.admin.groups.add(self.group)

    def test_check_permissions_lost_and_found_success(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_lostandfound",
                    "name": "Can create lost and found",
                },
                {
                    "code_name": "change_lostandfound",
                    "name": "Can update lost and found",
                },
            ],
            model=LostAndFound,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                LostAndFound,
                [
                    "add_lostandfound",
                    "change_lostandfound",
                ],
            )

            self.assertTrue(response.success)

    def test_check_permissions_lost_and_found_fail(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_lostandfound",
                    "name": "Can create lost and found",
                },
                {
                    "code_name": "change_lostandfound",
                    "name": "Can update lost and found",
                },
            ],
            model=LostAndFound,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                LostAndFound,
                [
                    "add_lostandfound",
                    "change_lostandfound",
                    "delete_lostandfound",
                ],
            )

            self.assertFalse(response.success)


class TestPermissionsGrievance(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.admin = UserFactory()
        self.group = GroupFactory()
        self.admin.groups.add(self.group)

    def test_check_permissions_grievance_success(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_grievance",
                    "name": "Can create grievance",
                },
                {
                    "code_name": "change_grievance",
                    "name": "Can update grievance",
                },
            ],
            model=Grievance,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                Grievance,
                [
                    "add_grievance",
                    "change_grievance",
                ],
            )

            self.assertTrue(response.success)

    def test_check_permissions_grievance_fail(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_grievance",
                    "name": "Can create grievance",
                },
                {
                    "code_name": "change_grievance",
                    "name": "Can update grievance",
                },
            ],
            model=Grievance,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                Grievance,
                [
                    "add_grievance",
                    "change_grievance",
                    "delete_grievance",
                ],
            )

            self.assertFalse(response.success)


class TestPermissionsStaffIncidentReport(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.admin = UserFactory()
        self.group = GroupFactory()
        self.admin.groups.add(self.group)

    def test_check_permissions_staff_incident_report_success(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_staffincidentreport",
                    "name": "Can create staff incident report",
                },
                {
                    "code_name": "change_staffincidentreport",
                    "name": "Can update staff incident report",
                },
            ],
            model=StaffIncidentReport,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                StaffIncidentReport,
                [
                    "add_staffincidentreport",
                    "change_staffincidentreport",
                ],
            )

            self.assertTrue(response.success)

    def test_check_permissions_staff_incident_report_fail(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_staffincidentreport",
                    "name": "Can create staff incident report",
                },
                {
                    "code_name": "change_staffincidentreport",
                    "name": "Can update staff incident report",
                },
            ],
            model=StaffIncidentReport,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                StaffIncidentReport,
                [
                    "add_staffincidentreport",
                    "change_staffincidentreport",
                    "delete_staffincidentreport",
                ],
            )

            self.assertFalse(response.success)


class TestPermissionsWorkPlaceViolence(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.admin = UserFactory()
        self.group = GroupFactory()
        self.admin.groups.add(self.group)

    def test_check_permissions_workplace_violence_success(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_workplaceviolence",
                    "name": "Can create workplace violence",
                },
                {
                    "code_name": "change_workplaceviolence",
                    "name": "Can update workplace violence",
                },
            ],
            model=WorkPlaceViolence,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                WorkPlaceViolence,
                [
                    "add_workplaceviolence",
                    "change_workplaceviolence",
                ],
            )

            self.assertTrue(response.success)

    def test_check_permissions_workplace_violence_fail(self):
        response = PermissionsManagement().add_permissions_to_group(
            group=self.group,
            permissions=[
                {
                    "code_name": "add_workplaceviolence",
                    "name": "Can create workplace violence",
                },
                {
                    "code_name": "change_workplaceviolence",
                    "name": "Can update workplace violence",
                },
            ],
            model=WorkPlaceViolence,
        )
        if response.success:
            response = permissions_management.check_permissions(
                self.admin,
                WorkPlaceViolence,
                [
                    "add_workplaceviolence",
                    "change_workplaceviolence",
                    "delete_workplaceviolence",
                ],
            )

            self.assertFalse(response.success)
