from base.services.permissions.manage_permissions import PermissionsManagement
from django.contrib.auth.models import User, Group
from django.test import TestCase
from base.tests.base_setup import BaseTestSetup

from base.services.permissions.mixins import (
    BasePermissionsMixin,
    IncidentsPermissionsMixin,
)
from base.tests.factory import GroupFactory, UserFactory
from general_patient_visitor.models import GeneralPatientVisitor
from patient_visitor_grievance.models import Grievance


# TODO: Uncomment and implement the test for permissions when the functionality is ready.

# class TestPermissions(BaseTestSetup):
#     def setUp(self):
#         super().setUp()
#         self.user = UserFactory()
#         self.data = {
#             "group_name": "User Group",
#             "permissions": [
#                 {
#                     "code_name": "001",
#                     "name": "Can create",
#                 },
#                 {
#                     "code_name": "003",
#                     "name": "Can 003",
#                 },
#             ],
#         }
#         self.model = GeneralPatientVisitor

#     def test_get_incident_model_permissions(self):
#         self._authenticate_user(self.super_user)
#         response = self.client.get("/api/permissions/incident/general/")
#         if not response.status_code == 200:
#             self.fail(response)
#         self.assertTrue(response.status_code == 200)
#         expected_permissions = [
#             {"code_name": codename, "name": name}
#             for codename, name in IncidentsPermissionsMixin.custom_permissions
#         ]
#         self.assertEqual(response.data, expected_permissions)

#     def test_get_model_permissions(self):
#         self._authenticate_user(self.super_user)
#         response = self.client.get("/api/permissions/base/general/")
#         if not response.status_code == 200:
#             self.fail(response)
#         self.assertTrue(response.status_code == 200)
#         expected_permissions = [
#             {"code_name": codename, "name": name}
#             for codename, name in BasePermissionsMixin.custom_permissions
#         ]
#         self.assertEqual(response.data, expected_permissions)


class TestGetGroups(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.group = GroupFactory(name="Test Group")
        self.group2 = GroupFactory(name="Test Group 2")
        self.groups = [GroupFactory() for _ in range(15)]

    def test_get_groups(self):
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/permissions/")
        if not response.status_code == 200:
            self.fail(response)
        self.assertTrue(response.status_code == 200)
        self.assertEqual(len(response.data), 10)
        # self.assertEqual(len(response.data), len(self.groups) + 2)

    def test_pagination(self):
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/permissions/?page=1&page_size=5")
        if not response.status_code == 200:
            self.fail(response)
        self.assertTrue(response.status_code == 200)
        self.assertEqual(len(response.data), 5)

    def test_search(self):
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/permissions/?page=1&page_size=5&search=Test")
        if not response.status_code == 200:
            self.fail(response)
        self.assertTrue(response.status_code == 200)
        self.assertEqual(len(response.data), 2)


class TestDeleteGroup(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.group = GroupFactory(name="Test Group")
        self.group2 = GroupFactory(name="Test Group 2")
        self.groups = [GroupFactory() for _ in range(15)]

    def test_delete_group(self):
        self._authenticate_user(self.super_user)
        
        response = self.client.delete(
            "/api/permissions/", data={"id": self.group.id}, format="json"
        )
        self.assertEqual(response.status_code, 200)
        
        self.assertFalse(Group.objects.filter(id=self.group.id).exists())
    
    def test_delete_group_with_users(self):
        self._authenticate_user(self.super_user)
        user = UserFactory()
        self.group.user_set.add(user)
        
        response = self.client.delete(
            "/api/permissions/", data={"id": self.group.id}, format="json"
        )
        self.assertEqual(response.status_code, 400)
        
        self.assertTrue(Group.objects.filter(id=self.group.id).exists())
    
    def test_delete_group_with_permissions(self):
        self._authenticate_user(self.super_user)
        """ group with premissions"""
        self.group.permissions.add(Permission.objects.get(codename="add_user"))
        
        response = self.client.delete(
            "/api/permissions/", data={"id": self.group.id}, format="json"
        )
        self.assertEqual(response.status_code, 400)
        
        self.assertTrue(Group.objects.filter(id=self.group.id).exists())
    
    def test_delete_missing_group_id(self):
        self._authenticate_user(self.super_user)
        response = self.client.delete(
            "/api/permissions/", data={"id": 99999}, format="json"
        )
        self.assertEqual(response.status_code, 404)


class TestUpdateGroup(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.group = GroupFactory(name="Test Group")
        content_type = ContentType.objects.get_for_model(Grievance)
        self.permission1 = Permission.objects.create(
            codename="001",
            name="Can create",
            content_type=content_type
        )
        self.permission2 = Permission.objects.create(
            codename="002",
            name="Can delete",
            content_type=content_type
        )
        self.group.permissions.add(self.permission1, self.permission2)
        
    def test_update_group_name(self):
        self._authenticate_user(self.super_user)
        
        response = self.client.patch(
            "/api/permissions/", 
            data={
                "id": self.group.id,
                "group_name": "Updated Group Name"
            }, 
            format="json"
        )
        self.assertEqual(response.status_code, 200)
        
        self.group.refresh_from_db()
        self.assertEqual(self.group.name, "Updated Group Name")
        
    def test_update_group_permissions(self):
        self._authenticate_user(self.super_user)
        
        initial_permissions_count = self.group.permissions.count()
        self.assertEqual(initial_permissions_count, 2)
        
        content_type = ContentType.objects.get_for_model(Grievance)
        permission3 = Permission.objects.create(
            codename="003",
            name="Can update",
            content_type=content_type
        )
        
        response = self.client.patch(
            "/api/permissions/", 
            data={
                "id": self.group.id,
                "permissions": [
                    {
                        "feature": "patient_visitor_grievance",
                        "permissions": [
                            {
                                "code_name": "003",
                                "name": "Can update"
                            }
                        ]
                    }
                ]
            }, 
            format="json"
        )
        self.assertEqual(response.status_code, 200)
        
        self.group.refresh_from_db()
        self.assertEqual(self.group.permissions.count(), 1)
        self.assertTrue(self.group.permissions.filter(codename="003").exists())
        self.assertFalse(self.group.permissions.filter(codename="001").exists())
        self.assertFalse(self.group.permissions.filter(codename="002").exists())
        
    def test_update_group_with_empty_permissions(self):
        self._authenticate_user(self.super_user)

        initial_permissions_count = self.group.permissions.count()
        self.assertEqual(initial_permissions_count, 2)

        response = self.client.patch(
            "/api/permissions/", 
            data={
                "id": self.group.id,
                "permissions": []
            }, 
            format="json"
        )
        self.assertEqual(response.status_code, 200)
        
        self.group.refresh_from_db()
        self.assertEqual(self.group.permissions.count(), 0)
        
    def test_update_group_with_same_name(self):
        self._authenticate_user(self.super_user)
        
        response = self.client.patch(
            "/api/permissions/", 
            data={
                "id": self.group.id,
                "group_name": "Test Group"
            }, 
            format="json"
        )
        self.assertEqual(response.status_code, 200)
        
        self.group.refresh_from_db()
        self.assertEqual(self.group.name, "Test Group")
        
    def test_update_group_with_existing_name(self):
        self._authenticate_user(self.super_user)
        
        other_group = GroupFactory(name="Other Group")
        
        response = self.client.patch(
            "/api/permissions/", 
            data={
                "id": self.group.id,
                "group_name": "Other Group"
            }, 
            format="json"
        )
        self.assertEqual(response.status_code, 400)
        
        self.group.refresh_from_db()
        self.assertEqual(self.group.name, "Test Group")


permissions_management = PermissionsManagement()
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth.models import User, Group, Permission


class TestPermissionsToGroup(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.permissions = [
            {
                "code_name": "001",
                "name": "Can create",
            },
            {
                "code_name": "002",
                "name": "Can delete",
            },
        ]
        self.model = Grievance
        self.group = permissions_management.check_group("Test Group 2")
        content_type = ContentType.objects.get_for_model(self.model)
        for perm in self.permissions:
            Permission.objects.get_or_create(
                codename=perm["code_name"],
                name=perm["name"],
                content_type=content_type,
            )

    def test_add_permissions_to_group(self):
        response = permissions_management.add_permissions_to_group(
            self.group.data,
            permissions=self.permissions,
            model=self.model,
        )
        self.assertTrue(response.success)
        self.assertEqual(len(response.data.permissions.all()), 2)
        self.assertEqual(
            [p.codename for p in response.data.permissions.all()],
            ["001", "002"],
        )

    def test_remove_permissions_from_group(self):
        self.test_add_permissions_to_group()
        print(
            "Group permissions before removal:",
            self.group.data.permissions.all().count(),
        )
        data = {
            "permissions": [
                {
                    "feature": "patient_visitor_grievance",
                    "permissions": [
                        {"code_name": "001", "name": "Can create"},
                        {"code_name": "002", "name": "Can delete"},
                    ],
                }
            ]
        }

        response = self.client.delete(
            f"/api/permissions/{self.group.data.id}/remove-permissions/",
            data=data,
            format="json",
        )
        print(
            "Group permissions after removal:",
            self.group.data.permissions.all().count(),
        )
        if not response.status_code == 200:
            self.fail(response)
        self.assertTrue(response.status_code == 200)
        self.assertEqual(len(self.group.data.permissions.all()), 0)



