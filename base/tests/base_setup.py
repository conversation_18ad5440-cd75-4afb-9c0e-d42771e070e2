"""
Basic setup for the application

USERS:
- super_user (superuser)
- admin_user (admin)
-director_user (director)
- manager_user (manager)
- user_user (regular user)
"""

from django.test import TransactionTestCase
from rest_framework.test import APIClient
from accounts.tests.factory import UserProfileFactory
from base.constants import TEST_DEFAULTS

from django.contrib.auth.models import User, Group
from datetime import datetime, timedelta
import jwt
from base.tests.factory import DepartmentFactory, FacilityFactory
from core.settings import SECRET_KEY
from accounts.models import Profile


class BaseTestSetup(TransactionTestCase):
    def setUp(self):
        self.client = APIClient()
        self.endpoint = "/api/accounts"
        self.incidents_endpoint = "/api/incidents"
        self.adr_endpoint = f"{self.incidents_endpoint}/adverse-drug-reaction"
        self.general_patient_visitor_endpoint = f"{self.incidents_endpoint}/general"
        self.medication_error_endpoint = f"{self.incidents_endpoint}/medication_error"
        self.grievance_endpoint = f"{self.incidents_endpoint}/grievance"
        self.lost_and_found_endpoint = f"{self.incidents_endpoint}/lost_and_found"
        self.employee_incident_endpoint = f"{self.incidents_endpoint}/employee_incident"
        self.staff_incident_endpoint = f"{self.incidents_endpoint}/staff-incident"
        self.workplace_violence_endpoint = (
            f"{self.incidents_endpoint}/workplace_violence"
        )

        self.password = "testpassword123"
        self.patient = UserProfileFactory(profile_type="Patient")
        self.physician = UserProfileFactory(
            profile_type="Physician", patient=self.patient
        )
        self.visitor = UserProfileFactory(profile_type="Visitor", patient=self.patient)
        self.staff = UserProfileFactory(profile_type="Staff", patient=self.patient)
        self.family = UserProfileFactory(profile_type="Family", patient=self.patient)
        self.provider_info = UserProfileFactory(
            profile_type="Family", patient=self.patient
        )
        self.super_user = User.objects.create(
            username="super_user",
            email="<EMAIL>",
            first_name="Super",
            last_name="User",
            is_superuser=True,
        )

        # I added super user to this group to allow send to department to work
        qrm_group, _ = Group.objects.get_or_create(name="Quality/Risk manager")
        self.super_user.groups.add(qrm_group)

        self.super_user.set_password(self.password)
        self.super_user.save()

        self.admin_user = User.objects.create(
            username="admin_user",
            email="<EMAIL>",
            first_name="Admin",
            last_name="User",
        )
        self.admin_user.set_password(self.password)
        self.admin_user.save()

        self.director_user = User.objects.create(
            username="director_user",
            email="<EMAIL>",
            first_name="Director",
            last_name="User",
        )
        self.director_user.set_password(self.password)
        self.director_user.save()

        self.manager_user = User.objects.create(
            username="manager_user",
            email="<EMAIL>",
            first_name="Manager",
            last_name="User",
        )
        self.manager_user.set_password(self.password)
        self.manager_user.save()

        self.user_user = User.objects.create(
            username="user_user",
            email="<EMAIL>",
            first_name="User",
            last_name="User",
        )
        self.user_user.set_password(self.password)
        self.user_user.save()

        # user admin can access
        self.user_admin_can_access = User.objects.create(
            username="user_admin_can_access",
            email="<EMAIL>",
            first_name="User",
            last_name="Admin",
        )
        # user director can access
        self.user_director_can_access = User.objects.create(
            username="user_director_can_access",
            email="<EMAIL>",
            first_name="User",
            last_name="Director",
        )
        # user manager can access
        self.user_manager_can_access = User.objects.create(
            username="user_manager_can_access",
            email="<EMAIL>",
            first_name="User",
            last_name="Manager",
        )
        # user manager can access

        # Create groups and assign users
        super_user_group = Group.objects.get_or_create(name="Super user")[0]
        admin_group = Group.objects.get_or_create(name="Admin")[0]
        director_group = Group.objects.get_or_create(name="Director")[0]
        manager_group = Group.objects.get_or_create(name="Manager")[0]
        user_group = Group.objects.get_or_create(name="Staff")[0]

        super_user_group.user_set.add(self.super_user)
        admin_group.user_set.add(self.admin_user)
        director_group.user_set.add(self.director_user)
        manager_group.user_set.add(self.manager_user)
        user_group.user_set.add(self.user_user)

        # Create facilities
        self.super_user_fac = FacilityFactory()
        self.admin_user_fac = FacilityFactory()
        self.director_user_fac = FacilityFactory()
        self.manager_user_fac = FacilityFactory()
        self.user_user_fac = FacilityFactory()

        # Create departments
        self.super_user_dept = DepartmentFactory(facility=self.super_user_fac)
        self.admin_user_dept = DepartmentFactory(facility=self.admin_user_fac)
        self.director_user_dept = DepartmentFactory(facility=self.director_user_fac)
        self.manager_user_dept = DepartmentFactory(facility=self.manager_user_fac)
        self.pharmacy_manager_user_dept = DepartmentFactory(
            name="Pharmacy",
            facility=self.manager_user_fac,
            header_of_department=self.manager_user,
        )
        self.user_user_dept = DepartmentFactory(facility=self.user_user_fac)

        # Create profiles
        self.super_user_profile, _ = Profile.objects.get_or_create(
            user=self.super_user,
            defaults={
                "facility": self.super_user_fac,
                "department": self.super_user_dept,
                "is_test_account": True,
            },
        )
        self.super_user_profile.access_to_facilities.add(self.super_user_fac)
        self.super_user_profile.access_to_department.add(self.super_user_dept)

        self.admin_user_profile, _ = Profile.objects.get_or_create(
            user=self.admin_user,
            defaults={
                "facility": self.admin_user_fac,
                "department": self.admin_user_dept,
                "is_test_account": True,
            },
        )
        self.admin_user_profile.access_to_facilities.add(self.admin_user_fac)
        self.admin_user_profile.access_to_department.add(self.admin_user_dept)

        self.director_user_profile, _ = Profile.objects.get_or_create(
            user=self.director_user,
            defaults={
                "facility": self.director_user_fac,
                "department": self.director_user_dept,
                "is_test_account": True,
            },
        )
        self.director_user_profile.access_to_facilities.add(self.director_user_fac)
        self.director_user_profile.access_to_department.add(self.director_user_dept)

        self.manager_user_profile, _ = Profile.objects.get_or_create(
            user=self.manager_user,
            defaults={
                "facility": self.manager_user_fac,
                "department": self.manager_user_dept,
                "is_test_account": True,
            },
        )
        self.manager_user_profile.access_to_facilities.add(self.manager_user_fac)
        self.manager_user_profile.access_to_department.add(self.manager_user_dept)

        self.user_user_profile, _ = Profile.objects.get_or_create(
            user=self.user_user,
            defaults={
                "facility": self.user_user_fac,
                "department": self.user_user_dept,
                "is_test_account": True,
            },
        )
        self.user_user_profile.access_to_facilities.add(self.user_user_fac)
        self.user_user_profile.access_to_department.add(self.user_user_dept)

        self.user_admin_can_access_profile, _ = Profile.objects.get_or_create(
            user=self.user_admin_can_access,
            defaults={
                "facility": self.admin_user_fac,
            },
        )

        self.user_director_can_access_profile, _ = Profile.objects.get_or_create(
            user=self.user_director_can_access,
            defaults={
                "facility": self.director_user_fac,
            },
        )

        self.user_manager_can_access_profile, _ = Profile.objects.get_or_create(
            user=self.user_manager_can_access,
            defaults={
                "facility": self.manager_user_fac,
            },
        )

    def _authenticate_user(self, user):
        """Helper method to authenticate a user"""

        token_payload = {
            "user_id": user.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")

        self.client = APIClient()
        self.client.force_authenticate(user=user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")


class CleanUpDatabase:
    def __init__(self, data):
        self.data = data

    def delete_user(self):
        if self.data.get("email") == TEST_DEFAULTS.ACTIVE_EMAIL:
            try:
                user = User.objects.get(email=self.data["email"])
                user.delete()
            except User.DoesNotExist:
                pass
