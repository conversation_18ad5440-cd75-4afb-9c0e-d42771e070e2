from django.db import models
from django.contrib.auth.models import User

from accounts.models import Profile
from base.models import BaseModel
from documents.models import Document
from base.models import Facility
from base.models import Department
from reviews.models import Review


class StaffIncidentType(BaseModel):
    type = models.CharField(max_length=50)

    def __str__(self):
        return self.type


class StaffWitness(BaseModel):
    pass
    witness_name = models.ForeignKey(
        Profile, on_delete=models.SET_NULL, null=True, blank=True
    )

    def __str__(self):
        return f"Witness Name: {self.witness_name}"


# we need to add search indexes to optimize performance during searching and querying
class StaffIncidentReportBase(BaseModel):

    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES,
        max_length=255,
        default="Draft",
    )
    report_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    patient_info = models.ForeignKey(
        Profile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    incident_status = models.CharField(max_length=500, null=True, blank=True)
    current_step = models.IntegerField(default=1, null=True, blank=True)
    job_title = models.CharField(max_length=100, blank=True, null=True)
    supervisor = models.CharField(max_length=100, blank=True, null=True)
    date_of_injury_or_near_miss = models.DateField(blank=True, null=True)
    time_of_injury_or_near_miss = models.TimeField(blank=True, null=True)
    witnesses = models.ManyToManyField(StaffWitness, blank=True)
    location = models.CharField(max_length=200, blank=True, null=True)
    activity_at_time_of_incident = models.TextField(blank=True, null=True)
    incident_description = models.TextField(blank=True, null=True)
    preventive_measures = models.TextField(blank=True, null=True)
    body_parts_injured = models.TextField(blank=True, null=True)
    doctor_consulted = models.BooleanField(default=False, blank=True, null=True)
    doctor_consulted_info = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="doctor_consulted_info",
    )
    doctor_consulted_dated = models.DateField(blank=True, null=True)
    doctor_consulted_time = models.TimeField(blank=True, null=True)
    previous_injury = models.BooleanField(default=False, blank=True, null=True)
    previous_injury_date = models.DateField(blank=True, null=True)

    report_completed = models.BooleanField(default=False, blank=True, null=True)
    reviews = models.ManyToManyField(
        Review, related_name="employee_incident_reviews_field", blank=True
    )
    documents = models.ManyToManyField(
        Document, related_name="employee_incident_documents_field", blank=True
    )
    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )
    is_resolved = models.BooleanField(default=False, blank=True, null=True)

    def __str__(self):
        return f"Incident Report by {self.patient_info} on {self.date_of_injury_or_near_miss}"


class StaffIncidentReport(StaffIncidentReportBase):
    is_modified = models.BooleanField(default=False, blank=True, null=True)


class StaffIncidentReportVersion(StaffIncidentReportBase):
    original_report = models.ForeignKey(
        StaffIncidentReport,
        on_delete=models.CASCADE,
        related_name="versions",
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "staff_incident_version"


from django.db import models

from base.models import BaseModel
from documents.models import Document
from base.models import Facility
from base.models import Department
from reviews.models import Review
from accounts.models import Profile
from staff_incident_reports.models import StaffIncidentReport


class StaffIncidentInvestigation(BaseModel):
    SEX_CHOICES = [("male", "Male"), ("female", "Female"), ("other", "Other")]

    MARITAL_STATUS_CHOICES = [
        ("single", "Single"),
        ("married", "Married"),
        ("divorced", "Divorced"),
        ("widowed", "Widowed"),
    ]
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]
    report_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES,
        max_length=255,
        default="Draft",
    )
    employee_report = models.OneToOneField(
        StaffIncidentReport, null=True, blank=True, on_delete=models.SET_NULL
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    name_of_injured_staff = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="name_of_injured_staff",
    )
    date_of_hire = models.DateField(null=True, blank=True)
    marital_status = models.CharField(
        max_length=10, choices=MARITAL_STATUS_CHOICES, null=True, blank=True
    )

    part_of_body_injured = models.TextField(max_length=1000, null=True, blank=True)
    nature_of_injury = models.TextField(max_length=1000, null=True, blank=True)
    employee_prior_activity = models.TextField(max_length=1000, null=True, blank=True)
    accident_details = models.TextField(max_length=1000, null=True, blank=True)
    equipment_or_tools = models.TextField(max_length=1000, null=True, blank=True)
    witnesses = models.TextField(max_length=1000, null=True, blank=True)
    date_of_event = models.DateField(null=True, blank=True)
    time_of_event = models.TimeField(null=True, blank=True)
    event_location = models.TextField(max_length=1000, null=True, blank=True)
    cause_of_event = models.TextField(max_length=1000, null=True, blank=True)
    safety_regulations = models.TextField(max_length=1000, null=True, blank=True)
    date_claim_notified = models.DateField(null=True, blank=True)
    claim = models.CharField(max_length=100, null=True, blank=True)
    went_to_doctor_or_hospital = models.BooleanField(null=True, blank=True)
    doctor_info = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="doctor_info",
    )
    hospital_name = models.CharField(max_length=100, null=True, blank=True)
    recommendations = models.TextField(max_length=1000, null=True, blank=True)
    reviews = models.ManyToManyField(
        Review, related_name="health_investigation_reviews_field", blank=True
    )

    documents = models.ManyToManyField(
        Document,
        related_name="health_investigation_incident_documents_field",
        blank=True,
    )

    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )
    is_resolved = models.BooleanField(default=False)

    def __str__(self):
        return f"Health Incident Report: {self.name_of_injured_staff} - {self.date_of_event}"
