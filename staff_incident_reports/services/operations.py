from accounts.models import Profile
from accounts.services.user_profile.service import UserProfileService
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import check_anonymous
from base.models import Department, Facility
from base.services.forms import check_user_facility
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from base.services.responses import APIResponse, RepositoryResponse
from documents.models import Document
from incidents.services.query import IncidentQueryService
from incidents.views.send_to_department import send_incident_submission_email
from reviews.models import Review
from staff_incident_reports.models import StaffIncidentInvestigation, StaffIncidentReport
from staff_incident_reports.serializers import (
    GetStaffIncidentInvestigationSerializer,
    StaffIncidentInvestigationSerializer,
    StaffIncidentInvestigationUpdateSerializer,
    StaffIncidentReportSerializer,
    StaffIncidentReportUpdateSerializer,
)
from staff_incident_reports.new_serializers import GetStaffIncidentReportSerializer
from tasks.models import ReviewProcessTasks
from activities.services import ActivityLogService
from activities.models import ActivityType

user_profile_service = UserProfileService()


class StaffIncidentReportService:
    def __init__(self, user):
        self.logging_service = LoggingService()
        self.user = user
        self.query_service = IncidentQueryService(
            user=user,
            model=StaffIncidentReport,
            serializer=GetStaffIncidentReportSerializer,
        )

    def get_incident_by_id(self, incident_id) -> APIResponse:
        try:
            incident = self.query_service.get_incident_by_id(incident_id=incident_id)
            if not incident.success:
                return APIResponse(
                    success=False,
                    message=incident.message,
                    data=None,
                    code=400,
                )
            
            has_investigation = StaffIncidentInvestigationService(self.user)._has_investigation(
                report_id=incident_id
            )
            if not has_investigation:
                incident.data["has_investigation"] = False
            else:
                incident.data["has_investigation"] = True
            return APIResponse(
                success=True,
                message="Staff Incident Report retrieved successfully",
                data=incident.data,
                code=200,
            )
        except StaffIncidentReport.DoesNotExist:
            return APIResponse(
                success=False,
                message="Staff Incident Report not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_incidents_list(self, filters=None) -> APIResponse:
        try:
            incidents = self.query_service.get_incidents(
                user=self.user,
                model=StaffIncidentReport,
                serializer=GetStaffIncidentReportSerializer,
                prefetch_fields=[
                    "documents",
                    "reviews",
                    "review_tasks",
                    "witnesses",
                ],
                related_fields=[
                    "report_facility",
                    "department",
                    "supervisor",
                    "patient_info",
                    "doctor_consulted_info",
                    "created_by",
                    "review_process",
                ],
                filters=filters,
            )

            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Staff Incident Reports retrieved successfully",
                data=incidents.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def create_incident(self, data) -> APIResponse:
        try:
            facility_response = check_user_facility(data, self.user)
            if not facility_response.success:
                return APIResponse(
                    success=False,
                    message=facility_response.message,
                    data=None,
                    code=400,
                )
            is_facility_or_report_facility_id = any(
                key in ["facility_id", "report_facility_id"] for key in data.keys()
            )
            if not is_facility_or_report_facility_id:
                return APIResponse(
                    success=False,
                    message="Facility id is required",
                    code=400,
                )
            request_data = data.copy()
            if "facility_id" in data:
                request_data.pop("facility_id", None)
                facility_response = self._process_facility(data["facility_id"])
                if not facility_response.success:
                    return APIResponse(
                        success=False,
                        message=facility_response.message,
                        code=400,
                    )
                facility = facility_response.data
                request_data["report_facility"] = facility

            document_ids = request_data.pop("documents", [])
            review_ids = request_data.pop("reviews", [])
            review_task_ids = request_data.pop("review_tasks", [])

            profile_fields = [
                "staff_involved",
                "patient_info",
                "supervisor",
                "doctor_consulted_info",
            ]

            for field in profile_fields:
                if field in data:
                    profile_result = user_profile_service.get_or_create_profile(
                        data[field]
                    )
                    if not profile_result.success:
                        return APIResponse(
                            success=False,
                            message=profile_result.message,
                            data=None,
                            code=400,
                        )
                    request_data[field] = profile_result.data.id

            if "witnesses" in data:
                witness_profiles = []
                for witness_data in data["witnesses"]:
                    witness_result = user_profile_service.get_or_create_profile(
                        witness_data
                    )
                    if not witness_result.success:
                        return APIResponse(
                            success=False,
                            message=witness_result.message,
                            data=None,
                            code=400,
                        )
                    witness_profiles.append(witness_result.data.id)
                request_data["witnesses"] = witness_profiles

            request_data["created_by"] = self.user.id
            serializer = StaffIncidentReportSerializer(data=request_data)

            if serializer.is_valid():
                instance = serializer.save()

                if document_ids:
                    instance.documents.set(Document.objects.filter(id__in=document_ids))
                if review_ids:
                    instance.reviews.set(Review.objects.filter(id__in=review_ids))
                if review_task_ids:
                    instance.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )

                save_notification(
                    facility=serializer.data["report_facility"],
                    group_name="Admin",
                    notification_type="info",
                    notification_category="incident",
                    message="A new Staff Incident Report is submitted",
                    item_id=serializer.data["id"],
                )

                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=instance,
                    activity_type=ActivityType.CREATED,
                    description="Staff Incident Report created"
                )

                return APIResponse(
                    success=True,
                    message="Staff Incident Report created successfully",
                    data=serializer.data,
                    code=201,
                )

            self.logging_service.log_error(serializer.errors)
            return APIResponse(
                success=False, message="An error occured", data=None, code=400
            )

        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False, message="Internal server error", data=None, code=500
            )

    def update_incident(self, id, data) -> APIResponse:
        try:
            incident = StaffIncidentReport.objects.get(id=id)
            profile = Profile.objects.get(user=self.user)

            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, profile.facility)
                and not is_manager_user(self.user, incident.department)
                and not self.user == incident.created_by
            ):
                return APIResponse(
                    success=False,
                    message="You do not have enough rights to update this incident",
                    data=None,
                    code=403,
                )

            request_data = data.copy()
            document_ids = request_data.pop("documents", None)
            review_ids = request_data.pop("reviews", None)
            review_task_ids = request_data.pop("review_tasks", None)

            request_data["report_facility"] = incident.report_facility.id

            profile_fields = [
                "staff_involved",
                "patient_info",
                "supervisor",
                "doctor_consulted_info",
            ]

            for field in profile_fields:
                if field in data:
                    profile_result = user_profile_service.get_or_create_profile(
                        data[field]
                    )
                    if not profile_result.success:
                        return APIResponse(
                            success=False,
                            message=profile_result.message,
                            data=None,
                            code=400,
                        )
                    request_data[field] = profile_result.data.id

            if "witnesses" in data:
                witness_profiles = []
                for witness_data in data["witnesses"]:
                    witness_result = user_profile_service.get_or_create_profile(
                        witness_data
                    )
                    if not witness_result.success:
                        return APIResponse(
                            success=False,
                            message=witness_result.message,
                            data=None,
                            code=400,
                        )
                    witness_profiles.append(witness_result.data.id)
                request_data["witnesses"] = witness_profiles

            if "department" in data:
                try:
                    department_obj = Department.objects.get(id=data["department"])
                    request_data["department"] = department_obj.id
                except Department.DoesNotExist:
                    return APIResponse(
                        success=False,
                        message="Department not found",
                        data=None,
                        code=400,
                    )
                except Exception as e:
                    self.logging_service.log_error(e)
                    return APIResponse(
                        success=False,
                        message="Error processing department",
                        data=None,
                        code=500,
                    )

            data = check_anonymous(request_data, self.user)
            old_status = incident.status
            serializer = StaffIncidentReportUpdateSerializer(
                incident, data=data, partial=True
            )

            if serializer.is_valid():
                serializer.save()

                if document_ids is not None:
                    incident.documents.set(Document.objects.filter(id__in=document_ids))
                if review_ids is not None:
                    incident.reviews.set(Review.objects.filter(id__in=review_ids))
                if review_task_ids is not None:
                    incident.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )

                new_status = data.get("status")

                if old_status != new_status:
                    ActivityLogService.create_activity(
                        user=self.user,
                        content_object=incident,
                        activity_type=ActivityType.STATUS_CHANGED,
                        description=f"Status changed from {old_status} to {new_status}"
                    )

                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=incident,
                    activity_type=ActivityType.UPDATED,
                    description="Staff Incident Report updated"
                )

                if "status" in data and data.get("status") == "Open":
                    send_incident_submission_email(
                        incident=incident,
                        incident_type=data.get("incident_type", "Staff Incident"),
                    )

                serialized_data = GetStaffIncidentReportSerializer(incident)
                return APIResponse(
                    success=True,
                    message="Staff Incident Report updated successfully",
                    data=serialized_data.data,
                    code=200,
                )

            self.logging_service.log_error(serializer.errors)
            return APIResponse(
                success=False,
                message="An error occured due to invalid data",
                data=None,
                code=400,
            )

        except StaffIncidentReport.DoesNotExist:
            return APIResponse(
                success=False,
                message="Staff Incident Report not found",
                data=None,
                code=404,
            )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False, message="User profile not found", data=None, code=404
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False, message="Internal server error", data=None, code=500
            )
    
    def _process_facility(self, facility_id):
        try:
            facility = Facility.objects.get(id=facility_id)

            return RepositoryResponse(
                success=True, message="Facility retrieved", data=facility
            )
        except Facility.DoesNotExist:
            return APIResponse(
                success=False,
                message="Facility not found",
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving the facility",
                errors={"detail": "Internal server error"},
            )


class StaffIncidentInvestigationService:
    def __init__(self, user):
        self.user = user
        self.logging_service = LoggingService()
        self.query_service = IncidentQueryService(
            user=user,
            model=StaffIncidentInvestigation,
            serializer=GetStaffIncidentInvestigationSerializer,
        )

    def get_investigation_by_id(self, report_id, investigation_id) -> APIResponse:
        try:
            investigation = StaffIncidentInvestigation.objects.select_related(
                "report_facility", "employee_report", "department", "name_of_injured_staff", "doctor_info"
            ).prefetch_related("reviews", "documents").get(
                id=investigation_id,
                employee_report__id=report_id
            )
            serialized_data = GetStaffIncidentInvestigationSerializer(investigation).data
            return APIResponse(
                success=True,
                message="Staff incident investigation retrieved successfully",
                data=serialized_data,
                code=200,
            )
        except StaffIncidentInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Investigation not found",
                data=None,
                code=404
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500
            )

    def get_investigations_list(self, report_id, filters=None) -> APIResponse:
        try:
            report = StaffIncidentReport.objects.get(id=report_id)
            filters = filters or {}
            filters["employee_report__id"] = report.id
            investigations = self.query_service.get_incidents(
                user=self.user,
                model=StaffIncidentInvestigation,
                serializer=GetStaffIncidentInvestigationSerializer,
                prefetch_fields=["reviews", "documents"],
                related_fields=["name_of_injured_staff", "doctor_info", "employee_report", "report_facility", "department"],
                filters=filters,
            )
            if not investigations.success:
                return APIResponse(
                    success=False,
                    message=investigations.message,
                    data=None,
                    code=400,
                )
            return APIResponse(
                success=True,
                message="Investigations retrieved successfully",
                data=investigations.data,
                code=200,
            )
        except StaffIncidentReport.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident report not found",
                data=None,
                code=404
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500
            )

    def create_investigation(self, report_id, data) -> APIResponse:
        try:
            report = StaffIncidentReport.objects.get(id=report_id)

            documents = data.pop("documents", [])
            reviews = data.pop("reviews", [])

            data["employee_report"] = report.id
            data["report_facility"] = report.report_facility.id
            data["department"] = report.department.id if report.department else None

            profile_fields = ["name_of_injured_staff", "doctor_info"]

            for field in profile_fields:
                if field in data:
                    profile_result = user_profile_service.get_or_create_profile(data[field])
                    if not profile_result.success:
                        return APIResponse(
                            success=False,
                            message=profile_result.message,
                            data=None,
                            code=400
                        )
                    data[field] = profile_result.data.id

            serializer = StaffIncidentInvestigationSerializer(data=data)
            if serializer.is_valid():
                investigation = serializer.save()
                if documents:
                    investigation.documents.set(Document.objects.filter(id__in=documents))
                if reviews:
                    investigation.reviews.set(Review.objects.filter(id__in=reviews))
                investigation.save()

                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=report,
                    activity_type=ActivityType.INVESTIGATION_STARTED,
                    description="Investigation started"
                )

                return APIResponse(
                    success=True,
                    message="Investigation created successfully",
                    data=GetStaffIncidentInvestigationSerializer(investigation).data,
                    code=201
                )
            else:
                return APIResponse(
                    success=False,
                    message=serializer.errors,
                    data=None,
                    code=400
                )
        except StaffIncidentReport.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident report not found",
                data=None,
                code=404
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500
            )

    def update_investigation(self, investigation_id, report_id, data) -> APIResponse:
        try:
            StaffIncidentReport.objects.get(id=report_id)
            investigation = StaffIncidentInvestigation.objects.get(id=investigation_id)
            profile = Profile.objects.get(user=self.user)

            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, profile.facility)
                and not is_manager_user(self.user, investigation.department)
                and not self.user == investigation.created_by
            ):
                return APIResponse(
                    success=False,
                    message="You do not have permission to update this investigation",
                    data=None,
                    code=403
                )

            documents = data.pop("documents", None)
            reviews = data.pop("reviews", None)
            profile_fields = ["name_of_injured_staff", "doctor_info"]

            for field in profile_fields:
                if field in data:
                    profile_result = user_profile_service.get_or_create_profile(data[field])
                    if not profile_result.success:
                        return APIResponse(
                            success=False,
                            message=profile_result.message,
                            data=None,
                            code=400
                        )
                    data[field] = profile_result.data.id

            serializer = StaffIncidentInvestigationUpdateSerializer(investigation, data=data, partial=True)
            if serializer.is_valid():
                updated_instance = serializer.save()
                if documents is not None:
                    updated_instance.documents.set(Document.objects.filter(id__in=documents))
                if reviews is not None:
                    updated_instance.reviews.set(Review.objects.filter(id__in=reviews))
                updated_instance.save()

                if investigation.status != updated_instance.status:
                    if updated_instance.status == "Closed":
                        ActivityLogService.create_activity(
                            user=self.user,
                            content_object=updated_instance,
                            activity_type=ActivityType.INVESTIGATION_COMPLETED,
                            description="Investigation completed"
                        )
                    else:
                        ActivityLogService.create_activity(
                            user=self.user,
                            content_object=updated_instance,
                            activity_type=ActivityType.UPDATED,
                            description="Investigation updated"
                        )

                return APIResponse(
                    success=True,
                    message="Investigation updated successfully",
                    data=GetStaffIncidentInvestigationSerializer(updated_instance).data,
                    code=200
                )
            else:
                return APIResponse(
                    success=False,
                    message=serializer.errors,
                    data=None,
                    code=400
                )

        except StaffIncidentReport.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident report not found",
                data=None,
                code=404
            )
        except StaffIncidentInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Investigation not found",
                data=None,
                code=404
            )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                message="Profile not found",
                data=None,
                code=404
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500
            )

    def _has_investigation(self, report_id) -> bool:
        try:
            return StaffIncidentInvestigation.objects.filter(
                employee_report__id=report_id
            ).exists()
        except Exception as e:
            self.logging_service.log_error(e)
            return False