from rest_framework import serializers
from accounts.serializers import UserProfileSerializer
from facilities.serializers import FacilitySerializer
from staff_incident_reports.models import StaffIncidentReport
from base.serializers import BaseModelSerializer, DepartmentSerializer


class GetStaffIncidentReportSerializer(BaseModelSerializer):
    report_facility = FacilitySerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)
    patient_info = UserProfileSerializer(read_only=True)
    supervisor = UserProfileSerializer(read_only=True)
    witnesses = UserProfileSerializer(many=True, read_only=True)
    doctor_consulted_info = UserProfileSerializer(read_only=True)

    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

    def get_doctor_consulted(self, obj):
        if obj.doctor_consulted_info:
            return {
                "id": obj.doctor_consulted_info.id,
                "first_name": obj.doctor_consulted_info.first_name,
                "last_name": obj.doctor_consulted_info.last_name,
                "email": obj.doctor_consulted_info.email,
            }
        return None

    def get_supervisor(self, obj):
        if obj.get_supervisor:
            return {
                "id": obj.get_supervisor.id,
                "first_name": obj.get_supervisor.first_name,
                "last_name": obj.get_supervisor.last_name,
                "email": obj.get_supervisor.email,
            }
        return None
