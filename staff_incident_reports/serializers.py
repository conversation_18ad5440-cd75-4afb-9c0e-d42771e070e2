# serializers.py

from rest_framework import serializers

from accounts.models import Profile
from accounts.serializers import GetProfileSerializer, UserProfileSerializer, UserSerializer
from base.models import GeneralWitness
from base.serializers import DepartmentSerializer
from documents.serializers import DocumentSerializer
from facilities.serializers import FacilitySerializer
from general_patient_visitor.models import GeneralPatientVisitor
from staff_incident_reports.models import (
    StaffIncidentInvestigation,
    StaffIncidentReport,
    StaffIncidentReportVersion,
)

from general_patient_visitor.models import GeneralPatientVisitor


class IncidentSerializer(serializers.ModelSerializer):
    class Meta:
        model = GeneralPatientVisitor
        fields = "__all__"


class WitnessSerializer(serializers.ModelSerializer):
    class Meta:
        model = GeneralWitness
        fields = "__all__"


class HealthInvestigationSerializer(serializers.ModelSerializer):

    class Meta:
        model = StaffIncidentInvestigation
        fields = "__all__"


class GetHealthInvestigationSerializer(serializers.ModelSerializer):
    name_of_injured_staff = GetProfileSerializer()
    created_by = UserSerializer()
    doctor_info = GetProfileSerializer()

    class Meta:
        model = StaffIncidentInvestigation
        fields = "__all__"


class InitialReportSerializer(serializers.ModelSerializer):

    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

    # def __init__(self, *args, **kwargs):
    #     super(InitialReportSerializer, self).__init__(*args, **kwargs)

    #     for field in self.fields.values():
    #         field.required = True


class IncidentDescriptionSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()

    class Meta:
        model = StaffIncidentReport
        fields = [
            "id",
            "location",
            "activity_at_time_of_incident",
            "incident_description",
            "preventive_measures",
        ]

    def __init__(self, *args, **kwargs):
        super(IncidentDescriptionSerializer, self).__init__(*args, **kwargs)

        for field in self.fields.values():
            field.required = True


class FinalReportSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()

    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super(FinalReportSerializer, self).__init__(*args, **kwargs)

        for field in self.fields.values():
            field.required = True


class ReportCompletedSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()

    class Meta:
        model = StaffIncidentReport
        fields = ["id", "report_completed"]

    def __init__(self, *args, **kwargs):
        super(ReportCompletedSerializer, self).__init__(*args, **kwargs)

        for field in self.fields.values():
            field.required = True


class FullReportSerializer(serializers.ModelSerializer):
    patient_info = GetProfileSerializer()
    doctor_consulted_info = GetProfileSerializer()
    report_facility = FacilitySerializer()

    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super(FullReportSerializer, self).__init__(*args, **kwargs)

        for field in self.fields.values():
            field.required = True

    def update(self, instance, validated_data):
        witnesses_data = validated_data.pop("witnesses", None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance


class ModifyReportSerializer(serializers.ModelSerializer):
    witnesses = WitnessSerializer(many=True)
    patient_info = serializers.PrimaryKeyRelatedField(
        queryset=Profile.objects.all(), allow_null=True
    )
    doctor_consulted_info = serializers.PrimaryKeyRelatedField(
        queryset=Profile.objects.all(), allow_null=True
    )

    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def update(self, instance, validated_data):
        witnesses_data = validated_data.pop("witnesses", None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance


class ModifyReportVersionSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffIncidentReportVersion
        fields = "__all__"


class IDResponseSerializer(serializers.Serializer):
    id = serializers.IntegerField()

    class Meta:
        model = StaffIncidentReport
        fields = ["id"]

class StaffIncidentReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffIncidentReport
        fields = "__all__"

class StaffIncidentReportUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffIncidentReport
        fields = "__all__"


class GetStaffIncidentInvestigationSerializer(serializers.ModelSerializer):
    employee_report = StaffIncidentReportSerializer(read_only=True)
    report_facility = FacilitySerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)
    name_of_injured_staff = UserProfileSerializer(read_only=True)
    doctor_info = UserProfileSerializer(read_only=True)
    documents = DocumentSerializer(many=True, read_only=True)

    class Meta:
        model = StaffIncidentInvestigation
        fields = "__all__"


class StaffIncidentInvestigationUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffIncidentInvestigation
        fields = "__all__"
        read_only_fields = ["id", "created_by", "created_at", "updated_at"]


class StaffIncidentInvestigationSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffIncidentInvestigation
        fields = "__all__"


