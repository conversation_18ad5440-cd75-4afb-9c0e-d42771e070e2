from django.db import models
from django.contrib.auth.models import User
from accounts.models import UserProfile, Profile
from base.models import BaseModel, Department, Facility
from base.services.permissions.mixins import IncidentsPermissionsMixin
from documents.models import Document
from reviews.models import Review
from tasks.models import ReviewProcess, ReviewProcessTasks


class StaffIncidentType(BaseModel):
    type = models.CharField(max_length=50)

    def __str__(self):
        return self.type


class StaffIncidentReportBase(BaseModel):
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]

    # Status and Basic Info
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES, max_length=255, default="Draft"
    )
    report_facility = models.ForeignKey(
        Facility, blank=True, null=True, on_delete=models.SET_NULL
    )
    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    incident_status = models.CharField(max_length=500, null=True, blank=True)
    is_resolved = models.BooleanField(default=False, blank=True, null=True)

    # Staff Information
    patient_info = models.ForeignKey(
        UserProfile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_patient_info",
    )
    job_title = models.CharField(max_length=100, blank=True, null=True)
    supervisor = models.ForeignKey(
        UserProfile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_supervisor_info",
    )

    # Incident Details
    date_of_injury_or_near_miss = models.DateField(blank=True, null=True)
    time_of_injury_or_near_miss = models.TimeField(blank=True, null=True)
    witnesses = models.ManyToManyField(UserProfile, blank=True)
    location = models.CharField(max_length=200, blank=True, null=True)
    activity_at_time_of_incident = models.TextField(blank=True, null=True)
    incident_description = models.TextField(blank=True, null=True)
    preventive_measures = models.TextField(blank=True, null=True)
    body_parts_injured = models.TextField(blank=True, null=True)

    # Medical Consultation
    doctor_consulted = models.BooleanField(default=False, blank=True, null=True)
    doctor_consulted_info = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_doctor_consulted_info",
    )
    doctor_consulted_dated = models.DateField(blank=True, null=True)
    doctor_consulted_time = models.TimeField(blank=True, null=True)

    # Previous Injury
    previous_injury = models.BooleanField(default=False, blank=True, null=True)
    previous_injury_date = models.DateField(blank=True, null=True)

    # Status and Related Models
    report_completed = models.BooleanField(default=False, blank=True, null=True)
    reviews = models.ManyToManyField(
        Review, related_name="%(class)s_employee_incident_reviews_field", blank=True
    )
    documents = models.ManyToManyField(
        Document, related_name="%(class)s_employee_incident_documents_field", blank=True
    )

    def __str__(self):
        return f"Incident Report by {self.patient_info} on {self.date_of_injury_or_near_miss}"

    class Meta:
        abstract = True
        permissions = IncidentsPermissionsMixin.custom_permissions


class StaffIncidentReport(StaffIncidentReportBase):
    is_modified = models.BooleanField(default=False, blank=True, null=True)
    review_process = models.ForeignKey(
        ReviewProcess,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_review_process",
    )
    review_tasks = models.ManyToManyField(
        ReviewProcessTasks,
        blank=True,
        related_name="%(class)s_review_tasks",
    )


class StaffIncidentReportVersion(StaffIncidentReportBase):
    original_report = models.ForeignKey(
        StaffIncidentReport,
        on_delete=models.CASCADE,
        related_name="versions",
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "staff_incident_version"


class StaffIncidentInvestigation(BaseModel):
    SEX_CHOICES = [("male", "Male"), ("female", "Female"), ("other", "Other")]

    MARITAL_STATUS_CHOICES = [
        ("single", "Single"),
        ("married", "Married"),
        ("divorced", "Divorced"),
        ("widowed", "Widowed"),
    ]

    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]

    # Status and Basic Info
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES, max_length=255, default="Draft"
    )
    report_facility = models.ForeignKey(
        Facility, blank=True, null=True, on_delete=models.SET_NULL
    )
    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    employee_report = models.OneToOneField(
        StaffIncidentReport, null=True, blank=True, on_delete=models.SET_NULL
    )
    is_resolved = models.BooleanField(default=False)

    # Staff Information
    name_of_injured_staff = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_name_of_injured_staff",
    )
    date_of_hire = models.DateField(null=True, blank=True)
    marital_status = models.CharField(
        max_length=10, choices=MARITAL_STATUS_CHOICES, null=True, blank=True
    )

    # Injury Details
    part_of_body_injured = models.TextField(max_length=1000, null=True, blank=True)
    nature_of_injury = models.TextField(max_length=1000, null=True, blank=True)

    # Incident Information
    employee_prior_activity = models.TextField(max_length=1000, null=True, blank=True)
    accident_details = models.TextField(max_length=1000, null=True, blank=True)
    equipment_or_tools = models.TextField(max_length=1000, null=True, blank=True)
    witnesses = models.TextField(max_length=1000, null=True, blank=True)
    date_of_event = models.DateField(null=True, blank=True)
    time_of_event = models.TimeField(null=True, blank=True)
    event_location = models.TextField(max_length=1000, null=True, blank=True)
    cause_of_event = models.TextField(max_length=1000, null=True, blank=True)
    safety_regulations = models.TextField(max_length=1000, null=True, blank=True)

    # Claim and Medical Information
    date_claim_notified = models.DateField(null=True, blank=True)
    claim = models.CharField(max_length=100, null=True, blank=True)
    went_to_doctor_or_hospital = models.BooleanField(null=True, blank=True)
    doctor_info = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_doctor_info",
    )
    hospital_name = models.CharField(max_length=100, null=True, blank=True)

    # Follow-up
    recommendations = models.TextField(max_length=1000, null=True, blank=True)

    # Related Models
    reviews = models.ManyToManyField(
        Review, related_name="%(class)s_health_investigation_reviews_field", blank=True
    )
    documents = models.ManyToManyField(
        Document,
        related_name="%(class)s_health_investigation_incident_documents_field",
        blank=True,
    )

    def __str__(self):
        return f"Health Incident Report: {self.name_of_injured_staff} - {self.date_of_event}"
