from base.tests.base_setup import BaseTestSetup
from documents.models import Document
from staff_incident_reports.models import StaffIncidentReport
from staff_incident_reports.services.actions import StaffIncidentReportActionService
from staff_incident_reports.services.operations import (
    StaffIncidentInvestigationService,
    StaffIncidentReportService,
)
from staff_incident_reports.tests.factory import (
    StaffIncidentInvestigationFactory,
    StaffIncidentReportFactory,
)
from base.tests.factory import (
    ProfileFactory,
    UserProfileFactory,
)
from tasks.tests.factory import ReviewTemplateFactory


class TestStaffIncidentReportModifyIncidentNewUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = StaffIncidentReportFactory(created_by=self.super_user)
        self.service = StaffIncidentReportActionService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "patient_info": {
                    "first_name": "<PERSON>",
                    "last_name": "<PERSON>",
                    "email": "<EMAIL>",
                    "profile_type": "Patient",
                },
                "supervisor": {
                    "first_name": "<PERSON>",
                    "last_name": "<PERSON>",
                    "profile_type": "Staff",
                },
                "doctor_consulted_info": {
                    "first_name": "Dr",
                    "last_name": "Who",
                    "profile_type": "Doctor",
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_fields(self):
        self.service.data.get("patient_info").pop("profile_type")
        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestStaffIncidentReportModifyIncidentExistingUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = StaffIncidentReportFactory(created_by=self.super_user)
        self.service = StaffIncidentReportActionService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "patient_info": {
                    "user_id": self.patient.id,
                },
                "supervisor": {
                    "user_id": self.provider_info.id,
                },
                "doctor_consulted_info": {
                    "user_id": self.physician.id,
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_user_id(self):
        self.service.data.get("supervisor").pop("user_id")
        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_modify_incident_invalid_user_id(self):
        self.service.data.get("doctor_consulted_info")["user_id"] = 9999
        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestStaffIncidentReportSendForReview(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = StaffIncidentReportFactory(created_by=self.super_user)
        self.review_template = ReviewTemplateFactory()
        self.assignee_1 = ProfileFactory()
        self.assignee_2 = ProfileFactory()
        self.service = StaffIncidentReportActionService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "review_template": self.review_template.id,
            },
        )

    def test_send_for_review_success(self):
        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_send_for_review_invalid_template(self):
        self.service.data["review_template"] = 9999
        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_missing_template(self):
        self.service.data.pop("review_template")
        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_with_assignees_and_template(self):
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]
        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_with_assignees_only(self):
        self.service.data.pop("review_template")
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]
        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestStaffIncidentReportMarkClose(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = StaffIncidentReportFactory(created_by=self.super_user)
        self.service = StaffIncidentReportActionService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={"action": "mark-closed"},
        )

    def test_mark_close_success(self):
        response = self.service.mark_closed()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestStaffIncidentGetById(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = StaffIncidentReportService(user=self.super_user)

    def test_get_incident_by_id(self):
        incident = StaffIncidentReportFactory(created_by=self.super_user)
        response = self.service.get_incident_by_id(incident.id)

        self.assertTrue(response.success)
        self.assertFalse(response.data["has_investigation"])
        self.assertIsNotNone(response.data)

    def test_get_incident_by_id_not_found(self):
        response = self.service.get_incident_by_id(9999)
        self.assertFalse(response.success)


class TestStaffIncidentReportServiceCase(BaseTestSetup):
    """Base test case for Staff Incident Report Service operations"""

    def setUp(self):
        super().setUp()
        self.service = StaffIncidentReportService(user=self.super_user)

    def test_get_incidents_as_superuser(self):
        """Creating incidents for super user access"""
        # 5 in authorized facility
        for _ in range(5):
            StaffIncidentReportFactory(
                department=self.super_user_dept,
                report_facility=self.super_user_fac,
                created_by=self.super_user,
            )

        # 2 in another facility
        for _ in range(2):
            StaffIncidentReportFactory()

        response = self.service.get_incidents_list()

        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 7)


class TestStaffIncidentReportServiceAsAdmin(BaseTestSetup):
    """Test Staff Incident Report Service with Admin user role"""

    def setUp(self):
        super().setUp()
        self.service = StaffIncidentReportService(user=self.admin_user)

    def test_get_incidents_as_admin(self):
        """Admin users should see incidents from facilities they have access to"""
        # Create incidents in admin's facility
        StaffIncidentReportFactory.create_batch(
            3,
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )

        # Create incidents in other facility (should not be visible)
        StaffIncidentReportFactory.create_batch(2)

        response = self.service.get_incidents_list()
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 3)


class TestStaffIncidentReportServiceAsDirector(BaseTestSetup):
    """Test Staff Incident Report Service with Director user role"""

    def setUp(self):
        super().setUp()
        self.service = StaffIncidentReportService(user=self.director_user)

    def test_get_incidents_as_director(self):
        """Director users should see incidents from their facility"""
        # Create incidents in director's facility
        StaffIncidentReportFactory.create_batch(
            4,
            created_by=self.director_user,
            department=self.director_user_dept,
            report_facility=self.director_user_fac,
        )

        # Create incidents in other facility (should not be visible)
        StaffIncidentReportFactory.create_batch(3)

        response = self.service.get_incidents_list()
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 4)


class TestStaffIncidentReportServiceAsManager(BaseTestSetup):
    """Test Staff Incident Report Service with Manager user role"""

    def setUp(self):
        super().setUp()
        self.service = StaffIncidentReportService(user=self.manager_user)

    def test_get_incidents_as_manager(self):
        """Manager users should see incidents from departments they have access to"""
        # Create incidents in manager's department
        StaffIncidentReportFactory.create_batch(
            3,
            created_by=self.manager_user,
            department=self.manager_user_dept,
            report_facility=self.manager_user_fac,
        )

        # Create incidents in other department (should not be visible)
        StaffIncidentReportFactory.create_batch(2)

        response = self.service.get_incidents_list()
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 3)


class TestStaffIncidentReportServiceAsRegularUser(BaseTestSetup):
    """Test Staff Incident Report Service with Regular User role"""

    def setUp(self):
        super().setUp()
        self.service = StaffIncidentReportService(user=self.user_user)

    def test_get_incidents_as_regular_user(self):
        """Regular users should see incidents they created or are assigned to review"""
        # Create incidents created by the regular user
        StaffIncidentReportFactory.create_batch(
            2,
            created_by=self.user_user,
            department=self.user_user_dept,
            report_facility=self.user_user_fac,
        )

        # Create incidents by other users (should not be visible)
        StaffIncidentReportFactory.create_batch(
            3,
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )

        response = self.service.get_incidents_list()
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 2)


class TestCreateStaffIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = StaffIncidentReportService(user=self.super_user)
        self.staff = UserProfileFactory(profile_type="Staff")
        witnesses = [UserProfileFactory(profile_type="Witness").id for _ in range(3)]

        self.valid_data = {
            "facility_id": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "date_of_injury_or_near_miss": "2025-05-21",
            "time_of_injury_or_near_miss": "12:00:00",
            "location": "Nurse Station",
            "incident_description": "Staff slipped and fell",
            "witnesses": [{"user_id": witnesses[0]}, {"user_id": witnesses[1]}],
            "staff_involved": {"user_id": self.staff.id},
            "preventive_measures": "Wear masks",
            "status": "Draft",
        }

    def test_create_incident_success(self):
        response = self.service.create_incident(self.valid_data)
        # test_create_incident_success: {response}
        pass

        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertIsNotNone(response.data["id"])
        self.assertEqual(response.data["location"], self.valid_data["location"])

    def test_create_incident_minimal_fields(self):
        data = {
            "facility_id": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "activity_at_time_of_incident": "Surgery",
            "preventive_measures": "Non",
        }

        response = self.service.create_incident(data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)

    def test_create_incident_with_invalid_facility(self):
        data = self.valid_data.copy()
        data["facility_id"] = 99999

        response = self.service.create_incident(data)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_create_incident_with_documents(self):
        doc = Document.objects.create(
            name="Injury Photo", document_url="http://example.com/photo.pdf"
        )
        data = self.valid_data.copy()
        data["documents"] = [doc.id]

        response = self.service.create_incident(data)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["documents"]), 1)


class TestUpdateStaffIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = StaffIncidentReportService(user=self.super_user)
        self.staff = UserProfileFactory(profile_type="Staff")

        self.incident = StaffIncidentReportFactory(
            report_facility=self.super_user_fac,
            department=self.super_user_dept,
            created_by=self.super_user,
        )

        self.update_data = {
            "status": "Open",
            "incident_description": "Updated description",
            "staff_involved": {"user_id": self.staff.id},
            "date_of_injury_or_near_miss": "2025-05-22",
            "time_of_injury_or_near_miss": "13:00:00",
            "location": "Updated Location",
        }

    def test_update_incident_success(self):
        response = self.service.update_incident(self.incident.id, self.update_data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["status"], "Open")
        self.assertEqual(response.data["location"], self.update_data["location"])

    def test_update_nonexistent_incident(self):
        response = self.service.update_incident(9999, self.update_data)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_update_unauthorized_user(self):
        service = StaffIncidentReportService(user=self.user_user)
        response = service.update_incident(self.incident.id, self.update_data)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)

    def test_partial_update(self):
        data = {
            "incident_description": "Partially updated",
        }
        response = self.service.update_incident(self.incident.id, data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestStaffIncidentInvestigationServiceGetById(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = StaffIncidentInvestigationService(user=self.super_user)

    def test_get_investigation_by_id_success(self):
        report = StaffIncidentReportFactory(created_by=self.super_user)
        investigation = StaffIncidentInvestigationFactory(employee_report=report)

        response = self.service.get_investigation_by_id(report.id, investigation.id)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_investigation_by_id_not_found(self):
        response = self.service.get_investigation_by_id(9999, 8888)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)


class TestListStaffIncidentInvestigationService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = StaffIncidentInvestigationService(user=self.user_user)

    def test_get_investigations_list(self):

        for _ in range(5):
            report = StaffIncidentReportFactory(created_by=self.user_user)
            StaffIncidentInvestigationFactory(
                employee_report=report,
                report_facility=self.super_user_fac,
                department=self.super_user_dept,
            )
        StaffIncidentInvestigationFactory.create_batch(2)

        response = self.service.get_investigations_list(report_id=report.id)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 1)


class TestCreateStaffIncidentInvestigationService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = StaffIncidentInvestigationService(user=self.super_user)
        self.report = StaffIncidentReportFactory(created_by=self.super_user)

        self.valid_data = {
            "status": "Draft",
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "name_of_injured_staff": {"user_id": self.staff.id},
            "date_of_hire": "2024-01-01",
            "marital_status": "single",
            "documents": [],
        }

    def test_create_investigation_success(self):
        response = self.service.create_investigation(self.report.id, self.valid_data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertIsNotNone(response.data["id"])

    def test_create_investigation_missing_required_fields(self):
        invalid_data = self.valid_data.copy()
        invalid_data.get("name_of_injured_staff").pop("user_id")
        response = self.service.create_investigation(self.report.id, invalid_data)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_create_investigation_with_documents(self):
        document = Document.objects.create(
            name="Incident Photo", document_url="http://example.com/photo.jpg"
        )
        data = self.valid_data.copy()
        data["documents"] = [document.id]

        response = self.service.create_investigation(self.report.id, data)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["documents"]), 1)


class TestUpdateStaffIncidentInvestigationService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = StaffIncidentInvestigationService(user=self.super_user)
        self.report = StaffIncidentReportFactory(
            report_facility=self.super_user_fac,
            department=self.super_user_dept,
            created_by=self.super_user,
        )
        self.investigation = StaffIncidentInvestigationFactory(
            employee_report=self.report
        )

        self.update_data = {
            "status": "Pending Review",
            "cause_of_event": "Updated cause",
            "safety_regulations": "Updated safety notes",
            "documents": [],
        }

    def test_update_investigation_success(self):
        response = self.service.update_investigation(
            self.investigation.id, self.report.id, self.update_data
        )
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["status"], "Pending Review")

    def test_update_investigation_not_found(self):
        response = self.service.update_investigation(
            99999, self.report.id, self.update_data
        )
        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_update_investigation_unauthorized(self):
        unauthorized_service = StaffIncidentInvestigationService(user=self.user_user)
        response = unauthorized_service.update_investigation(
            self.investigation.id, self.report.id, self.update_data
        )
        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)

    def test_update_investigation_with_documents(self):
        doc = Document.objects.create(
            name="X-ray Report", document_url="http://example.com/xray.pdf"
        )
        data = self.update_data.copy()
        data["documents"] = [doc.id]

        response = self.service.update_investigation(
            self.investigation.id, self.report.id, data
        )
        self.assertTrue(response.success)
        self.assertEqual(doc.id, response.data["documents"][0].get("id"))
