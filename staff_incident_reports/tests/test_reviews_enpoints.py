from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserFactory
from reviews.tests.factory import ReviewFactory
from staff_incident_reports.tests.factory import StaffIncidentReportFactory


class TestGetIncidentReviews(BaseTestSetup):
    """Test getting incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    # case for staff incident reports
    def test_get_staff_incident_reports_reviews(self):
        """Test getting reviews for a staff incident report"""
        incident = StaffIncidentReportFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(3, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        self._authenticate_user(self.user)
        response = self.client.get(
            f"/api/incidents/staff-incident/{incident.id}/reviews/"
        )

        if not response.status_code == 200:
            self.fail(f"Failed to get reviews: {response.data}")
        # check number of reviews returned
        self.assertEqual(len(response.data), 3)


class TestCreateIncidentReview(BaseTestSetup):
    """Test creating incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    def test_create_staff_incident_report_review(self):
        """Test creating a review for a staff incident report"""
        incident = StaffIncidentReportFactory()
        self._authenticate_user(self.user)
        # clear existing reviews
        incident.reviews.clear()
        response = self.client.post(
            f"/api/incidents/staff-incident/{incident.id}/reviews/",
            data={
                "content": "This is a test review for staff incident report",
            },
            format="json",
        )

        if not response.status_code == 201:
            self.fail(f"Failed to create review: {response.data}")
        # check that the review was created
        self.assertEqual(
            incident.reviews.first().content,
            "This is a test review for staff incident report",
        )
