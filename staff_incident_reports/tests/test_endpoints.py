from django.urls import reverse
from rest_framework import status
from base.tests.base_setup import BaseTestSetup
from staff_incident_reports.models import StaffIncidentReport
from staff_incident_reports.tests.factory import (
    StaffIncidentInvestigationFactory,
    StaffIncidentReportFactory,
)
from base.tests.factory import UserProfileFactory
from datetime import date, time

from tasks.tests.factory import ReviewTemplateFactory


class TestStaffIncidentReportAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/staff-incident/"
        self._authenticate_user(self.super_user)

        self.patient_info = UserProfileFactory(profile_type="Staff")
        self.supervisor = UserProfileFactory(profile_type="Staff")

        self.valid_data = {
            "facility_id": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "status": "Open",
            "patient_info": {"user_id": self.patient_info.id},
            "job_title": "Nurse",
            "supervisor": {"user_id": self.supervisor.id},
            "date_of_injury_or_near_miss": date.today().isoformat(),
            "time_of_injury_or_near_miss": time(14, 30).isoformat(),
            "location": "Room 201",
            "activity_at_time_of_incident": "Assisting patient",
            "incident_description": "Slip and fall while lifting patient",
            "preventive_measures": "Use lifting equipment",
            "body_parts_injured": "Lower back",
            "doctor_consulted": True,
            "doctor_consulted_info": {"user_id": self.supervisor.id},
            "doctor_consulted_dated": date.today().isoformat(),
            "doctor_consulted_time": time(15, 0).isoformat(),
            "previous_injury": False,
        }

    def test_create_incident_success(self):
        """Test POST request creates incident successfully"""
        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["job_title"], self.valid_data["job_title"])
        self.assertEqual(response.data["location"], self.valid_data["location"])

    def test_create_incident_invalid_data(self):
        """Test POST request fails with invalid data"""
        invalid_data = self.valid_data.copy()
        invalid_data["patient_info"]["user_id"] = None

        response = self.client.post(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_incident_unauthenticated(self):
        """Test POST request fails without authentication"""
        self.client.logout()

        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestStaffIncidentReportAPIAsSuperUser(BaseTestSetup):
    """Test Staff Incident Report API endpoints as Super User"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/staff-incident/"
        self._authenticate_user(self.super_user)

    def test_get_incidents_as_superuser(self):
        """Test superuser can access all incidents"""
        # Create incidents in super user's facility
        StaffIncidentReportFactory.create_batch(
            3, created_by=self.super_user, report_facility=self.super_user_fac
        )

        # Create incidents in other facilities (should be accessible to superuser)
        StaffIncidentReportFactory.create_batch(2)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 5)

    def test_get_incidents_with_filters(self):
        """Test getting filtered incidents"""
        StaffIncidentReportFactory(
            status="Draft",
            created_by=self.super_user,
            report_facility=self.super_user_fac,
        )
        StaffIncidentReportFactory(
            status="Open",
            created_by=self.super_user,
            report_facility=self.super_user_fac,
        )

        response = self.client.get(f"{self.url}?status=Draft")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)


class TestStaffIncidentReportAPIAsAdmin(BaseTestSetup):
    """Test Staff Incident Report API endpoints as Admin"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/staff-incident/"
        self._authenticate_user(self.admin_user)

    def test_get_incidents_as_admin(self):
        """Test admin can access incidents from their facility"""
        # Create incidents in admin's facility
        StaffIncidentReportFactory.create_batch(
            3,
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )

        # Create incidents in other facility (should not be accessible)
        StaffIncidentReportFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)


class TestStaffIncidentReportAPIAsDirector(BaseTestSetup):
    """Test Staff Incident Report API endpoints as Director"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/staff-incident/"
        self._authenticate_user(self.director_user)

    def test_get_incidents_as_director(self):
        """Test director can access incidents from their facility"""
        # Create incidents in director's facility
        StaffIncidentReportFactory.create_batch(
            4,
            created_by=self.director_user,
            department=self.director_user_dept,
            report_facility=self.director_user_fac,
        )

        # Create incidents in other facility (should not be accessible)
        StaffIncidentReportFactory.create_batch(2)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 4)


class TestStaffIncidentReportAPIAsManager(BaseTestSetup):
    """Test Staff Incident Report API endpoints as Manager"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/staff-incident/"
        self._authenticate_user(self.manager_user)

    def test_get_incidents_as_manager(self):
        """Test manager can access incidents from their department"""
        # Create incidents in manager's department
        StaffIncidentReportFactory.create_batch(
            3,
            created_by=self.manager_user,
            department=self.manager_user_dept,
            report_facility=self.manager_user_fac,
        )

        # Create incidents in other department (should not be accessible)
        StaffIncidentReportFactory.create_batch(2)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)


class TestStaffIncidentReportAPIAsRegularUser(BaseTestSetup):
    """Test Staff Incident Report API endpoints as Regular User"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/staff-incident/"
        self._authenticate_user(self.user_user)

    def test_get_incidents_as_regular_user(self):
        """Test regular user can access incidents they created or are assigned to review"""
        # Create incidents created by the regular user
        StaffIncidentReportFactory.create_batch(
            2,
            created_by=self.user_user,
            department=self.user_user_dept,
            report_facility=self.user_user_fac,
        )

        # Create incidents by other users (should not be visible)
        StaffIncidentReportFactory.create_batch(
            3,
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)


class TestStaffIncidentReportDetailsAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.report = StaffIncidentReportFactory(
            created_by=self.super_user,
            department=self.super_user_dept,
            report_facility=self.super_user_fac,
        )
        self.url = f"/api/incidents/staff-incident/{self.report.id}/"
        self._authenticate_user(self.super_user)
        self.update_data = {
            "job_title": "Updated Job Title",
            "location": "Updated Room 305",
        }

    def test_get_incident_details(self):
        """Test GET request returns incident details"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_nonexistent_incident(self):
        """Test GET request for non-existent incident"""
        response = self.client.get("/api/incidents/staff-incident/99999/")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_incident(self):
        """Test PUT request updates incident"""
        response = self.client.put(self.url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["job_title"], self.update_data["job_title"])

    def test_patch_send_for_review(self):
        """Test PATCH request sends incident for review"""
        review_template = ReviewTemplateFactory()
        data = {
            "action": "send-for-review",
            "review_template": review_template.id,
            "description": "Please review this case",
        }

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_mark_closed(self):
        """Test PATCH request marks incident as closed"""
        data = {"action": "mark-closed"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_modify_incident(self):
        """Test PATCH request modifies incident"""
        data = {"action": "modify", "location": "Modified Room 204"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["location"], data["location"])

    def test_patch_invalid_action(self):
        """Test PATCH request with invalid action"""
        data = {"action": "invalid-action"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_patch_missing_action(self):
        """Test PATCH request without action"""
        response = self.client.patch(self.url, {}, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class TestStaffIncidentInvestigationAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.staff_incident = StaffIncidentReportFactory(created_by=self.super_user)
        self.url = (
            f"{self.staff_incident_endpoint}/{self.staff_incident.id}/investigation/"
        )
        self._authenticate_user(self.super_user)
        self.valid_data = {
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "status": "Draft",
            "recommendations": "Test recommendations",
            "name_of_injured_staff": {"user_id": self.staff.id},
            "documents": [],
        }

    def test_get_investigations_list(self):
        """Test GET request returns list of investigations"""
        StaffIncidentInvestigationFactory(
            employee_report=self.staff_incident,
            report_facility=self.super_user_fac,
            department=self.super_user_dept,
        )

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_get_investigations_with_filters(self):
        """Test GET request with filters"""
        StaffIncidentInvestigationFactory(
            employee_report=self.staff_incident, status="Draft"
        )

        response = self.client.get(f"{self.url}?status=Draft")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_get_investigations_multiple_filters(self):
        """Test GET request with multiple filters"""
        StaffIncidentInvestigationFactory(
            employee_report=self.staff_incident,
            status="Open",
            report_facility=self.super_user_fac,
        )
        StaffIncidentInvestigationFactory(
            status="Draft",
            report_facility=self.super_user_fac,
            employee_report=StaffIncidentReportFactory(),
        )

        response = self.client.get(
            f"{self.url}?status=Open&report_facility={self.super_user_fac.id}"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_create_investigation_success(self):
        """Test POST request creates investigation"""
        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("id", response.data)

    def test_create_investigation_with_minimal_data(self):
        """Test POST request with minimal required data"""
        minimal_data = {
            "report_facility": self.super_user_fac.id,
            "status": "Draft",
            "name_of_injured_staff": {
                "first_name": "newStafff",
                "last_name": "test",
                "profile_type": "Staff",
            },
        }

        response = self.client.post(self.url, minimal_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("id", response.data)

    def test_create_investigation_invalid_data(self):
        """Test POST request with invalid data fails"""
        invalid_data = self.valid_data.copy()
        invalid_data.get("name_of_injured_staff").pop("user_id")

        response = self.client.post(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_investigation_invalid_status(self):
        """Test POST request with invalid status fails"""
        invalid_data = self.valid_data.copy()
        invalid_data["status"] = "InvalidStatus"

        response = self.client.post(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_investigation_unauthenticated(self):
        """Test POST request without authentication fails"""
        self.client.logout()

        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_investigation_nonexistent_incident(self):
        """Test POST request for non-existent staff incident"""
        url = f"{self.staff_incident_endpoint}/99999/investigation/"

        response = self.client.post(url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_get_investigations_empty_list(self):
        """Test GET request returns empty list when no investigations exist"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)


class TestStaffIncidentInvestigationDetailAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.staff_incident = StaffIncidentReportFactory(created_by=self.super_user)
        self.investigation = StaffIncidentInvestigationFactory(
            employee_report=self.staff_incident,
            report_facility=self.super_user_fac,
            department=self.super_user_dept,
        )
        self.url = f"{self.staff_incident_endpoint}/{self.staff_incident.id}/investigation/{self.investigation.id}/"
        self._authenticate_user(self.super_user)
        self.update_data = {
            "status": "Open",
            "recommendations": "Updated recommendations",
            "accident_details": "Updated accident details",
        }

    def test_get_investigation_details(self):
        """Test GET request returns investigation details"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.investigation.id)

    def test_get_investigation_details_with_all_fields(self):
        """Test GET request returns all investigation fields"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("id", response.data)
        self.assertIn("status", response.data)
        self.assertIn("recommendations", response.data)
        self.assertIn("report_facility", response.data)

    def test_get_nonexistent_investigation(self):
        """Test GET request for non-existent investigation"""
        url = f"{self.staff_incident_endpoint}/{self.staff_incident.id}/investigation/99999/"

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_get_investigation_wrong_incident(self):
        """Test GET request for investigation with wrong incident ID"""
        url = f"{self.staff_incident_endpoint}/99999/investigation/{self.investigation.id}/"

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_investigation(self):
        """Test PUT request updates investigation"""
        response = self.client.put(self.url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(response.data["status"], "Open")
        self.assertEqual(response.data["recommendations"], "Updated recommendations")

    def test_update_investigation_partial_data(self):
        """Test PUT request with partial update data"""
        partial_data = {
            "status": "Pending Review",
        }

        response = self.client.put(self.url, partial_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], "Pending Review")

    def test_update_investigation_full_data(self):
        """Test PUT request with comprehensive update data"""
        full_data = {
            "status": "Resolved",
            "recommendations": "Investigation completed successfully",
            "accident_details": "Comprehensive accident details",
            "nature_of_injury": "Detailed nature of injury findings",
            "part_of_body_injured": "Specific body parts affected",
        }

        response = self.client.put(self.url, full_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], "Resolved")
        self.assertEqual(
            response.data["recommendations"], "Investigation completed successfully"
        )

    def test_update_investigation_invalid_data(self):
        """Test PUT request with invalid data fails"""
        invalid_data = self.update_data.copy()
        invalid_data["status"] = "InvalidStatus"

        response = self.client.put(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_investigation_empty_data(self):
        """Test PUT request with empty data"""
        empty_data = {}

        response = self.client.put(self.url, empty_data, format="json")

        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )

    def test_update_nonexistent_investigation(self):
        """Test PUT request for non-existent investigation"""
        url = f"{self.staff_incident_endpoint}/{self.staff_incident.id}/investigation/99999/"

        response = self.client.put(url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_investigation_unauthenticated(self):
        """Test PUT request without authentication fails"""
        self.client.logout()

        response = self.client.put(self.url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_investigation_wrong_incident(self):
        """Test PUT request with wrong incident ID"""
        url = f"{self.staff_incident_endpoint}/99999/investigation/{self.investigation.id}/"

        response = self.client.put(url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_get_investigation_unauthenticated(self):
        """Test GET request without authentication fails"""
        self.client.logout()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestStaffIncidentReportDetailsAPIAsSuperUser(BaseTestSetup):
    """Test Staff Incident Report detail API endpoints as Super User"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.super_user)
        self.report = StaffIncidentReportFactory(
            created_by=self.super_user,
            department=self.super_user_dept,
            report_facility=self.super_user_fac,
        )
        self.url = f"/api/incidents/staff-incident/{self.report.id}/"

    def test_get_incident_details_as_superuser(self):
        """Test superuser can view any incident details"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_superuser(self):
        """Test superuser can update any incident"""
        update_data = {"job_title": "Superuser Updated Title"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )


class TestStaffIncidentReportDetailsAPIAsAdmin(BaseTestSetup):
    """Test Staff Incident Report detail API endpoints as Admin"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.admin_user)
        self.report = StaffIncidentReportFactory(
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )
        self.url = f"/api/incidents/staff-incident/{self.report.id}/"

    def test_get_incident_details_as_admin(self):
        """Test admin can view incident details in their facility"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_admin(self):
        """Test admin can update incident in their facility"""
        update_data = {"job_title": "Admin Updated Title"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )


class TestStaffIncidentReportDetailsAPIAsDirector(BaseTestSetup):
    """Test Staff Incident Report detail API endpoints as Director"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.director_user)
        self.report = StaffIncidentReportFactory(
            created_by=self.director_user,
            department=self.director_user_dept,
            report_facility=self.director_user_fac,
        )
        self.url = f"/api/incidents/staff-incident/{self.report.id}/"

    def test_get_incident_details_as_director(self):
        """Test director can view incident details in their facility"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_director(self):
        """Test director can update incident in their facility"""
        update_data = {"job_title": "Director Updated Title"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )


class TestStaffIncidentReportDetailsAPIAsManager(BaseTestSetup):
    """Test Staff Incident Report detail API endpoints as Manager"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.manager_user)
        self.report = StaffIncidentReportFactory(
            created_by=self.manager_user,
            department=self.manager_user_dept,
            report_facility=self.manager_user_fac,
        )
        self.url = f"/api/incidents/staff-incident/{self.report.id}/"

    def test_get_incident_details_as_manager(self):
        """Test manager can view incident details in their department"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_manager(self):
        """Test manager can update incident in their department"""
        update_data = {"job_title": "Manager Updated Title"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )


class TestStaffIncidentReportDetailsAPIAsRegularUser(BaseTestSetup):
    """Test Staff Incident Report detail API endpoints as Regular User"""

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.user_user)
        self.report = StaffIncidentReportFactory(
            created_by=self.user_user,
            department=self.user_user_dept,
            report_facility=self.user_user_fac,
        )
        self.url = f"/api/incidents/staff-incident/{self.report.id}/"

    def test_get_incident_details_as_regular_user(self):
        """Test regular user can view incident details they created"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_incident_as_regular_user(self):
        """Test regular user can update incident they created"""
        update_data = {"job_title": "User Updated Title"}
        response = self.client.patch(self.url, update_data, format="json")
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )
