import os
from django.conf import settings
from typing import Type
from base.services.emails import send_email
from base.services.logging.logger import LoggingService
from complaints.models import Complaint

logger = LoggingService()


def send_complaint_to_department(complaint: Type[Complaint]):
    """
    Send the complaint to the specified department
    """

    try:
        for dep in complaint.department.all():
            header_email = (
                dep.header_of_department.email
                if dep.header_of_department
                else "No header assigned"
            )

            recipient_email = (
                dep.header_of_department.email if dep.header_of_department else None
            )
            first_name = (
                dep.header_of_department.first_name
                if dep.header_of_department
                else None
            )

            if not recipient_email:
                logger.log_info(
                    "No email found for the department header. Cannot send email."
                )
                return
            html_file_path = os.path.join(
                os.path.dirname(__file__), "send_to_department.html"
            )
            with open(html_file_path, "r") as file:
                html_content = file.read()

            html_body = html_content.format(
                first_name=first_name if first_name else "Head of Department",
                details=complaint.details,
                complainer_name=(
                    complaint.created_by.first_name
                    if complaint.created_by
                    else "Unknown"
                ),
                url=settings.MAIN_DOMAIN_NAME + f"/complaints/?id={complaint.id}",
            )
            send_email(
                recipient_email=recipient_email,
                data={"first_name": first_name, "email": recipient_email},
                html_body=html_body,
                subject="Complaint Assigned to Your Department",
            )
    except Exception as e:
        logger.log_error(e)
