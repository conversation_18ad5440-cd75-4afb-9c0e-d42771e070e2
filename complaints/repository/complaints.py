# Repository class for complaints
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from complaints.models import Complaint
from reviews.serializers import ComplaintSerializer
from typing import Type


class ComplaintsRepository:
    def __init__(self):
        self.logger = LoggingService()

    def create_complaint(self, complaint_data) -> RepositoryResponse:
        try:
            serializer = ComplaintSerializer(data=complaint_data)
            if not serializer.is_valid():
                self.logger.get_serializer_error_message(serializer)
                return RepositoryResponse(
                    success=False,
                    message="Invalid complaint data",
                    data=serializer.errors,
                )
            serializer.save()
            return RepositoryResponse(
                success=True,
                message="Complaint created successfully",
                data=serializer.instance,
            )
        except Exception as e:
            self.logger.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while creating the complaint",
            )

    def update_complaint(
        self, complaint: Type[Complaint], update_data
    ) -> RepositoryResponse:
        try:
            serializer = ComplaintSerializer(complaint, data=update_data, partial=True)
            if not serializer.is_valid():
                self.logger.get_serializer_error_message(serializer)
                return RepositoryResponse(
                    success=False,
                    message="Invalid complaint data",
                    data=serializer.errors,
                )
            serializer.save()
            return RepositoryResponse(
                success=True,
                message="Complaint updated successfully",
                data=serializer.instance,
            )
        except Exception as e:
            self.logger.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while updating the complaint",
            )

    def delete_complaint(self, complaint: Type[Complaint]) -> RepositoryResponse:
        try:
            complaint.delete()
            return RepositoryResponse(
                success=True,
                message="Complaint deleted successfully",
            )
        except Exception as e:
            self.logger.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while deleting the complaint",
            )

    def get_complaint_by_id(self, complaint_id: int) -> RepositoryResponse:
        try:
            complaint = Complaint.objects.get(id=complaint_id)
            return RepositoryResponse(
                success=True,
                message="Complaint retrieved successfully",
                data=complaint,
            )
        except Complaint.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Complaint not found",
            )
        except Exception as e:
            self.logger.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while retrieving the complaint",
            )
