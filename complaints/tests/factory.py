import factory
from complaints.models import Complaint
from base.tests.factory import UserFactory, FacilityFactory, DepartmentFactory


class ComplaintFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Complaint

    complain_facility = factory.SubFactory(FacilityFactory)
    date_of_complaint = factory.Faker("date")
    patient_name = factory.Faker("name")
    phone_number = factory.Faker("phone_number")
    medical_record_number = factory.Sequence(lambda n: f"MRN{n:06d}")
    complaint_nature = factory.Faker("sentence", nb_words=4)
    complaint_type = factory.Iterator(["Service", "Quality", "Billing", "Staff"])
    resolved_by_staff = factory.Faker("boolean")
    how_complaint_was_taken = factory.Iterator(
        ["Phone", "Email", "In-person", "Written"]
    )
    details = factory.Faker("text", max_nb_chars=200)

    @factory.post_generation
    def department(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted is not None:
            for dept in extracted:
                self.department.add(dept)
        else:
            # Add a default department
            dept = DepartmentFactory()
            self.department.add(dept)

    @factory.post_generation
    def assigned_to(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for user in extracted:
                self.assigned_to.add(user)
