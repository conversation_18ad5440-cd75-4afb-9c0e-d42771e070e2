from django.urls import reverse
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from rest_framework import status
from rest_framework.test import APITestCase
from base.services.permissions.manage_permissions import PermissionsManagement
from base.tests.base_setup import BaseTestSetup
from complaints.models import Complaint
from complaints.tests.factory import ComplaintFactory
from base.tests.factory import UserFactory, FacilityFactory, DepartmentFactory


class TestComplaintsListCreateAPI(BaseTestSetup):
    """Test cases for complaints list and create endpoints"""

    def setUp(self):
        super().setUp()
        self.complaints_endpoint = "/api/complaints/"
        self.facility = FacilityFactory()
        self.department = DepartmentFactory()

        # Sample data for creating complaints
        self.valid_complaint_data = {
            "complain_facility": self.facility.id,
            "date_of_complaint": "2024-01-15",
            "patient_name": "John Doe",
            "phone_number": "************",
            "medical_record_number": "MRN123456",
            "complaint_nature": "Poor service quality",
            "complaint_type": "Service",
            "resolved_by_staff": False,
            "how_complaint_was_taken": "Phone",
            "details": "Patient complained about waiting time",
        }

    def test_get_complaints_list_success(self):
        """Test retrieving complaints list successfully"""
        # Create test complaints
        complaints = [ComplaintFactory() for _ in range(3)]

        # Authenticate user
        self._authenticate_user(self.admin_user)

        response = self.client.get(self.complaints_endpoint)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        self.assertEqual(len(response.data["results"]), 3)
        self.assertIn("total_count", response.data)
        self.assertIn("has_next", response.data)
        self.assertIn("has_previous", response.data)

    def test_get_complaints_list_empty(self):
        """Test retrieving empty complaints list"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        response = self.client.get(self.complaints_endpoint)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        self.assertEqual(len(response.data["results"]), 0)

    def test_get_complaints_list_unauthenticated(self):
        """Test retrieving complaints list without authentication"""
        response = self.client.get(self.complaints_endpoint)

        # Django REST framework returns 403 for IsAuthenticated permission
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_complaint_success(self):
        """Test creating a complaint successfully"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        response = self.client.post(
            self.complaints_endpoint, data=self.valid_complaint_data, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("id", response.data)

        # Verify complaint was created
        complaint = Complaint.objects.get(id=response.data["id"])
        self.assertEqual(
            complaint.patient_name, self.valid_complaint_data["patient_name"]
        )
        self.assertEqual(complaint.created_by, self.admin_user)

    def test_create_complaint_invalid_data(self):
        """Test creating a complaint with invalid data"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        invalid_data = {
            "complain_facility": "invalid_facility_id",
            "date_of_complaint": "invalid_date",
        }

        response = self.client.post(
            self.complaints_endpoint, data=invalid_data, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_complaint_unauthenticated(self):
        """Test creating a complaint without authentication"""
        response = self.client.post(
            self.complaints_endpoint, data=self.valid_complaint_data, format="json"
        )

        # Django REST framework returns 403 for IsAuthenticated permission
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_complaint_missing_required_fields(self):
        """Test creating a complaint with missing required fields"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        incomplete_data = {
            "patient_name": "John Doe",
            # Missing other required fields
        }

        response = self.client.post(
            self.complaints_endpoint, data=incomplete_data, format="json"
        )

        # The service might still create the complaint with minimal data
        # This depends on the actual validation in the service
        self.assertIn(
            response.status_code,
            [
                status.HTTP_201_CREATED,  # If service accepts minimal data
                status.HTTP_400_BAD_REQUEST,  # If validation fails
            ],
        )

    def test_get_complaints_with_pagination(self):
        """Test complaints list with pagination"""
        # Create more complaints than default page size
        complaints = [ComplaintFactory() for _ in range(15)]

        # Authenticate user
        self._authenticate_user(self.admin_user)

        # Test first page
        response = self.client.get(f"{self.complaints_endpoint}?page=1&page_size=10")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 10)
        self.assertTrue(response.data["has_next"])
        self.assertFalse(response.data["has_previous"])

        # Test second page
        response = self.client.get(f"{self.complaints_endpoint}?page=2&page_size=10")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 5)
        self.assertFalse(response.data["has_next"])
        self.assertTrue(response.data["has_previous"])


class TestComplaintsDetailAPI(BaseTestSetup):
    """Test cases for complaints detail endpoints (GET, PUT, DELETE)"""

    def setUp(self):
        super().setUp()
        self.complaint = ComplaintFactory()
        self.complaints_detail_endpoint = f"/api/complaints/{self.complaint.id}/"
        self.invalid_complaint_endpoint = "/api/complaints/99999/"

        self.update_data = {
            "patient_name": "Jane Doe Updated",
            "phone_number": "************",
            "details": "Updated complaint details",
        }

    def test_get_complaint_detail_success(self):
        """Test retrieving a specific complaint successfully"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        response = self.client.get(self.complaints_detail_endpoint)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.complaint.id)
        self.assertEqual(response.data["patient_name"], self.complaint.patient_name)

    def test_get_complaint_detail_not_found(self):
        """Test retrieving a non-existent complaint"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        response = self.client.get(self.invalid_complaint_endpoint)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_complaint_detail_unauthenticated(self):
        """Test retrieving complaint detail without authentication"""
        response = self.client.get(self.complaints_detail_endpoint)

        # Django REST framework returns 403 for IsAuthenticated permission
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_complaint_success(self):
        """Test updating a complaint successfully"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        response = self.client.put(
            self.complaints_detail_endpoint, data=self.update_data, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify complaint was updated
        updated_complaint = Complaint.objects.get(id=self.complaint.id)
        self.assertEqual(
            updated_complaint.patient_name, self.update_data["patient_name"]
        )
        self.assertEqual(
            updated_complaint.phone_number, self.update_data["phone_number"]
        )

    def test_update_complaint_not_found(self):
        """Test updating a non-existent complaint"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        response = self.client.put(
            self.invalid_complaint_endpoint, data=self.update_data, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_complaint_unauthenticated(self):
        """Test updating a complaint without authentication"""
        response = self.client.put(
            self.complaints_detail_endpoint, data=self.update_data, format="json"
        )

        # Django REST framework returns 403 for IsAuthenticated permission
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_partial_update_complaint_success(self):
        """Test partial update of a complaint using PATCH"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        partial_data = {"patient_name": "Partially Updated Name"}

        response = self.client.put(
            self.complaints_detail_endpoint, data=partial_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_delete_complaint_success(self):
        """Test deleting a complaint successfully"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        response = self.client.delete(self.complaints_detail_endpoint)

        # Delete operations typically return 204 No Content
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Verify complaint was deleted
        with self.assertRaises(Complaint.DoesNotExist):
            Complaint.objects.get(id=self.complaint.id)

    def test_delete_complaint_not_found(self):
        """Test deleting a non-existent complaint"""
        # Authenticate user
        self._authenticate_user(self.admin_user)

        response = self.client.delete(self.invalid_complaint_endpoint)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_complaint_unauthenticated(self):
        """Test deleting a complaint without authentication"""
        response = self.client.delete(self.complaints_detail_endpoint)

        # Django REST framework returns 403 for IsAuthenticated permission
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestComplaintsPermissions(BaseTestSetup):
    """Test access permissions for different user types"""

    def setUp(self):
        super().setUp()
        self.complaint = ComplaintFactory()
        self.complaints_endpoint = "/api/complaints/"
        self.complaints_detail_endpoint = f"/api/complaints/{self.complaint.id}/"

        self.valid_complaint_data = {
            "complain_facility": self.admin_user_fac.id,
            "date_of_complaint": "2024-01-15",
            "patient_name": "John Doe",
            "phone_number": "************",
            "medical_record_number": "MRN123456",
            "complaint_nature": "Poor service quality",
            "complaint_type": "Service",
            "resolved_by_staff": False,
            "how_complaint_was_taken": "Phone",
            "details": "Patient complained about waiting time",
        }

    def test_super_user_access(self):
        """Test that super user can access all complaint operations"""
        self._authenticate_user(self.super_user)

        # Test GET list
        response = self.client.get(self.complaints_endpoint)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test POST
        response = self.client.post(
            self.complaints_endpoint, data=self.valid_complaint_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Test GET detail
        response = self.client.get(self.complaints_detail_endpoint)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test PUT
        response = self.client.put(
            self.complaints_detail_endpoint,
            data={"patient_name": "Updated Name"},
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test DELETE
        response = self.client.delete(self.complaints_detail_endpoint)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_admin_user_access(self):
        """Test that admin user can access complaint operations"""
        self._authenticate_user(self.admin_user)

        # Test GET list
        response = self.client.get(self.complaints_endpoint)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test POST
        response = self.client.post(
            self.complaints_endpoint, data=self.valid_complaint_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_director_user_access(self):
        """Test that director user can access complaint operations"""
        self._authenticate_user(self.director_user)

        # Test GET list
        response = self.client.get(self.complaints_endpoint)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test POST
        response = self.client.post(
            self.complaints_endpoint, data=self.valid_complaint_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_manager_user_access(self):
        """Test that manager user can access complaint operations"""
        self._authenticate_user(self.manager_user)

        # Test GET list
        response = self.client.get(self.complaints_endpoint)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test POST
        response = self.client.post(
            self.complaints_endpoint, data=self.valid_complaint_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_regular_user_access(self):
        """Test that regular user can access complaint operations"""
        self._authenticate_user(self.user_user)

        # Test GET list
        response = self.client.get(self.complaints_endpoint)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test POST
        response = self.client.post(
            self.complaints_endpoint, data=self.valid_complaint_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)


class TestComplaintsFilters(BaseTestSetup):
    """Test complaint filtering functionality"""

    def setUp(self):
        super().setUp()
        self.complaints_endpoint = "/api/complaints/"

        # Clear any existing complaints first
        Complaint.objects.all().delete()

        # Create test complaints with specific attributes
        self.complaint1 = ComplaintFactory(
            date_of_complaint="2024-01-15",
            resolved_by_staff=True,
            created_by=self.admin_user,
        )
        self.complaint1.department.add(self.admin_user_dept)
        self.complaint1.assigned_to.add(self.admin_user)

        self.complaint2 = ComplaintFactory(
            date_of_complaint="2024-01-20",
            resolved_by_staff=False,
            created_by=self.manager_user,
        )
        self.complaint2.department.add(self.manager_user_dept)
        self.complaint2.assigned_to.add(self.manager_user)

    def test_filter_by_created_by(self):
        """Test filtering complaints by created_by user"""
        self._authenticate_user(self.admin_user)

        response = self.client.get(
            f"{self.complaints_endpoint}?created_by={self.admin_user.id}"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["created_by"], self.admin_user.id)

    def test_filter_by_date_of_complaint(self):
        """Test filtering complaints by date_of_complaint"""
        self._authenticate_user(self.admin_user)

        response = self.client.get(
            f"{self.complaints_endpoint}?date_of_complaint=2024-01-15"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["date_of_complaint"], "2024-01-15")

    def test_filter_by_resolved_status(self):
        """Test filtering complaints by resolved_by_staff status"""
        self._authenticate_user(self.admin_user)

        response = self.client.get(f"{self.complaints_endpoint}?resolved_by_staff=True")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertEqual(len(results), 1)
        self.assertTrue(results[0]["resolved_by_staff"])

    def test_filter_by_department(self):
        """Test filtering complaints by department"""
        self._authenticate_user(self.admin_user)

        response = self.client.get(
            f"{self.complaints_endpoint}?department={self.admin_user_dept.id}"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertEqual(len(results), 1)
        self.assertIn(self.admin_user_dept.id, results[0]["department"])

    def test_filter_by_assigned_to(self):
        """Test filtering complaints by assigned_to user"""
        self._authenticate_user(self.admin_user)

        response = self.client.get(
            f"{self.complaints_endpoint}?assigned_to={self.admin_user.id}"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertEqual(len(results), 1)
        self.assertIn(self.admin_user.id, results[0]["assigned_to"])

    def test_multiple_filters(self):
        """Test filtering with multiple filters"""
        self._authenticate_user(self.admin_user)

        response = self.client.get(
            f"{self.complaints_endpoint}?created_by={self.admin_user.id}&resolved_by_staff=True"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["created_by"], self.admin_user.id)
        self.assertTrue(results[0]["resolved_by_staff"])

    def test_filters_with_no_results(self):
        """Test filtering with filters that return no results"""
        self._authenticate_user(self.admin_user)

        response = self.client.get(
            f"{self.complaints_endpoint}?created_by={self.admin_user.id}&date_of_complaint=2024-01-20"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]
        self.assertEqual(len(results), 0)


class TestComplaintsErrorHandling(BaseTestSetup):
    """Test error handling in complaints API"""

    def setUp(self):
        super().setUp()
        self.complaints_endpoint = "/api/complaints/"

    def test_invalid_json_format(self):
        """Test handling of invalid JSON in POST request"""
        self._authenticate_user(self.admin_user)

        response = self.client.post(
            self.complaints_endpoint,
            data="invalid json",
            content_type="application/json",
        )

        # The API catches the parse error and returns 500
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)

    def test_method_not_allowed(self):
        """Test handling of unsupported HTTP methods"""
        self._authenticate_user(self.admin_user)

        # PATCH is not implemented for list endpoint
        response = self.client.patch(self.complaints_endpoint)

        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_large_payload(self):
        """Test handling of unusually large request payload"""
        self._authenticate_user(self.admin_user)

        large_data = {
            "complain_facility": self.admin_user_fac.id,
            "patient_name": "x" * 1000,  # Very long name
            "details": "x" * 10000,  # Very long details
        }

        response = self.client.post(
            self.complaints_endpoint, data=large_data, format="json"
        )

        # This should either succeed or fail gracefully
        self.assertIn(
            response.status_code,
            [
                status.HTTP_201_CREATED,
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            ],
        )


class ComplaintsWorkflowTestCase(BaseTestSetup):
    def setUp(self):
        self.department = DepartmentFactory(header_of_department=UserFactory())

        self.complaint = ComplaintFactory(
            department=[], created_by=UserFactory(first_name="SIBOMANA Test")
        )
        self.user = UserFactory()
        self.permissions = PermissionsManagement()

    # Test sending complaint to department with permissions
    def test_send_to_department_success(self):
        # Get or create the permission
        permissions = self.permissions.add_permissions_to_user(
            self.user,
            [{"code_name": "can_send_to_department", "name": "Can send to department"}],
            Complaint,
        )

        if not permissions.success:
            self.fail(f"Failed to add permissions: {permissions.message}")

        self._authenticate_user(self.user)
        response = self.client.patch(
            f"/api/complaints/{self.complaint.id}/",
            {
                "action": "send_to_department",
                "department_id": self.department.id,
            },
            format="json",
        )
        if not response.status_code == status.HTTP_200_OK:
            self.fail(
                f"Expected status code 200, got {response.status_code} with response: {response.data}"
            )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_send_to_non_existent_department(self):
        # Get or create the permission
        permissions = self.permissions.add_permissions_to_user(
            self.user,
            [{"code_name": "can_send_to_department", "name": "Can send to department"}],
            Complaint,
        )

        if not permissions.success:
            self.fail(f"Failed to add permissions: {permissions.message}")

        self._authenticate_user(self.user)
        response = self.client.patch(
            f"/api/complaints/{self.complaint.id}/",
            {
                "action": "send_to_department",
                "department_id": 9999,  # Non-existent department ID
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
        self.assertEqual(response.data["error"], "Department not found.")

    # Test sending complaint without permission
    def test_send_to_department_without_permission(self):
        self._authenticate_user(self.user)
        response = self.client.patch(
            f"/api/complaints/{self.complaint.id}/",
            {
                "action": "send_to_department",
                "department_id": self.department.id,
            },
            format="json",
        )
        self.assertIn("error", response.data)
        self.assertEqual(
            response.data["error"],
            "User does not have permission to send complaints to department.",
        )
