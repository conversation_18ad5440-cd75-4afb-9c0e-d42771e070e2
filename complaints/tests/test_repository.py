from django.test import TestCase
from django.contrib.auth.models import User
from complaints.repository.complaints import ComplaintsRepository
from complaints.models import Complaint
from base.models import Facility, Department
from base.tests.factory import UserFactory, FacilityFactory, DepartmentFactory
from complaints.tests.factory import ComplaintFactory


class TestCreateComplaint(TestCase):
    def setUp(self):
        self.repository = ComplaintsRepository()
        self.facility = FacilityFactory()
        self.department = DepartmentFactory()
        self.user = UserFactory()

    def test_create_complaint_success(self):
        """Test successful complaint creation"""
        complaint_data = {
            "complain_facility": self.facility.id,
            "date_of_complaint": "2024-01-15",
            "patient_name": "<PERSON>",
            "phone_number": "************",
            "medical_record_number": "MRN123456",
            "complaint_nature": "Poor service quality",
            "complaint_type": "Service",
            "resolved_by_staff": False,
            "how_complaint_was_taken": "Phone",
            "details": "Patient complained about waiting time",
        }

        response = self.repository.create_complaint(complaint_data)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Complaint created successfully")
        self.assertIsNotNone(response.data)
        self.assertIsInstance(response.data, Complaint)

    def test_create_complaint_invalid_data(self):
        """Test complaint creation with invalid data"""
        invalid_data = {
            "complain_facility": "invalid_facility_id",
            "date_of_complaint": "invalid_date",
        }

        response = self.repository.create_complaint(invalid_data)

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Invalid complaint data")
        self.assertIsNotNone(response.data)

    def test_create_complaint_empty_data(self):
        """Test complaint creation with empty data"""
        empty_data = {}

        response = self.repository.create_complaint(empty_data)

        # This should succeed because all fields are optional in the model
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Complaint created successfully")
        self.assertIsNotNone(response.data)

    def test_create_complaint_with_many_to_many_fields(self):
        """Test creating complaint with many-to-many relationships"""
        departments = [DepartmentFactory(), DepartmentFactory()]
        users = [UserFactory(), UserFactory()]

        complaint_data = {
            "complain_facility": self.facility.id,
            "patient_name": "Test Patient",
            "department": [dept.id for dept in departments],
            "assigned_to": [user.id for user in users],
        }

        response = self.repository.create_complaint(complaint_data)

        self.assertTrue(response.success)
        complaint = response.data
        self.assertEqual(complaint.department.count(), 2)
        self.assertEqual(complaint.assigned_to.count(), 2)


class TestUpdateComplaint(TestCase):
    def setUp(self):
        self.repository = ComplaintsRepository()
        self.complaint = ComplaintFactory()

    def test_update_complaint_success(self):
        """Test successful complaint update"""
        update_data = {
            "patient_name": "Updated Patient Name",
            "resolved_by_staff": True,
        }

        response = self.repository.update_complaint(self.complaint, update_data)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Complaint updated successfully")
        self.assertIsNotNone(response.data)

        # Refresh from database to verify update
        self.complaint.refresh_from_db()
        self.assertEqual(self.complaint.patient_name, "Updated Patient Name")
        self.assertTrue(self.complaint.resolved_by_staff)

    def test_update_complaint_invalid_data(self):
        """Test complaint update with invalid data"""
        invalid_update_data = {"date_of_complaint": "invalid_date_format"}

        response = self.repository.update_complaint(self.complaint, invalid_update_data)

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Invalid complaint data")
        self.assertIsNotNone(response.data)

    def test_update_complaint_with_many_to_many_fields(self):
        """Test updating complaint with many-to-many relationships"""
        departments = [DepartmentFactory(), DepartmentFactory()]
        users = [UserFactory(), UserFactory()]

        update_data = {
            "department": [dept.id for dept in departments],
            "assigned_to": [user.id for user in users],
        }

        response = self.repository.update_complaint(self.complaint, update_data)

        self.assertTrue(response.success)
        self.complaint.refresh_from_db()
        self.assertEqual(self.complaint.department.count(), 2)
        self.assertEqual(self.complaint.assigned_to.count(), 2)

    def test_update_complaint_partial_update(self):
        """Test partial update of complaint fields"""
        original_name = self.complaint.patient_name
        original_type = self.complaint.complaint_type

        # Update only one field
        update_data = {"resolved_by_staff": True}

        response = self.repository.update_complaint(self.complaint, update_data)

        self.assertTrue(response.success)
        self.complaint.refresh_from_db()

        # Check that only the updated field changed
        self.assertTrue(self.complaint.resolved_by_staff)
        self.assertEqual(self.complaint.patient_name, original_name)
        self.assertEqual(self.complaint.complaint_type, original_type)


class TestDeleteComplaint(TestCase):
    def setUp(self):
        self.repository = ComplaintsRepository()

    def test_delete_complaint_success(self):
        """Test successful complaint deletion"""
        complaint = ComplaintFactory()
        complaint_id = complaint.id

        response = self.repository.delete_complaint(complaint)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Complaint deleted successfully")

        # Verify complaint is deleted from database
        with self.assertRaises(Complaint.DoesNotExist):
            Complaint.objects.get(id=complaint_id)

    def test_delete_complaint_with_relationships(self):
        """Test deleting complaint that has many-to-many relationships"""
        complaint = ComplaintFactory()
        departments = [DepartmentFactory(), DepartmentFactory()]
        users = [UserFactory(), UserFactory()]

        # Clear existing relationships and add new ones
        complaint.department.clear()
        complaint.assigned_to.clear()
        complaint.department.add(*departments)
        complaint.assigned_to.add(*users)

        complaint_id = complaint.id

        response = self.repository.delete_complaint(complaint)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Complaint deleted successfully")

        # Verify complaint is deleted from database
        with self.assertRaises(Complaint.DoesNotExist):
            Complaint.objects.get(id=complaint_id)


class TestGetComplaintById(TestCase):
    def setUp(self):
        self.repository = ComplaintsRepository()

    def test_get_complaint_by_id_success(self):
        """Test successful complaint retrieval by ID"""
        complaint = ComplaintFactory()

        response = self.repository.get_complaint_by_id(complaint.id)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Complaint retrieved successfully")
        self.assertEqual(response.data, complaint)

    def test_get_complaint_by_id_not_found(self):
        """Test complaint retrieval with non-existent ID"""
        non_existent_id = 99999

        response = self.repository.get_complaint_by_id(non_existent_id)

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Complaint not found")
        self.assertIsNone(response.data)

    def test_get_complaint_by_id_with_relationships(self):
        """Test complaint retrieval with many-to-many relationships"""
        complaint = ComplaintFactory()
        departments = [DepartmentFactory(), DepartmentFactory()]
        users = [UserFactory(), UserFactory()]

        # Clear existing relationships and add new ones
        complaint.department.clear()
        complaint.assigned_to.clear()
        complaint.department.add(*departments)
        complaint.assigned_to.add(*users)

        response = self.repository.get_complaint_by_id(complaint.id)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Complaint retrieved successfully")
        self.assertEqual(response.data, complaint)
        self.assertEqual(response.data.department.count(), 2)
        self.assertEqual(response.data.assigned_to.count(), 2)
