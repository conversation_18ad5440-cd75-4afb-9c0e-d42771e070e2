from django.db import models
from base.models import BaseModel
from django.contrib.auth.models import User

from base.models import Facility
from base.models import Department
from base.services.permissions.mixins import IncidentsPermissionsMixin


# Create your models here.
class Complaint(BaseModel):
    complain_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    date_of_complaint = models.DateField(null=True, blank=True)
    patient_name = models.CharField(max_length=500, null=True, blank=True)
    phone_number = models.CharField(max_length=500, null=True, blank=True)
    medical_record_number = models.CharField(max_length=255, null=True, blank=True)
    complaint_nature = models.CharField(max_length=255, null=True, blank=True)
    department = models.ManyToManyField(
        Department, blank=True, related_name="complaint_department"
    )

    complaint_type = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    resolved_by_staff = models.BooleanField(default=False)
    assigned_to = models.ManyToMany<PERSON>ield(User, blank=True)
    how_complaint_was_taken = models.CharField(max_length=255, null=True, blank=True)
    details = models.TextField(blank=True, max_length=255, null=True)
    complaint_document = models.URLField(
        max_length=500,
        null=True,
        blank=True,
        help_text="URL to the complaint document",
    )

    def __str__(self):
        return self.patient_name if self.patient_name else "No patient name available"

    class Meta:
        permissions = IncidentsPermissionsMixin.custom_permissions
