from typing import Type
from django.contrib.auth.models import User
from base.models import Department
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse, RepositoryResponse
from complaints.emails.send_to_department import send_complaint_to_department
from complaints.models import Complaint
from reviews.serializers import ComplaintSerializer
from rest_framework import status

logger = LoggingService()


class ComplaintsWorkflow:
    def __init__(self, user: Type[User], complaint: Type[Complaint]):
        self.user = user
        self.complaint = complaint

    def send_to_department(self, department_id: int) -> APIResponse:
        """
        Send the complaint to a specific department.
        """
        try:

            # TODO: Validate if the user has permission to send complaints to the department
            if not self.user.has_perm("complaints.can_send_to_department"):
                return APIResponse(
                    success=False,
                    message="User does not have permission to send complaints to department.",
                    data=None,
                    code=status.HTTP_403_FORBIDDEN,
                )

            # check if the department exists
            department = Department.objects.get(id=department_id)
            header_email = (
                department.header_of_department.email
                if department.header_of_department
                else "No header assigned"
            )
            if not department:
                return APIResponse(
                    success=False,
                    message="Department not found.",
                    data=None,
                    code=status.HTTP_404_NOT_FOUND,
                )

            if (
                self.complaint.department
                and department_id
                in self.complaint.department.values_list("id", flat=True)
            ):
                return APIResponse(
                    success=False,
                    message="Complaint is already assigned to this department.",
                    data=None,
                )

            self.complaint.department.add(department)
            for dep in self.complaint.department.all():
                header_email = (
                    dep.header_of_department.email
                    if dep.header_of_department
                    else "No header assigned"
                )
            self.complaint.save()
            send_complaint_to_department(self.complaint)
            serialized_complaint = ComplaintSerializer(self.complaint)
            return APIResponse(
                success=True,
                message=f"Complaint sent to department {department.name} successfully.",
                data=serialized_complaint.data,
                code=status.HTTP_200_OK,
            )
        except Department.DoesNotExist:
            return APIResponse(
                success=False,
                message="Department not found.",
                data=None,
                code=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logger.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while sending the complaint to the department.",
                data=None,
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
