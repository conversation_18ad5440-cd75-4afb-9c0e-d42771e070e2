# Service class for complaints operations
from complaints.repository.complaints import ComplaintsRepository
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from complaints.models import Complaint
from complaints.services.workflow import ComplaintsWorkflow
from reviews.serializers import ComplaintSerializer
from rest_framework import status
from activities.services import ActivityLogService
from activities.models import ActivityType
from base.models import Department


class ComplaintsService:
    def __init__(self):
        self.repository = ComplaintsRepository()
        self.logger = LoggingService()

    def create_complaint(self, complaint_data, logged_in_user) -> APIResponse:
        """
        Create a new complaint.
        """
        try:
            complaint_data["created_by"] = logged_in_user.id
            response = self.repository.create_complaint(complaint_data)
            if not response.success:
                self.logger.log_error(response.message)
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=status.HTTP_400_BAD_REQUEST,
                )
            ActivityLogService.create_activity(
                user=logged_in_user,
                content_object=response.data,
                activity_type=ActivityType.CREATED,
                description="Complaint created"
            )

            serializer = ComplaintSerializer(response.data)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Complaint created successfully.",
                code=status.HTTP_201_CREATED,
            )
        except Exception as e:
            self.logger.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the complaint.",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def update_complaint(self, complaint_id, update_data, user=None) -> APIResponse:
        """
        Update an existing complaint.
        """
        try:
            complaint = Complaint.objects.filter(id=complaint_id).first()
            if not complaint:
                return APIResponse(
                    success=False,
                    message="Complaint not found.",
                    code=status.HTTP_404_NOT_FOUND,
                )

            response = self.repository.update_complaint(complaint, update_data)
            if not response.success:
                self.logger.log_error(response.message)
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=status.HTTP_400_BAD_REQUEST,
                )
            if user:
                ActivityLogService.create_activity(
                    user=user,
                    content_object=response.data,
                    activity_type=ActivityType.UPDATED,
                    description="Complaint updated"
                )

            serializer = ComplaintSerializer(response.data)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Complaint updated successfully.",
                code=status.HTTP_200_OK,
            )
        except Exception as e:
            self.logger.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while updating the complaint.",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def delete_complaint(self, complaint_id, user=None) -> APIResponse:
        """
        Delete a complaint.
        """
        try:
            complaint = Complaint.objects.filter(id=complaint_id).first()
            if not complaint:
                return APIResponse(
                    success=False,
                    message="Complaint not found.",
                    code=status.HTTP_404_NOT_FOUND,
                )

            if user:
                ActivityLogService.create_activity(
                    user=user,
                    content_object=complaint,
                    activity_type=ActivityType.DELETED,
                    description="Complaint deleted"
                )

            response = self.repository.delete_complaint(complaint)
            if not response.success:
                self.logger.log_error(response.message)
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=status.HTTP_400_BAD_REQUEST,
                )
            return APIResponse(
                success=True,
                message="Complaint deleted successfully.",
                code=status.HTTP_204_NO_CONTENT,
            )
        except Exception as e:
            self.logger.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while deleting the complaint.",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def send_to_department(self, user, complaint_id, department_id) -> APIResponse:
        """
        Send the complaint to a specific department.
        """
        try:
            complaint = self.repository.get_complaint_by_id(complaint_id)
            if not complaint.success:
                return APIResponse(
                    success=False,
                    message=complaint.message,
                    code=status.HTTP_400_BAD_REQUEST,
                )
            response = ComplaintsWorkflow(user, complaint.data).send_to_department(
                department_id
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=status.HTTP_400_BAD_REQUEST,
                )

            department = Department.objects.get(id=department_id)
            ActivityLogService.create_activity(
                user=user,
                content_object=complaint.data,
                activity_type=ActivityType.SENT_TO_DEPARTMENT,
                highlight_objects=department,
                destination_objects=department
            )

            return APIResponse(
                success=True,
                data=response.data,
                message=response.message,
                code=status.HTTP_200_OK,
            )
        except Exception as e:
            self.logger.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while sending the complaint to the department.",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
