# Service class for complaints query operations
from datetime import datetime
from complaints.repository.complaints import ComplaintsRepository
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from complaints.models import Complaint
from reviews.serializers import ComplaintSerializer
from typing import Type
from rest_framework import status
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger


class ComplaintsQueryService:
    def __init__(self):
        self.repository = ComplaintsRepository()
        self.logger = LoggingService()

    def get_complaint_by_id(self, complaint_id: int) -> APIResponse:
        """
        Get a complaint by its ID.
        """
        try:
            response = self.repository.get_complaint_by_id(complaint_id)
            if not response.success:
                self.logger.log_error(response.message)
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=status.HTTP_404_NOT_FOUND,
                )
            serializer = ComplaintSerializer(response.data)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Complaint retrieved successfully.",
                code=status.HTTP_200_OK,
            )
        except Exception as e:
            self.logger.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving the complaint.",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_complaints(self, filters=None, page=1, page_size=10) -> APIResponse:
        try:
            complaints = (
                Complaint.objects.select_related(
                    "complain_facility", "created_by", "updated_by"
                )
                .prefetch_related("department", "assigned_to", "assignees")
                .all()
            )

            if filters:
                """Apply filters if provided
                Filters can include:
                - created_by: int, Filter by the user who created the complaint
                - date_of_complaint: string, Filter by the date of the complaint
                - department: int, Filter by the department associated with the complaint
                - resolved_by_staff: bool, Filter by the staff who resolved the complaint
                - assigned_to: int, Filter by the user to whom the complaint is assigned
                - created_at: string, Filter by the creation date of the complaint
                """

                if "created_by" in filters:
                    complaints = complaints.filter(created_by=filters["created_by"])
                if "date_of_complaint" in filters:
                    complaints = complaints.filter(
                        date_of_complaint=filters["date_of_complaint"]
                    )
                if "department" in filters:
                    complaints = complaints.filter(department=filters["department"])
                if "resolved_by_staff" in filters:
                    complaints = complaints.filter(
                        resolved_by_staff=filters["resolved_by_staff"]
                    )
                if "assigned_to" in filters:
                    complaints = complaints.filter(assigned_to=filters["assigned_to"])
                if "created_at" in filters:
                    complaints = complaints.filter(
                        created_at__date=filters["created_at"]
                    )
                start_date = filters.get("start_date")
                end_date = filters.get("end_date")
                if start_date or end_date:
                    complaints = complaints.exclude(date_of_complaint__isnull=True)
                    try:
                        if start_date:
                            start = datetime.strptime(start_date, "%Y-%m-%d").date()
                            complaints = complaints.filter(date_of_complaint__gte=start)
                        if end_date:
                            end = datetime.strptime(end_date, "%Y-%m-%d").date()
                            complaints = complaints.filter(date_of_complaint__lte=end)
                    except ValueError as e:
                        self.logger.log_error(e)

            # Order by most recent first for better performance
            complaints = complaints.order_by("-created_at")

            # Pagination
            paginator = Paginator(complaints, page_size)
            has_next = paginator.num_pages > page
            has_previous = page > 1
            total_pages = paginator.num_pages
            total_count = paginator.count

            serializer = ComplaintSerializer(paginator.page(page), many=True)

            data = {
                "results": serializer.data,
                "has_next": has_next,
                "has_previous": has_previous,
                "total_pages": total_pages,
                "total_count": total_count,
            }

            return APIResponse(
                success=True,
                data=data,
                message="Complaints retrieved successfully.",
                code=status.HTTP_200_OK,
            )
        except EmptyPage:
            return APIResponse(
                success=False,
                message="Page not found.",
                code=status.HTTP_404_NOT_FOUND,
            )
        except PageNotAnInteger:
            return APIResponse(
                success=False,
                message="Invalid page number.",
                code=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            self.logger.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving complaints.",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
