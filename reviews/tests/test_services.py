from django.test import TransactionTestCase
from django.contrib.auth.models import User
from adverse_drug_reaction.tests.factory import *
from general_patient_visitor.tests.factory import *
from lost_and_found.tests.factory import *
from medication_error.tests.factory import *
from patient_visitor_grievance.tests.factory import *
from staff_incident_reports.tests.factory import *
from workplace_violence_reports.tests.factory import *
from reviews.models import Review
from reviews.services.review import ReviewsOperations
from reviews.tests.factory import ReviewFactory
from base.tests.factory import UserFactory, FacilityFactory, DepartmentFactory
from base.services.responses import APIResponse
from rest_framework import status
from unittest.mock import patch, Mock
from complaints.tests.factory import ComplaintFactory
from complaints.models import Complaint


class TestGetIncidentReviews(TransactionTestCase):
    """Test getting incident reviews"""

    def setUp(self):
        self.user = UserFactory()

    # cases for adverse drug reaction incident reviews
    def test_get_adverse_drug_reaction_reviews(self):
        """Test getting reviews for an adverse drug reaction incident"""
        incident = AdverseDrugReactionFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(3, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="AdverseDrugReaction",
            app_label="adverse_drug_reaction",
            incident_id=incident.id,
        )
        response = reviews_operations.get_incident_reviews()

        if not response.success:
            self.fail(f"Failed to get reviews: {response.message}")

        # check number of reviews returned
        self.assertEqual(len(response.data), 3)

    # cases for general patient visitor incident reviews
    def test_get_general_patient_visitor_reviews(self):
        """Test getting reviews for a general patient visitor incident"""
        incident = GeneralPatientVisitorFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(2, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="GeneralPatientVisitor",
            app_label="general_patient_visitor",
            incident_id=incident.id,
        )
        response = reviews_operations.get_incident_reviews()

        if not response.success:
            self.fail(f"Failed to get reviews: {response.message}")

        # check number of reviews returned
        self.assertEqual(len(response.data), 2)

    # cases for lost and found incident reviews
    def test_get_lost_and_found_reviews(self):
        """Test getting reviews for a lost and found incident"""
        incident = LostAndFoundFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(4, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="LostAndFound",
            app_label="lost_and_found",
            incident_id=incident.id,
        )
        response = reviews_operations.get_incident_reviews()

        if not response.success:
            self.fail(f"Failed to get reviews: {response.message}")

        # check number of reviews returned
        self.assertEqual(len(response.data), 4)

    # cases for medication error incident reviews
    def test_get_medication_error_reviews(self):
        """Test getting reviews for a medication error incident"""
        incident = MedicationErrorFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(5, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="MedicationError",
            app_label="medication_error",
            incident_id=incident.id,
        )
        response = reviews_operations.get_incident_reviews()

        if not response.success:
            self.fail(f"Failed to get reviews: {response.message}")

        # check number of reviews returned
        self.assertEqual(len(response.data), 5)

    # cases for patient visitor grievance incident reviews
    def test_get_patient_visitor_grievance_reviews(self):
        """Test getting reviews for a patient visitor grievance incident"""
        incident = GrievanceFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(2, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="Grievance",
            app_label="patient_visitor_grievance",
            incident_id=incident.id,
        )
        response = reviews_operations.get_incident_reviews()

        if not response.success:
            self.fail(f"Failed to get reviews: {response.message}")

        # check number of reviews returned
        self.assertEqual(len(response.data), 2)

    # cases for staff incident reports incidents reviews
    def test_get_staff_incident_reports_reviews(self):
        """Test getting reviews for a staff incident report"""
        incident = StaffIncidentReportFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(3, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="StaffIncidentReport",
            app_label="staff_incident_reports",
            incident_id=incident.id,
        )
        response = reviews_operations.get_incident_reviews()

        if not response.success:
            self.fail(f"Failed to get reviews: {response.message}")

        # check number of reviews returned
        self.assertEqual(len(response.data), 3)

    # cases for workplace violence incident reviews
    def test_get_workplace_violence_reviews(self):
        """Test getting reviews for a workplace violence incident"""
        incident = WorkPlaceViolenceFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(4, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="WorkPlaceViolence",
            app_label="workplace_violence_reports",
            incident_id=incident.id,
        )
        response = reviews_operations.get_incident_reviews()

        if not response.success:
            self.fail(f"Failed to get reviews: {response.message}")

        # check number of reviews returned
        self.assertEqual(len(response.data), 4)


class TestCreateIncidentReview(TransactionTestCase):
    """Test creating incident reviews"""

    def setUp(self):
        self.user = UserFactory()

    # cases for adverse drug reaction incident reviews
    def test_create_adverse_drug_reaction_review(self):
        """Test creating a review for an adverse drug reaction incident"""
        incident = AdverseDrugReactionFactory()
        # clear any existing reviews
        incident.reviews.clear()
        content = "This is a test review content"

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="AdverseDrugReaction",
            app_label="adverse_drug_reaction",
            incident_id=incident.id,
        )
        response = reviews_operations.create_review(content)

        if not response.success:
            self.fail(f"Failed to create review: {response.message}")

        # check if the review was created and added to the incident
        self.assertTrue(Review.objects.filter(content=content).exists())
        self.assertEqual(incident.reviews.count(), 1)
        self.assertEqual(incident.reviews.first().content, content)

    # cases for general patient visitor incident reviews
    def test_create_general_patient_visitor_review(self):
        """Test creating a review for a general patient visitor incident"""
        incident = GeneralPatientVisitorFactory()
        # clear any existing reviews
        incident.reviews.clear()
        content = "This is a test review content"

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="GeneralPatientVisitor",
            app_label="general_patient_visitor",
            incident_id=incident.id,
        )
        response = reviews_operations.create_review(content)

        if not response.success:
            self.fail(f"Failed to create review: {response.message}")

        # check if the review was created and added to the incident
        self.assertTrue(Review.objects.filter(content=content).exists())
        self.assertEqual(incident.reviews.count(), 1)
        self.assertEqual(incident.reviews.first().content, content)

    # cases for lost and found incident reviews
    def test_create_lost_and_found_review(self):
        """Test creating a review for a lost and found incident"""
        incident = LostAndFoundFactory()
        # clear any existing reviews
        incident.reviews.clear()
        content = "This is a test review content"

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="LostAndFound",
            app_label="lost_and_found",
            incident_id=incident.id,
        )
        response = reviews_operations.create_review(content)

        if not response.success:
            self.fail(f"Failed to create review: {response.message}")

        # check if the review was created and added to the incident
        self.assertTrue(Review.objects.filter(content=content).exists())
        self.assertEqual(incident.reviews.count(), 1)
        self.assertEqual(incident.reviews.first().content, content)

    # cases for medication error incident reviews
    def test_create_medication_error_review(self):
        """Test creating a review for a medication error incident"""
        incident = MedicationErrorFactory()
        # clear any existing reviews
        incident.reviews.clear()
        content = "This is a test review content"

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="MedicationError",
            app_label="medication_error",
            incident_id=incident.id,
        )
        response = reviews_operations.create_review(content)

        if not response.success:
            self.fail(f"Failed to create review: {response.message}")

        # check if the review was created and added to the incident
        self.assertTrue(Review.objects.filter(content=content).exists())
        self.assertEqual(incident.reviews.count(), 1)
        self.assertEqual(incident.reviews.first().content, content)

    # cases for patient visitor grievance incident reviews
    def test_create_patient_visitor_grievance_review(self):
        """Test creating a review for a patient visitor grievance incident"""
        incident = GrievanceFactory()
        # clear any existing reviews
        incident.reviews.clear()
        content = "This is a test review content"

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="Grievance",
            app_label="patient_visitor_grievance",
            incident_id=incident.id,
        )
        response = reviews_operations.create_review(content)

        if not response.success:
            self.fail(f"Failed to create review: {response.message}")

        # check if the review was created and added to the incident
        self.assertTrue(Review.objects.filter(content=content).exists())
        self.assertEqual(incident.reviews.count(), 1)
        self.assertEqual(incident.reviews.first().content, content)

    # cases for staff incident reports incidents reviews
    def test_create_staff_incident_reports_review(self):
        """Test creating a review for a staff incident report"""
        incident = StaffIncidentReportFactory()
        # clear any existing reviews
        incident.reviews.clear()
        content = "This is a test review content"

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="StaffIncidentReport",
            app_label="staff_incident_reports",
            incident_id=incident.id,
        )
        response = reviews_operations.create_review(content)

        if not response.success:
            self.fail(f"Failed to create review: {response.message}")

        # check if the review was created and added to the incident
        self.assertTrue(Review.objects.filter(content=content).exists())
        self.assertEqual(incident.reviews.count(), 1)
        self.assertEqual(incident.reviews.first().content, content)

    # cases for workplace violence incident reviews
    def test_create_workplace_violence_review(self):
        """Test creating a review for a workplace violence incident"""
        incident = WorkPlaceViolenceFactory()
        # clear any existing reviews
        incident.reviews.clear()
        content = "This is a test review content"

        reviews_operations = ReviewsOperations(
            user=self.user,
            model_name="WorkPlaceViolence",
            app_label="workplace_violence_reports",
            incident_id=incident.id,
        )
        response = reviews_operations.create_review(content)

        if not response.success:
            self.fail(f"Failed to create review: {response.message}")

        # check if the review was created and added to the incident
        self.assertTrue(Review.objects.filter(content=content).exists())
        self.assertEqual(incident.reviews.count(), 1)
        self.assertEqual(incident.reviews.first().content, content)
