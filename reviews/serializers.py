from rest_framework import serializers
from accounts.serializers import UserSerializer
from complaints.models import Complaint
from reviews.models import Review


class ReviewsSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()

    class Meta:
        model = Review
        fields = "__all__"


class ComplaintSerializer(serializers.ModelSerializer):
    class Meta:
        model = Complaint
        fields = "__all__"
