from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from reviews.models import Review
from reviews.serializers import ReviewsSerializer
from django.apps import apps
from rest_framework import status

logger = LoggingService()


class ReviewsOperations:
    def __init__(self, user, model_name, app_label, incident_id):
        self.user = user
        self.model_name = model_name
        self.app_label = app_label
        self.incident_id = incident_id

    def get_incident_reviews(self) -> APIResponse:
        try:
            # Get the model class dynamically
            model_class = apps.get_model(self.app_label, self.model_name)

            # Retrieve the incident instance
            incident = model_class.objects.get(pk=self.incident_id)

            # Get all reviews for the incident
            reviews = incident.reviews.all().order_by("-created_at")
            serializer = ReviewsSerializer(reviews, many=True)
            return APIResponse(
                message="Reviews retrieved successfully",
                data=serializer.data,
                success=True,
                code=status.HTTP_200_OK,
            )

        except model_class.DoesNotExist as e:
            logger.log_error(e)
            return APIResponse(
                message={"error": f"No incident with that id exists"},
                success=False,
                code=status.HTTP_404_NOT_FOUND,
            )

    def create_review(self, content) -> APIResponse:
        try:
            # Get the model class dynamically
            model_class = apps.get_model(self.app_label, self.model_name)

            # Retrieve the incident instance
            incident = model_class.objects.get(pk=self.incident_id)

            # Create or get the review
            new_review, created = Review.objects.get_or_create(
                created_by=self.user,
                content=content,
            )

            if created:
                # Add the review to the incident
                incident.reviews.add(new_review)
                incident.save()
                reviews = incident.reviews.all()
                reviews_serializer = ReviewsSerializer(reviews, many=True)
                return APIResponse(
                    message="Review created successfully",
                    success=True,
                    data=reviews_serializer.data,
                    code=status.HTTP_201_CREATED,
                )
            else:
                reviews = incident.reviews.all()
                reviews_serializer = ReviewsSerializer(reviews, many=True)
                return APIResponse(
                    message="Review already exists",
                    success=True,
                    data=reviews_serializer.data,
                    code=status.HTTP_200_OK,
                )

        except Exception as e:
            logger.log_error(e)
            return APIResponse(
                message={"error": f"Error creating review"},
                success=False,
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
