from pathlib import Path
import os
from datetime import timedelta
from dotenv import load_dotenv

load_dotenv()
BASE_DIR = Path(__file__).resolve().parent.parent


SECRET_KEY = os.getenv("SECRET_KEY")
# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True
ENVIRONMENT = os.getenv("ENVIRONMENT")

SEND_TEST_EMAILS = False
ENCRYPTION_KEY = (
    os.getenv("ENCRYPTION_KEY")
    if ENVIRONMENT == "production"
    else os.getenv("TEST_ENCRYPTION_KEY")
)
ALLOWED_HOSTS = [
    "localhost",
    "127.0.0.1",
    "qc-api.csrtesting.com",
    "www.qc-api.csrtesting.com",
    "q-control-backend-test.onrender.com",
    "q-control-backend.onrender.com",
    "https://q-control-frontend-csr-ltd.vercel.app",
]

# Application definition
INSTALLED_APPS = [
    "general_patient_visitor",
    "adverse_drug_reaction",
    "patient_visitor_grievance",
    "lost_and_found",
    "medication_error",
    "staff_incident_reports",
    "workplace_violence_reports",
    "reviews",
    "jazzmin",
    "rest_framework",
    "rest_framework_simplejwt",
    "corsheaders",
    "accounts",
    "api",
    "base",
    "incidents",
    "activities",
    "documents",
    "complaints",
    "facilities",
    "support",
    "tasks",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "tinymce",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "core.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"

# Database

if ENVIRONMENT == "production":
    DOMAIN_NAME = os.getenv("MAIN_DOMAIN_NAME")
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": os.getenv("DB_NAME"),
            "USER": os.getenv("DB_USERNAME"),
            "PASSWORD": os.getenv("DB_PASSWORD"),
            "HOST": os.getenv("DB_HOST"),
            "PORT": os.getenv("DB_PORT"),
            "SSL": os.getenv("SSL"),
        }
    }
elif ENVIRONMENT == "test":
    DOMAIN_NAME = os.getenv("TEST_DOMAIN_NAME")
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": os.getenv("TEST_DB_NAME"),
            "USER": os.getenv("TEST_DB_USERNAME"),
            "PASSWORD": os.getenv("TEST_DB_PASSWORD"),
            "HOST": os.getenv("TEST_DB_HOST"),
            "PORT": os.getenv("TEST_DB_PORT"),
            "SSL": os.getenv("SSL"),
        }
    }
else:
    DOMAIN_NAME = os.getenv("DEV_DOMAIN_NAME")
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        }
    }


# Password validation

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)

STATIC_URL = "/static/"
MEDIA_URL = "/media/"
MEDIA_ROOT = BASE_DIR / "media/"
STATIC_ROOT = BASE_DIR / "static"

if not DEBUG:
    STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
    STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"


# Default primary key field type

MAIN_EMAIL = os.getenv("MAIN_EMAIL")
MAIN_DOMAIN_NAME = os.getenv("MAIN_DOMAIN_NAME")

EMAIL_SERVER_TOKEN = os.getenv("EMAIL_SERVER_TOKEN")
MAIN_EMAIL = os.getenv("MAIN_EMAIL")


REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.AllowAny",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.SessionAuthentication",
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ],
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=2),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=90),
    # ... other JWT settings (optional)
}

REST_FRAME_SIMPLEJWT = {
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.RefreshToken",),
}


CORS_ALLOWED_ORIGINS = (
    "http://localhost:3000",
    "http://localhost:3001",
    "http://127.0.0.1:8000",
    "http://127.0.0.1:5500",
    "http://localhost:8000",
    "https://csrtesting.com",
    "https://q-control.csrlimited.com",
    "https://q-control-frontend-csr-ltd.vercel.app",
    "https://q-control-frontend-beta.vercel.app",
    "chrome-extension://dnioeiomkjagcooolffejkcpjdedfnan",
    "https://chmc-auth-backend.onrender.com",
    "https://chmc-auth-backend-test.onrender.com",
)

CORS_ALLOWED_ORIGIN_REGEXES = [
    r"^https?://(?:.*\.)?csrtesting\.com$",
    r"^https?://(?:.*\.)?q-control\.csrlimited\.com$",
    r"^https?://(?:.*\.)?q-control-frontend-csr-ltd\.vercel\.app$",
    r"^https?://(?:.*\.)?q-control-frontend-beta\.vercel\.app$",
]

CORS_ALLOW_METHODS = (
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
)
CORS_ALLOW_HEADERS = (
    "accept",
    "authorization",
    "content-type",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
)

CSRF_TRUSTED_ORIGINS = [
    "https://q-control-backend-test.onrender.com",
    "http://localhost:3000",
    "http://localhost:3001",
]
JAZZMIN_UI_TWEAKS = {"theme": "cosmo"}

CLOUDINARY_NAME = os.getenv("CLOUDINARY_NAME")
CLOUDINARY_API_KEY = os.getenv("CLOUDINARY_API_KEY")
CLOUDINARY_SECRET_KEY = os.getenv("CLOUDINARY_SECRET_KEY")

# aws


# settings
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = os.getenv("AWS_STORAGE_BUCKET_NAME")
AWS_DEFAULT_ACL = "public-read"
AWS_S3_ENDPOINT_URL = "https://syd1.digitaloceanspaces.com"
AWS_S3_OBJECT_PARAMETERS = {"CacheControl": "max-age=86400"}
# static settings
# public media settings
PUBLIC_MEDIA_LOCATION = "media"
MEDIA_URL = f"https://{AWS_S3_ENDPOINT_URL}/{PUBLIC_MEDIA_LOCATION}/"
DEFAULT_FILE_STORAGE = "documents.storage_backends.PublicMediaStorage"
# private media settings
PRIVATE_MEDIA_LOCATION = "private"
PRIVATE_FILE_STORAGE = "documents.storage_backends.PrivateMediaStorage"


if not ENVIRONMENT == "production":
    # Redis configuration
    REDIS_HOST = "localhost"
    REDIS_PORT = 6379
    REDIS_DB = 0
    REDIS_PASSWORD = None
else:
    REDIS_HOST = os.getenv("REDIS_HOST")
    REDIS_PORT = int(os.getenv("REDIS_PORT"))
    REDIS_DB = int(os.getenv("REDIS_DB"))
    REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")

# Cache time to live is 1 hour
REDIS_CACHE_TTL = 3600

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Logging Configuration
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "simple": {
            "format": "{levelname} {asctime} {message}",
            "style": "{",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "json": {
            "format": '{"level": "%(levelname)s", "time": "%(asctime)s", "module": "%(module)s", "message": "%(message)s"}',
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    },
    "filters": {
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        },
        "require_debug_false": {
            "()": "django.utils.log.RequireDebugFalse",
        },
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
        "file_application": {
            "level": "INFO",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": os.path.join(BASE_DIR, "logs", "application.log"),
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "formatter": "verbose",
            "encoding": "utf-8",
        },
        "file_error": {
            "level": "ERROR",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": os.path.join(BASE_DIR, "logs", "error.log"),
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "formatter": "verbose",
            "encoding": "utf-8",
        },
        "file_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": os.path.join(BASE_DIR, "logs", "debug.log"),
            "when": "midnight",
            "interval": 1,
            "backupCount": 7,
            "formatter": "verbose",
            "encoding": "utf-8",
            "filters": ["require_debug_true"],
        },
        "file_django": {
            "level": "INFO",
            "class": "logging.handlers.TimedRotatingFileHandler",
            "filename": os.path.join(BASE_DIR, "logs", "django.log"),
            "when": "midnight",
            "interval": 1,
            "backupCount": 30,
            "formatter": "verbose",
            "encoding": "utf-8",
        },
    },
    "root": {
        "handlers": ["console", "file_application"],
        "level": "INFO",
    },
    "loggers": {
        "django": {
            "handlers": ["file_django", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "django.request": {
            "handlers": ["file_error", "console"],
            "level": "ERROR",
            "propagate": False,
        },
        "django.db.backends": {
            "handlers": ["file_debug"],
            "level": "DEBUG",
            "propagate": False,
        },
        "reviews": {
            "handlers": ["file_application", "file_error", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "complaints": {
            "handlers": ["file_application", "file_error", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "incidents": {
            "handlers": ["file_application", "file_error", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "accounts": {
            "handlers": ["file_application", "file_error", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "base": {
            "handlers": ["file_application", "file_error", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        # Add other app loggers as needed
        "activities": {
            "handlers": ["file_application", "file_error", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "documents": {
            "handlers": ["file_application", "file_error", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "facilities": {
            "handlers": ["file_application", "file_error", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
        "tasks": {
            "handlers": ["file_application", "file_error", "console"],
            "level": "DEBUG" if DEBUG else "INFO",
            "propagate": False,
        },
    },
}
