from django.contrib import admin
from django.urls import path, include
from django.conf.urls.static import static
from django.conf import settings

admin.site.site_header = "Q-Control Admin"
admin.site.site_title = "Q-Control Admin Portal"
admin.site.index_title = "Q-Control Limited"

urlpatterns = [
    path("admin/", admin.site.urls),
    path(
        "api/accounts/",
        include("api.urls.accounts"),
    ),
    path(
        "api/titles/",
        include("api.urls.titles"),
    ),
    path(
        "api/incidents/overview/",
        include("api.urls.overview"),
    ),
    # path(
    #     "api/incidents/general/",
    #     include("api.urls.general_incident"),
    # ),
    # path(
    #     "api/incidents/employee_health_investigation/",
    #     include("api.urls.employee_health_investigation"),
    # ),
    # path(
    #     "api/incidents/employee_incident/",
    #     include("api.urls.employee_incident"),
    # ),
    path(
        "api/incidents/grievance/",
        include("api.urls.new_grievance"),
    ),
    path(
        "api/incidents/lost-found/",
        include("api.urls.new_lost_found"),
    ),
    path(
        "api/incidents/adverse-drug-reaction/",
        include("api.urls.adr"),
    ),
    path(
        "api/incidents/general-visitor/",
        include("api.urls.general_patient_visitor_incident"),
    ),
    path("api/incidents/staff-incident/", include("api.urls.staff_incident")),
    path(
        "api/incidents/medication-error/",
        include("api.urls.new_medication_error"),
    ),
    path(
        "api/incidents/workplace-violence/",
        include("api.urls.workplace_violence"),
    ),
    # path(
    #     "api/incidents/grievance-investigation/",
    #     include("api.urls.grievance_investigation"),
    # ),
    path("api/tasks/", include("api.urls.tasks")),
    path("api/departments/", include("api.urls.departments")),
    path("api/complaints/", include("api.urls.complaints")),
    path("api/activities/", include("api.urls.activities")),
    path("api/documents/", include("api.urls.documents")),
    path("api/facilities/", include("api.urls.facilities")),
    path("api/support/", include("api.urls.support")),
    path("api/notifications/", include("api.urls.notifications")),
    path("api/settings/", include("api.urls.settings")),
    path("api/permissions/", include("api.urls.permissions")),
    path("api/users/", include("api.urls.users")),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
