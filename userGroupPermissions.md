# User Group Permissions Matrix

**Last Updated: August 14, 2025**

This document defines the specific permissions each user group should have in the Quality Control system, based on the role-based access control implementation and user flow requirements.

---

## **User Groups Overview**

The system implements 7 primary user groups with hierarchical permissions:

1. **Regular User** (Base level)
2. **Manager** (Department level)
3. **Director** (Facility level - Read-only)
4. **Admin** (Facility level - Full access)
5. **Quality/Risk Manager** (Global level)
6. **Super User** (System level)
7. **User Editor** (Account management only)

---

## **Base Permissions**

All user groups inherit these base permissions unless explicitly restricted:

| Permission     | Description               | Available To      |
| -------------- | ------------------------- | ----------------- |
| `view_list`    | Can view incident lists   | All groups        |
| `view_details` | Can view incident details | All groups        |
| `export_list`  | Can export incident lists | Manager and above |
| `export`       | Can export incident data  | Manager and above |

---

## **Incident-Specific Permissions**

### **Complete Permission Matrix**

| Permission                 | Regular User | Manager       | Director    | Admin       | Quality/Risk Manager | Super User | User Editor |
| -------------------------- | ------------ | ------------- | ----------- | ----------- | -------------------- | ---------- | ----------- |
| **Base Permissions**       |              |               |             |             |                      |            |             |
| `view_list`                | ✅ Own only  | ✅ Department | ✅ Facility | ✅ Facility | ✅ All facilities    | ✅ All     | ❌          |
| `view_details`             | ✅ Own only  | ✅ Department | ✅ Facility | ✅ Facility | ✅ All facilities    | ✅ All     | ❌          |
| `export_list`              | ❌           | ✅ Department | ✅ Facility | ✅ Facility | ✅ All facilities    | ✅ All     | ❌          |
| `export`                   | ❌           | ✅ Department | ✅ Facility | ✅ Facility | ✅ All facilities    | ✅ All     | ❌          |
| **Incident Management**    |              |               |             |             |                      |            |             |
| `add_review`               | ❌           | ✅            | ❌          | ✅          | ✅                   | ✅         | ❌          |
| `assign_incident`          | ❌           | ✅            | ❌          | ✅          | ✅                   | ✅         | ❌          |
| `close_incident`           | ❌           | ❌            | ❌          | ✅          | ✅                   | ✅         | ❌          |
| `rate_severity`            | ❌           | ❌            | ❌          | ✅          | ✅                   | ✅         | ❌          |
| `change_incident`          | ❌           | ✅ Limited    | ❌          | ✅          | ✅                   | ✅         | ❌          |
| `send_for_review`          | ❌           | ✅            | ❌          | ✅          | ✅                   | ✅         | ❌          |
| `mark_as_resolved`         | ❌           | ❌            | ❌          | ✅          | ✅                   | ✅         | ❌          |
| `send_to_department`       | ❌           | ✅            | ❌          | ✅          | ✅                   | ✅         | ❌          |
| `manage_restricted_access` | ❌           | ❌            | ❌          | ✅          | ✅                   | ✅         | ❌          |
| `delete_incident`          | ❌           | ❌            | ❌          | ❌          | ❌                   | ✅         | ❌          |
| `view_users_incidents`     | ❌           | ✅ Department | ✅ Facility | ✅ Facility | ✅ All facilities    | ✅ All     | ❌          |

---

## **Detailed Group Specifications**

### **1. Regular User**

**Group Name:** `User`

**Core Permissions:**

- Create incident reports (all types)
- View own incident reports
- Upload documents to own incidents
- Submit complaints

**Restrictions:**

- Cannot view other users' incidents
- Cannot modify or delete any incidents
- Cannot export data
- Cannot assign or review incidents

**Django Permissions:**

```python
permissions = [
    'view_list',           # Own incidents only
    'view_details',        # Own incidents only
]
```

---

### **2. Manager**

**Group Name:** `Manager`

**Core Permissions:**

- All Regular User permissions
- View unrestricted incidents in assigned department
- Add reviews and comments
- Send incidents to Quality/Risk department
- Export department-level data
- Modify incidents within department (limited)
- Assign incidents within department

**Restrictions:**

- Cannot close incidents
- Cannot access restricted information without approval
- Department-bound access only

**Django Permissions:**

```python
permissions = [
    'view_list',           # Department incidents
    'view_details',        # Department incidents
    'export_list',         # Department data
    'export',              # Department data
    'add_review',          # Can add reviews
    'assign_incident',     # Within department
    'change_incident',     # Limited modification
    'send_for_review',     # To Quality/Risk
    'send_to_department',  # Workflow actions
    'view_users_incidents', # Department scope
]
```

---

### **3. Director**

**Group Name:** `Director`

**Core Permissions:**

- All Regular User permissions
- Read-only access to all facility incidents
- View dashboards and analytics
- Export facility-level data
- View logs and reports

**Restrictions:**

- No editing or modification rights
- Cannot assign or review incidents
- Restricted information requires approval

**Django Permissions:**

```python
permissions = [
    'view_list',           # Facility incidents
    'view_details',        # Facility incidents
    'export_list',         # Facility data
    'export',              # Facility data
    'view_users_incidents', # Facility scope
]
```

---

### **4. Admin**

**Group Name:** `Admin`

**Core Permissions:**

- All Manager and Director permissions
- Full access to facility incidents
- Modify and edit incident reports
- Send incidents to departments
- Manage user accounts within facility
- Grant/revoke restricted access
- Close incidents (except own)
- Rate incident severity

**Restrictions:**

- Cannot close own incidents
- Facility-bound access
- Cannot delete incidents

**Django Permissions:**

```python
permissions = [
    'view_list',              # Facility incidents
    'view_details',           # Facility incidents
    'export_list',            # Facility data
    'export',                 # Facility data
    'add_review',             # Can add reviews
    'assign_incident',        # Full assignment rights
    'close_incident',         # Except own
    'rate_severity',          # Severity ratings
    'change_incident',        # Full modification
    'send_for_review',        # Workflow actions
    'mark_as_resolved',       # Resolution rights
    'send_to_department',     # Department workflow
    'manage_restricted_access', # Access control
    'view_users_incidents',   # Facility scope
]
```

---

### **5. Quality/Risk Manager**

**Group Name:** `Quality/Risk Manager`

**Core Permissions:**

- All Admin permissions across all facilities
- Global incident access
- Add severity ratings
- Close incidents (except own)
- Assign restricted access globally
- Cross-facility data export

**Restrictions:**

- Cannot close own incidents
- Cannot delete incidents

**Django Permissions:**

```python
permissions = [
    'view_list',              # All facilities
    'view_details',           # All facilities
    'export_list',            # Global data
    'export',                 # Global data
    'add_review',             # Global reviews
    'assign_incident',        # Global assignment
    'close_incident',         # Except own
    'rate_severity',          # Global severity
    'change_incident',        # Global modification
    'send_for_review',        # Global workflow
    'mark_as_resolved',       # Global resolution
    'send_to_department',     # Global workflow
    'manage_restricted_access', # Global access control
    'view_users_incidents',   # Global scope
]
```

---

### **6. Super User**

**Group Name:** `Super user`

**Core Permissions:**

- All system permissions
- Delete incidents and reports
- Modify any user account
- Configure system settings
- Database backups and maintenance
- Override all restrictions

**Restrictions:**

- None (Full system access)

**Django Permissions:**

```python
permissions = [
    'view_list',              # Global access
    'view_details',           # Global access
    'export_list',            # Global data
    'export',                 # Global data
    'add_review',             # Global reviews
    'assign_incident',        # Global assignment
    'close_incident',         # Including own
    'rate_severity',          # Global severity
    'change_incident',        # Global modification
    'send_for_review',        # Global workflow
    'mark_as_resolved',       # Global resolution
    'send_to_department',     # Global workflow
    'manage_restricted_access', # Global access control
    'delete_incident',        # Deletion rights
    'view_users_incidents',   # Global scope
]
```

---

### **7. User Editor**

**Group Name:** `User Editor`

**Core Permissions:**

- Create and edit user accounts
- Deactivate user accounts
- Submit own incident reports
- Manage user profiles

**Restrictions:**

- No access to incident reports (except own)
- No access to dashboards or analytics
- Cannot export data

**Django Permissions:**

```python
permissions = [
    'view_list',           # Own incidents only
    'view_details',        # Own incidents only
    # No incident management permissions
    # Account management handled separately
]
```

---

## **Special Department-Specific Permissions**

### **Enhanced Manager Permissions by Department**

| Department          | Additional Permissions               | Special Access                            |
| ------------------- | ------------------------------------ | ----------------------------------------- |
| **Pharmacy**        | Medication Error/ADR review rights   | Full access to pharmacy-related incidents |
| **HR**              | Workplace Violence additional rights | Enhanced access to HR incidents           |
| **Employee Health** | Staff Incident log access            | Health-related incident oversight         |
| **Quality/Risk**    | Global incident access               | Cross-facility incident management        |

---

## **Permission Implementation Notes**

### **Role-Based Access Control (RBAC)**

- Each group maps to Django's built-in Group model
- Permissions are enforced at the service and API levels
- Role hierarchy determines incident visibility scope

### **Facility and Department Scoping**

- Users are assigned to facilities and departments via Profile model
- Access is automatically scoped based on user's facility/department assignment
- Managers have access to their specific department's incidents
- Admins and Directors have facility-wide access
- Quality/Risk Managers and Super Users have global access

### **Business Rule Enforcement**

- **No Self-Closing**: Users cannot close their own incidents (system-enforced)
- **Restricted Access**: Sensitive incidents require explicit approval
- **Audit Trail**: All permission changes and access grants are logged
- **Regular User Restrictions**: Cannot modify incidents, even their own reports

### **Permission Validation**

Each action is validated through multiple layers:

1. Django Group membership check
2. Facility/Department access validation
3. Incident ownership verification
4. Business rule compliance check

---

## **Migration and Setup**

### **Django Groups to Create**

```python
groups_to_create = [
    'User',
    'Manager',
    'Director',
    'Admin',
    'Quality/Risk Manager',
    'Super user',
    'User Editor'
]
```

### **Permission Assignment Script**

```python
# Example for Admin group
admin_permissions = [
    'view_list', 'view_details', 'export_list', 'export',
    'add_review', 'assign_incident', 'close_incident',
    'rate_severity', 'change_incident', 'send_for_review',
    'mark_as_resolved', 'send_to_department',
    'manage_restricted_access', 'view_users_incidents'
]
```

This permission matrix ensures proper role-based access control while maintaining security and business rule compliance across the Quality Control system.
