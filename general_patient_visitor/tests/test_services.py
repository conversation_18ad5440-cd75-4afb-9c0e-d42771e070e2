from base.services.responses import APIResponse
from general_patient_visitor.models import GeneralPatientVisitor
from general_patient_visitor.services.actions import GPVActionsService
from general_patient_visitor.services.operations import GPVService
from base.tests.factory import GeneralPatientVisitorFactory
from base.models import Department, Facility
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import *
from unittest.mock import patch, MagicMock
from rest_framework import status

from tasks.tests.factory import ReviewTemplateFactory


class TestGPVServiceCase(BaseTestSetup):
    """Test GPV Service operations as Superuser"""

    def setUp(self):
        super().setUp()
        # Use the super_user from BaseTestSetup
        self.service = GPVService(user=self.super_user)

    def test_get_incident_details(self):
        user = UserFactory()
        service = GPVService(user)
        incident = GeneralPatientVisitorFactory(created_by=user)
        response = service.get_incident_by_id(
            incident.id,
        )

        self.assertEqual(response.success, True)

    def test_get_incidents_as_superuser(self):
        # create 5 new incidents for new facility
        for _ in range(5):
            GeneralPatientVisitorFactory(
                department=self.super_user_dept,
                report_facility=self.super_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            GeneralPatientVisitorFactory()
        service = GPVService(self.super_user)
        response = service.get_incidents_list()

        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data), 7)


class TestGPVServiceAsAdmin(BaseTestSetup):
    """Test GPV Service operations as Admin user"""

    def setUp(self):
        super().setUp()
        self.service = GPVService(user=self.admin_user)

    def test_get_incidents_as_admin(self):
        """Test getting incidents as an admin user"""
        # Admin should see incidents from their facility
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGPVServiceAsDirector(BaseTestSetup):
    """Test GPV Service operations as Director user"""

    def setUp(self):
        super().setUp()
        self.service = GPVService(user=self.director_user)

    def test_get_incidents_as_director(self):
        """Test getting incidents as a director user"""
        # Director should see incidents from their facility
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGPVServiceAsManager(BaseTestSetup):
    """Test GPV Service operations as Manager user"""

    def setUp(self):
        super().setUp()
        self.service = GPVService(user=self.manager_user)

    def test_get_incidents_as_manager(self):
        """Test getting incidents as a manager user"""
        # Manager should see incidents from their department
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGPVServiceAsRegularUser(BaseTestSetup):
    """Test GPV Service operations as Regular user"""

    def setUp(self):
        super().setUp()
        self.service = GPVService(user=self.user_user)

    def test_get_incidents_as_regular_user(self):
        """Test getting incidents as a regular user"""
        # Regular user should see their own incidents and assigned reviews
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestGPVServiceCreateIncidentNewUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = GPVService(user=self.super_user)
        self.incident_data = {
            "facility_id": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "patient_visitor": {
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "profile_type": "Patient",
            },
            "physician_notified": {
                "first_name": "Dr",
                "last_name": "Smith",
                "profile_type": "Physician",
            },
            "family_notified": {
                "first_name": "Jane",
                "last_name": "Doe",
                "profile_type": "Family",
            },
            "status": "Open",
        }

    def test_create_incident_success(self):
        response = self.service.create_incident(
            data=self.incident_data, user=self.super_user
        )

        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertIsNotNone(response.data)


class TestGPVServiceModifyIncidentNewUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = GeneralPatientVisitorFactory(created_by=self.super_user)
        self.service = GPVActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "patient_visitor": {
                    "first_name": "John",
                    "last_name": "Doe",
                    "email": "<EMAIL>",
                    "profile_type": "Patient",
                },
                "physician_notified": {
                    "first_name": "Dr",
                    "last_name": "Smith",
                    "profile_type": "Physician",
                },
                "family_notified": {
                    "first_name": "Jane",
                    "last_name": "Doe",
                    "profile_type": "Family",
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_fields(self):
        self.service.data.get("patient_visitor").pop("profile_type")

        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestGPVServiceModifyIncidentExistingUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = GeneralPatientVisitorFactory(created_by=self.super_user)
        self.service = GPVActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "patient_visitor": {
                    "user_id": self.patient.id,
                },
                "physician_notified": {
                    "user_id": self.physician.id,
                },
                "family_notified": {
                    "user_id": self.family.id,
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_fields(self):
        self.service.data.get("patient_visitor").pop("user_id")

        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_modify_incident_invalid_user_id(self):
        self.service.data.get("patient_visitor")["user_id"] = 9999

        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestGPVServiceSendReviewIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = GeneralPatientVisitorFactory(created_by=self.super_user)
        self.review_template = ReviewTemplateFactory()
        self.assignee_1 = ProfileFactory()
        self.assignee_2 = ProfileFactory()
        self.assignee_3 = ProfileFactory()
        self.service = GPVActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "review_template": self.review_template.id,
            },
        )

    def test_send_for_review_success(self):
        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_send_for_review_invalid_template(self):
        self.service.data["review_template"] = 9999

        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_missing_template(self):
        self.service.data.pop("review_template")

        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_assignees_and_template(self):
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]

        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_assignees(self):
        self.service.data.pop("review_template")
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]

        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestGPVServiceMarkClose(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = GeneralPatientVisitorFactory(created_by=self.super_user)
        self.service = GPVActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "action": "mark-closed",
            },
        )

    def test_mark_close_success(self):
        response = self.service.mark_closed()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
