import factory
from factory.django import DjangoModelFactory
from factory import SubFactory, Iterator, Sequence, LazyFunction
from django.utils import timezone
from accounts.tests.factory import UserProfileFactory
from base.tests.factory import (
    FacilityFactory,
    DepartmentFactory,
    DocumentFactory,
    ReviewFactory,
)


class GeneralPatientVisitorFactory(DjangoModelFactory):
    class Meta:
        model = "general_patient_visitor.GeneralPatientVisitor"

    status = Iterator(
        [
            "Draft",
            "Open",
            "Pending Assigned",
            "Pending Review",
            "Pending Approval",
            "Resolved",
        ]
    )
    report_facility = SubFactory(FacilityFactory)
    department = SubFactory(DepartmentFactory)
    current_step = Sequence(lambda n: n + 1)
    category = Iterator(["Outpatient", "Inpatient"])
    is_resolved = factory.Faker("boolean")
    patient_visitor = SubFactory(UserProfileFactory)
    medical_record_number = Sequence(lambda n: f"MRN{n:06d}")
    incident_date = LazyFunction(lambda: timezone.now().date())
    incident_time = LazyFunction(lambda: timezone.now().time())
    description = factory.Faker("text", max_nb_chars=1000)

    location = factory.Faker("city")
    consulting_diagnosis = factory.Faker("sentence")
    patient_status_prior = factory.Faker("sentence")
    medicine = factory.Faker("word")
    other_status = factory.Faker("word")
    incident_type = Iterator(
        [
            "Workplace Violence",
            "Adverse Drug Reaction",
            "Health Investigation",
            "Staff Incident Report",
            "Grievance Investigation",
        ]
    )

    fell_from = factory.Faker("word")
    fall_related_type = factory.Faker("text", max_nb_chars=100)
    morse_fall_score = factory.Faker("text", max_nb_chars=10)
    fall_type_other = factory.Faker("text", max_nb_chars=100)
    fall_type_other_text = factory.Faker("text", max_nb_chars=100)
    fall_type_agreement = factory.Faker("text", max_nb_chars=1000)

    treatment_type = factory.Faker("text", max_nb_chars=100)

    equipment_type = factory.Faker("word")
    equipment_malfunction = factory.Faker("text", max_nb_chars=1000)
    removed_from_service = factory.Faker("text", max_nb_chars=1000)
    engineering_staff_notified = factory.Faker("text", max_nb_chars=1000)
    equipment_serial_number = factory.Faker("text", max_nb_chars=1000)
    equipment_lot_number = factory.Faker("text", max_nb_chars=1000)
    equipment_manufacturer = factory.Faker("text", max_nb_chars=1000)
    equipment_model = factory.Faker("text", max_nb_chars=1000)

    other_type_specimen_other = factory.Faker("text", max_nb_chars=1000)

    outcome = factory.Faker("sentence")
    preventable = factory.Faker("word")
    severity_rating = factory.Faker("word")
    reason_for_escalation = factory.Faker("text", max_nb_chars=1000)

    physician_notified = SubFactory(UserProfileFactory)
    date_physician_notified = LazyFunction(lambda: timezone.now())
    time_physician_notified = LazyFunction(lambda: timezone.now().time())

    family_notified = SubFactory(UserProfileFactory)
    date_family_notified = LazyFunction(lambda: timezone.now())
    time_family_notified = LazyFunction(lambda: timezone.now().time())

    notified_by = SubFactory(UserProfileFactory)

    immediate_action_taken = factory.Faker("text", max_nb_chars=1000)
    outcome_actions_taken = factory.Faker("text", max_nb_chars=1000)
    brief_summary_of_incident = factory.Faker("text", max_nb_chars=1000)
    response = factory.Faker("word")

    @factory.post_generation
    def reviews(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for review in extracted:
                self.reviews.add(review)
        else:
            self.reviews.add(ReviewFactory())

    @factory.post_generation
    def documents(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for doc in extracted:
                self.documents.add(doc)
        else:
            self.documents.add(DocumentFactory())


class GeneralPatientVisitorVersionFactory(GeneralPatientVisitorFactory):
    class Meta:
        model = "general_patient_visitor.GeneralPatientVisitorVersion"

    original_report = SubFactory(GeneralPatientVisitorFactory)
