from base.tests.base_setup import BaseTestSetup
from base.tests.factory import GeneralPatientVisitorFactory, UserFactory
from reviews.tests.factory import ReviewFactory


class TestGetIncidentReviews(BaseTestSetup):
    """Test getting incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    # cases for general patient visitor incident reviews
    def test_get_general_patient_visitor_reviews(self):
        """Test getting reviews for a general patient visitor incident"""
        incident = GeneralPatientVisitorFactory()
        # clear any existing reviews
        incident.reviews.clear()
        reviews = ReviewFactory.create_batch(2, created_by=self.user)

        for review in reviews:
            incident.reviews.add(review)
        self._authenticate_user(self.user)
        response = self.client.get(
            f"/api/incidents/general-visitor/{incident.id}/reviews/",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to get reviews: {response.data}")
        # check number of reviews returned
        self.assertEqual(len(response.data), 2)


class TestCreateIncidentReview(BaseTestSetup):
    """Test creating incident reviews"""

    def setUp(self):
        super().setUp()
        self.user = UserFactory()

    # cases for general patient visitor incident reviews
    def test_create_general_patient_visitor_review(self):
        """Test creating a review for a general patient visitor incident"""
        incident = GeneralPatientVisitorFactory()
        # clear any existing reviews
        incident.reviews.clear()
        self._authenticate_user(self.user)
        response = self.client.post(
            f"/api/incidents/general-visitor/{incident.id}/reviews/",
            {"content": "This is a test review"},
        )

        if not response.status_code == 201:
            self.fail(f"Failed to create review: {response.data}")
        # check that the review was created
        self.assertEqual(incident.reviews.first().content, "This is a test review")
