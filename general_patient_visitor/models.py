from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from accounts.models import UserProfile
from base.models import BaseModel, Department, Facility
from base.services.permissions.mixins import IncidentsPermissionsMixin
from documents.models import Document
from reviews.models import Review
from tasks.models import ReviewProcess, ReviewProcessTasks


class GeneralPatientVisitorBase(BaseModel):
    INCIDENT_TYPE_CHOICES = [
        ("Workplace Violence", "Workplace Violence"),
        ("Adverse Drug Reaction", "Adverse Drug Reaction"),
        ("Health Investigation", "Health Investigation"),
        ("Staff Incident Report", "Staff Incident Report"),
        ("Grievance Investigation", "Grievance Investigation"),
    ]

    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]
    # Status and Basic Information
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES, max_length=255, default="Draft"
    )

    report_facility = models.ForeignKey(
        Facility, blank=True, null=True, on_delete=models.SET_NULL
    )
    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    category = models.CharField(max_length=50, null=True, blank=True)
    is_resolved = models.BooleanField(default=False)

    # Patient Information
    patient_visitor = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        related_name="%(class)s_name",
    )
    medical_record_number = models.CharField(max_length=100, null=True, blank=True)
    incident_date = models.DateField(null=True, blank=True)
    incident_time = models.TimeField(null=True, blank=True)
    description = models.TextField(max_length=1000, null=True, blank=True)

    # Location and Initial Status
    location = models.CharField(max_length=100, null=True, blank=True)
    consulting_diagnosis = models.CharField(max_length=200, null=True, blank=True)
    patient_status_prior = models.CharField(max_length=500, null=True, blank=True)
    medicine = models.CharField(max_length=100, null=True, blank=True)
    other_status = models.CharField(max_length=100, null=True, blank=True)
    incident_type = models.CharField(max_length=50, null=True, blank=True)

    # Fall-related Information
    fell_from = models.CharField(max_length=50, blank=True, null=True)
    fall_related_type = models.TextField(max_length=100, null=True, blank=True)
    morse_fall_score = models.TextField(max_length=10, null=True, blank=True)
    fall_type_other = models.TextField(max_length=100, null=True, blank=True)
    fall_type_other_text = models.TextField(max_length=100, null=True, blank=True)
    fall_type_agreement = models.TextField(max_length=1000, null=True, blank=True)

    # Treatment Information
    treatment_type = models.TextField(max_length=100, null=True, blank=True)

    # Equipment Information
    equipment_type = models.CharField(max_length=500, blank=True, null=True)
    equipment_malfunction = models.TextField(max_length=1000, null=True, blank=True)
    removed_from_service = models.TextField(max_length=1000, null=True, blank=True)
    engineering_staff_notified = models.TextField(
        max_length=1000, null=True, blank=True
    )
    equipment_serial_number = models.TextField(max_length=1000, null=True, blank=True)
    equipment_lot_number = models.TextField(max_length=1000, null=True, blank=True)
    equipment_manufacturer = models.TextField(max_length=1000, null=True, blank=True)
    equipment_model = models.TextField(max_length=1000, null=True, blank=True)

    # Other Information
    other_type_specimen_other = models.TextField(max_length=1000, null=True, blank=True)

    # Outcome and Notifications
    outcome = models.CharField(max_length=500, null=True, blank=True)
    preventable = models.CharField(max_length=3, null=True, blank=True)
    severity_rating = models.CharField(max_length=500, null=True, blank=True)
    reason_for_escalation = models.TextField(max_length=1000, blank=True, null=True)

    physician_notified = models.ForeignKey(
        UserProfile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_physician_notified",
    )
    date_physician_notified = models.DateTimeField(null=True, blank=True)
    time_physician_notified = models.TimeField(null=True, blank=True)

    family_notified = models.ForeignKey(
        UserProfile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_family_notified",
    )
    date_family_notified = models.DateTimeField(null=True, blank=True)
    time_family_notified = models.TimeField(null=True, blank=True)

    notified_by = models.ForeignKey(
        UserProfile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_notified_by",
    )

    # Actions and Summary
    immediate_action_taken = models.TextField(max_length=1000, null=True, blank=True)
    outcome_actions_taken = models.TextField(max_length=1000, null=True, blank=True)
    brief_summary_of_incident = models.TextField(max_length=1000, null=True, blank=True)
    response = models.CharField(max_length=50, null=True, blank=True)

    # Related Models
    reviews = models.ManyToManyField(
        Review, related_name="%(class)s_reviews", blank=True
    )
    documents = models.ManyToManyField(
        Document,
        related_name="%(class)s_documents",
        blank=True,
    )

    def __str__(self):
        return (
            f"Incident Report: {self.patient_visitor.first_name}"
            if self.patient_visitor
            else "No patient visitor"
        )

    def clean(self):
        if self.incident_date and self.incident_date > timezone.now().date():
            raise ValidationError("Incident date cannot be in the future")

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    class Meta:
        abstract = True
        permissions = IncidentsPermissionsMixin.custom_permissions


class GeneralPatientVisitor(GeneralPatientVisitorBase):
    """General Patient Visitor Report Model"""

    is_modified = models.BooleanField(default=False)
    review_process = models.ForeignKey(
        ReviewProcess,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_review_process",
    )
    review_tasks = models.ManyToManyField(
        ReviewProcessTasks,
        related_name="%(class)s_review_tasks",
        blank=True,
    )


class GeneralPatientVisitorVersion(GeneralPatientVisitorBase):
    original_report = models.ForeignKey(
        GeneralPatientVisitor,
        on_delete=models.CASCADE,
        related_name="versions",
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "general_patient_visitor_version"
