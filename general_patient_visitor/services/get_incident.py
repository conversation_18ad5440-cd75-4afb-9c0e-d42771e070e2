from django.contrib.auth.models import User
from accounts.models import UserProfile
from accounts.repositories.familty_member import PatientFamilyRepository
from accounts.repositories.patient_profile import PatientProfileRepository
from accounts.repositories.profile import ProfileRepository
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.general_incident.incident_list import (
    get_latest_incident_reports,
)
from base.models import Facility
from base.services.auth import generate_email
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from base.services.responses import APIResponse, RepositoryResponse
from general_patient_visitor.models import (
    GeneralPatientVisitor,
)
from general_patient_visitor.repositories.repository import IncidentRepository
from general_patient_visitor.serializers import (
    GeneralPatientVisitorVersionSerializer,
    IncidentListSerializer,
)

from base.services.incidents.get_incidents import GetIncidentsService
from incidents.views.send_to_department import send_incident_submission_email

incidents_service = GetIncidentsService()
logging_service = LoggingService()
patient_repository = PatientProfileRepository()


class GeneralPatientVisitorService:
    """
    Service class for GeneralPatientVisitor model
    """

    def __init__(self):
        self.incident_repository = IncidentRepository()
        self.profile_repository = ProfileRepository()
        self.patient_repository = PatientProfileRepository()
        self.family_member_repository = PatientFamilyRepository()

    def get_incident_by_id(self, user, id) -> APIResponse:

        try:
            incident_data = GeneralPatientVisitor.objects.get(id=id)

            if (
                not is_super_user(user)
                and not is_admin_user(user, incident_data.report_facility)
                and not is_manager_user(user, incident_data.department)
                and not incident_data.created_by == user
            ):
                return APIResponse(
                    success=False,
                    message="You do not have permission to access this incident",
                    data=None,
                    code=403,
                )

            success, incident, modifications, message = (
                incidents_service.get_latest_version(
                    incident_data=incident_data,
                    modelName=GeneralPatientVisitor,
                    incidentSerializer=IncidentListSerializer,
                    incident_id=id,
                )
            )
            if not success:
                return APIResponse(
                    success=False,
                    message=message,
                    data=None,
                    code=404,
                )
            if success:
                return APIResponse(
                    success=True,
                    message="Incident retrieved successfully",
                    data={"incident": incident, "modifications": modifications},
                    code=200,
                )
        except GeneralPatientVisitor.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                data=None,
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_incidents_list(self, user, facility_id=None, department_id=None):
        try:
            incidents_response = incidents_service.get_incidents(
                app_label="general_patient_visitor",
                model_name="GeneralPatientVisitor",
                user=user,
                facility_id=facility_id,
                department_id=department_id,
            )
            if not incidents_response.success:
                return incidents_response
            incidents_found, versions, message = get_latest_incident_reports(
                incidents_response.data, IncidentListSerializer
            )
            if not incidents_found:
                return APIResponse(
                    success=False,
                    message=message,
                    data=None,
                    code=400,
                )
            return APIResponse(
                success=True,
                message="Incidents retrieved successfully",
                data={"incidents": versions},
                code=200,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve incidents",
                data=None,
                code=500,
            )

    def create_incident(self, data: dict, user: User) -> APIResponse:
        request_metadata = data.pop("meta", None)
        try:
            # Process the data before sending to repository
            processed_data = data.copy()
            patient_visitor = None
            physician_notified = None
            family_notified = None
            notified_by = None
            facility = None

            if not "facility_id" or not "report_facility_id" in data:
                return APIResponse(
                    success=False,
                    message="Facility id is required",
                    code=400,
                )
            if "facility_id" in data:
                processed_data.pop("facility_id", None)
                facility_response = self._process_facility(data["facility_id"])
                if not facility_response.success:
                    return APIResponse(
                        success=False,
                        message=facility_response.message,
                        code=400,
                    )
                facility = facility_response.data
                processed_data["report_facility"] = facility

            if "patient_visitor" in processed_data:

                result = self._process_patient_visitor(
                    processed_data["patient_visitor"]
                )
                if not result.success:
                    return APIResponse(
                        success=False,
                        message=result.message,
                        code=400,
                    )
                patient_visitor = result.data
            if "family_notified" in processed_data:
                if not patient_visitor:
                    return APIResponse(
                        success=False,
                        message="Patient or visitor is required in for family member to be recorded",
                        code=400,
                    )
                family_notified_response = self._process_family_member(
                    patient=patient_visitor, data=data.get("family_notified")
                )
                if not family_notified_response.success:
                    return APIResponse(
                        success=False,
                        message=family_notified_response.message,
                        code=400,
                    )
                family_notified = family_notified_response.data

            if "physician_notified" in processed_data:
                physician_notified_response = (
                    self.profile_repository.process_staff_member(
                        data["physician_notified"]
                    )
                )
                if not physician_notified_response.success:
                    return APIResponse(
                        success=False,
                        message=physician_notified_response.message,
                        code=400,
                    )

                physician_notified = physician_notified_response.data

            if "notified_by" in processed_data:
                notified_by_response = self.profile_repository.process_staff_member(
                    data["notified_by"]
                )
                if not notified_by_response.success:
                    return APIResponse(
                        success=False, message=notified_by_response.message, code=400
                    )
                notified_by = notified_by_response.data
            # add related data to the incident data

            if patient_visitor:
                processed_data["patient_visitor"] = patient_visitor

            if physician_notified:
                processed_data["physician_notified"] = physician_notified
            if family_notified:
                processed_data["family_notified"] = family_notified

            if notified_by:
                processed_data["notified_by"] = notified_by

            # add metadata to the incident data
            processed_data["created_by"] = user

            """
            1. Check if incident data has incident_id
            2. if yes, if yes, create a version
            3. if no, if yes, create a new incident
            """

            if "incident_id" in processed_data:

                processed_data["original_report_id"] = processed_data.pop(
                    "incident_id", None
                )
                version = self.incident_repository.create_incident_version(
                    processed_data
                )
                if not version.success:
                    return RepositoryResponse(
                        success=False, message=version.message, data=None
                    )
                serializer = GeneralPatientVisitorVersionSerializer(version.data)
                return RepositoryResponse(
                    success=True,
                    message="Incident updated successfully",
                    data=serializer.data,
                )
            else:
                incident = self.incident_repository.create_incident(processed_data)
                if not incident.success:
                    return APIResponse(
                        success=False,
                        message=incident.message,
                        code=400,
                    )

                # Create notification for new incident
                save_notification(
                    facility=facility,
                    group_name="Quality/Risk Management",
                    notification_type="info",
                    notification_category="incident",
                    message="A new incident is submitted",
                    item_id=incident.data.id,
                )
                serializer = IncidentListSerializer(incident.data)
                return APIResponse(
                    success=True,
                    message="Incident created successfully",
                    data=serializer.data,
                )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the incident",
                errors={"detail": "Internal server error"},
            )

    def update_incident(self, incident_id: int, data: dict, user: User) -> APIResponse:
        request_metadata = data.pop("meta", None)
        try:
            patient_visitor = None
            physician_notified = None
            family_notified = None
            notified_by = None
            # Get existing incident
            incident_result, _ = self.incident_repository.get_incident_by_id(
                incident_id
            )
            if not incident_result.success:
                return APIResponse(
                    success=False, message="Incident not found", code=404
                )

            # Process update data
            processed_data = data.copy()
            processed_data["updated_by"] = user.id

            # Handle related profiles
            related_fields = {
                "patient_visitor": data.get("patient_visitor"),
                "physician_notified": data.get("physician_notified"),
                "family_notified": data.get("family_notified"),
                "notified_by": data.get("notified_by"),
            }

            patient_visitor = None
            if "patient_visitor" in data:
                processed_data.pop("patient_visitor", None)
                response = self._process_patient_visitor(data["patient_visitor"])
                if not response.success:
                    return APIResponse(
                        success=False,
                        message=response.message,
                        data=None,
                        code=400,
                    )
                patient_visitor = response.data

            if "family_notified" in processed_data:
                family_data = data.get("family_notified")
                patient_id = family_data.get("patient_id", None)

                if not patient_id:
                    return APIResponse(
                        success=False,
                        message="Patient id is required in for family member to be recorded",
                        code=400,
                    )
                patient = patient_repository.get_patient_by_id(patient_id)

                if not patient.success:
                    return APIResponse(
                        success=False,
                        message="Patient or visitor is was not found",
                        code=400,
                    )

                family_notified_response = self._process_family_member(
                    patient=patient.data, data=data.get("family_notified")
                )
                if not family_notified_response.success:
                    return APIResponse(
                        success=False,
                        message=family_notified_response.message,
                        code=400,
                    )
                family_notified = family_notified_response.data

            if "physician_notified" in processed_data:
                physician_notified_response = (
                    self.profile_repository.process_staff_member(
                        data["physician_notified"]
                    )
                )
                if not physician_notified_response.success:
                    return APIResponse(
                        success=False,
                        message=physician_notified_response.message,
                        code=400,
                    )

                physician_notified = physician_notified_response.data

            if "notified_by" in processed_data:
                notified_by_response = self.profile_repository.process_staff_member(
                    data["notified_by"]
                )
                if not notified_by_response.success:
                    return APIResponse(
                        success=False, message=notified_by_response.message, code=400
                    )
                notified_by = notified_by_response.data
            # add related data to the incident data
            if patient_visitor:
                processed_data["patient_visitor"] = (
                    patient_visitor.id if patient_visitor else None
                )
            # processed_data["patient_visitor"] = patient_visitor
            if physician_notified:
                processed_data["physician_notified"] = (
                    physician_notified.id if physician_notified else None
                )
            if family_notified:
                processed_data["family_notified"] = (
                    family_notified.id if family_notified else None
                )
            if notified_by:
                processed_data["notified_by"] = notified_by.id if notified_by else None

            # add metadata to the incident data
            result = self.incident_repository.update_incident(
                incident_id, processed_data
            )

            if not result.success:
                return APIResponse(
                    success=False,
                    message=result.message,
                    errors=result.data if result.data else {"detail": result.message},
                )

            # Handle versioning if needed
            if processed_data.get("create_version", False):
                self._create_incident_version(incident_id, result.data)
            return APIResponse(
                success=True,
                message="Incident updated successfully",
                data=result.data,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while updating the incident",
                errors={"detail": "Internal server error"},
            )

    def _process_patient_visitor(self, data):
        patient_data = None

        try:
            """This code was being used to restrict users to create invalid MRN"""
            # if "medical_record_number" in data and data.get(
            #     "medical_record_number", None
            # ):
            #     existing_patient_visitor = self.patient_repository.get_patient_by_mrn(
            #         data["medical_record_number"]
            #     )
            #     if not existing_patient_visitor.success:
            #         return APIResponse(
            #             success=False,
            #             message="Patient not found. Leave MRN field empty to create a new one",
            #             code=404,
            #             data=None,
            #         )
            # patient_update_response = patient_service.update_patient(
            #     existing_patient_visitor.data.id, data
            # )
            # if not patient_update_response.success:
            #     return APIResponse(
            #         success=False,
            #         message=patient_update_response.message,
            #         code=400,
            #         data=None,
            #     )
            # patient_data = existing_patient_visitor.data
            # else:
            mew_patient_visitor = self.patient_repository.create_patient(data)
            if not mew_patient_visitor.success:
                return RepositoryResponse(
                    success=False,
                    message="Failed to create patient",
                    data=None,
                )
            patient_data = mew_patient_visitor.data
            return RepositoryResponse(
                success=True,
                message="Patient created successfully",
                data=patient_data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the patient_visitor",
                errors={"detail": "Internal server error"},
            )

    def _process_facility(self, facility_id):
        try:
            facility = Facility.objects.get(id=facility_id)

            return RepositoryResponse(
                success=True, message="Facility retrieved", data=facility
            )
        except Facility.DoesNotExist:
            return APIResponse(
                success=False,
                message="Facility not found",
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving the facility",
                errors={"detail": "Internal server error"},
            )

    def _process_family_member(self, patient, data) -> RepositoryResponse:
        member = None
        try:
            member = UserProfile.objects.filter(
                patient=patient,
                first_name=data.get("first_name"),
                last_name=data.get("last_name"),
            ).first()
            if not member:
                family_data = data.copy()
                family_data["patient"] = patient
                family_member = self.family_member_repository.create_family_member(
                    family_data
                )
                if not family_member.success:
                    return RepositoryResponse(
                        success=False,
                        message="Failed to create family member",
                        data=None,
                    )
                member = family_member.data
            return RepositoryResponse(
                success=True, message="Family member created successfully", data=member
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while processing family member",
                data=None,
            )

    def create_version(self, incident_id, incident_data, user):
        """
        1. Check if incident exists and get it's id, facility id
        2. Then create a new version of the incident using create method
        3. Modify the incident data to include "original_report", "report_facility", "created_by"
        4. Update the incident with updated_by at and updated_by
        5. Return the new version data
        """

        try:
            incident = GeneralPatientVisitor.objects.get(id=incident_id)

            if (
                not is_super_user(user)
                and not is_admin_user(user, incident.report_facility)
                and not is_manager_user(user, incident.department)
            ) and not incident.created_by == user:
                return APIResponse(
                    success=False,
                    message="You are not authorized to create a version of this incident",
                    code=403,
                )

            incident_data["incident_id"] = incident.id
            incident_data["report_facility_id"] = incident.report_facility.id
            incident_data["created_by_id"] = user.id
            incident_data["status"] = incident_data.get("status", "Draft")

            response = self.create_incident(incident_data, user)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    errors=response.data,
                )

            if "status" in incident_data:
                new_status = incident_data["status"]

                incident.status = new_status
                incident.save()

                if new_status == "Open":
                    send_incident_submission_email(
                        incident=incident, incident_type="General Patient/Visitor"
                    )

            return APIResponse(
                success=True,
                message="Incident version created successfully",
                data=response.data,
            )
        except GeneralPatientVisitor.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the incident version",
                errors={"detail": "Internal server error"},
            )
