from general_patient_visitor.models import GeneralPatientVisitor
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from documents.services.operations import DocumentsOperations
from documents.services.query import DocumentQuery
from activities.services import ActivityLogService
from activities.models import ActivityType

logging_service = LoggingService()


class GPVDocumentService:
    """
    Service class for handling document-related operations.
    """

    def __init__(self, incident_id, user):
        self.incident_id = incident_id
        self.user = user
        self.doc_query_service = DocumentQuery()
        self.doc_operations = DocumentsOperations()

    def get_documents(self, params=None) -> APIResponse:
        """
        Retrieve a document by incident ID.
        """
        try:
            incident = GeneralPatientVisitor.objects.get(id=self.incident_id)

            # add incident to query parameters if provided
            if params is None:
                params = {}
            params["instance"] = incident

            print("params are: ", params)

            # Use the document query service to get documents
            response = self.doc_query_service.get_documents(query_params=params)
            if not response:
                return APIResponse(
                    success=False,
                    message="No documents found for this incident",
                    code=404,
                )
            return APIResponse(
                success=response.success,
                message=response.message,
                data=response.data,
                code=response.code,
            )
        except GeneralPatientVisitor.DoesNotExist:
            return APIResponse(
                success=False,
                message="Document not found",
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error while retrieving document",
                code=500,
            )

    def create_document(self, files) -> APIResponse:
        try:
            incident = GeneralPatientVisitor.objects.get(id=self.incident_id)
            response = self.doc_operations.create_document(
                files=files,
                incident=incident,
                user=self.user,
            )

            if isinstance(response, tuple):
                message, data = response
                return APIResponse(
                    success=False,
                    message=message,
                    data=data,
                    code=400,
                )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=response.data,
                    code=response.code,
                )

            files_data = []
            if response.data and 'files' in response.data:
                for doc in response.data['files']:
                    files_data.append({
                        'document_id': doc['id'],
                        'document_url': doc.get('url', f'/documents/{doc.get("name", "unknown")}'),
                        'name': doc.get('name', 'Unknown'),
                        'file_type': doc.get('file_type', 'unknown')
                    })

            ActivityLogService.create_activity(
                user=self.user,
                content_object=incident,
                activity_type=ActivityType.DOCUMENT_ADDED,
                files=files_data
            )

            return APIResponse(
                success=True,
                message="Document created successfully",
                data=response.data,
                code=201,
            )
        except GeneralPatientVisitor.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to create document",
                data=None,
                code=500,
            )
