from typing import Optional, Tuple, List
from django.contrib.auth.models import User
from accounts.models import Profile
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from general_patient_visitor.serializers import IncidentSerializer
from general_patient_visitor.models import (
    GeneralPatientVisitor,
    GeneralPatientVisitorVersion,
)

logging_service = LoggingService()


class IncidentRepository:
    @staticmethod
    def create_incident(data: dict) -> RepositoryResponse:
        try:

            incident = GeneralPatientVisitor.objects.create(**data)
            return RepositoryResponse(
                success=True,
                message="Incident created successfully",
                data=incident,
            )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def create_incident_version(data: dict) -> RepositoryResponse:
        try:
            version = GeneralPatientVisitorVersion.objects.create(**data)
            return RepositoryResponse(
                success=True,
                message="Incident version created successfully",
                data=version,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error creating Incident version",
                data=None,
            )

    @staticmethod
    def get_incident_by_id(
        incident_id: int,
    ) -> Tuple[RepositoryResponse, Optional[IncidentSerializer]]:
        try:
            incident = GeneralPatientVisitor.objects.get(id=incident_id)
            serializer = IncidentSerializer(incident)
            return RepositoryResponse(success=True, data=serializer.data), serializer
        except GeneralPatientVisitor.DoesNotExist:
            return RepositoryResponse(success=False, message="Incident not found"), None
        except Exception as e:
            logging_service.log_error(e)
            return (
                RepositoryResponse(success=False, message="Internal server error"),
                None,
            )

    @staticmethod
    def update_incident(incident_id: int, data: dict) -> RepositoryResponse:
        try:
            incident = GeneralPatientVisitor.objects.get(id=incident_id)
            serializer = IncidentSerializer(incident, data=data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return RepositoryResponse(
                    success=True,
                    message="Incident updated successfully",
                    data=serializer.data,
                )
            else:
                for error in serializer.errors:
                    logging_service.log_error(error)
                    
                return RepositoryResponse(
                    success=True,
                    message="Validation failed",
                    data=serializer.errors,
                )
        except GeneralPatientVisitor.DoesNotExist:
            return RepositoryResponse(success=False, message="Incident not found")
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def get_incidents_by_user_id(user_id: int) -> RepositoryResponse:
        try:
            incidents = GeneralPatientVisitor.objects.filter(created_by=user_id)
            serializer = IncidentSerializer(incidents, many=True)
            return RepositoryResponse(
                success=True,
                message="Incidents retrieved successfully",
                data=serializer.data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def delete_incident(incident_id: int) -> RepositoryResponse:
        try:
            incident = GeneralPatientVisitor.objects.get(id=incident_id)
            incident.delete()
            return RepositoryResponse(
                success=True,
                message="Incident deleted successfully",
            )
        except GeneralPatientVisitor.DoesNotExist:
            return RepositoryResponse(success=False, message="Incident not found")
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(success=False, message="Internal server error")
