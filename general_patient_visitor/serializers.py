# serializers.py

from rest_framework import serializers
from accounts.serializers import (
    GetProfileSerializer,
    ProfileSerializer,
    UserProfileSerializer,
    UserSerializer,
)
from base.models import GeneralWitness
from base.serializers import DepartmentSerializer
from documents.models import Document
from base.models import Facility

from documents.serializers import DocumentSerializer
from general_patient_visitor.models import (
    GeneralPatientVisitor,
    GeneralPatientVisitorVersion,
)
from reviews.models import Review


class IncidentSerializer(serializers.ModelSerializer):

    reviews = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Review.objects.all(), required=False
    )
    documents = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Document.objects.all(), required=False
    )

    class Meta:
        model = GeneralPatientVisitor
        fields = "__all__"


class FacilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = Facility
        fields = ["id", "name"]


class IncidentListSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    reviews = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Review.objects.all(), required=False
    )
    documents = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Document.objects.all(), required=False
    )
    report_facility = FacilitySerializer()
    patient_visitor = UserProfileSerializer()
    physician_notified = UserProfileSerializer()
    family_notified = UserProfileSerializer()
    notified_by = UserProfileSerializer()

    class Meta:
        model = GeneralPatientVisitor
        fields = "__all__"


class IncidentUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = GeneralPatientVisitor
        fields = "__all__"


class GeneralPatientVisitorVersionSerializer(serializers.ModelSerializer):
    reviews = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Review.objects.all(), required=False
    )
    documents = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Document.objects.all(), required=False
    )

    class Meta:
        model = GeneralPatientVisitorVersion
        fields = "__all__"


class GeneralPatientVisitorUpdateVersionSerializer(serializers.ModelSerializer):
    class Meta:
        model = GeneralPatientVisitorVersion
        fields = "__all__"


class WitnessSerializer(serializers.ModelSerializer):
    class Meta:
        model = GeneralWitness
        fields = "__all__"


class NewGeneralPatientVisitorSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)
    assignees = UserSerializer(many=True, read_only=True)
    patient_visitor = UserProfileSerializer(read_only=True)
    physician_notified = UserProfileSerializer(read_only=True)
    family_notified = UserProfileSerializer(read_only=True)
    notified_by = UserProfileSerializer(read_only=True)
    report_facility = FacilitySerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)
    # reviews = ReviewSerializer(many=True, read_only=True)
    documents = DocumentSerializer(many=True, read_only=True)

    class Meta:
        model = GeneralPatientVisitor
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at")

    def create(self, validated_data):
        request = self.context.get("request")
        if request and request.user:
            validated_data["created_by"] = request.user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        request = self.context.get("request")
        if request and request.user:
            validated_data["updated_by"] = request.user
            validated_data["is_modified"] = True
        return super().update(instance, validated_data)
