# models.py

from django.db import models
from accounts.models import PatientFamilyProfile, PatientProfile, Profile
from base.models import BaseModel, Department, Facility
from documents.models import Document

# from base.models import Facility

from reviews.models import Review
from django.core.exceptions import ValidationError
from django.utils import timezone


# we need to add search indexes to optimize performance during searching and querying
class GeneralPatientVisitorBase(BaseModel):

    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]

    INCIDENT_TYPE_CHOICES = [
        ("Workplace Violence", "Workplace Violence"),
        ("Adverse Drug Reaction", "Adverse Drug Reaction"),
        ("Health Investigation", "Health Investigation"),
        ("Staff Incident Report", "Staff Incident Report"),
        ("Grievance Investigation", "Grievance Investigation"),
    ]

    # General fields
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES,
        max_length=255,
        default="Draft",
    )
    report_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    category = models.CharField(max_length=50, null=True, blank=True)
    patient_visitor = models.ForeignKey(
        PatientProfile,
        on_delete=models.SET_NULL,
        null=True,
        related_name="%(class)s_name",
    )
    incident_date = models.DateField(null=True, blank=True)
    incident_time = models.TimeField(null=True, blank=True)
    medical_record_number = models.CharField(max_length=100, null=True, blank=True)
    description = models.TextField(max_length=1000, null=True, blank=True)

    # Fields for updating incident location and status
    location = models.CharField(max_length=100, null=True, blank=True)
    consulting_diagnosis = models.CharField(max_length=200, null=True, blank=True)
    patient_status_prior = models.CharField(max_length=500, null=True, blank=True)
    medicine = models.CharField(max_length=100, null=True, blank=True)
    other_status = models.CharField(max_length=100, null=True, blank=True)

    # Incident type fields
    incident_type = models.CharField(max_length=50, null=True, blank=True)

    # Fall-related fields
    fell_from = models.CharField(max_length=50, blank=True, null=True)
    fall_related_type = models.TextField(max_length=100, null=True, blank=True)
    morse_fall_score = models.TextField(max_length=10, null=True, blank=True)
    fall_type_other = models.TextField(max_length=100, null=True, blank=True)
    fall_type_other_text = models.TextField(max_length=100, null=True, blank=True)
    fall_type_agreement = models.TextField(max_length=1000, null=True, blank=True)

    # Treatment-related fields
    treatment_type = models.TextField(max_length=100, null=True, blank=True)

    # Equipment-related fields
    equipment_type = models.CharField(max_length=500, blank=True, null=True)
    equipment_malfunction = models.TextField(max_length=1000, null=True, blank=True)
    removed_from_service = models.TextField(max_length=1000, null=True, blank=True)
    engineering_staff_notified = models.TextField(
        max_length=1000, null=True, blank=True
    )
    equipment_serial_number = models.TextField(max_length=1000, null=True, blank=True)
    equipment_lot_number = models.TextField(max_length=1000, null=True, blank=True)
    equipment_manufacturer = models.TextField(max_length=1000, null=True, blank=True)
    equipment_model = models.TextField(max_length=1000, null=True, blank=True)

    # Other type fields
    other_type_specimen_other = models.TextField(max_length=1000, null=True, blank=True)

    # Fields for updating incident outcome
    outcome = models.CharField(max_length=500, null=True, blank=True)
    preventable = models.CharField(max_length=3, null=True, blank=True)
    severity_rating = models.CharField(max_length=500, null=True, blank=True)
    reason_for_escalation = models.TextField(max_length=1000, blank=True, null=True)
    physician_notified = models.ForeignKey(
        Profile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_physician_notified",
    )
    date_physician_notified = models.DateTimeField(null=True, blank=True)
    time_physician_notified = models.TimeField(null=True, blank=True)
    family_notified = models.ForeignKey(
        PatientFamilyProfile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_family_notified",
    )
    date_family_notified = models.DateTimeField(null=True, blank=True)
    time_family_notified = models.TimeField(null=True, blank=True)
    notified_by = models.ForeignKey(
        Profile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_notified_by",
    )
    immediate_action_taken = models.TextField(max_length=1000, null=True, blank=True)
    outcome_actions_taken = models.TextField(max_length=1000, null=True, blank=True)
    brief_summary_of_incident = models.TextField(max_length=1000, null=True, blank=True)
    response = models.CharField(max_length=50, null=True, blank=True)
    reviews = models.ManyToManyField(
        Review,
        related_name="%(class)s_reviews",
    )

    is_resolved = models.BooleanField(default=False)

    documents = models.ManyToManyField(
        Document,
        related_name="%(class)s_documents",
    )

    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )

    def __str__(self):
        return (
            f"Incident Report: {self.patient_visitor.first_name}"
            if self.patient_visitor
            else "No patient visitor"
        )

    def clean(self):
        if self.incident_date and self.incident_date > timezone.now().date():
            raise ValidationError("Incident date cannot be in the future")

    def save(self, *args, **kwargs):

        # self.full_clean()
        super().save(*args, **kwargs)

    class Meta:
        abstract = True


class GeneralPatientVisitor(GeneralPatientVisitorBase):
    is_modified = models.BooleanField(default=False)


class GeneralPatientVisitorVersion(GeneralPatientVisitorBase):
    original_report = models.ForeignKey(
        GeneralPatientVisitor,
        on_delete=models.CASCADE,
        related_name="versions",
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "general_patient_visitor_version"
