from base.serializers import BaseModelSerializer
from general_patient_visitor.models import GeneralPatientVisitor
from rest_framework import serializers


class GetGeneralPatientVisitorSerializer(BaseModelSerializer):
    report_facility = serializers.SerializerMethodField()
    department = serializers.SerializerMethodField()
    patient_visitor = serializers.SerializerMethodField()
    physician_notified = serializers.SerializerMethodField()
    family_notified = serializers.SerializerMethodField()
    notified_by = serializers.SerializerMethodField()

    class Meta:
        model = GeneralPatientVisitor
        fields = "__all__"

    def get_report_facility(self, obj):
        if obj.report_facility:
            return {"id": obj.report_facility.id, "name": obj.report_facility.name}
        return None

    def get_department(self, obj):
        if obj.department:
            return {"id": obj.department.id, "name": obj.department.name}
        return None

    def get_patient_visitor(self, obj):
        if obj.patient_visitor:
            return {
                "id": obj.patient_visitor.id,
                "first_name": obj.patient_visitor.first_name,
                "last_name": obj.patient_visitor.last_name,
                "email": obj.patient_visitor.email,
                "medical_record_number": obj.patient_visitor.medical_record_number,
            }
        return None

    def get_physician_notified(self, obj):
        if obj.physician_notified:
            return {
                "id": obj.physician_notified.id,
                "first_name": obj.physician_notified.first_name,
                "last_name": obj.physician_notified.last_name,
                "email": obj.physician_notified.email,
            }
        return None

    def get_family_notified(self, obj):
        if obj.family_notified:
            return {
                "id": obj.family_notified.id,
                "first_name": obj.family_notified.first_name,
                "last_name": obj.family_notified.last_name,
                "email": obj.family_notified.email,
            }
        return None

    def get_notified_by(self, obj):
        if obj.notified_by:
            return {
                "id": obj.notified_by.id,
                "first_name": obj.notified_by.first_name,
                "last_name": obj.notified_by.last_name,
                "email": obj.notified_by.email,
            }
        return None
