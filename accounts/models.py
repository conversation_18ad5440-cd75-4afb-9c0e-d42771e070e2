import uuid
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

from base.models import BaseModel
from base.services.permissions.mixins import (
    BasePermissionsMixin,
    IncidentsPermissionsMixin,
)


class PasswordResetCode(models.Model):
    email = models.EmailField()
    code = models.CharField(max_length=8)
    expires_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return self.email + self.code


class AddressMixin(models.Model):
    # Contact Information
    address = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=255, null=True, blank=True)
    state = models.CharField(max_length=255, null=True, blank=True)
    zip_code = models.CharField(max_length=20, null=True, blank=True)
    phone_number = models.Char<PERSON>ield(max_length=20, null=True, blank=True)

    # Personal Information
    birth_country = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    gender = models.CharField(max_length=200, null=True, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    age = models.IntegerField(null=True, blank=True)

    class Meta:
        abstract = True


class Title(models.Model):
    name = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="title_created_by"
    )
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="title_updated_by"
    )

    def __str__(self):
        return f"{self.name}"

    class Meta:
        permissions = IncidentsPermissionsMixin.custom_permissions


class Profile(
    AddressMixin, models.Model
):  # for recording information about the users who will access the system
    # permission level choices
    PERMISSION_LEVELS_CHOICES = [
        ("Corporate", "Corporate"),
        ("Facility", "Facility"),
        ("Department", "Department"),
    ]

    # profile types
    # User Information
    user = models.OneToOneField(User, on_delete=models.SET_NULL, blank=True, null=True)
    cohesive_user_id = models.UUIDField(
        editable=False,
        null=True,
        blank=True,
    )

    # Authentication
    totp = models.CharField(max_length=1000, blank=True, null=True)
    mfa_enabled = models.BooleanField(default=False)
    is_test_account = models.BooleanField(default=False)

    # Organization Access
    access_to_department = models.ManyToManyField(
        "base.Department", related_name="access_to_departments"
    )
    access_to_facilities = models.ManyToManyField(
        "base.Facility", related_name="access_to_facilities"
    )
    facility = models.ForeignKey(
        "base.Facility",
        blank=True,
        null=True,
        related_name="profile_facility",
        on_delete=models.SET_NULL,
    )
    department = models.ForeignKey(
        "base.Department",
        blank=True,
        null=True,
        related_name="profile_department",
        on_delete=models.SET_NULL,
    )
    # the following is comment out because the client does not want to use it for now
    # permission_level = models.CharField(
    #     max_length=255,
    #     default="Department",
    #     blank=True,
    #     null=True,
    #     choices=PERMISSION_LEVELS_CHOICES,
    # )
    review_permissions = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="profile_created_by"
    )
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="profile_updated_by"
    )
    title = models.ForeignKey(
        Title,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="profile_title",
    )

    def soft_delete(self):
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def restore(self):
        self.is_deleted = False
        self.deleted_at = None
        self.save()

    def __str__(self):
        return (
            f"{self.user.first_name} {self.user.last_name}" if self.user else "No user"
        )

    class Meta:
        permissions = BasePermissionsMixin.custom_permissions


class UserProfile(
    AddressMixin, models.Model
):  # for recording information about the users who will not access the system
    # profile types
    PROFILE_TYPE_CHOICES = [
        ("Staff", "Staff"),
        ("Supervisor", "Supervisor"),
        ("Patient", "Patient"),
        ("Family", "Family"),
        ("Physician", "Physician"),
        ("Visitor", "Visitor"),
        ("Assailant", "Assailant"),
        ("Victim", "Victim"),
        ("Witness", "Witness"),
        ("Involved Party", "Involved Party"),
        ("Observer", "Observer"),
        ("Inpatient", "Inpatient"),
        ("Outpatient", "Outpatient"),
        ("ER", "ER"),
    ]
    # User Information
    profile_type = models.CharField(
        max_length=255,
        default="Staff",
        blank=True,
        null=True,
        choices=PROFILE_TYPE_CHOICES,
    )
    first_name = models.CharField(max_length=266, db_index=True)
    last_name = models.CharField(max_length=266, db_index=True)
    middle_name = models.CharField(max_length=266, null=True, blank=True)
    email = models.EmailField(max_length=255, null=True, blank=True)
    medical_record_number = models.CharField(max_length=255, null=True, blank=True)
    patient = models.ForeignKey("self", on_delete=models.PROTECT, null=True, blank=True)
    relationship_to_patient = models.CharField(max_length=255, null=True, blank=True)
    sex = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return f"{self.first_name} - {self.first_name}"

    class Meta:
        permissions = BasePermissionsMixin.custom_permissions


class UserPreference(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    user_timezone = models.CharField(max_length=3000, blank=True, null=True)
