from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.serializers import ProfileSerializer
from accounts.services.permisions import AccountsPermissionsService
from accounts.services.profile.services import ProfileService
from base.services.logging.logger import LoggingService

logging_service = LoggingService()


from rest_framework import status
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.response import Response
from django.contrib.auth import authenticate

from accounts.services.auth_srvices import AuthService
from api.views.auth.login import user_details
from base.services.logging.logger import LoggingService
from base.services.permissions.security import SecurityService

from base.services.permissions.permissions import APIKeyAuthentication


encryption_service = SecurityService()
logger = LoggingService()


# login a user using email and password
@api_view(["POST"])
@permission_classes([])
def login_email_password(request):
    auth_service = AuthService()
    try:

        response = auth_service.login_email_password(request.data)
        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            response.data,
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        logger.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@authentication_classes([APIKeyAuthentication])
def check_access(request):
    auth_service = AuthService()
    try:
        response = auth_service.check_access(request.data)
        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            response.data,
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        logger.log_error(e)
        return Response(
            {"message": "Error authenticating with backend"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST", "GET"])
# @permission_classes([IsAuthenticated])
def profiles_api_view(request):

    profile_service = ProfileService(user=request.user)
    account_perms = AccountsPermissionsService(request.user)
    try:
        if request.method == "GET":
            # check permissions
            can_list_accounts = account_perms.can_list_accounts()
            if not can_list_accounts.success:
                return Response(
                    {
                        "error": can_list_accounts.message,
                        "data": can_list_accounts.data,
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )
            facility = request.query_params.get("facility", None)
            department = request.query_params.get("department", None)
            page = request.query_params.get("page", 1)
            page_size = request.query_params.get("page_size", 10)
            q = request.query_params.get("q", None)
            permissions = request.query_params.get("permissions", None)
            sort_by = request.query_params.get("sort_by", None)
            sort_order = request.query_params.get("sort_order", None)

            params = {
                "facility": facility,
                "department": department,
                "page": page,
                "page_size": page_size,
                "q": q,
                "permissions": permissions,
                "sort_by": sort_by,
                "sort_order": sort_order,
            }

            profiles = profile_service.get_profiles(params)
            if not profiles.success:
                return Response(
                    {"error": profiles.message},
                    status=profiles.code,
                )
            return Response(profiles.data, status=status.HTTP_200_OK)

        elif request.method == "POST":
            print("I am here")
            can_create_account = account_perms.can_create_account()
            if not can_create_account.success:
                return Response(
                    {
                        "error": can_create_account.message,
                        "data": can_create_account.data,
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )
            profile = profile_service.new_profile(request.data)
            if not profile.success:
                return Response(
                    {"error": profile.message},
                    status=profile.code,
                )
            return Response(
                profile.data,
                status=status.HTTP_201_CREATED,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "PATCH", "DELETE"])
@permission_classes([IsAuthenticated])
def get_user_details_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    account_perms = AccountsPermissionsService(request.user)
    try:
        # get user details
        if request.method == "GET":
            can_view_account = account_perms.can_view_account(profile_id)
            if not can_view_account.success:
                return Response(
                    {"error": can_view_account.message},
                    status=status.HTTP_403_FORBIDDEN,
                )

            user_profile = profile_service.get_user_profile(profile_id)
            if not user_profile.success:
                return Response(
                    {"error": user_profile.message},
                    status=user_profile.code,
                )
            return Response(user_profile.data, status=status.HTTP_200_OK)

        # update user details
        elif request.method == "PUT":
            can_edit_account = account_perms.can_update_account(
                profile_id, request.data
            )
            if not can_edit_account.success:
                return Response(
                    {"error": can_edit_account.message},
                    status=status.HTTP_403_FORBIDDEN,
                )
            user_profile = profile_service.update_user_profile(profile_id, request.data)

            if not user_profile.success:
                return Response(
                    {"error": user_profile.message},
                    status=user_profile.code,
                )
            return Response(user_profile.data, status=status.HTTP_200_OK)

        # delete profile
        elif request.method == "DELETE":
            can_delete_account = account_perms.can_delete_account(profile_id)
            if not can_delete_account.success:
                return Response(
                    {"error": can_delete_account.message},
                    status=status.HTTP_403_FORBIDDEN,
                )
            user_profile = profile_service.delete_profile(profile_id)
            if not user_profile.success:
                return Response({"error": user_profile.message}, user_profile.code)
            return Response(status=status.HTTP_204_NO_CONTENT)

        # actions
        if request.method == "PATCH":
            can_edit_account = account_perms.can_update_account(
                profile_id, request.data
            )
            if not can_edit_account.success:
                return Response(
                    {"error": can_edit_account.message},
                    status=status.HTTP_403_FORBIDDEN,
                )
            action = request.data.get("action")

            if action == "activate":
                user_profile = profile_service.activate_profile(profile_id)

            elif action == "deactivate":
                user_profile = profile_service.deactivate_profile(profile_id)

            else:
                return Response(
                    {"error": "Invalid action"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not user_profile.success:
                return Response(
                    {"error": user_profile.message},
                    status=user_profile.code,
                )
            return Response(user_profile.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST", "DELETE"])
@permission_classes([IsAuthenticated])
def get_user_permissions_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    if request.method == "POST":
        try:
            permissions = profile_service.add_permissions(profile_id, request.data)
            if not permissions.success:
                return Response({"error": permissions.message}, status=permissions.code)
            return Response(permissions.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    elif request.method == "GET":

        try:
            permissions = profile_service.get_user_permissions(request.user, profile_id)

            if not permissions.success:
                return Response({"error": permissions.message}, status=permissions.code)
            return Response(permissions.data, status=status.HTTP_200_OK)

        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    elif request.method == "DELETE":

        try:
            permissions = profile_service.remove_permissions(profile_id, request.data)
            if not permissions.success:
                return Response({"error": permissions.message}, status=permissions.code)
            return Response(permissions.data, status=status.HTTP_200_OK)

        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    else:
        return Response(
            {"error": "Invalid HTTP method"}, status=status.HTTP_405_METHOD_NOT_ALLOWED
        )


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_user_details_api_view(request, user_id):
    profile_service = ProfileService(user=request.user)
    try:

        user_profile = profile_service.update_user_profile(request.user, user_id)
        serializer = ProfileSerializer(user_profile, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
def delete_profile_api_view(request, user_id):
    profile_service = ProfileService(user=request.user)
    try:
        user_profile = profile_service.delete_profile(request.user, user_id)
        user_profile.delete()
        return Response(
            {"message": "Profile deleted successfully"}, status=status.HTTP_200_OK
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_incidents_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    try:
        filters = request.query_params
        incidents = profile_service.get_user_incidents(
            profile_id, filters=filters, logged_in_user=request.user
        )
        if not incidents.success:
            return Response({"error": incidents.message}, status=incidents.code)
        return Response(incidents.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_documents_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    try:
        documents = profile_service.get_user_documents(profile_id)
        if not documents.success:
            return Response({"error": documents.message}, status=documents.code)
        return Response(documents.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_complaints_list_api_view(request, profile_id):
    profile_service = ProfileService(user=request.user)
    try:
        complaints = profile_service.get_user_complaints(profile_id)
        if not complaints.success:
            return Response({"error": complaints.message}, status=complaints.code)
        return Response(complaints.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "DELETE"])
@permission_classes([IsAuthenticated])
def user_drafts_api_view(request):
    """
    Handle user draft operations:
    GET: Retrieve all drafts for the authenticated user
    DELETE: Delete multiple drafts for the authenticated user
    """
    profile_service = ProfileService(user=request.user)

    try:
        if request.method == "GET":
            """Get user_id from query params (optional, defaults to current user)"""
            user_id = request.query_params.get("user_id", None)
            if user_id:
                try:
                    user_id = int(user_id)
                except ValueError:
                    return Response(
                        {"error": "Invalid user_id parameter"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            drafts = profile_service.get_user_drafts(user_id)
            if not drafts.success:
                return Response(
                    {"error": drafts.message},
                    status=drafts.code,
                )
            return Response(drafts.data, status=status.HTTP_200_OK)

        elif request.method == "DELETE":
            if not request.data:
                return Response(
                    {"error": "Request body is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            result = profile_service.delete_user_drafts(request.data)
            if not result.success:
                return Response(
                    {"error": result.message, "details": result.data},
                    status=result.code,
                )

            return Response(
                {"message": result.message, "data": result.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
