from typing import Optional, List
from accounts.models import Profile
from base.services.auth import generate_email
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from django.contrib.auth.models import User

from base.validadors.models import validate_model_data


logging_service = LoggingService()


class ProfileRepository:
    def create_profile(self, user, data: dict) -> RepositoryResponse:
        try:
            is_valid = validate_model_data(Profile, data)
            if not is_valid.success:
                return RepositoryResponse(
                    success=False,
                    message=is_valid.message,
                    data=None,
                )
            # Extract M2M fields
            departments = data.pop("access_to_department", [])
            facilities = data.pop("access_to_facilities", [])

            # Create profile
            profile = Profile.objects.create(**data)
            profile.user = user
            profile.save()
            # Add M2M relationships if provided

            if departments:
                profile.access_to_department.set(departments)
            if facilities:
                profile.access_to_facilities.set(facilities)
            return RepositoryResponse(
                success=True, message="Profile created successfully", data=profile
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(success=False, message="Internal server error")

    def process_staff_member(self, data):
        try:
            if isinstance(data, list):
                if len(data) > 0:
                    data = data[0]
                else:
                    return RepositoryResponse(
                        success=False,
                        message="Invalid data format - empty list provided",
                        data=None,
                    )
            user_obj = None
            profile = None

            if "email" not in data:
                email = generate_email(
                    first_name=data.get("first_name"), last_name=data.get("last_name")
                )
            else:
                email = data.get("email")

            user_obj = User.objects.filter(email=email).first()

            if not user_obj:
                try:
                    user_obj = User.objects.create_user(
                        username=email,
                        email=email,
                        first_name=data.get("first_name"),
                        last_name=data.get("last_name"),
                    )
                except Exception as e:
                    logging_service.log_error(e)
                    return RepositoryResponse(
                        success=False,
                        message="Failed to create user for the physician notified",
                        data=None,
                    )

            profile_data = {
                "phone_number": data.get("phone_number"),
                "gender": data.get("gender"),
                "date_of_birth": data.get("date_of_birth"),
                "address": data.get("address"),
                "birth_country": data.get("birth_country"),
                "city": data.get("city"),
                "state": data.get("state"),
                "zip_code": data.get("zip_code"),
            }
            profile = None
            existing_profile = self.get_profile_by_user_id(user_obj.id)

            if existing_profile.success:
                profile = existing_profile.data
            else:
                profile_response = self.create_profile(user_obj, profile_data)
                if not profile_response.success:
                    return RepositoryResponse(
                        success=False,
                        message=profile_response.message,
                        data=None,
                    )

                profile = profile_response.data
            return RepositoryResponse(
                success=True,
                message="Physician notified created successfully",
                data=profile,
            )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error processing physician notified",
                data=None,
            )

    @staticmethod
    def get_profile_by_id(profile_id: int) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            return RepositoryResponse(success=True, data=profile)
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def get_profile_by_user_id(user_id: int) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(user_id=user_id)
            return RepositoryResponse(success=True, data=profile)
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def update_profile(profile_id: int, data: dict) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(id=profile_id)

            # Handle M2M fields separately
            departments = data.pop("access_to_department", None)
            facilities = data.pop("access_to_facilities", None)

            # Update regular fields
            for key, value in data.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)

            # Update M2M relationships if provided
            if departments is not None:
                profile.access_to_department.set(departments)
            if facilities is not None:
                profile.access_to_facilities.set(facilities)

            profile.save()
            return RepositoryResponse(
                success=True, message="Profile updated successfully", data=profile
            )
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def delete_profile(profile_id: int) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            profile.delete()
            return RepositoryResponse(
                success=True, message="Profile deleted successfully"
            )
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def get_profiles_list(
        filters: Optional[dict] = None,
        order_by: str = "-id",
        page: int = 1,
        page_size: int = 10,
    ) -> RepositoryResponse:
        try:
            queryset = Profile.objects.all()

            # Apply filters
            if filters:
                queryset = queryset.filter(**filters)

            # Apply ordering
            if order_by:
                queryset = queryset.order_by(order_by)

            # Apply pagination
            total_count = queryset.count()
            start = (page - 1) * page_size
            end = start + page_size
            queryset = queryset[start:end]

            return RepositoryResponse(
                success=True,
                data={
                    "items": list(queryset),
                    "total": total_count,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total_count + page_size - 1) // page_size,
                },
            )
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def add_facility_access(profile_id: int, facility_id: int) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            profile.access_to_facilities.add(facility_id)
            return RepositoryResponse(
                success=True, message="Facility access added successfully"
            )
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def remove_facility_access(profile_id: int, facility_id: int) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            profile.access_to_facilities.remove(facility_id)
            return RepositoryResponse(
                success=True, message="Facility access removed successfully"
            )
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def add_department_access(
        profile_id: int, department_id: int
    ) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            profile.access_to_department.add(department_id)
            return RepositoryResponse(
                success=True, message="Department access added successfully"
            )
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def remove_department_access(
        profile_id: int, department_id: int
    ) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            profile.access_to_department.remove(department_id)
            return RepositoryResponse(
                success=True, message="Department access removed successfully"
            )
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def get_profile_facilities(profile_id: int) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            facilities = profile.access_to_facilities.all()
            return RepositoryResponse(success=True, data=list(facilities))
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")

    @staticmethod
    def get_profile_departments(profile_id: int) -> RepositoryResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            departments = profile.access_to_department.all()
            return RepositoryResponse(success=True, data=list(departments))
        except Profile.DoesNotExist:
            return RepositoryResponse(success=False, message="Profile not found")
        except Exception as e:
            return RepositoryResponse(success=False, message="Internal server error")
