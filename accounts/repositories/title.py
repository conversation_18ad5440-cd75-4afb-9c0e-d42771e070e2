from accounts.models import Title
from accounts.serializers import TitleSerializer
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse


class TitleRepository:
    def __init__(self, user):
        self.user = user
        self.logging_service = LoggingService()

    def get_titles(self) -> RepositoryResponse:
        try:
            titles = Title.objects.all()
            return RepositoryResponse(
                success=True,
                data=titles,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while retrieving titles.",
            )

    def get_title_by_id(self, title_id) -> RepositoryResponse:
        try:
            title = Title.objects.get(id=title_id)
            return RepositoryResponse(
                success=True,
                data=title,
                message="Title retrieved successfully.",
            )
        except Title.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Title not found.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while retrieving title.",
            )

    def create_title(self, data) -> RepositoryResponse:
        try:
            required_fields = ["name"]
            missing_fields = self.logging_service.check_required_fields(
                data, required_fields
            )
            if missing_fields:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=missing_fields,
                )
            serializer = TitleSerializer(data=data)
            if not serializer.is_valid():
                self.logging_service.log_error(serializer.errors)
                return RepositoryResponse(
                    success=False,
                    data=serializer.errors,
                    message="Invalid data provided.",
                )
            title = Title.objects.create(**serializer.validated_data)
            return RepositoryResponse(
                success=True,
                data=title,
                message="Title created successfully.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while creating title.",
            )

    def update_title(self, title_id, data) -> RepositoryResponse:
        try:
            serializer = TitleSerializer(data=data, partial=True)
            if not serializer.is_valid():
                return RepositoryResponse(
                    success=False,
                    data=serializer.errors,
                    message="Invalid data provided.",
                )
            title = Title.objects.get(id=title_id)
            for attr, value in serializer.validated_data.items():
                setattr(title, attr, value)
            title.save()
            return RepositoryResponse(
                success=True,
                data=title,
                message="Title updated successfully.",
            )
        except Title.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Title not found.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while updating title.",
            )

    def delete_title(self, title_id) -> RepositoryResponse:
        try:
            title = Title.objects.get(id=title_id)
            title.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Title deleted successfully.",
            )
        except Title.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Title not found.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while deleting title.",
            )
