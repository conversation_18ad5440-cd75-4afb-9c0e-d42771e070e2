from typing import Optional, List
from django.db.models import Q

from accounts.models import UserProfile
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse

logging_service = LoggingService()


class PatientProfileRepository:

    def create_patient(self, data: dict) -> RepositoryResponse:
        try:
            patient = UserProfile.objects.create(**data)
            return RepositoryResponse(
                success=True,
                message="Patient created successfully",
                data=patient,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error creating patient",
                data=None,
            )

    def get_patient_by_id(self, patient_id: int) -> RepositoryResponse:
        try:
            return RepositoryResponse(
                success=True,
                message="Patient retrieved successfully",
                data=UserProfile.objects.get(id=patient_id),
            )
        except UserProfile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Patient not found",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error retrieving patient",
                data=None,
            )

    def get_patient_by_mrn(self, mrn: str) -> RepositoryResponse:
        try:
            return RepositoryResponse(
                success=True,
                message="Patient retrieved successfully",
                data=UserProfile.objects.get(medical_record_number=mrn),
            )
        except UserProfile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Patient not found",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error retrieving patient",
                data=None,
            )

    def search_patients(self, search_term: str) -> List[RepositoryResponse]:
        try:
            data = UserProfile.objects.filter(
                Q(first_name__icontains=search_term)
                | Q(last_name__icontains=search_term)
                | Q(medical_record_number__icontains=search_term)
                | Q(email__icontains=search_term)
            )

            return RepositoryResponse(
                success=True,
                message="Patients retrieved successfully",
                data=data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error searching patients",
                data=None,
            )

    def update_patient(self, patient_id: int, data: dict) -> RepositoryResponse:
        try:
            patient = UserProfile.objects.get(id=patient_id)
            for key, value in data.items():
                setattr(patient, key, value)
            patient.save()
            return RepositoryResponse(
                success=True,
                message="Patient updated successfully",
                data=patient,
            )
        except UserProfile.DoesNotExist:
            return {
                "success": False,
                "message": "Patient not found",
                "data": None,
            }
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error updating patient",
                data=None,
            )

    def delete_patient(self, patient_id: int) -> RepositoryResponse:
        try:
            patient = UserProfile.objects.get(id=patient_id)
            patient.delete()
            return RepositoryResponse(
                success=True,
                message="Patient deleted successfully",
                data=None,
            )
        except UserProfile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Patient not found",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error deleting patient",
                data=None,
            )
