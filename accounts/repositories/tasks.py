from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from tasks.models import ReviewTemplateTasks
from tasks.serializers import ReviewTemplateTaskSerializer


class ReviewTemplateTasksRepository:
    def __init__(self, user):
        self.user = user
        self.logging_service = LoggingService()

    def get_tasks(self, template_id) -> RepositoryResponse:
        try:
            tasks = ReviewTemplateTasks.objects.filter(review_template=template_id)
            return RepositoryResponse(
                success=True,
                data=tasks,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while retrieving tasks.",
            )

    def get_task_by_id(self, task_id) -> RepositoryResponse:
        try:
            task = ReviewTemplateTasks.objects.get(id=task_id)
            return RepositoryResponse(
                success=True,
                data=task,
                message="Task retrieved successfully.",
            )
        except ReviewTemplateTasks.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Task not found.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while retrieving task.",
            )

    def create_task(self, data) -> RepositoryResponse:
        try:
            serializer = ReviewTemplateTaskSerializer(data=data)
            if not serializer.is_valid():
                self.logging_service.log_error(serializer.errors)
                return RepositoryResponse(
                    success=False,
                    data=serializer.errors,
                    message="Invalid data provided.",
                )
            task = ReviewTemplateTasks.objects.create(**serializer.validated_data)
            return RepositoryResponse(
                success=True,
                data=task,
                message="Task created successfully.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while creating task.",
            )

    def update_task(self, task_id, data) -> RepositoryResponse:
        try:
            serializer = ReviewTemplateTaskSerializer(data=data, partial=True)
            if not serializer.is_valid():
                self.logging_service.get_serializer_error_message(serializer)
                return RepositoryResponse(
                    success=False,
                    data=serializer.errors,
                    message="Invalid data provided.",
                )
            task = ReviewTemplateTasks.objects.get(id=task_id)
            for attr, value in serializer.validated_data.items():
                setattr(task, attr, value)
            task.save()
            return RepositoryResponse(
                success=True,
                data=task,
                message="Task updated successfully.",
            )
        except ReviewTemplateTasks.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Task not found.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while updating task.",
            )

    def delete_task(self, task_id) -> RepositoryResponse:
        try:
            task = ReviewTemplateTasks.objects.get(id=task_id)
            task.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Task deleted successfully.",
            )
        except ReviewTemplateTasks.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Task not found.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Internal server error occurred while deleting task.",
            )
