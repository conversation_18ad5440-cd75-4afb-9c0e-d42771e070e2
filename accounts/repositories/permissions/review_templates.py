from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from tasks.models import ReviewTemplates
from tasks.serializers import ReviewTemplateSerializer


class ReviewTemplatesRepository:
    def __init__(self, user):
        self.user = user
        self.logging_service = LoggingService()

    def get_review_templates(self) -> RepositoryResponse:
        try:
            review_templates = ReviewTemplates.objects.all()
            if not review_templates:
                return RepositoryResponse(
                    success=False,
                    message="No review templates found.",
                    data=None,
                )
            return RepositoryResponse(
                success=True,
                message="Review templates retrieved successfully.",
                data=review_templates,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while retrieving review templates.",
                data=None,
            )

    def get_review_template_by_id(self, template_id) -> RepositoryResponse:
        try:
            review_template = ReviewTemplates.objects.get(id=template_id)

            return RepositoryResponse(
                success=True,
                message="Review template retrieved successfully.",
                data=review_template,
            )
        except ReviewTemplates.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Review template not found.",
                data=None,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while retrieving the review template.",
                data=None,
            )

    def create_review_template(self, data) -> RepositoryResponse:
        try:
            required_fields = [
                "incident_type",
                "name",
                "description",
            ]

            missing_fields = self.logging_service.check_required_fields(
                data, required_fields
            )

            if missing_fields:
                return RepositoryResponse(
                    success=False,
                    message=missing_fields,
                    data=None,
                )

            serializer = ReviewTemplateSerializer(data=data)
            if not serializer.is_valid():
                self.logging_service.log_error(serializer.errors)
                return RepositoryResponse(
                    success=False,
                    message="Invalid data provided.",
                    data=serializer.errors,
                )

            review_template = ReviewTemplates.objects.create(**data)
            return RepositoryResponse(
                success=True,
                message="Review template created successfully.",
                data=review_template,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while creating the review template.",
                data=None,
            )

    def update_review_template(self, template_id, data) -> RepositoryResponse:
        try:
            serializer = ReviewTemplateSerializer(data=data)
            if not serializer.is_valid():
                return RepositoryResponse(
                    success=False,
                    message="Invalid data provided.",
                    data=serializer.errors,
                )

            review_template = ReviewTemplates.objects.get(id=template_id)
            review_template = serializer.update(
                instance=review_template,
                validated_data=serializer.validated_data,
            )
            return RepositoryResponse(
                success=True,
                message="Review template updated successfully.",
                data=review_template,
            )
        except ReviewTemplates.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Review template not found.",
                data=None,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while updating the review template.",
                data=None,
            )

    def delete_review_template(self, template_id) -> RepositoryResponse:
        try:
            review_template = ReviewTemplates.objects.get(id=template_id)
            # check if not tasks
            if review_template.review_template_tasks.exists():
                return RepositoryResponse(
                    success=False,
                    message="Cannot delete review template with existing tasks.",
                    data=None,
                )
            review_template.delete()
            return RepositoryResponse(
                success=True,
                message="Review template deleted successfully.",
                data=None,
            )
        except ReviewTemplates.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Review template not found.",
                data=None,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while deleting the review template.",
                data=None,
            )
