from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from tasks.models import ReviewGroups
from tasks.serializers import ReviewGroupSerializer


class ReviewGroupsRepository:
    """
    Repository to manage review groups.
    """

    def __init__(self, user):
        self.user = user
        # self.user_profile = user.profile
        # self.permissions = user.permissions
        self.logging_service = LoggingService()

    def get_review_groups(self) -> RepositoryResponse:
        """
        Get all review groups.
        """

        try:
            groups = ReviewGroups.objects.all().prefetch_related("members")
            return RepositoryResponse(
                message="Retrieved review groups successfully",
                success=True,
                data=groups,
            )
        except Exception as e:
            self.logging_service(e)
            return RepositoryResponse(
                success=False,
                message="Failed to get review groups",
                data=None,
            )

    def get_review_group(self, group_id) -> RepositoryResponse:
        """
        Get a review group by ID.
        """
        try:
            group = ReviewGroups.objects.prefetch_related("members").get(id=group_id)

            return RepositoryResponse(
                success=True,
                message="Retrieved review group successfully",
                data=group,
            )

        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False, message="Failed to get a review group", data=None
            )
        return

    def create_review_group(self, data) -> RepositoryResponse:
        """
        Create a new review group.
        """
        try:
            required_fields = ["title", "description"]
            missing_fields = self.logging_service.check_required_fields(
                data, required_fields
            )
            if missing_fields:
                return RepositoryResponse(
                    success=False,
                    message="Missing required fields",
                    data=missing_fields,
                )
            # Check if the review group already exists
            existing_group = ReviewGroups.objects.filter(title=data["title"]).first()

            if existing_group:
                return RepositoryResponse(
                    success=False,
                    message="Review group already exists",
                    data=existing_group,
                )
            # validate review group data
            serializer = ReviewGroupSerializer(data=data)
            if not serializer.is_valid():
                return RepositoryResponse(
                    success=False,
                    message="Invalid data",
                    data=serializer.errors,
                )
            # Create the review group
            review_group = ReviewGroups.objects.create(**data)

            return RepositoryResponse(
                success=True,
                message="Review group created successfully",
                data=review_group,
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message="Error creating review group",
                data="Internal server error",
            )
        return ReviewGroups.objects.create(**data)

    def update_review_group(self, group_id, data) -> RepositoryResponse:
        """
        Update an existing review group.
        """
        try:
            group = ReviewGroups.objects.get(id=group_id)
            serializer = ReviewGroupSerializer(group, data=data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return RepositoryResponse(
                    success=True,
                    message="Review group updated successfully",
                    data=group,
                )
            else:
                return RepositoryResponse(
                    success=False,
                    message="Invalid data",
                    data=serializer.errors,
                )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False, message="Failed to update review group", data=None
            )

    def delete_review_group(self, group_id):
        """
        Delete review group
        """
        try:
            group = ReviewGroups.objects.get(id=group_id)
            # check if the group has any associated tasks
            if group.review_template_groups.exists():
                return RepositoryResponse(
                    success=False,
                    message="Cannot delete review group with associated tasks",
                    data=None,
                )
            # check if the group has any associated members
            if group.members.exists():
                return RepositoryResponse(
                    success=False,
                    message="Cannot delete review group with associated members",
                    data=None,
                )
            group.delete()
            return RepositoryResponse(
                success=True,
                message="Review group deleted successfully",
                data=None,
            )
        except ReviewGroups.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Review group does not exist",
                data=None,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return RepositoryResponse(
                success=False, message="Failed to delete review group", data=None
            )
