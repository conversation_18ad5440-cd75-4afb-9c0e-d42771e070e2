from typing import Optional, List
from django.db.models import Q

from accounts.models import UserProfile
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse

logging_service = LoggingService()


class PatientFamilyRepository:
    def create_family_member(self, data: dict) -> RepositoryResponse:
        try:
            patient = UserProfile.objects.create(**data)
            return RepositoryResponse(
                success=True,
                message="Family member created successfully",
                data=patient,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error creating family member",
                data=None,
            )

    def get_family_member_by_id(self, member_id: int) -> Optional[RepositoryResponse]:
        try:
            patient = UserProfile.objects.get(id=member_id)
            return RepositoryResponse(
                success=True,
                message="Family member retrieved successfully",
                data=patient,
            )
        except UserProfile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Family member not found",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error retrieving family member",
                data=None,
            )

    def get_family_members_by_patient(
        self, patient_id: int
    ) -> List[RepositoryResponse]:
        try:
            patient = UserProfile.objects.filter(patient_id=patient_id)
            return RepositoryResponse(
                success=True,
                message="Family members retrieved successfully",
                data=patient,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error retrieving family members for patient",
                data=None,
            )

    def update_family_member(
        self, member_id: int, data: dict
    ) -> Optional[RepositoryResponse]:
        try:
            member = UserProfile.objects.get(id=member_id)
            for key, value in data.items():
                setattr(member, key, value)
            member.save()
            return RepositoryResponse(
                success=True,
                message="Family member updated successfully",
                data=member,
            )
        except UserProfile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Family member not found",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error updating family member",
                data=None,
            )

    def delete_family_member(self, member_id: int) -> RepositoryResponse:
        try:
            member = UserProfile.objects.get(id=member_id)
            member.delete()
            return RepositoryResponse(
                success=True,
                message="Family member deleted successfully",
                data=None,
            )
        except UserProfile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Family member not found",
                data=None,
            )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error deleting family member",
                data=None,
            )
