from django.contrib import admin
from accounts.models import (
    Profile,
    Title,
    UserPreference,
    UserProfile,
)
from tasks.models import (
    ReviewGroups,
    ReviewProcess,
    ReviewProcessTasks,
    ReviewTemplateTasks,
    ReviewTemplates,
)


# Register your models here.
@admin.register(Profile)
class Profile(admin.ModelAdmin):
    list_display = (
        "user",
        "id",
        "phone_number",
        "gender",
        "date_of_birth",
        "address",
        "birth_country",
        "facility",
    )

    search_fields = ("user__email", "user__first_name", "user__last_name")


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "first_name",
        "last_name",
        "email",
        "phone_number",
    )

    search_fields = ("first_name", "last_name", "phone_number")


class UserPreferenceAdmin(admin.ModelAdmin):
    list_display = ("user", "user_timezone")
    search_fields = ("user__email", "user__first_name", "user__last_name")


@admin.register(ReviewGroups)
class ReviewGroupsAdmin(admin.ModelAdmin):
    list_display = ("title", "created_at")
    search_fields = ("title", "created_at")


@admin.register(ReviewTemplates)
class ReviewTemplatesAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "created_at")


@admin.register(ReviewTemplateTasks)
class ReviewTemplateTasksAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "created_at")


@admin.register(ReviewProcessTasks)
class ReviewProcessTasksAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "created_at")


@admin.register(ReviewProcess)
class ReviewProcessAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "created_at")


@admin.register(Title)
class TitleAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "created_at")
    search_fields = ("name", "description")


admin.register(ReviewTemplates, ReviewGroupsAdmin)
admin.site.register(UserPreference, UserPreferenceAdmin)
