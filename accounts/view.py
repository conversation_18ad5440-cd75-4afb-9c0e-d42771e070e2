from rest_framework import status
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.response import Response
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken

from accounts.services.auth_srvices import AuthService
from api.views.auth.login import user_details
from base.services.logging.logger import LoggingService
from base.services.permissions.security import SecurityService
from general_patient_visitor.models import GeneralPatientVisitor
import json
from base.services.permissions.permissions import APIKeyAuthentication


encryption_service = SecurityService()
logger = LoggingService()


# login a user using email and password
@api_view(["POST"])
@permission_classes([])
def login_email_password(request):
    auth_service = AuthService()
    try:

        response = auth_service.login_email_password(request.data)
        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            response.data,
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        logger.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@authentication_classes([APIKeyAuthentication])
def check_access(request):
    auth_service = AuthService()
    try:
        response = auth_service.check_access(request.data)
        if not response.success:
            return Response(
                {"error": response.message},
                status=response.code,
            )
        return Response(
            response.data,
            status=status.HTTP_200_OK,
        )
    except Exception as e:
        logger.log_error(e)
        return Response(
            {"message": "Error authenticating with backend"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
