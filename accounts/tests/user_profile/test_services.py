from django.test import TestCase
from accounts.services.user_profile.service import UserProfileService
from base.tests.factory import UserProfileFactory
from accounts.models import UserProfile


class TestUserProfileService(TestCase):
    def setUp(self):
        self.service = UserProfileService()

    def test_get_or_create_profile_existing(self):
        # Create a user profile to test retrieval
        existing_profile = UserProfileFactory()
        data = {
            "user_id": existing_profile.id,
        }
        response = self.service.get_or_create_profile(data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_or_create_profile_new(self):
        # Test creating a new user profile
        data = {
            "first_name": "<PERSON>",
            "last_name": "Do<PERSON>",
            "email": "<EMAIL>",
            "phone_number": "**********",
            "address": "123 Main St",
            "gender": "M",
            "profile_type": "Patient",
        }
        response = self.service.get_or_create_profile(data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_or_create_profile_missing_fields(self):
        # Test creating a new user profile with missing required fields
        data = {
            "last_name": "Doe",
            "email": "<EMAIL>",
        }
        response = self.service.get_or_create_profile(data)
        self.assertFalse(response.success)
        self.assertIn("Missing required fields", response.message)
