from django.test import TransactionTestCase
from accounts.services.profile.services import ProfileService
from accounts.tests.factory import ProfileFactory
from complaints.tests.factory import ComplaintFactory


class TestGetAllComplaints(TransactionTestCase):
    def setUp(self):
        self.profile = ProfileFactory()
        self.service = ProfileService()
        self.complaints = [
            ComplaintFactory(created_by=self.profile.user) for _ in range(3)
        ]
        self.other_complaints = [ComplaintFactory() for _ in range(2)]

    def test_get_all_complaints_success(self):
        response = self.service.get_user_complaints(self.profile.id)

        if not response.success:
            self.fail(f"Failed to get all complaints: {response}")
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), len(self.complaints))
