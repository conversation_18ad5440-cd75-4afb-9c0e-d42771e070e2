from accounts.models import Title
from accounts.tests.factory import TitleFactory
from base.tests.base_setup import BaseTestSetup


class TitleTestBase(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.title_data = {
            "name": "Test Title",
            "description": "This is a test title.",
        }


class TitleCreateTests(TitleTestBase):
    def test_title_creation(self):
        self._authenticate_user(self.super_user)

        response = self.client.post(
            f"/api/titles/", data=self.title_data, format="json"
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["name"], self.title_data["name"])
        self.assertEqual(response.data["description"], self.title_data["description"])


class TitleRetrieveTests(TitleTestBase):
    def test_title_retrieval(self):
        self._authenticate_user(self.super_user)
        title = TitleFactory(created_by=self.super_user)

        response = self.client.get(f"/api/titles/{title.id}/")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], title.name)
        self.assertEqual(response.data["description"], title.description)
        self.assertEqual(response.data["created_by"]["id"], self.super_user.id)


class TitleListTests(TitleTestBase):
    def test_get_titles(self):
        [TitleFactory(created_by=self.super_user) for _ in range(5)]
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/titles/")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 5)

    def test_get_titles_with_query_params(self):
        [TitleFactory(created_by=self.super_user, name=f"Title {i}") for i in range(5)]
        TitleFactory(created_by=self.super_user, name="Special Title")
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/titles/?q=Special Title")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 1)

    def test_get_titles_with_pagination(self):
        [TitleFactory(created_by=self.super_user, name=f"Title {i}") for i in range(5)]
        TitleFactory(created_by=self.super_user, name="Special Title")
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/titles/?page=1&page_size=2")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 2)

    def test_search_titles_by_name_and_description(self):
        self._authenticate_user(self.super_user)
        TitleFactory(created_by=self.super_user, name="Alpha", description="First desc")
        TitleFactory(created_by=self.super_user, name="Beta", description="Second desc")
        TitleFactory(
            created_by=self.super_user, name="Gamma", description="Alpha in desc"
        )

        # Search by name
        response = self.client.get("/api/titles/?q=Alpha")
        self.assertEqual(response.status_code, 200)
        # Should match both name and description
        self.assertTrue(any("Alpha" in r["name"] for r in response.data["results"]))
        self.assertTrue(
            any("Alpha" in r["description"] for r in response.data["results"])
        )

        # Search by description
        response = self.client.get("/api/titles/?q=Second desc")
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            any("Second desc" in r["description"] for r in response.data["results"])
        )

    def test_sort_titles_by_name_and_created_at(self):
        self._authenticate_user(self.super_user)
        TitleFactory(created_by=self.super_user, name="Charlie")
        TitleFactory(created_by=self.super_user, name="Alpha")
        TitleFactory(created_by=self.super_user, name="Bravo")

        # Sort by name ascending
        response = self.client.get("/api/titles/?order_by=name&sort_order=asc")
        self.assertEqual(response.status_code, 200)
        names = [r["name"] for r in response.data["results"]]
        self.assertEqual(names, sorted(names))

        # Sort by name descending
        response = self.client.get("/api/titles/?order_by=name&sort_order=desc")
        self.assertEqual(response.status_code, 200)
        names = [r["name"] for r in response.data["results"]]
        self.assertEqual(names, sorted(names, reverse=True))

        # Sort by created_at ascending
        response = self.client.get("/api/titles/?order_by=created_at&sort_order=asc")
        self.assertEqual(response.status_code, 200)
        created_ats = [r["created_at"] for r in response.data["results"]]
        self.assertEqual(created_ats, sorted(created_ats))

        # Sort by created_at descending
        response = self.client.get("/api/titles/?order_by=created_at&sort_order=desc")
        self.assertEqual(response.status_code, 200)
        created_ats = [r["created_at"] for r in response.data["results"]]
        self.assertEqual(created_ats, sorted(created_ats, reverse=True))

    def test_titles_pagination_response(self):
        self._authenticate_user(self.super_user)
        for i in range(7):
            TitleFactory(created_by=self.super_user, name=f"Title {i}")
        response = self.client.get("/api/titles/?page=2&page_size=3")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["page"], 2)
        self.assertEqual(response.data["page_size"], 3)
        self.assertEqual(len(response.data["results"]), 3)
        self.assertTrue(response.data["has_next"])  # page 2 of 3
        self.assertTrue(response.data["has_previous"])  # not first page
        self.assertEqual(response.data["count"], 7)


class TitleUpdateTests(TitleTestBase):
    def test_update_title(self):
        self._authenticate_user(self.super_user)
        title = TitleFactory(created_by=self.super_user)

        updated_data = {
            "name": "Updated Title",
            "description": "Updated Description",
        }

        response = self.client.put(
            f"/api/titles/{title.id}/", data=updated_data, format="json"
        )
        if not response.status_code == 200:
            self.fail(f"Failed to update title {response.data}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], updated_data["name"])
        self.assertEqual(response.data["description"], updated_data["description"])


class TitleDeleteTests(TitleTestBase):
    def test_delete_title(self):
        self._authenticate_user(self.super_user)
        title = TitleFactory(created_by=self.super_user)
        title_id = title.id
        response = self.client.delete(f"/api/titles/{title.id}/")
        self.assertEqual(response.status_code, 204)

        # Verify that the title no longer exists
        self.assertIsNone(Title.objects.filter(id=title_id).first())
