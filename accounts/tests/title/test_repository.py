from django.test import TestCase
from accounts.models import Title
from accounts.repositories.title import TitleRepository
from base.tests.factory import UserFactory


class TestTitleRepository(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.repository = TitleRepository(user=self.user)
        self.title_data = {
            "name": "Test Title",
            "description": "This is a test title.",
        }

    def test_get_titles(self):
        Title.objects.create(**self.title_data, created_by=self.user)
        response = self.repository.get_titles()
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0].name, self.title_data["name"])

    def test_get_title_by_id(self):
        title = Title.objects.create(**self.title_data, created_by=self.user)
        response = self.repository.get_title_by_id(title.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, self.title_data["name"])

    def test_get_title_by_id_not_found(self):
        response = self.repository.get_title_by_id(999)
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Title not found.")

    def test_create_title(self):
        response = self.repository.create_title(self.title_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, self.title_data["name"])

    def test_create_title_invalid_data(self):
        invalid_data = {"name": ""}  # Missing required fields
        response = self.repository.create_title(invalid_data)
        self.assertFalse(response.success)

    def test_update_title(self):
        title = Title.objects.create(**self.title_data, created_by=self.user)
        updated_data = {"name": "Updated Title"}
        response = self.repository.update_title(title.id, updated_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, updated_data["name"])

    def test_update_title_not_found(self):
        updated_data = {"name": "Updated Title"}
        response = self.repository.update_title(999, updated_data)
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Title not found.")

    def test_delete_title(self):
        title = Title.objects.create(**self.title_data, created_by=self.user)
        response = self.repository.delete_title(title.id)
        self.assertTrue(response.success)
        self.assertEqual(Title.objects.count(), 0)

    def test_delete_title_not_found(self):
        response = self.repository.delete_title(999)
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Title not found.")
