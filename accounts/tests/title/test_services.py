from django.test import TestCase
from accounts.models import Title
from accounts.services.title.services import TitleService
from accounts.tests.factory import TitleFactory
from base.tests.factory import UserFactory


class TitleServiceTestCase(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.service = TitleService(user=self.user)

    def test_get_title_by_id(self):
        title = TitleFactory(created_by=self.user, updated_by=self.user)
        response = self.service.get_title_by_id(title.id)

        self.assertTrue(response.success)
        self.assertEqual(response.data["id"], title.id)
        self.assertEqual(response.data["name"], title.name)

    def test_get_title_by_id_not_found(self):
        response = self.service.get_title_by_id(9999)

        self.assertFalse(response.success)

    def test_create_title(self):
        data = {
            "name": "Test Title",
            "description": "Test Description",
        }

        response = self.service.create_title(data)

        self.assertTrue(response.success)
        self.assertEqual(response.data["name"], data["name"])
        self.assertEqual(response.data["description"], data["description"])

    def test_create_title_with_no_description(self):
        data = {"name": "Test Title"}

        response = self.service.create_title(data)

        self.assertTrue(response.success)
        self.assertEqual(response.data["name"], data["name"])

    def test_create_title_missing_fields(self):
        data = {
            "description": "Test Description",
        }

        response = self.service.create_title(data)

        self.assertFalse(response.success)
        self.assertIn("name", response.message)

    def test_update_title(self):
        title = TitleFactory(created_by=self.user, updated_by=self.user)
        data = {
            "name": "Updated Title",
            "description": "Updated Description",
        }

        response = self.service.update_title(title.id, data)

        self.assertTrue(response.success)
        self.assertEqual(response.data["name"], data["name"])
        self.assertEqual(response.data["description"], data["description"])

    def test_update_title_not_found(self):
        data = {
            "name": "Updated Title",
            "description": "Updated Description",
        }

        response = self.service.update_title(9999, data)

        self.assertFalse(response.success)

    def test_delete_title(self):
        title = TitleFactory(created_by=self.user, updated_by=self.user)

        response = self.service.delete_title(title.id)

        self.assertTrue(response.success)
        self.assertEqual(Title.objects.count(), 0)
