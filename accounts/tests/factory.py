import factory
from accounts.models import Title, Profile, UserProfile
from django.contrib.auth.models import User

from base.tests.factory import UserFactory


class TitleFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Title

    name = factory.Faker("job")
    description = factory.Faker("text")
    created_by = factory.SubFactory(UserFactory)
    updated_by = factory.SubFactory(UserFactory)


class ProfileFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Profile

    user = factory.SubFactory(UserFactory)
    cohesive_user_id = factory.Faker("uuid4")
    totp = factory.Faker("password")
    mfa_enabled = factory.Faker("boolean")
    is_test_account = factory.Faker("boolean")
    review_permissions = factory.Faker("boolean")
    is_deleted = factory.Faker("boolean")
    title = factory.SubFactory(TitleFactory)


class UserProfileFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = UserProfile

    profile_type = factory.Faker(
        "random_element",
        elements=[
            "Staff",
            "Supervisor",
            "Patient",
            "Family",
            "Physician",
            "Visitor",
            "Assailant",
            "Victim",
            "Witness",
            "Involved Party",
            "Observer",
        ],
    )
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    middle_name = factory.Faker("first_name")
    email = factory.Faker("email")
    medical_record_number = factory.Faker("ean8")
    relationship_to_patient = factory.Faker("word")
