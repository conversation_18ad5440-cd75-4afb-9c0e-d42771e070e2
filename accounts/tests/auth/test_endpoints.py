"""
Test authentication
LOGIN
1. User can login
2. User can't login with incorrect email or password
3. User can't login with inactive account
4. User can't login with locked account
5. User can't login with expired session

PASSWORD RESET
1. User can reset password
2. User can't reset password with incorrect email
3. User can't reset password with expired reset code
4. User can login with new password
"""

import jwt
from django.conf import settings
from django.test import TestCase
from rest_framework.test import APIClient
from base.tests.factory import *
from rest_framework import status
from base.tests.factory import *
from django.contrib.auth.models import User


class TestLoginCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.endpoint = "/api/accounts"

        self.password = "testpassword123"
        self.user = User.objects.create(
            username="testuser123",
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
        )
        self.user.set_password(self.password)
        self.user.save()

        self.profile = ProfileFactory(user=self.user, is_test_account=True)
        self.facility = FacilityFactory()
        self.department = DepartmentFactory()
        self.profile.access_to_facilities.add(self.facility)

    def test_login_success(self):
        credentials = {
            "username": self.user.username,
            "password": self.password,
        }
        response = self.client.post(
            f"{self.endpoint}/token/email-password/", credentials
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("access", response.data)
        self.assertIn("refresh", response.data)

        # Decode the access token
        access_token = response.data["access"]
        decoded_token = jwt.decode(
            access_token, settings.SECRET_KEY, algorithms=["HS256"]
        )

        # Verify claims in the token
        self.assertEqual(decoded_token["first_name"], self.user.first_name)
        self.assertEqual(decoded_token["last_name"], self.user.last_name)
        self.assertEqual(decoded_token["email"], self.user.email)

    def test_login_incorrect_email(self):
        credentials = {
            "username": "incorrect_email",
            "password": self.password,
        }
        response = self.client.post(
            f"{self.endpoint}/token/email-password/", credentials
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
