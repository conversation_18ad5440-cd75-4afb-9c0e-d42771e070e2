from django.test import TestCase
from accounts.models import Profile
from accounts.services.profile.services import ProfileService
from accounts.services.profile.utils import ProfileUtilsService
from accounts.tests.factory import TitleFactory
from base.tests.factory import *
from base.constants import TEST_DEFAULTS
from base.services.permissions.manage_permissions import PermissionsManagement
from base.tests.base_setup import BaseTestSetup
from general_patient_visitor.models import GeneralPatientVisitor
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth.models import Permission, User


utils_service = ProfileUtilsService()
permissions_management = PermissionsManagement()


class TestProfileService(TestCase):
    def setUp(self):
        self.facility = FacilityFactory()
        self.department = DepartmentFactory(facility=self.facility)
        self.title = TitleFactory()

        self.profile_service = ProfileService(user=UserFactory())

        self.permissions = [
            {
                "code_name": "001",
                "name": "Can create",
            },
            {
                "code_name": "002",
                "name": "Can delete",
            },
        ]
        self.model = GeneralPatientVisitor
        self.group = permissions_management.check_group("Test Group 2")
        content_type = ContentType.objects.get_for_model(self.model)
        for perm in self.permissions:
            Permission.objects.get_or_create(
                codename=perm["code_name"],
                name=perm["name"],
                content_type=content_type,
            )

        self.valid_data = {
            "email": TEST_DEFAULTS.ACTIVE_EMAIL,
            "first_name": "John",
            "last_name": "Doe",
            "facility_id": self.facility.id,
            "department_id": self.department.id,
            "title_id": self.title.id,
        }

    def test_create_profile_successful(self):
        response = self.profile_service.new_profile(self.valid_data)

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data["id"])
        self.assertEqual(response.data["title"]["id"], self.title.id)
        self.assertIsNotNone(response.data)

        # check if a user is in members of the facility
        user = User.objects.get(email=TEST_DEFAULTS.ACTIVE_EMAIL)
        self.assertTrue(self.facility.staff_members.filter(id=user.id).exists())
        # check if a user is in members of the department
        self.assertTrue(self.department.members.filter(id=user.id).exists())

    def test_create_profile_with_permissions(self):
        valid_data = {
            "email": TEST_DEFAULTS.ACTIVE_EMAIL,
            "first_name": "John",
            "last_name": "Doe",
            "facility_id": self.facility.id,
            "department_id": self.department.id,
            "title_id": self.title.id,
            "permissions": [
                {
                    "feature": "general",
                    "permissions": [
                        {
                            "code_name": "001",
                            "name": "Can create",
                        },
                        {
                            "code_name": "002",
                            "name": "Can delete",
                        },
                    ],
                }
            ],
        }
        response = self.profile_service.new_profile(valid_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data["id"])
        self.assertIsNotNone(response.data)

        # assert if the create user has permissions that we send
        user = User.objects.get(email=TEST_DEFAULTS.ACTIVE_EMAIL)
        user_permissions = user.user_permissions.all()
        self.assertEqual(len(user_permissions), len(self.permissions))
        for permission in self.permissions:
            self.assertTrue(
                user_permissions.filter(codename=permission["code_name"]).exists()
            )

    def test_create_profile_with_permission_group(self):
        valid_data = {
            "email": TEST_DEFAULTS.ACTIVE_EMAIL,
            "first_name": "John",
            "last_name": "Doe",
            "facility_id": self.facility.id,
            "department_id": self.department.id,
            "title_id": self.title.id,
            "permissions_groups": [
                self.group.data.id,
            ],
        }
        response = self.profile_service.new_profile(valid_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data["id"])
        self.assertIsNotNone(response.data)
        # assert if the create user has permissions that we send
        user = User.objects.get(email=TEST_DEFAULTS.ACTIVE_EMAIL)
        self.assertTrue(user.groups.filter(id=self.group.data.id).exists())

    def test_create_user_with_access_to_facility(self):
        """Test creating a user with access to facility"""

        data = {
            "email": TEST_DEFAULTS.ACTIVE_EMAIL,
            "first_name": "Access",
            "last_name": "To Facility",
            "facility_id": self.facility.id,
            "department_id": self.department.id,
            "title_id": self.title.id,
            "permissions": [
                {
                    "feature": "general",
                    "permissions": [
                        {
                            "code_name": "001",
                            "name": "Can create",
                        },
                        {
                            "code_name": "002",
                            "name": "Can delete",
                        },
                    ],
                }
            ],
            "access_to_facilities": [self.facility.id],
            "access_to_departments": [self.department.id],
        }
        response = self.profile_service.new_profile(data)
        if not response.success:
            self.fail(response)
        self.assertTrue(response.success)

        # Assert user data
        user = User.objects.get(email=TEST_DEFAULTS.ACTIVE_EMAIL)

        # Assert profile data
        profile = Profile.objects.get(user=user)
        self.assertIn(self.facility, profile.access_to_facilities.all())
        self.assertIn(self.department, profile.access_to_department.all())

    def test_post_user_to_external_api(self):
        data = {
            "email": TEST_DEFAULTS.ACTIVE_EMAIL,
            "first_name": "Piere",
            "last_name": "NGUWENEZA",
            "password": "testpassword123",
            "facility": "CSR Limited",
            "department": "Software Dev",
            "role": "Software Developer",
            "title_id": self.title.id,
        }
        response = utils_service.handle_external_user(data)
        self.assertTrue(response.success)

        self.assertIsNotNone(response.data["id"])
        self.assertIsNotNone(response.data)


class TestUserPermissions(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.group = GroupFactory()
        self.user.groups.add(self.group)

        self.permissions = [
            {
                "code_name": "001",
                "name": "Can create",
            },
            {
                "code_name": "002",
                "name": "Can delete",
            },
        ]

        self.model = GeneralPatientVisitor
        PermissionsManagement().add_permissions_to_group(
            self.group,
            permissions=self.permissions,
            model=self.model,
        )

    def test_get_user_permissions(self):
        response = self.profile_service.get_user_permissions(self.user, self.user.id)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)


class TestGetUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.profile = ProfileFactory()
        self.user = UserFactory(
            first_name="John",
            last_name="Doe",
        )
        self.user2 = UserFactory(email="<EMAIL>")
        self.profiles = [ProfileFactory() for _ in range(2)]
        self.profile2 = [ProfileFactory() for _ in range(2)]
        self.profile4 = ProfileFactory(user=self.user)
        self.profile4 = ProfileFactory(user=self.user2)
        self.profile_service = ProfileService(user=self.user)

    def test_get_profiles_success(self):
        response = self.profile_service.get_profiles()
        if not response.success:
            self.fail(f"Failed to get profiles {response}")
        self.assertGreater(len(response.data), 1)

    def test_get_profiles_with_name(self):
        response = self.profile_service.get_profiles({"q": "John"})
        self.assertTrue(response.success)
        self.assertGreater(len(response.data["results"]), 0)

    def test_get_profiles_with_email(self):
        response = self.profile_service.get_profiles({"q": "<EMAIL>"})
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)

    def test_sort_by_first_name_ascending(self):
        """Test sorting profiles by first name in ascending order"""
        response = self.profile_service.get_profiles({
            "sort_by": "first_name",
            "sort_order": "asc"
        })
        self.assertTrue(response.success)
        results = response.data["results"]

        first_names = [
            result["user"]["first_name"] for result in results if result["user"]
        ]
        self.assertEqual(first_names, sorted(first_names))

    def test_sort_by_first_name_descending(self):
        """Test sorting profiles by first name in descending order"""
        response = self.profile_service.get_profiles({
            "sort_by": "first_name",
            "sort_order": "desc"
        })
        self.assertTrue(response.success)
        results = response.data["results"]

        first_names = [
            result["user"]["first_name"] for result in results if result["user"]
        ]
        self.assertEqual(first_names, sorted(first_names, reverse=True))

    def test_sort_by_last_name_ascending(self):
        """Test sorting profiles by last name in ascending order"""
        response = self.profile_service.get_profiles({
            "sort_by": "last_name",
            "sort_order": "asc"
        })
        self.assertTrue(response.success)
        results = response.data["results"]

        last_names = [
            result["user"]["last_name"] for result in results if result["user"]
        ]
        self.assertEqual(last_names, sorted(last_names))

    def test_sort_by_email_ascending(self):
        """Test sorting profiles by email in ascending order"""
        response = self.profile_service.get_profiles({
            "sort_by": "email",
            "sort_order": "asc"
        })
        self.assertTrue(response.success)
        results = response.data["results"]

        emails = [result["user"]["email"] for result in results if result["user"]]
        self.assertEqual(emails, sorted(emails))

    def test_sort_by_created_at_descending(self):
        """Test sorting profiles by creation date (newest first)"""
        response = self.profile_service.get_profiles({
            "sort_by": "created_at",
            "sort_order": "desc"
        })
        self.assertTrue(response.success)
        results = response.data["results"]

        created_dates = [result["created_at"] for result in results]
        from datetime import datetime

        created_datetimes = [
            datetime.fromisoformat(date.replace("Z", "+00:00"))
            for date in created_dates
        ]
        self.assertEqual(created_datetimes, sorted(created_datetimes, reverse=True))

    def test_sort_by_updated_at_ascending(self):
        """Test sorting profiles by update date (oldest first)"""
        response = self.profile_service.get_profiles({
            "sort_by": "updated_at",
            "sort_order": "asc"
        })
        self.assertTrue(response.success)
        results = response.data["results"]

        updated_dates = [result["updated_at"] for result in results]
        from datetime import datetime

        updated_datetimes = [
            datetime.fromisoformat(date.replace("Z", "+00:00"))
            for date in updated_dates
        ]
        self.assertEqual(updated_datetimes, sorted(updated_datetimes))

    def test_sort_by_invalid_field_uses_default(self):
        """Test that invalid sort field falls back to default sorting"""
        response = self.profile_service.get_profiles({
            "sort_by": "invalid_field",
            "sort_order": "asc"
        })
        self.assertTrue(response.success)
        self.assertGreater(len(response.data["results"]), 0)

    def test_sort_with_search_query(self):
        """Test sorting combined with search functionality"""
        response = self.profile_service.get_profiles({
            "q": "John",
            "sort_by": "last_name",
            "sort_order": "asc"
        })
        self.assertTrue(response.success)
        results = response.data["results"]

        if len(results) > 1:
            last_names = [
                result["user"]["last_name"] for result in results if result["user"]
            ]
            self.assertEqual(last_names, sorted(last_names))

    def test_sort_with_pagination(self):
        """Test sorting combined with pagination"""
        response = self.profile_service.get_profiles({
            "sort_by": "first_name",
            "sort_order": "asc",
            "page": 1, 
            "page_size": 2
        })
        self.assertTrue(response.success)
        self.assertEqual(response.data["page"], 1)
        self.assertEqual(response.data["page_size"], 2)

        results = response.data["results"]
        if len(results) > 1:
            first_names = [
                result["user"]["first_name"] for result in results if result["user"]
            ]
            self.assertEqual(first_names, sorted(first_names))

    def test_default_sort_order_is_ascending(self):
        """Test that if no order is specified, ascending order is used"""
        response = self.profile_service.get_profiles({"sort_by": "first_name"})
        self.assertTrue(response.success)
        results = response.data["results"]

        first_names = [
            result["user"]["first_name"] for result in results if result["user"]
        ]
        self.assertEqual(first_names, sorted(first_names))

    def test_invalid_sort_order_falls_back_to_ascending(self):
        """Test that invalid sort order falls back to ascending"""
        response = self.profile_service.get_profiles({
            "sort_by": "first_name",
            "sort_order": "invalid_order"
        })
        self.assertTrue(response.success)
        results = response.data["results"]

        first_names = [
            result["user"]["first_name"] for result in results if result["user"]
        ]
        self.assertEqual(first_names, sorted(first_names))


class TestUpdateProfile(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.profile_service = ProfileService(user=UserFactory())
        self.profile = ProfileFactory()
        self.facility = FacilityFactory()
        self.title = TitleFactory()
        self.department = DepartmentFactory(facility=self.facility)
        self.user = UserFactory(
            first_name="John",
            last_name="Doe",
        )
        self.profile2 = ProfileFactory(user=self.user)
        self.valid_data = {
            "email": TEST_DEFAULTS.ACTIVE_EMAIL,
            "first_name": "UpdatedFirstName",
            "last_name": "UpdatedLastName",
            "facility_id": self.facility.id,
            "department_id": self.department.id,
            "state": "Updated State",
            "zip_code": "12345",
            "phone_number": "************",
            "birth_country": "Updated Country",
            "address": "123 Updated Address",
            "city": "Updated City",
            "gender": "Updated Gender",
            "date_of_birth": "1990-01-01",
            "age": 35,
            "review_permissions": True,
            "is_test_account": False,
            "access_to_facilities": [self.facility.id],
            "access_to_departments": [self.department.id],
            "title_id": self.title.id,
        }

    def test_update_profile_successful(self):
        response = self.profile_service.update_user_profile(
            self.profile.id, self.valid_data
        )
        if not response.success:
            self.fail(f"Failed to update profile {response}")
        self.assertEqual(response.data["user"]["email"], self.valid_data["email"])
        self.assertEqual(
            response.data["user"]["first_name"], self.valid_data["first_name"]
        )
        self.assertEqual(
            response.data["user"]["last_name"], self.valid_data["last_name"]
        )
        self.assertEqual(
            response.data["facility"]["id"], self.valid_data["facility_id"]
        )
        self.assertEqual(
            response.data["department"]["id"], self.valid_data["department_id"]
        )
        self.assertEqual(response.data["state"], self.valid_data["state"])
        self.assertEqual(response.data["zip_code"], self.valid_data["zip_code"])
        self.assertEqual(response.data["phone_number"], self.valid_data["phone_number"])
        self.assertEqual(
            response.data["birth_country"], self.valid_data["birth_country"]
        )
        self.assertEqual(response.data["address"], self.valid_data["address"])
        self.assertEqual(response.data["city"], self.valid_data["city"])
        self.assertEqual(response.data["gender"], self.valid_data["gender"])
        self.assertEqual(
            response.data["date_of_birth"], self.valid_data["date_of_birth"]
        )
        self.assertEqual(response.data["age"], self.valid_data["age"])

        self.assertEqual(
            response.data["review_permissions"], self.valid_data["review_permissions"]
        )
        self.assertEqual(
            response.data["is_test_account"], self.valid_data["is_test_account"]
        )

        # Assert user data
        user = User.objects.get(email=TEST_DEFAULTS.ACTIVE_EMAIL)

        # Assert profile data
        profile = Profile.objects.get(user=user)
        self.assertIn(self.facility, profile.access_to_facilities.all())
        self.assertIn(self.department, profile.access_to_department.all())
        self.assertEqual(len(profile.access_to_facilities.all()), 1)
        self.assertEqual(len(profile.access_to_department.all()), 1)


# add user to group
class TestUserPermissions(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.group = GroupFactory()
        self.profile_service = ProfileService(user=self.user)

    def test_add_user_to_group(self):
        data = {
            "id": self.group.id,
        }
        response = self.profile_service.add_permissions(self.user.id, data)

        if not response.success:
            self.fail(f"Failed to add user to group {response}")
        self.assertTrue(response.success)
        self.assertTrue(self.user.groups.filter(id=self.group.id).exists())


# remove user from group
class TestRemoveUserPermissions(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.group = GroupFactory()
        self.user.groups.add(self.group)
        self.profile_service = ProfileService(user=self.user)

    def test_remove_user_from_group(self):
        data = {
            "id": self.group.id,
        }
        response = self.profile_service.remove_permissions(self.user.id, data)

        if not response.success:
            self.fail(f"Failed to remove user from group {response}")
        self.assertTrue(response.success)
        self.assertFalse(self.user.groups.filter(id=self.group.id).exists())


class TestActivateDeactivateProfile(TestCase):
    def setUp(self):
        self.profile = ProfileFactory()
        self.user = self.profile.user
        self.profile_service = ProfileService(user=self.user)

    def test_activate_profile_successful(self):
        # Ensure the profile and user are initially inactive
        self.user.is_active = False
        self.user.save()
        self.profile.is_deleted = True
        self.profile.save()

        # Activate the profile
        response = self.profile_service.activate_profile(self.profile.id)

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Profile activated successfully")
        self.user.refresh_from_db()
        self.profile.refresh_from_db()
        self.assertTrue(self.user.is_active)
        self.assertFalse(self.profile.is_deleted)

    def test_deactivate_profile_successful(self):
        # Ensure the profile and user are initially active
        self.user.is_active = True
        self.user.save()
        self.profile.is_deleted = False
        self.profile.save()

        # Deactivate the profile
        response = self.profile_service.deactivate_profile(self.profile.id)

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Profile deactivated successfully")
        self.user.refresh_from_db()
        self.profile.refresh_from_db()
        self.assertFalse(self.user.is_active)


class TestDeleteProfile(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.profile = ProfileFactory()
        self.user = self.profile.user
        self.profile_service = ProfileService(user=self.user)

    def test_delete_profile_successful(self):
        # Ensure the profile and user are initially active
        self.user.is_active = True
        self.user.save()
        self.profile.is_deleted = False
        self.profile.save()

        # Delete the profile
        response = self.profile_service.delete_profile(self.profile.id)

        # Assertions
        self.assertTrue(response.success)
        self.user.refresh_from_db()
        self.profile.refresh_from_db()
        self.assertTrue(self.profile.is_deleted)
