from rest_framework import status
from accounts.models import Profile
from accounts.services.profile.services import ProfileService
from accounts.services.profile.utils import ProfileUtilsService
from accounts.tests.factory import TitleFactory
from base.tests.factory import *
from base.constants import TEST_DEFAULTS
from base.services.permissions.manage_permissions import PermissionsManagement
from base.tests.base_setup import BaseTestSetup
from django.contrib.auth.models import User, Permission
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from unittest.mock import patch
import uuid
from datetime import datetime

from general_patient_visitor.models import GeneralPatientVisitor


utils_service = ProfileUtilsService()
permissions_management = PermissionsManagement()


# TODO: Uncomment and complete the test for updating a profile
class TestCreateProfile(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.facility = FacilityFactory()
        self.department = DepartmentFactory(facility=self.facility)
        self.title = TitleFactory()

        self.permissions = [
            {
                "code_name": "001",
                "name": "Can create",
            },
            {
                "code_name": "002",
                "name": "Can delete",
            },
        ]
        self.model = GeneralPatientVisitor
        self.group = permissions_management.check_group("Test Group 2")
        content_type = ContentType.objects.get_for_model(self.model)
        for perm in self.permissions:
            Permission.objects.get_or_create(
                codename=perm["code_name"],
                name=perm["name"],
                content_type=content_type,
            )

    def test_create_user(self):
        """Test creating a user with combined features: super user, permission group, and access to facility"""
        self._authenticate_user(self.super_user)

        data = {
            "email": TEST_DEFAULTS.ACTIVE_EMAIL,
            "first_name": "Combined",
            "last_name": "User",
            "facility_id": self.facility.id,
            "department_id": self.department.id,
            "permissions": [
                {
                    "feature": "general",
                    "permissions": [
                        {
                            "code_name": "001",
                            "name": "Can create",
                        },
                        {
                            "code_name": "002",
                            "name": "Can delete",
                        },
                    ],
                }
            ],
            "permissions_groups": [
                self.group.data.id,
            ],
            "access_to_facilities": [self.facility.id],
            "access_to_departments": [self.department.id],
            "state": "Test State",
            "zip_code": "12345",
            "phone_number": "************",
            "birth_country": "Test Country",
            "address": "123 Test Address",
            "city": "Test City",
            "gender": "Test Gender",
            "date_of_birth": "1990-01-01",
            "age": 35,
            "review_permissions": True,
            "is_test_account": False,
            "title_id": self.title.id,
        }

        response = self.client.post(f"/api/users/", data, format="json")
        if not response.status_code == status.HTTP_201_CREATED:
            self.fail(response.data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Assert user data
        user = User.objects.get(email=data["email"])
        self.assertEqual(user.first_name, data["first_name"])
        self.assertEqual(user.last_name, data["last_name"])
        self.assertEqual(user.email, data["email"])

        # Assert profile data
        profile = Profile.objects.get(user=user)
        self.assertEqual(profile.facility.id, data["facility_id"])
        self.assertEqual(profile.department.id, data["department_id"])
        self.assertEqual(profile.state, data["state"])
        self.assertEqual(profile.zip_code, data["zip_code"])
        self.assertEqual(profile.phone_number, data["phone_number"])
        self.assertEqual(profile.birth_country, data["birth_country"])
        self.assertEqual(profile.address, data["address"])
        self.assertEqual(profile.city, data["city"])
        self.assertEqual(profile.gender, data["gender"])
        self.assertEqual(str(profile.date_of_birth), data["date_of_birth"])
        self.assertEqual(profile.age, data["age"])
        self.assertEqual(profile.review_permissions, data["review_permissions"])
        self.assertEqual(profile.is_test_account, data["is_test_account"])
        self.assertEqual(profile.title.id, data["title_id"])

        # Assert permissions
        user_permissions = user.user_permissions.all()
        self.assertEqual(len(user_permissions), len(self.permissions))
        for permission in self.permissions:
            self.assertTrue(
                user_permissions.filter(codename=permission["code_name"]).exists()
            )

        # Assert permission group
        self.assertTrue(user.groups.filter(id=self.group.data.id).exists())

        # Assert access to facilities and departments
        self.assertIn(self.facility, profile.access_to_facilities.all())
        self.assertIn(self.department, profile.access_to_department.all())

    """Failure tests"""

    def test_create_user_failure_unauthenticated(self):
        """Test creating a user as a authenticated user"""
        data = {
            "email": TEST_DEFAULTS.ACTIVE_EMAIL,
            "first_name": "SIBOMANA",
            "last_name": "Alphonse",
            "department_id": self.admin_user_dept.id,
            "role": "Quality/Risk Manager",
            "facility": {
                "id": self.admin_user_fac.id,
                "name": self.admin_user_fac.name,
            },
            "permissions": [
                {
                    "facilities": [
                        {"id": fac.id, "name": fac.name}
                        for fac in [self.admin_user_fac]
                    ],
                    "departments": [
                        {"id": dept.id, "name": dept.name}
                        for dept in [self.admin_user_dept]
                    ],
                }
            ],
        }
        response = self.client.post(f"{self.endpoint}/users/new/", data, format="json")
        self.assertNotEqual(response.status_code, status.HTTP_201_CREATED)


class TestDeleteProfile(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.profile = ProfileFactory()

    def test_delete_profile_success(self):
        self._authenticate_user(self.super_user)
        response = self.client.delete(f"/api/users/{self.profile.id}/")

        if not response.status_code == status.HTTP_204_NO_CONTENT:
            self.fail(response.data)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertTrue(Profile.objects.get(id=self.profile.id).is_deleted)


class TestGetProfile(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.profile = ProfileFactory()

    def test_get_profile_success(self):
        """Test getting a user as a super user"""
        self._authenticate_user(self.super_user)
        response = self.client.get(f"/api/users/{self.profile.id}/")
        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # check user data
        self.assertEqual(response.data["user"]["email"], self.profile.user.email)


# TODO: Uncomment and complete the test for updating a profile
class TestUpdateProfile(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self.title = TitleFactory()
        self.profile = ProfileFactory()
        self.facility = FacilityFactory()
        self.new_facility = FacilityFactory()
        self.new_department = DepartmentFactory(facility=self.new_facility)
        self.department = DepartmentFactory(facility=self.facility)
        self.user = UserFactory(
            first_name="John",
            last_name="Doe",
        )

    def test_update_user_success_as_super_user(self):
        """Test updating a user as a super user"""
        self._authenticate_user(self.super_user)
        profile = self.client.post(
            "/api/users/",
            {
                "email": TEST_DEFAULTS.ACTIVE_EMAIL,
                "first_name": "UpdatedFirstName",
                "last_name": "UpdatedLastName",
                "facility_id": self.new_facility.id,
                "title_id": self.title.id,
                "department_id": self.new_department.id,
            },
            format="json",
        )

        if not profile.status_code == status.HTTP_201_CREATED:
            self.fail(profile.data)

        data = {
            "email": TEST_DEFAULTS.ACTIVE_EMAIL,
            "first_name": "UpdatedFirstName",
            "last_name": "UpdatedLastName",
            "facility_id": self.new_facility.id,
            "title_id": self.title.id,
            "department_id": self.new_department.id,
            "state": "Updated State",
            "zip_code": "12345",
            "phone_number": "************",
            "birth_country": "Updated Country",
            "address": "123 Updated Address",
            "city": "Updated City",
            "gender": "Updated Gender",
            "date_of_birth": "1990-01-01",
            "age": 35,
            "review_permissions": True,
            "is_test_account": False,
            "access_to_facilities": [self.facility.id],
            "access_to_departments": [self.department.id],
            "title_id": self.title.id,
        }
        response = self.client.put(
            f"/api/users/{profile.data['id']}/", data, format="json"
        )
        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # check user data
        self.assertEqual(response.data["user"]["email"], data["email"])
        self.assertEqual(response.data["user"]["first_name"], data["first_name"])
        self.assertEqual(response.data["user"]["last_name"], data["last_name"])
        self.assertEqual(response.data["state"], data["state"])
        self.assertEqual(response.data["zip_code"], data["zip_code"])
        self.assertEqual(response.data["phone_number"], data["phone_number"])
        self.assertEqual(response.data["birth_country"], data["birth_country"])
        self.assertEqual(response.data["address"], data["address"])
        self.assertEqual(response.data["city"], data["city"])
        self.assertEqual(response.data["gender"], data["gender"])
        self.assertEqual(response.data["date_of_birth"], data["date_of_birth"])
        self.assertEqual(response.data["age"], data["age"])
        self.assertEqual(
            response.data["review_permissions"], data["review_permissions"]
        )
        self.assertEqual(response.data["is_test_account"], data["is_test_account"])

        # check related fields
        self.assertEqual(response.data["facility"]["id"], self.new_facility.id)
        self.assertEqual(response.data["title"]["id"], self.title.id)

        # Assert user data
        user = User.objects.get(email=TEST_DEFAULTS.ACTIVE_EMAIL)
        # Assert profile data
        profile = Profile.objects.get(user=user)
        self.assertIn(self.facility, profile.access_to_facilities.all())
        self.assertIn(self.department, profile.access_to_department.all())


class TestGetProfiles(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.profile = ProfileFactory()
        self.user = UserFactory(
            first_name="John",
            last_name="Doe",
        )
        self.user2 = UserFactory(email="<EMAIL>")
        self.profiles = [ProfileFactory() for _ in range(2)]
        self.profile2 = [ProfileFactory() for _ in range(2)]
        self.profile4 = ProfileFactory(user=self.user)
        self.profile4 = ProfileFactory(user=self.user2)

    def test_get_profiles_success(self):
        self._authenticate_user(self.super_user)
        response = self.client.get(f"/api/users/")
        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data["results"]), 1)

    def test_get_profiles_with_name(self):
        self._authenticate_user(self.super_user)
        response = self.client.get(f"/api/users/?q=John")
        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data["results"]), 0)

    def test_get_profiles_with_email(self):
        self._authenticate_user(self.super_user)
        response = self.client.get(f"/api/users/?q=<EMAIL>")
        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data["results"]), 0)

    def test_pagination(self):
        self._authenticate_user(self.super_user)
        response = self.client.get(f"/api/users/?page=1&page_size=2")
        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 2)

    def test_sort_by_first_name_ascending_endpoint(self):
        """Test sorting profiles by first name ascending via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?sort_by=first_name&order=asc")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        first_names = [
            result["user"]["first_name"] for result in results if result["user"]
        ]
        self.assertEqual(first_names, sorted(first_names))

    def test_sort_by_first_name_descending_endpoint(self):
        """Test sorting profiles by first name descending via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get(
            "/api/users/?sort_by=first_name&sort_order=desc&page_size=100"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        first_names = [
            result["user"]["first_name"] for result in results if result["user"]
        ]
        # print(f"the firstname: {first_names}")
        # print([result["user"]["first_name"] for result in response.data["results"]])

        self.assertEqual(first_names, sorted(first_names, reverse=True))

    def test_sort_by_last_name_ascending_endpoint(self):
        """Test sorting profiles by last name ascending via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?sort_by=last_name&order=asc")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        last_names = [
            result["user"]["last_name"] for result in results if result["user"]
        ]
        self.assertEqual(last_names, sorted(last_names))

    def test_sort_by_email_ascending_endpoint(self):
        """Test sorting profiles by email ascending via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?sort_by=email&order=asc")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        emails = [result["user"]["email"] for result in results if result["user"]]
        self.assertEqual(emails, sorted(emails))

    def test_sort_by_created_at_descending_endpoint(self):
        """Test sorting profiles by creation date descending via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?sort_by=created_at&order=desc")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        created_dates = [result["created_at"] for result in results]

        created_datetimes = []
        for date in created_dates:
            dt = datetime.fromisoformat(date.replace("Z", "+00:00"))
            dt = dt.replace(second=0, microsecond=0)
            created_datetimes.append(dt)

        expected_sorted = sorted(created_datetimes, reverse=True)

        self.assertEqual(created_datetimes, expected_sorted)

    def test_sort_by_updated_at_ascending_endpoint(self):
        """Test sorting profiles by update date ascending via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?sort_by=updated_at&order=asc")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        updated_dates = [result["updated_at"] for result in results]

        updated_datetimes = []
        for date in updated_dates:
            dt = datetime.fromisoformat(date.replace("Z", "+00:00"))
            dt = dt.replace(second=0, microsecond=0)
            updated_datetimes.append(dt)

        expected_sorted = sorted(updated_datetimes)

        self.assertEqual(updated_datetimes, expected_sorted)

    def test_sort_by_invalid_field_endpoint(self):
        """Test that invalid sort field still returns successful response with default sorting"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?sort_by=invalid_field&order=asc")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data["results"]), 0)

    def test_sort_with_search_query_endpoint(self):
        """Test sorting combined with search query via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?q=John&sort_by=last_name&order=asc")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        if len(results) > 1:
            last_names = [
                result["user"]["last_name"] for result in results if result["user"]
            ]
            self.assertEqual(last_names, sorted(last_names))

    def test_sort_with_pagination_endpoint(self):
        """Test sorting combined with pagination via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get(
            "/api/users/?sort_by=first_name&order=asc&page=1&page_size=2"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["page"], 1)
        self.assertEqual(response.data["page_size"], 2)
        self.assertEqual(len(response.data["results"]), 2)

        results = response.data["results"]
        if len(results) > 1:
            first_names = [
                result["user"]["first_name"] for result in results if result["user"]
            ]
            self.assertEqual(first_names, sorted(first_names))

    def test_default_sort_order_is_ascending(self):
        """Test that if no order is specified, ascending order is used"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?sort_by=first_name")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        first_names = [
            result["user"]["first_name"] for result in results if result["user"]
        ]
        self.assertEqual(first_names, sorted(first_names))

    def test_invalid_sort_order_falls_back_to_ascending(self):
        """Test that invalid sort order falls back to ascending"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?sort_by=first_name&order=invalid")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        first_names = [
            result["user"]["first_name"] for result in results if result["user"]
        ]
        self.assertEqual(first_names, sorted(first_names))

    def test_sort_with_email_filter_endpoint(self):
        """Test sorting combined with email search via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get(
            "/api/users/?q=<EMAIL>&sort_by=email&order=asc"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data["results"]), 0)

        emails = [
            result["user"]["email"]
            for result in response.data["results"]
            if result["user"]
        ]
        self.assertIn("<EMAIL>", emails)

    def test_sort_multiple_parameters_endpoint(self):
        """Test sorting with multiple query parameters via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get(
            "/api/users/?sort_by=created_at&order=desc&page=1&page_size=3&q=John"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["page"], 1)
        self.assertEqual(response.data["page_size"], 3)

        results = response.data["results"]
        if len(results) > 1:
            created_dates = [result["created_at"] for result in results]
            created_datetimes = []
            for date in created_dates:
                dt = datetime.fromisoformat(date.replace("Z", "+00:00"))
                dt = dt.replace(second=0, microsecond=0)
                created_datetimes.append(dt)

            expected_sorted = sorted(created_datetimes, reverse=True)
            self.assertEqual(created_datetimes, expected_sorted)

    def test_sort_case_insensitive_endpoint(self):
        """Test that sorting handles case insensitive properly via API endpoint"""
        self._authenticate_user(self.super_user)
        response = self.client.get("/api/users/?sort_by=first_name&order=asc")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data["results"]

        self.assertGreater(len(results), 0)

        first_names = [
            result["user"]["first_name"] for result in results if result["user"]
        ]
        self.assertEqual(first_names, sorted(first_names))


# add user to group
class TestUserPermissions(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.group = GroupFactory()

    def test_add_user_to_group(self):
        data = {
            "id": self.group.id,
        }
        self._authenticate_user(self.super_user)
        response = self.client.post(
            f"/api/users/{self.user.id}/permissions/",
            data,
            format="json",
        )
        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertTrue(self.user.groups.filter(id=self.group.id).exists())


# remove user from group
class TestRemoveUserPermissions(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.group = GroupFactory()
        self.user.groups.add(self.group)

    def test_remove_user_from_group(self):
        data = {
            "id": self.group.id,
        }
        self._authenticate_user(self.super_user)
        response = self.client.delete(
            f"/api/users/{self.user.id}/permissions/",
            data,
            format="json",
        )
        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertFalse(self.user.groups.filter(id=self.group.id).exists())


class TestActivateDeactivateProfileEndpoints(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.profile = ProfileFactory()

    def test_activate_profile_endpoint_success(self):
        self._authenticate_user(self.super_user)
        data = {"action": "activate"}
        response = self.client.patch(
            f"/api/users/{self.profile.id}/", data, format="json"
        )

        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.profile.refresh_from_db()
        self.assertFalse(self.profile.is_deleted)
        self.assertTrue(self.profile.user.is_active)

    def test_deactivate_profile_endpoint_success(self):
        self._authenticate_user(self.super_user)
        data = {"action": "deactivate"}
        response = self.client.patch(
            f"/api/users/{self.profile.id}/", data, format="json"
        )

        if not response.status_code == status.HTTP_200_OK:
            self.fail(response.data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.profile.refresh_from_db()
        self.assertFalse(self.profile.user.is_active)
