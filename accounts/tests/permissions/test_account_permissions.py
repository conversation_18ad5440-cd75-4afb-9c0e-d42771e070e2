"""
Tests for accounts permissions service.
"""

from django.test import TestCase
from django.contrib.auth.models import Group

from accounts.models import Profile
from accounts.services.permisions import AccountsPermissionsService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.tests.factory import UserFactory, ProfileFactory


class TestCreateProfile(TestCase):
    """Test the AccountsPermissionsService permission checking for creating profiles"""

    def setUp(self):
        """Set up test data"""
        self.permission_manager = PermissionsManagement()
        self.test_user = UserFactory()

    def test_user_without_permissions_cannot_create_account(self):
        """FAIL: Test that user without any permissions cannot create accounts"""
        service = AccountsPermissionsService(self.test_user)
        result = service.can_create_account()

        self.assertFalse(result.success)
        self.assertIn("does not have enough permissions", result.message)

    def test_inactive_user_with_permissions_cannot_create_account(self):
        """FAIL: Test that inactive user cannot create accounts even with permissions"""
        # Make user inactive
        self.test_user.is_active = False
        self.test_user.save()

        # Add permission using PermissionsManagement
        permissions = [{"code_name": "add_profile", "name": "Can add profile"}]
        self.permission_manager.add_permissions_to_user(
            self.test_user, permissions, Profile
        )

        service = AccountsPermissionsService(self.test_user)
        result = service.can_create_account()

        self.assertFalse(result.success)
        self.assertIn("not active", result.message)

    def test_active_user_with_direct_permission_can_create_account(self):
        """SUCCESS: Test that active user with direct permission can create accounts"""
        # Add permission using PermissionsManagement
        permissions = [{"code_name": "add_profile", "name": "Can add profile"}]
        self.permission_manager.add_permissions_to_user(
            self.test_user, permissions, Profile
        )

        service = AccountsPermissionsService(self.test_user)
        result = service.can_create_account()

        self.assertTrue(result.success)
        self.assertIn("can create an account", result.message)

    def test_active_user_in_authorized_group_can_create_account(self):
        """SUCCESS: Test that active user in authorized group can create accounts"""
        # Create and add user to authorized group
        group = Group.objects.create(name="User editors")
        self.test_user.groups.add(group)

        service = AccountsPermissionsService(self.test_user)
        result = service.can_create_account()

        self.assertTrue(result.success)
        self.assertIn("can create an account", result.message)


class TestViewProfile(TestCase):
    """Test the AccountsPermissionsService permission checking for viewing profiles"""

    def setUp(self):
        """Set up test data"""
        self.permission_manager = PermissionsManagement()
        self.test_user = UserFactory()
        self.test_profile = ProfileFactory(user=self.test_user)
        self.other_profile = ProfileFactory()

    def test_user_without_permissions_cannot_view_other_account(self):
        """FAIL: Test that user without permissions cannot view other accounts"""
        service = AccountsPermissionsService(self.test_user)
        result = service.can_view_account(self.other_profile.id)

        self.assertFalse(result.success)
        self.assertIn("does not have enough permissions", result.message)

    def test_inactive_user_cannot_view_account(self):
        """FAIL: Test that inactive user cannot view accounts"""
        self.test_user.is_active = False
        self.test_user.save()

        service = AccountsPermissionsService(self.test_user)
        result = service.can_view_account(self.other_profile.id)

        self.assertFalse(result.success)
        self.assertIn("not active", result.message)

    def test_user_can_view_own_account(self):
        """SUCCESS: Test that user can view their own account"""
        service = AccountsPermissionsService(self.test_user)
        result = service.can_view_account(self.test_profile.id)

        self.assertTrue(result.success)
        self.assertIn("own account", result.message)

    def test_user_with_permission_can_view_account(self):
        """SUCCESS: Test that user with permission can view any account"""
        permissions = [{"code_name": "view_profile", "name": "Can view profile"}]
        self.permission_manager.add_permissions_to_user(
            self.test_user, permissions, Profile
        )

        service = AccountsPermissionsService(self.test_user)
        result = service.can_view_account(self.other_profile.id)

        self.assertTrue(result.success)
        self.assertIn("User can view the account", result.message)


class TestListProfiles(TestCase):
    """Test the AccountsPermissionsService permission checking for listing profiles"""

    def setUp(self):
        """Set up test data"""
        self.permission_manager = PermissionsManagement()
        self.test_user = UserFactory()

    def test_user_without_permissions_cannot_list_accounts(self):
        """FAIL: Test that user without permissions cannot list accounts"""
        service = AccountsPermissionsService(self.test_user)
        result = service.can_list_accounts()

        self.assertFalse(result.success)
        self.assertIn("does not have enough permissions", result.message)

    def test_inactive_user_cannot_list_accounts(self):
        """FAIL: Test that inactive user cannot list accounts"""
        self.test_user.is_active = False
        self.test_user.save()

        service = AccountsPermissionsService(self.test_user)
        result = service.can_list_accounts()

        self.assertFalse(result.success)
        self.assertIn("not active", result.message)

    def test_user_with_permission_can_list_accounts(self):
        """SUCCESS: Test that user with permission can list accounts"""
        permissions = [{"code_name": "view_list", "name": "Can view list"}]
        self.permission_manager.add_permissions_to_user(
            self.test_user, permissions, Profile
        )

        service = AccountsPermissionsService(self.test_user)
        result = service.can_list_accounts()

        self.assertTrue(result.success)
        self.assertIn("can list accounts", result.message)

    def test_user_in_authorized_group_can_list_accounts(self):
        """SUCCESS: Test that user in authorized group can list accounts"""
        group = Group.objects.create(name="Super user")
        self.test_user.groups.add(group)

        service = AccountsPermissionsService(self.test_user)
        result = service.can_list_accounts()

        self.assertTrue(result.success)
        self.assertIn("can list accounts", result.message)


class TestUpdateProfile(TestCase):
    """Test the AccountsPermissionsService permission checking for updating profiles"""

    def setUp(self):
        """Set up test data"""
        self.permission_manager = PermissionsManagement()
        self.test_user = UserFactory()
        self.test_profile = ProfileFactory(user=self.test_user)
        self.other_profile = ProfileFactory()

    def test_user_without_permissions_cannot_update_other_account(self):
        """FAIL: Test that user without permissions cannot update other accounts"""
        service = AccountsPermissionsService(self.test_user)
        result = service.can_update_account(
            self.other_profile.id, {"first_name": "John"}
        )

        self.assertFalse(result.success)
        self.assertIn("does not have enough permissions", result.message)

    def test_inactive_user_cannot_update_account(self):
        """FAIL: Test that inactive user cannot update accounts"""
        self.test_user.is_active = False
        self.test_user.save()

        service = AccountsPermissionsService(self.test_user)
        result = service.can_update_account(
            self.other_profile.id, {"first_name": "John"}
        )

        self.assertFalse(result.success)
        self.assertIn("not active", result.message)

    def test_user_can_update_own_account(self):
        """SUCCESS: Test that user can update their own account"""
        service = AccountsPermissionsService(self.test_user)
        result = service.can_update_account(
            self.test_profile.id, {"first_name": "John"}
        )

        self.assertTrue(result.success)
        self.assertIn("own account", result.message)

    def test_user_with_permission_can_update_account(self):
        """SUCCESS: Test that user with permission can update any account"""
        permissions = [{"code_name": "change_profile", "name": "Can change profile"}]
        self.permission_manager.add_permissions_to_user(
            self.test_user, permissions, Profile
        )

        service = AccountsPermissionsService(self.test_user)
        result = service.can_update_account(
            self.other_profile.id, {"first_name": "John"}
        )

        self.assertTrue(result.success)
        self.assertIn("can update the account", result.message)

    def test_admin_can_update_account(self):
        """SUCCESS: Test that Admin can update accounts per userFlow.md"""
        # Add user to Admin group
        group = Group.objects.create(name="Admin")
        self.test_user.groups.add(group)

        service = AccountsPermissionsService(self.test_user)
        result = service.can_update_account(
            self.other_profile.id, {"first_name": "John"}
        )

        self.assertTrue(result.success)
        self.assertIn("can update the account", result.message)

    def test_regular_user_cannot_update_restricted_fields(self):
        """FAIL: Test that regular user cannot update restricted fields even on own account"""
        service = AccountsPermissionsService(self.test_user)
        result = service.can_update_account(
            self.test_profile.id, {"permissions": ["admin"]}
        )

        self.assertFalse(result.success)
        self.assertIn("Only Super users can modify restricted fields", result.message)

    def test_super_user_can_update_restricted_fields(self):
        """SUCCESS: Test that Super user can update restricted fields"""
        # Add user to Super user group
        group = Group.objects.create(name="Super user")
        self.test_user.groups.add(group)

        service = AccountsPermissionsService(self.test_user)
        result = service.can_update_account(
            self.other_profile.id, {"permissions": ["admin"], "mfa_enabled": True}
        )

        self.assertTrue(result.success)
        self.assertIn("can update the account", result.message)


class TestDeleteProfile(TestCase):
    """Test the AccountsPermissionsService permission checking for deleting profiles"""

    def setUp(self):
        """Set up test data"""
        self.permission_manager = PermissionsManagement()
        self.test_user = UserFactory()
        self.test_profile = ProfileFactory(user=self.test_user)
        self.other_profile = ProfileFactory()

    def test_user_without_permissions_cannot_delete_account(self):
        """FAIL: Test that user without permissions cannot delete accounts"""
        service = AccountsPermissionsService(self.test_user)
        result = service.can_delete_account(self.other_profile.id)

        self.assertFalse(result.success)
        self.assertIn("does not have enough permissions", result.message)

    def test_inactive_user_cannot_delete_account(self):
        """FAIL: Test that inactive user cannot delete accounts"""
        self.test_user.is_active = False
        self.test_user.save()

        service = AccountsPermissionsService(self.test_user)
        result = service.can_delete_account(self.other_profile.id)

        self.assertFalse(result.success)
        self.assertIn("not active", result.message)

    def test_user_cannot_delete_own_account(self):
        """FAIL: Test that user cannot delete their own account"""
        permissions = [{"code_name": "delete_profile", "name": "Can delete profile"}]
        self.permission_manager.add_permissions_to_user(
            self.test_user, permissions, Profile
        )

        service = AccountsPermissionsService(self.test_user)
        result = service.can_delete_account(self.test_profile.id)

        self.assertFalse(result.success)
        self.assertIn("cannot delete their own accounts", result.message)

    def test_user_with_permission_can_delete_other_account(self):
        """SUCCESS: Test that user with permission can delete other accounts"""
        permissions = [{"code_name": "delete_profile", "name": "Can delete profile"}]
        self.permission_manager.add_permissions_to_user(
            self.test_user, permissions, Profile
        )

        service = AccountsPermissionsService(self.test_user)
        result = service.can_delete_account(self.other_profile.id)

        self.assertTrue(result.success)
        self.assertIn("User can delete the account", result.message)

    def test_admin_can_delete_other_account(self):
        """SUCCESS: Test that Admin can delete other accounts per userFlow.md"""
        # Add user to Admin group
        group = Group.objects.create(name="Admin")
        self.test_user.groups.add(group)

        service = AccountsPermissionsService(self.test_user)
        result = service.can_delete_account(self.other_profile.id)

        self.assertTrue(result.success)
        self.assertIn("User can delete the account", result.message)
