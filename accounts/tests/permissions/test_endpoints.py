from accounts.services.permissions.review_groups import ReviewGroupsService
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import (
    ProfileFactory,
    UserFactory,
)
from tasks.tests.factory import ReviewGroupFactory


class TestGetReviewGroups(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.groups = [
            ReviewGroupFactory(),
            ReviewGroupFactory(),
        ]

    def test_get_review_groups_success(self):
        self._authenticate_user(self.admin_user)

        response = self.client.get("/api/permissions/review-groups/")
        if not response.status_code == 200:
            self.fail(f"Failed to get review groups, f{response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), len(self.groups))

    def test_get_review_groups_search(self):
        groups = [
            ReviewGroupFactory(title="Test Group 1"),
            ReviewGroupFactory(title="Test Group 2"),
        ]

        self._authenticate_user(self.admin_user)
        response = self.client.get(
            f"/api/permissions/review-groups/?search=Test Group 1",
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to get review groups with filters, f{response.data}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 4)

    def test_get_review_groups_pagination(self):
        groups = [
            ReviewGroupFactory(),
            ReviewGroupFactory(),
            ReviewGroupFactory(),
        ]
        self._authenticate_user(self.admin_user)
        response = self.client.get(
            f"/api/permissions/review-groups/?page=1&page_size=2",
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to get review groups with pagination, f{response.data}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 2)


class TestCreateReviewGroup(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.data = {
            "title": "Test Group",
            "description": "Test Description",
        }

    def test_create_review_group_success(self):
        self._authenticate_user(self.admin_user)

        response = self.client.post(
            "/api/permissions/review-groups/", data=self.data, format="json"
        )
        if not response.status_code == 201:
            self.fail(f"Failed to create review group, f{response.data}")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["title"], self.data["title"])
        self.assertEqual(response.data["description"], self.data["description"])


class TestGetReviewGroup(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()

    def test_get_review_group_success(self):
        self._authenticate_user(self.admin_user)

        response = self.client.get(f"/api/permissions/review-groups/{self.group.id}/")
        if not response.status_code == 200:
            self.fail(f"Failed to get review group, f{response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["id"], self.group.id)


class TestUpdateReviewGroup(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()
        self.data = {
            "title": "Updated Group",
            "description": "Updated Description",
        }

    def test_update_review_group(self):
        self._authenticate_user(self.admin_user)

        response = self.client.put(
            f"/api/permissions/review-groups/{self.group.id}/",
            self.data,
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to get review group, f{response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["title"], self.data["title"])
        self.assertEqual(response.data["description"], self.data["description"])


class TestDeleteReviewGroup(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.group = ReviewGroupFactory()

    def test_delete_review_group(self):
        self.group.members.clear()
        self._authenticate_user(self.admin_user)

        response = self.client.delete(
            f"/api/permissions/review-groups/{self.group.id}/"
        )
        if not response.status_code == 204:
            self.fail(f"Failed to delete review group, f{response.data}")

        self.assertEqual(response.status_code, 204)


class TextAddMemberToGroup(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.group = ReviewGroupFactory()
        self.member = ProfileFactory()

    def test_add_member_to_group(self):
        self._authenticate_user(self.admin_user)

        response = self.client.patch(
            f"/api/permissions/review-groups/{self.group.id}/members/",
            data={
                "member_id": self.member.id,
                "action": "add",
            },
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to add member to group, f{response.data}")

        self.assertEqual(response.status_code, 200)
        self.group.refresh_from_db()
        self.assertIn(self.member, self.group.members.all())


class TestRemoveGroupMember(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.profile = ProfileFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()

    def test_add_group_member(self):
        self.group.members.add(self.profile)
        self._authenticate_user(self.admin_user)

        response = self.client.patch(
            f"/api/permissions/review-groups/{self.group.id}/members/",
            data={
                "member_id": self.profile.id,
                "action": "remove",
            },
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to remove member from group, f{response.data}")

        self.assertEqual(response.status_code, 200)
        self.group.refresh_from_db()
        self.assertNotIn(self.profile, self.group.members.all())


class TestGetGroupMembers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()
        self.profile = ProfileFactory()
        self.group.members.add(self.profile)

    def test_get_group_members(self):
        self._authenticate_user(self.admin_user)

        response = self.client.get(
            f"/api/permissions/review-groups/{self.group.id}/members/"
        )
        if not response.status_code == 200:
            self.fail(f"Failed to get group members, f{response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertIn(self.profile.id, [member["id"] for member in response.data])
