from django.test import TransactionTestCase
from accounts.services.permissions.review_groups import ReviewGroupsService
from base.tests.factory import *
from tasks.models import ReviewGroups
from tasks.tests.factory import ReviewGroupFactory


# get review groups
class TestGetReviewGroups(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)

    def test_get_review_groups_success(self):
        groups = [
            ReviewGroupFactory(),
            ReviewGroupFactory(),
        ]
        params = {}
        response = self.service.get_review_groups(params)
        if not response.success:
            self.fail(f"Failed to get review groups: {response}")

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), len(groups))

    def test_get_review_groups_search(self):
        groups = [
            ReviewGroupFactory(title="Test Group 1"),
            ReviewGroupFactory(title="Test Group 2"),
        ]

        filters = {
            "q": "Test Group 1",
        }

        response = self.service.get_review_groups(filters)
        if not response.success:
            self.fail(f"Failed to get review groups with filters: {response}")

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)

    def test_get_review_groups_pagination(self):
        groups = [
            ReviewGroupFactory(),
            ReviewGroupFactory(),
            ReviewGroupFactory(),
        ]

        params = {
            "page": 1,
            "page_size": 2,
        }

        response = self.service.get_review_groups(params)
        if not response.success:
            self.fail(f"Failed to get review groups with pagination: {response}")

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 2)


# create review group
class TestCreateReviewGroup(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.data = {
            "title": "Test Group",
            "description": "Test Description",
        }

    def test_create_review_group_success(self):
        response = self.service.create_review_group(data=self.data)
        if not response.success:
            self.fail(f"Failed to create review group: {response}")
        self.assertTrue(response.success)
        self.assertEqual(response.data["title"], self.data["title"])
        self.assertEqual(response.data["description"], self.data["description"])


# update review group
class TestUpdateReviewGroup(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()
        self.data = {
            "title": "Updated Group",
            "description": "Updated Description",
        }

    def test_update_review_group_success(self):
        response = self.service.update_review_group(
            group_id=self.group.id, data=self.data
        )
        if not response.success:
            self.fail(f"Failed to update review group: {response}")
        self.assertTrue(response.success)
        self.assertEqual(response.data["title"], self.data["title"])
        self.assertEqual(response.data["description"], self.data["description"])


# delete review group
class TestDeleteReviewGroup(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()

    def test_delete_review_group_success(self):
        self.group.members.clear()
        response = self.service.delete_review_group(group_id=self.group.id)
        if not response.success:
            self.fail(f"Failed to delete review group: {response}")
        self.assertTrue(response.success)
        self.assertIsNone(ReviewGroups.objects.filter(id=self.group.id).first())


class TestGetReviewGroupDetails(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()

    def test_get_review_group_details_success(self):
        response = self.service.get_group_by_id(group_id=self.group.id)
        if not response.success:
            self.fail(f"Failed to get review group details: {response}")
        self.assertTrue(response.success)
        self.assertEqual(response.data["title"], self.group.title)
        self.assertEqual(response.data["description"], self.group.description)


class TestAddGroupMember(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.profile = ProfileFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()

    def test_add_group_member(self):
        response = self.service.add_member(self.group.id, self.profile.id)
        if not response.success:
            self.fail(f"Failed to add a member to the group: {response}")
        self.assertTrue(response.success)

        self.group.refresh_from_db()
        self.assertIn(self.profile, self.group.members.all())


class TestRemoveGroupMember(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.profile = ProfileFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()
        self.group.members.add(self.profile)

    def test_remove_group_member(self):
        response = self.service.remove_member(self.group.id, self.profile.id)
        if not response.success:
            self.fail(f"Failed to remove a member from the group: {response}")
        self.assertTrue(response.success)

        self.group.refresh_from_db()
        self.assertNotIn(self.profile, self.group.members.all())


class TestGetGroupMembers(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.service = ReviewGroupsService(user=self.user)
        self.group = ReviewGroupFactory()
        self.profile = ProfileFactory()
        self.group.members.add(self.profile)

    def test_get_group_members(self):
        response = self.service.get_review_group_members(group_id=self.group.id)
        if not response.success:
            self.fail(f"Failed to get group members: {response}")
        self.assertTrue(response.success)
