from django.test import TransactionTestCase

from accounts.repositories.permissions.review_groups import ReviewGroupsRepository
from base.tests.factory import (
    ProfileFactory,
    UserFactory,
)
from tasks.models import ReviewGroups
from tasks.tests.factory import ReviewGroupFactory


class TestCreateReviewGroup(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.repo = ReviewGroupsRepository(user=self.user)

    def test_create_review_group_success(self):
        data = {
            "title": "Test Group",
            "description": "Test Description",
        }

        response = self.repo.create_review_group(data)

        if not response.success:
            self.fail(f"Failed to create review group: {response}")
        self.assertTrue(response.success)


class TestGetReviewGroup(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.repo = ReviewGroupsRepository(user=self.user)
        self.group = ReviewGroupFactory()

    def test_get_review_group_success(self):
        response = self.repo.get_review_group(self.group.id)

        if not response.success:
            self.fail(f"Failed to get review group: {response}")
        self.assertTrue(response.success)


class TestUpdateReviewGroup(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.repo = ReviewGroupsRepository(user=self.user)
        self.group = ReviewGroupFactory()

    def test_update_review_group_success(self):
        data = {
            "title": "Updated Group",
            "description": "Updated Description",
        }

        response = self.repo.update_review_group(self.group.id, data)

        if not response.success:
            self.fail(f"Failed to update review group: {response}")
        self.assertTrue(response.success)
        self.assertEqual(response.data.title, "Updated Group")
        self.assertEqual(response.data.description, "Updated Description")


class DeleteReviewGroup(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.profile = ProfileFactory()
        self.repo = ReviewGroupsRepository(user=self.user)
        self.group = ReviewGroupFactory()

        self.group2 = ReviewGroupFactory()

        self.group3 = ReviewGroupFactory()
        # add members to the group 2
        self.group2.members.add(self.profile)
        self.group2.save()
        # add members to the group 3

    def test_delete_review_group_success(self):
        self.group.members.clear()
        response = self.repo.delete_review_group(self.group.id)

        if not response.success:
            self.fail(f"Failed to delete review group: {response}")
        self.assertTrue(response.success)
        self.assertEqual(response.data, None)

    def test_delete_review_group_with_members(self):
        response = self.repo.delete_review_group(self.group2.id)

        if response.success:
            self.fail(f"Failed to delete review group: {response}")
        self.assertFalse(response.success)
        self.assertEqual(ReviewGroups.objects.filter(id=self.group2.id).count(), 1)
