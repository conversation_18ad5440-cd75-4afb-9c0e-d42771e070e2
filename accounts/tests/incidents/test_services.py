"""Test classes for user's incidents"""

from accounts.services.profile.services import ProfileService
from adverse_drug_reaction.tests.factory import AdverseDrugReactionFactory
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import GeneralPatientVisitorFactory
from lost_and_found.tests.factory import LostAndFoundFactory
from medication_error.tests.factory import MedicationErrorFactory
from patient_visitor_grievance.tests.factory import GrievanceFactory
from staff_incident_reports.tests.factory import StaffIncidentReportFactory
from workplace_violence_reports.tests.factory import WorkPlaceViolenceFactory


class TestIncidents(BaseTestSetup):
    def setUp(self):
        """Set up the test case"""
        super().setUp()
        self.service = ProfileService()
        self.incidents = [
            # created_by=self.user_user
            GeneralPatientVisitorFactory(created_by=self.user_user),
            AdverseDrugReactionFactory(created_by=self.user_user),
            LostAndFoundFactory(created_by=self.user_user),
            MedicationErrorFactory(created_by=self.user_user),
            StaffIncidentReportFactory(created_by=self.user_user),
            WorkPlaceViolenceFactory(created_by=self.user_user),
            GrievanceFactory(created_by=self.user_user),
            # # created_by=other_user
            GeneralPatientVisitorFactory(created_by=self.super_user, status="Draft"),
            AdverseDrugReactionFactory(created_by=self.super_user, status="Draft"),
        ]

    def test_incidents_as_user(self):
        # logged in user should be a base user, we getting a base user's incidents

        response = self.service.get_user_incidents(
            profile_id=self.user_user_profile.id,
        )
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 7)

    def test_incidents_as_super_user(self):
        # logged in user should be a super user, we're getting a base user's incidents

        response = self.service.get_user_incidents(
            profile_id=self.user_user_profile.id,
        )
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 7)

    def test_super_user_incidents(self):
        # logged in user should be a super user, we're getting a super user's incidents

        response = self.service.get_user_incidents(
            profile_id=self.super_user_profile.id,
        )
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 2)

    def test_get_drafts_incidents(self):
        # logged in user should be a super user, we're getting a super user's incidents

        response = self.service.get_user_incidents(
            profile_id=self.super_user_profile.id,
            filters={"status": "Draft"},
        )
        self.assertTrue(response.success)
        self.assertGreaterEqual(len(response.data), 2)
