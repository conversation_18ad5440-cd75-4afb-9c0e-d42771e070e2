from django.test import TransactionTestCase
from accounts.services.profile.services import ProfileService
from accounts.tests.factory import ProfileFactory, UserFactory
from base.tests.base_setup import BaseTestSetup
from documents.models import Document


class TestGetUserDocuments(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self._authenticate_user(self.admin_user)
        self.service = ProfileService()
        self.profile = ProfileFactory()
        self.user = self.profile.user

        self.document1 = Document.objects.create(
            name="doc1",
            file_type=".pdf",
            created_by=self.user,
        )
        self.document2 = Document.objects.create(
            name="doc2",
            file_type=".docx",
            created_by=self.user,
        )

    def test_get_user_documents(self):
        response = self.client.get(
            f"/api/users/{self.profile.id}/documents/",
            format="json",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to get user documents: {response.data}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 2)
        doc_names = [doc["name"] for doc in response.data["results"]]
        self.assertIn("doc1", doc_names)
        self.assertIn("doc2", doc_names)
