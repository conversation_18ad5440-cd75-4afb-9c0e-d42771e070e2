from django.test import TransactionTestCase
from accounts.services.profile.services import ProfileService
from accounts.tests.factory import ProfileFactory, UserFactory
from documents.models import Document


class TestGetUserDocuments(TransactionTestCase):
    def setUp(self):
        self.service = ProfileService()
        self.profile = ProfileFactory()
        self.user = self.profile.user

        self.document1 = Document.objects.create(
            name="doc1",
            file_type=".pdf",
            created_by=self.user,
        )
        self.document2 = Document.objects.create(
            name="doc2",
            file_type=".docx",
            created_by=self.user,
        )

    def test_get_user_documents(self):
        response = self.service.get_user_documents(self.profile.id)

        if not response.success:
            self.fail(f"Failed to get user documents: {response.message}")
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 2)
        doc_names = [doc["name"] for doc in response.data["results"]]
        self.assertIn("doc1", doc_names)
        self.assertIn("doc2", doc_names)
