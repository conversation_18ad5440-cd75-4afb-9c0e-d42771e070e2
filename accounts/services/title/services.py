from accounts.models import Title
from accounts.repositories.title import TitleRepository
from accounts.serializers import TitleSerializer
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from django.core.paginator import Paginator
from django.db.models import Q


class TitleService:
    def __init__(self, user, data=None):
        self.user = user
        self.data = data
        self.logging_service = LoggingService()
        self.repository = TitleRepository(user=self.user)

    def get_titles(self, params) -> APIResponse:
        """
        Retrieves a paginated, optionally filtered and sorted list of Title objects.
        Args:
            params (dict): A dictionary of query parameters which may include:
                - "page" (int, optional): The page number for pagination (default: 1).
                - "page_size" (int, optional): The number of items per page (default: 10).
                - "q" (str, optional): A search query to filter titles by name or description.
                - "order_by" (str, optional): The field to sort by ("name" or "created_at").
                - "sort_order" (str, optional): The sorting order ("asc" or "desc", default: "asc").
        Returns:
            APIResponse: An object containing:
                - success (bool): Indicates if the operation was successful.
                - data (dict or None): Contains paginated results and metadata if successful, else None.
                    - "results": List of serialized Title objects.
                    - "count": Total number of matching Title objects.
                    - "page": Current page number.
                    - "page_size": Number of items per page.
                    - "has_next": Boolean indicating if there is a next page.
                    - "has_previous": Boolean indicating if there is a previous page.
                - message (str): A message describing the result.
        Raises:
            Handles all exceptions internally and logs errors using the logging service.
        """

        try:
            titles = Title.objects.all()

            # Extracting parameters
            page = int(params.get("page", 1))
            page_size = int(params.get("page_size", 10))
            search_query = params.get("q", None)
            sort_by = params.get("order_by", None)
            sorting_order = params.get("sort_order", "asc")

            # Filtering and ordering
            if search_query:
                titles = titles.filter(
                    Q(name__icontains=search_query)
                    | Q(description__icontains=search_query)
                )

            # sorting the titles by name or created_at
            if sort_by:
                if sort_by == "name":
                    titles = titles.order_by(
                        "name" if sorting_order == "asc" else "-name"
                    )
                elif sort_by == "created_at":
                    titles = titles.order_by(
                        "created_at" if sorting_order == "asc" else "-created_at"
                    )
                else:
                    return APIResponse(
                        success=False,
                        data=None,
                        message="Invalid sorting field provided.",
                    )
            # Paginate results
            total_count = titles.count()
            start = (page - 1) * page_size
            end = start + page_size
            paginated_titles = titles[start:end]
            has_next = end < total_count
            has_previous = start > 0

            serializer = TitleSerializer(paginated_titles, many=True)

            data = {
                "results": serializer.data,
                "count": total_count,
                "page": page,
                "page_size": page_size,
                "has_next": has_next,
                "has_previous": has_previous,
            }
            return APIResponse(
                success=True,
                data=data,
                message="Titles retrieved successfully.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Internal server error occurred while retrieving titles.",
            )

    def get_title_by_id(self, title_id) -> APIResponse:
        """
        Retrieve a title by its ID.
        Args:
            title_id (int): The ID of the title to retrieve.
        Returns:
            APIResponse: A response object containing the title data.
        """
        try:
            response = self.repository.get_title_by_id(title_id)
            if not response.success:
                return response

            serializer = TitleSerializer(response.data)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Title retrieved successfully.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Internal server error occurred while retrieving title.",
            )

    def create_title(self, data) -> APIResponse:
        """
        Create a new title.
        Args:
            data (dict): A dictionary containing the title data.
        Returns:
            APIResponse: A response object containing the created title data.
        """
        try:
            response = self.repository.create_title(data)
            if not response.success:
                return response

            serializer = TitleSerializer(response.data)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Title created successfully.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Internal server error occurred while creating title.",
            )

    def update_title(self, title_id, data) -> APIResponse:
        """
        Update an existing title by its ID.
        Args:
            title_id (int): The ID of the title to update.
        Returns:
            APIResponse: A response object containing the updated title data.
        """
        try:
            response = self.repository.update_title(title_id, data)
            if not response.success:
                return response

            serializer = TitleSerializer(response.data)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Title updated successfully.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Internal server error occurred while updating title.",
            )

    def delete_title(self, title_id) -> APIResponse:
        """
        Delete a title by its ID.
        Args:
            title_id (int): The ID of the title to delete.
        Returns:
            APIResponse: A response object indicating the success or failure of the deletion.
        """
        try:
            response = self.repository.delete_title(title_id)
            if not response.success:
                return response

            return APIResponse(
                success=True,
                data=None,
                message="Title deleted successfully.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Internal server error occurred while deleting title.",
            )
