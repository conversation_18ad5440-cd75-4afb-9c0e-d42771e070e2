from typing import Dict, Any, Optional, List
from accounts.serializers import UserProfileSerializer
from accounts.services.user_profile.query import UserIncidentDataQuery
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from accounts.models import Profile, UserProfile

logging_service = LoggingService()


class UserProfileService:

    def create_user_profile(self, data: Dict[str, Any]) -> APIResponse:
        pass

    def update_user_profile(self, patient_id: int, data: Dict[str, Any]) -> APIResponse:
        pass

    def get_user_profile(self, patient_id: int) -> APIResponse:
        pass

    def list_user_profiles(
        self, filters: Optional[Dict[str, Any]] = None
    ) -> APIResponse:
        pass

    def delete_user_profile(self, patient_id: int) -> APIResponse:
        pass

    def search_user_profiles(self, query: str) -> APIResponse:
        pass

    def get_or_create_profile(self, data) -> APIResponse:
        # This method should be implemented to get or create a user profile
        """
        Special cases:
        1. If user_id is provided in data, get the user profile with that user_id.
        2. If user_id is not provided in data, create a new user profile.
        """
        try:
            if "user_id" in data:
                user_profile = UserProfile.objects.get(id=data["user_id"])
                if not user_profile:
                    return APIResponse(
                        success=False,
                        message="User profile not found.",
                    )
                return APIResponse(
                    success=True,
                    data=user_profile,
                    message="User profile retrieved successfully.",
                )
            else:
                required_fields = ["first_name", "last_name", "profile_type"]
                missing_fields = logging_service.check_required_fields(
                    data, required_fields
                )
                if missing_fields:
                    return APIResponse(
                        success=False,
                        message=f"Missing required fields: {missing_fields}",
                    )
                user_profile = UserProfile.objects.create(**data)
                user_profile.save()
                return APIResponse(
                    success=True,
                    data=user_profile,
                    message="User profile created successfully.",
                )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while getting or creating the user profile.",
            )
