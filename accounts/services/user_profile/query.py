# class for getting user profile data


# incidents


from adverse_drug_reaction.models import AdverseDrugReaction
from adverse_drug_reaction.new_serializers import GetAdverseDrugReactionSerializer
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from general_patient_visitor.models import GeneralPatientVisitor
from general_patient_visitor.new_serializers import GetGeneralPatientVisitorSerializer

from lost_and_found.models import LostAndFound
from lost_and_found.new_serializers import GetLostAndFoundSerializer
from medication_error.models import MedicationError
from medication_error.new_serializers import GetMedicationErrorSerializer
from patient_visitor_grievance.models import Grievance
from patient_visitor_grievance.new_serializers import (
    GetPatientVisitorGrievanceSerializer,
)

from staff_incident_reports.models import StaffIncidentReport
from staff_incident_reports.new_serializers import GetStaffIncidentReportSerializer
from tasks.services.query import TasksQueryService
from workplace_violence_reports.models import WorkPlaceViolence
from workplace_violence_reports.new_serializers import GetWorkplaceViolenceSerializer


class UserIncidentDataQuery:
    def __init__(self, user_profile):
        self.user_profile = user_profile
        self.logging_service = LoggingService()

    """
    Class for getting user profile data
    """

    def __init__(self, user_profile):
        self.user_profile = user_profile

    def get_user_incidents(self, filters=None) -> APIResponse:
        """
        Get all incidents for a user.
        """
        try:
            user_incidents = []
            models = [
                {
                    "name": "General Patient Visitor",
                    "model": GeneralPatientVisitor,
                    "serializer": GetGeneralPatientVisitorSerializer,
                },
                {
                    "name": "Adverse Drug Reaction",
                    "model": AdverseDrugReaction,
                    "serializer": GetAdverseDrugReactionSerializer,
                },
                {
                    "name": "Lost and Found",
                    "model": LostAndFound,
                    "serializer": GetLostAndFoundSerializer,
                },
                {
                    "name": "Medication Error",
                    "model": MedicationError,
                    "serializer": GetMedicationErrorSerializer,
                },
                {
                    "name": "Staff Incident Report",
                    "model": StaffIncidentReport,
                    "serializer": GetStaffIncidentReportSerializer,
                },
                {
                    "name": "Grievance",
                    "model": Grievance,
                    "serializer": GetPatientVisitorGrievanceSerializer,
                },
                {
                    "name": "workplace violence",
                    "model": WorkPlaceViolence,
                    "serializer": GetWorkplaceViolenceSerializer,
                },
            ]

            for model_info in models:
                model = model_info["model"]
                serializer = model_info["serializer"]

                # Filter incidents created by the user
                incidents = model.objects.filter(
                    created_by=self.user_profile.user,
                )

                # Apply additional filters if provided
                if filters:
                    if "status" in filters:
                        incidents = incidents.filter(status=filters["status"])

                if incidents.exists():

                    # Serialize the incidents
                    incidents = serializer(incidents, many=True).data
                    # Append the serialized data to the user_incidents list
                    user_incidents.append(
                        {
                            "name": model_info["name"],
                            "incidents": incidents,
                        }
                    )

            return APIResponse(
                success=True,
                data=user_incidents,
                message="User incidents retrieved successfully.",
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while getting user incidents.",
            )


class UserComplaintsQuery:
    """
    Class for handling user complaints
    """


class UserDocumentsQuery:
    """
    Class for handling user documents
    """


class UserTasksQuery:
    """
    Class for handling user-specific task queries
    """

    def __init__(self):
        self.logging_service = LoggingService()
        self.query_service = TasksQueryService()

    def get_user_tasks(self, logged_in_user, user_id, filters=None) -> APIResponse:
        """
        Get all tasks for a specific user.

        Args:
            logged_in_user: The user requesting the tasks
            user_id: ID of the user whose tasks are to be retrieved
            filters: Optional filters to apply to the task query

        Returns:
            APIResponse with tasks data
        """
        try:
            if filters is None:
                filters = {}

            filters["reviewer_id"] = user_id

            response = self.query_service.get_tasks(filters)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                )
            return response
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving user tasks.",
                code=500,
            )

    def get_self_tasks(self, logged_in_user, filters=None) -> APIResponse:
        """
        Get all tasks created by the logged-in user.

        Args:
            logged_in_user: The user requesting the tasks
            filters: Optional filters to apply to the task query

        Returns:
            APIResponse with tasks data
        """
        try:
            if filters is None:
                filters = {}

            filters["created_by"] = logged_in_user.id

            response = self.query_service.get_tasks(filters)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                )
            return response
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving self tasks.",
                code=500,
            )
