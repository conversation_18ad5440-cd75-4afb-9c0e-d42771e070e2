# permissions service


from accounts.models import Profile
from base.services.responses import APIResponse, RepositoryResponse
from typing import Type
from django.contrib.auth.models import User


class AccountsPermissionsService:
    def __init__(self, user: Type[User]):
        self.user = user

    def can_create_account(self) -> APIResponse:
        """
        Checks if a user can create an account

        Conditions: User must be active AND have one of these:
        - User must have the 'accounts.add_profile' permission
        - User must be in a group called 'User editors' or 'Super user'
        """
        # User must be active to perform any actions
        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        # Check if user has direct permission or is in authorized groups
        if self._user_has_per(["accounts.add_profile"]) or self._user_in_group(
            ["User editors", "Super user"]
        ):
            return RepositoryResponse(
                success=True, message="User can create an account", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to create an account",
            data="User must be active, have 'add permissions' permission or be in 'User editors' or 'Super user' group",
        )

    def can_view_account(self, account_id) -> APIResponse:
        """
        Checks if a user can view an account
        Conditions:
        - User must be active
        - User can always view their own account
        - For other accounts: User must have the 'accounts.view_profile' permission
        - For other accounts: User must be in a group called 'User editors' or 'Super user' Or 'Admin' or 'Director' or 'Manager'
        """
        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        # Check if user is viewing their own account
        try:
            profile = Profile.objects.get(id=account_id)
            if profile.user.id == self.user.id:
                return RepositoryResponse(
                    success=True, message="User can view their own account", data=None
                )
        except Profile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Account does not exist",
            )

        # For viewing other accounts, check permissions
        if self._user_has_per(["accounts.view_profile"]) or self._user_in_group(
            ["User editors", "Super user", "Admin", "Director", "Manager"]
        ):
            return RepositoryResponse(
                success=True, message="User can view the account", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to view the account",
            data="User must be active, have 'view permissions' permission or be in 'User editors' or 'Super user' or 'Admin' or 'Director' or 'Manager' group",
        )

    def can_list_accounts(self) -> APIResponse:
        """
        Checks if a user can list accounts
        Conditions:
        - User must be active
        - User must have the 'accounts.view_list' permission
        - User must be in a group called 'User editors' or 'Super user' Or 'Admin' or 'Director' or 'Manager'
        """

        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        if self._user_has_per(["accounts.view_list"]) or self._user_in_group(
            ["User editors", "Super user", "Admin", "Director", "Manager"]
        ):
            return RepositoryResponse(
                success=True, message="User can list accounts", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to list accounts",
            data="User must be active, have 'list permissions' permission or be in 'User editors' or 'Super user' Or 'Admin' or 'Director' or 'Manager' group",
        )

    def can_update_account(self, account_id, request_data) -> APIResponse:
        """
        Checks if a user can update an account

        Conditions:
        - User must be active
        - User can update their own account (basic fields only)
        - OR User must have the 'accounts.change_profile' permission
        - OR User must be in a group called 'User editors' or 'Super user'

        Exceptional cases:
        - If restricted fields (permissions, user, totp, mfa_enabled, is_test_account,
          is_deleted, deleted_at, created_at, updated_at, created_by, updated_by)
          are in request_data, ONLY 'Super user' can modify these fields.
        """
        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        # Define restricted fields that only Super users can modify
        restricted_fields = {
            "permissions",
            "user",
            "totp",
            "mfa_enabled",
            "is_test_account",
            "is_deleted",
            "deleted_at",
            "created_at",
            "updated_at",
            "created_by",
            "updated_by",
        }

        # Check if any restricted fields are being modified
        has_restricted_fields = bool(
            restricted_fields.intersection(request_data.keys())
        )

        # If restricted fields are being modified, only Super users are allowed
        if has_restricted_fields:
            if not self._user_in_group(["Super user"]) and not self.user.is_superuser:
                return RepositoryResponse(
                    success=False,
                    message="Only Super users can modify restricted fields (permissions, user, system fields)",
                )

        # Check if user owns the account (can update basic fields)
        if self._user_owns_account(account_id):
            return RepositoryResponse(
                success=True, message="User can update their own account", data=None
            )

        # Check if user has permission or is in authorized groups for other accounts
        # Note: User Editors can create/edit/deactivate user accounts per userFlow.md
        if self._user_has_per(["accounts.change_profile"]) or self._user_in_group(
            ["User editors", "Super user", "Admin"]
        ):
            return RepositoryResponse(
                success=True, message="User can update the account", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to update this account",
            data="User must be active, have 'change permissions' permission or be in 'User editors', 'Admin', or 'Super user' group",
        )

    def can_delete_account(self, account_id) -> APIResponse:
        """
        Checks if a user can delete an account

        Conditions:
        - User must be active
        - User must have the 'accounts.delete_profile' permission
        - OR User must be in a group called 'User editors', 'Admin', or 'Super user'

        Security Note: Users cannot delete their own accounts for security reasons
        """
        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        # Prevent users from deleting their own accounts
        if self._user_owns_account(account_id):
            return RepositoryResponse(
                success=False,
                message="Users cannot delete their own accounts",
            )

        # Check if user has permission or is in authorized groups
        # Note: User Editors can delete user accounts per userFlow.md
        if self._user_has_per(["accounts.delete_profile"]) or self._user_in_group(
            ["User editors", "Super user", "Admin"]
        ):
            return RepositoryResponse(
                success=True, message="User can delete the account", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to delete this account",
            data="User must be active, have 'delete permissions' permission or be in 'User editors', 'Admin', or 'Super user' group",
        )

    def _user_has_per(self, perms):
        """
        Check if user has any of the specified permissions.
        perms should be a list of permission strings in format 'app_label.permission_codename'
        """
        return any(self.user.has_perm(perm) for perm in perms)

    def _user_in_group(self, group_names):
        """
        Check if user is in any of the specified groups.
        group_names should be a list of group name strings
        """
        return self.user.groups.filter(name__in=group_names).exists()

    def _user_owns_account(self, account_id):
        """Check if the user owns the specified account."""
        try:
            account = Profile.objects.get(id=account_id)
            owns_account = account.user.id == self.user.id
        except Profile.DoesNotExist:
            owns_account = False
        return owns_account
