from accounts.models import Profile
from accounts.repositories.permissions.review_groups import ReviewGroupsRepository
from accounts.serializers import (
    GetProfileSerializer,
)
from accounts.services.profile.services import ProfileService
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from django.db.models import Count
from django.db.models import Q

from tasks.models import ReviewGroups
from tasks.serializers import GetReviewGroupSerializer


class ReviewGroupsService:
    """
    Service to manage review groups.
    """

    def __init__(self, user):
        self.user = user
        self.repository = ReviewGroupsRepository(user=self.user)
        self.logging_service = LoggingService()
        self.profile_service = ProfileService()

    def get_review_groups(self, params) -> APIResponse:
        """
        Retrieves a paginated, searchable, and sortable list of review groups.

        Args:
            params (dict): A dictionary of query parameters which may include:
                - "q" (str, optional): Search query to filter groups by title or description.
                - "sort_by" (str, optional): Field to sort by ("title" or "created_at"). Defaults to "created_at".
                - "page" (int, optional): Page number for pagination. Defaults to 1.
                - "page_size" (int, optional): Number of items per page. Defaults to 10.

        Returns:
            APIResponse: An object containing:
                - success (bool): Whether the operation was successful.
                - message (str): A message describing the result.
                - data (dict or None): If successful, a dictionary with:
                    - "results": List of serialized review groups, each annotated with member count.
                    - "count": Total number of review groups matching the query.
                    - "page": Current page number.
                    - "page_size": Number of items per page.
                    - "total_pages": Total number of pages.
                    - "has_next": Whether there is a next page.
                    - "has_previous": Whether there is a previous page.
                - code (int): HTTP-like status code (200 for success, 500 for failure).

        Notes:
            - Logs errors using the logging service if an exception occurs.
            - Returns a failure response with code 500 in case of exceptions.
        """

        try:
            groups = ReviewGroups.objects.all().prefetch_related("members")

            # search by name or description
            search_query = params.get("q", "")
            if search_query:
                groups = groups.filter(
                    Q(title__icontains=search_query)
                    | Q(description__icontains=search_query)
                )

            # sort by title or created_at
            sort_by = params.get("sort_by", "created_at")
            sort_order = params.get("sort_order", "asc")

            if sort_by == "title":
                groups = groups.order_by("-title" if sort_order == "asc" else "title")

            else:
                groups = groups.order_by(
                    "-created_at" if sort_order == "asc" else "created_at"
                )

            # paginate results
            page = int(params.get("page", 1))
            page_size = int(params.get("page_size", 10))
            total_count = groups.count()
            start = (page - 1) * page_size
            end = start + page_size
            groups = groups[start:end]
            has_next = end < total_count
            has_previous = start > 0

            # annotate with member count
            annotated_data = groups.annotate(members_count=Count("members"))

            serializer = GetReviewGroupSerializer(
                annotated_data,
                many=True,
            )

            data = {
                "results": serializer.data,
                "count": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size,
                "has_next": has_next,
                "has_previous": has_previous,
            }
            return APIResponse(
                success=True,
                message="Retrieved review groups successfully",
                data=data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to get review groups",
                data=None,
                code=500,
            )

    def create_review_group(self, data):
        """Create a new reive group"""
        try:
            data["created_by_id"] = self.user.id
            response = self.repository.create_review_group(data=data)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = GetReviewGroupSerializer(response.data)
            return APIResponse(
                success=True,
                message="Created review group successfully",
                data=serializer.data,
                code=201,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to create review group",
                data=None,
                code=500,
            )

    def get_group_by_id(self, group_id):
        """Get a review group by ID"""
        try:
            response = self.repository.get_review_group(group_id=group_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = GetReviewGroupSerializer(response.data)
            return APIResponse(
                success=True,
                message="Retrieved review group successfully",
                data=serializer.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to get review group",
                data=None,
                code=500,
            )

    def update_review_group(self, group_id, data):
        """Update a review group"""
        try:
            data["updated_by"] = self.user
            response = self.repository.update_review_group(group_id=group_id, data=data)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = GetReviewGroupSerializer(response.data)
            return APIResponse(
                success=True,
                message="Updated review group successfully",
                data=serializer.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to update review group",
                data=None,
                code=500,
            )

    def delete_review_group(self, group_id):
        """Delete a review group"""
        try:
            response = self.repository.delete_review_group(group_id=group_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            return APIResponse(
                success=True,
                message="Deleted review group successfully",
                data=None,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to delete review group",
                data=None,
                code=500,
            )
        except ReviewGroups.DoesNotExist:
            return APIResponse(
                success=False,
                message="Review group does not exist",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to delete review group",
                data=None,
                code=500,
            )

    def add_member(self, group_id, member_d) -> APIResponse:
        """Add a member to a review group"""
        try:
            # check if the member exists
            profile = Profile.objects.get(id=member_d)

            group_response = self.repository.get_review_group(group_id)
            if not group_response.success:
                return APIResponse(
                    success=False,
                    message=group_response.message,
                    data=None,
                    code=400,
                )
            group_response.data.members.add(profile)
            group_response.data.save()

            return APIResponse(
                message="Member added to the group successfully",
                data=None,
                success=True,
                code=200,
            )
        except Profile.DoesNotExist:
            return APIResponse(
                message="Profile does not exist",
                success=False,
                data=None,
                code=404,
            )
        except Profile.MultipleObjectsReturned:
            return APIResponse(
                message="User have conflicting profiles",
                success=False,
                data=None,
                code=409,
            )
        except Exception as e:

            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to add member to review group",
                data=None,
                code=500,
            )

    def remove_member(self, group_id, member_id) -> APIResponse:
        """Remove a member from a review group"""
        try:
            # check if the member exists
            profile = Profile.objects.get(id=member_id)

            group_response = self.repository.get_review_group(group_id)
            if not group_response.success:
                return APIResponse(
                    success=False,
                    message=group_response.message,
                    data=None,
                    code=400,
                )
            group_response.data.members.remove(profile)
            group_response.data.save()

            return APIResponse(
                message="Member removed from the group successfully",
                data=None,
                success=True,
                code=200,
            )
        except Profile.DoesNotExist:
            return APIResponse(
                message="Profile does not exist",
                success=False,
                data=None,
                code=404,
            )
        except Profile.MultipleObjectsReturned:
            return APIResponse(
                message="User have conflicting profiles",
                success=False,
                data=None,
                code=409,
            )
        except Exception as e:

            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to remove member from review group",
                data=None,
                code=500,
            )

    def get_review_group_members(self, group_id):
        """Get all members of a review group"""
        try:
            response = self.repository.get_review_group(group_id=group_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            profiles = response.data.members.all()

            serializer = GetProfileSerializer(profiles, many=True)
            return APIResponse(
                success=True,
                message="Retrieved review group members successfully",
                data=serializer.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to get review group members",
                data=None,
                code=500,
            )
