import requests
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.services.responses import APIResponse
from django.contrib.auth.models import User, Group
import os
from django.db.models import Q


logging_service = LoggingService()


class ProfileWorkFlow:

    def get_user_permissions(self, user: User, user_id):
        try:
            permissions_groups = PermissionsManagement().get_permissions_groups(
                user, user_id
            )
            permissions = PermissionsManagement().get_user_permissions(user_id)
            data = {
                "groups": permissions_groups.data,
                "permissions": permissions.data,
            }
            return APIResponse(
                data=data,
                success=True,
                message="Permissions retrieved successfully",
                code=200,
            )

        except User.DoesNotExist:
            return APIResponse(
                success=False,
                message="User not found",
                data=None,
                code=400,
            )
        except Exception as e:

            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def add_permissions(self, user_id, data) -> APIResponse:

        errors = []
        try:
            user = User.objects.get(id=user_id)
            for group in data:
                try:
                    group_obj = Group.objects.get(id=group["id"])
                    user.groups.add(group_obj)

                except Group.DoesNotExist:
                    errors.append(f"Group is not found")
            if errors:
                logging_service.log_error(errors)
                return APIResponse(
                    data=errors,
                    success=False,
                    message="Failed to add permissions",
                    code=409,
                )
            return APIResponse(
                data=data,
                success=True,
                message="Permissions added successfully",
                code=200,
            )
        except User.DoesNotExist:
            return APIResponse(
                success=False,
                message="User not found",
                data=None,
                code=400,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error while adding permission",
                data=None,
                code=500,
            )

    def remove_permissions(self, user_id, data):
        errors = []
        try:
            user = User.objects.get(id=user_id)
            for group in data:
                try:
                    group_obj = Group.objects.get(id=group["id"])
                    user.groups.remove(group_obj)
                except Group.DoesNotExist:
                    errors.append(f"Group with id {group['id']} is not found")
            if errors:
                return APIResponse(
                    data=errors,
                    success=False,
                    message="Failed to remove permissions",
                    code=409,
                )
            return APIResponse(
                data=data,
                success=True,
                message="Permissions removed successfully",
                code=200,
            )
        except User.DoesNotExist:
            return APIResponse(
                success=False,
                message="User not found",
                data=None,
                code=400,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
            )

    def post_user_to_external_api(self, user_data):
        """
        Posts user data to the external CHMC auth backend API.
        """
        url = f"{os.getenv('CHMC_DOMAIN')}/api/accounts/backend/users"
        headers = {
            "X-API-KEY": os.getenv("CHMC_API_KEY"),
            "Content-Type": "application/json",
        }
        try:
            response = requests.post(url, json=user_data, headers=headers)
            if not response.status_code == 201:
                return APIResponse(
                    success=False,
                    message=response.json()["error"],
                    data=None,
                    code=response.status_code,
                )
            return APIResponse(
                data=response.json(),
                success=True,
                message="User created successfully",
                code=201,
            )
        except requests.exceptions.RequestException as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occured",
                data=None,
                code=500,
            )

    def update_user_on_external_api(self, user_id, user_data):
        """
        Updates user data on the external CHMC auth backend API.
        """
        url = f"{os.getenv('CHMC_DOMAIN')}/api/accounts/backend/users/{user_id}"
        headers = {
            "X-API-KEY": os.getenv("CHMC_API_KEY"),
            "Content-Type": "application/json",
        }
        try:
            response = requests.patch(url, json=user_data, headers=headers)
            if not response.status_code == 200:
                return APIResponse(
                    success=False,
                    message=response.json()["error"],
                    data=None,
                    code=response.status_code,
                )
            return APIResponse(
                data=response.json(),
                success=True,
                message="User updated successfully",
                code=200,
            )
        except requests.exceptions.RequestException as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occured",
                data=None,
                code=500,
            )

    # get external user

    # delete eternal user
