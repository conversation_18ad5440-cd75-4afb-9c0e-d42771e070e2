from accounts.models import Profile, Title
from accounts.services.profile.work_flow import ProfileWorkFlow
from base.models import Department, Facility
from base.services.auth import generate_random_password
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.services.responses import APIResponse, RepositoryResponse
from django.contrib.auth.models import User
from typing import Type
import requests
import os

from base.tests.base_setup import CleanUpDatabase
from base.utils.model_mapping import MODEL_MAPPING


logging_service = LoggingService()
workflow_service = ProfileWorkFlow()


class ProfileUtilsService:
    def handle_user(self, data) -> APIResponse:
        """
        Handles the creation of a new user and their profile.
        """
        try:
            email = data.get("email")
            first_name = data.get("first_name")
            last_name = data.get("last_name")

            if (
                User.objects.filter(email=email).exists()
                or User.objects.filter(username=email).exists()
            ):
                return APIResponse(
                    success=False,
                    message="User with the same email already exists",
                    data=None,
                )

            new_user = User.objects.create_user(
                username=email,
                email=email,
                first_name=first_name,
                last_name=last_name,
            )
            return APIResponse(
                success=True,
                message="User created successfully",
                data=new_user,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error creating user",
                data=None,
            )

    def handle_facility(self, data, new_user) -> APIResponse:
        """
        Handles the assignment of a facility to a new user.
        """
        try:
            facility_obj = Facility.objects.get(id=data.get("facility_id"))
            facility_obj.staff_members.add(new_user)
            return APIResponse(
                success=True,
                message="Facility assigned successfully",
                data=facility_obj,
            )
        except Facility.DoesNotExist:
            logging_service.log_error(
                f"Facility with ID {data.get('facility_id')} does not exist."
            )
            return APIResponse(
                success=False,
                message="Facility does not exist",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error assigning facility",
                data=None,
            )

    def handle_update_facility(self, data, new_user) -> APIResponse:
        """
        If the user is already assigned to a facility, this method will update the facility.
        If the user is not assigned to a facility, this method will assign the user to the facility.
        """
        try:
            facility_obj = Facility.objects.get(id=data.pop("facility_id", None))
            if new_user in facility_obj.staff_members.all():
                # User is already assigned to the facility, update the facility
                facility_obj.staff_members.remove(new_user)
                return APIResponse(
                    success=True,
                    message="User removed from facility successfully",
                    data=facility_obj,
                )
            else:
                # User is not assigned to the facility, assign the user to the facility
                facility_obj.staff_members.add(new_user)
                return APIResponse(
                    success=True,
                    message="User assigned to facility successfully",
                    data=facility_obj,
                )
        except Facility.DoesNotExist:
            logging_service.log_error(
                f"Facility with ID {data.get('facility_id')} does not exist."
            )
            return APIResponse(
                success=False,
                message="Facility does not exist",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error updating facility",
                data=None,
            )

    def handle_department(self, data, new_user, facility_obj) -> APIResponse:
        """
        Handles the assignment of a department to a new user.
        """
        try:
            department_obj = Department.objects.get(
                id=data.get("department_id"),
                facility=facility_obj,
            )
            department_obj.members.add(new_user)
            return APIResponse(
                success=True,
                message="Department assigned successfully",
                data=department_obj,
            )
        except Department.DoesNotExist:
            logging_service.log_error(
                f"Department with ID {data.get('department_id')} does not exist."
            )
            return APIResponse(
                success=False,
                message="Department does not exist",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error assigning department",
                data=None,
            )

    def handle_update_department(self, data, new_user, facility_obj) -> APIResponse:
        """
        If the user is already assigned to a department, this method will update the department.
        If the user is not assigned to a department, this method will assign the user to the department.
        """
        try:
            department_obj = Department.objects.get(
                id=data.pop("department_id", None),
                facility=facility_obj,
            )
            if new_user in department_obj.members.all():
                # User is already assigned to the department, update the department
                department_obj.members.remove(new_user)
                return APIResponse(
                    success=True,
                    message="User removed from department successfully",
                    data=department_obj,
                )
            else:
                # User is not assigned to the department, assign the user to the department
                department_obj.members.add(new_user)
                return APIResponse(
                    success=True,
                    message="User assigned to department successfully",
                    data=department_obj,
                )
        except Department.DoesNotExist:
            logging_service.log_error(
                f"Department with ID {data.get('department_id')} does not exist."
            )
            return APIResponse(
                success=False,
                message="Department does not exist",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error updating department",
                data=None,
            )

    def handle_password(self, new_user) -> APIResponse:
        """
        Handles the generation of a strong password for the new user.
        """
        try:
            password = generate_random_password()
            new_user.set_password(password)
            new_user.save()
            return APIResponse(
                success=True,
                message="Password generated successfully",
                data=password,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error generating password",
                data=None,
            )

    def handle_title(self, data) -> APIResponse:
        """
        Handles the assignment of a title to a new user.
        """
        try:
            title_obj = Title.objects.get(id=data.pop("title_id", None))

            return APIResponse(
                success=True,
                message="Title retrieved successfully",
                data=title_obj,
            )
        except Title.DoesNotExist:

            return APIResponse(
                success=False,
                message="Title does not exist",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error assigning title",
                data=None,
            )

    def handle_external_user(self, data, facility=None, department=None) -> APIResponse:
        """
        Handles the creation of a user on the external CHMC auth backend API.
        """
        try:
            external_user_data = {
                "email": data.get("email"),
                "first_name": data.get("first_name"),
                "last_name": data.get("last_name"),
                "role": data.get("role", ""),
                "permission_level": data.get("permission_level", "Corporate"),
            }
            if facility:
                external_user_data["facility"] = facility.name
            if department:
                external_user_data["department"] = department.name

            response = workflow_service.post_user_to_external_api(external_user_data)

            return response
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error creating external user",
                data=None,
            )

    def handle_user_permissions(self, user, permissions) -> APIResponse:
        """
        Handles the assignment of permissions to a new user using Django permissions model.
        """
        try:
            errors = []

            for permission in permissions:
                if "feature" not in permission or "permissions" not in permission:
                    errors.append("Invalid permissions structure")
                    continue

                if permission["feature"] not in MODEL_MAPPING:
                    logging_service.log_error(
                        f"Feature '{permission['feature']}' not found in MODEL_MAPPING."
                    )
                    errors.append(
                        f"Feature '{permission['feature']}' not found in MODEL_MAPPING."
                    )
                    continue

                model = MODEL_MAPPING[permission["feature"]]
                response = PermissionsManagement().add_permissions_to_user(
                    user=user,
                    model=model,
                    permissions=permission["permissions"],
                )
                if not response.success:
                    logging_service.log_error(
                        f"Failed to add permissions for feature '{permission['feature']}'"
                    )
                    errors.append(
                        f"Failed to add permissions for feature '{permission['feature']}'"
                    )

            if errors:
                return APIResponse(
                    success=False,
                    message="Error adding some permissions",
                    data=errors,
                )

            return APIResponse(
                success=True,
                message="Permissions added successfully",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error adding permissions",
                data=str(e),
            )

    def handle_user_update(self, user, data) -> APIResponse:
        """
        Updates the user details based on the provided data.
        """
        CleanUpDatabase(
            {
                "email": data.get("email", ""),
            }
        ).delete_user()
        # check if user already exists
        if data.get("email") and User.objects.filter(email=data.get("email")).exists():
            return APIResponse(
                success=False,
                message="User with the same email already in quality control",
                data=None,
            )
        try:
            user.email = data.get("email", user.email)
            user.first_name = data.get("first_name", user.first_name)
            user.last_name = data.get("last_name", user.last_name)
            user.save()

            return APIResponse(
                success=True,
                message="User updated successfully",
                data=user,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error updating user",
                data=None,
            )

    def handle_update_external_user(self, auth_id, data) -> APIResponse:
        """
        Updates the user on the external CHMC auth backend API.
        """
        try:

            external_user_data = {
                "email": data.get("email"),
                "first_name": data.get("first_name"),
                "last_name": data.get("last_name"),
                "role": data.get("role", ""),
                "permission_level": data.get("permission_level", "Department"),
            }

            response = workflow_service.update_user_on_external_api(
                user_id=auth_id, user_data=external_user_data
            )

            if not response.success:
                external_user_data["password"] = generate_random_password()
                new_user = workflow_service.post_user_to_external_api(
                    external_user_data
                )
                return new_user
            return response
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error updating external user",
                data=None,
            )

    def handle_access_to_facility(
        self, profile: Type[Profile], access_to_facility
    ) -> APIResponse:
        """
        Handle access to facility
        """
        facilities = []
        facility_ids = []
        failed_ids = []
        try:
            # Check facility
            for facility_id in access_to_facility:
                try:
                    facility = Facility.objects.get(id=facility_id)
                    facilities.append((facility))
                    facility_ids.append(facility.id)
                except Facility.DoesNotExist:
                    failed_ids.append(facility_id)

            # Grant access to facilities
            profile.access_to_facilities.set(facility_ids)

            return APIResponse(
                success=not failed_ids,
                message=(
                    "Facilities access added successfully"
                    if not failed_ids
                    else f"Facilities access added, but the following IDs failed: {failed_ids}"
                ),
                data={
                    "added_facilities": [facility.id for facility in facilities],
                    "failed_ids": failed_ids,
                },
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error handling access to facility",
                data=None,
            )

    def handle_access_to_departments(
        self, profile: Type[Profile], access_to_departments
    ) -> APIResponse:
        """Handle access to facilities"""
        departments = []
        departments_ids = []
        failed_departments = []

        try:
            # Check department

            for dep_id in access_to_departments:
                try:
                    dep = Department.objects.get(id=dep_id)
                    departments.append(dep)
                    departments_ids.append(dep.id)
                except Department.DoesNotExist:
                    failed_departments.append(dep_id)

            # Grand access to departments

            profile.access_to_department.set(departments_ids)

            return APIResponse(
                success=not failed_departments,
                message=(
                    "Departments access added successfully"
                    if not failed_departments
                    else f"Departments access added, but the following IDs failed: {failed_departments}"
                ),
                data={
                    "added_departments": [department.id for department in departments],
                    "failed_ids": failed_departments,
                },
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error handling access to facility",
                data=None,
            )
