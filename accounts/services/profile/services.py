import requests
from accounts.models import Profile
from accounts.repositories.profile import ProfileRepository
from accounts.serializers import GetProfileSerializer, UserSerializer
from accounts.services.profile.utils import ProfileUtilsService
from accounts.services.profile.work_flow import ProfileWorkFlow
from accounts.services.user_profile.query import UserIncidentDataQuery
from base.services.logging.logger import LoggingService
from base.services.permissions.manage_permissions import PermissionsManagement
from base.services.responses import APIResponse
from base.tests.base_setup import CleanUpDatabase
from base.utils.data import DataCleaner
from django.contrib.auth.models import User, Group
import os
from django.db.models import Q
import uuid
from django.core.paginator import Paginator, EmptyPage

from complaints.services.query import ComplaintsQueryService
from documents.services.query import DocumentQuery
from incidents.emails.welcome_email import send_welcome_email
from general_patient_visitor.models import GeneralPatientVisitor
from adverse_drug_reaction.models import AdverseDrugReaction
from incidents.services.permissions import IncidentPermissionsService
from patient_visitor_grievance.models import Grievance, GrievanceInvestigation
from staff_incident_reports.models import (
    StaffIncidentReport,
    StaffIncidentInvestigation,
)
from lost_and_found.models import LostAndFound
from medication_error.models import MedicationError
from workplace_violence_reports.models import WorkPlaceViolence


logging_service = LoggingService()
utils_service = ProfileUtilsService()
workflow_service = ProfileWorkFlow()


class ProfileService:
    def __init__(self, user=None):
        self.user = user

    def get_user_profile(self, profile_id):
        try:
            profile = (
                Profile.objects.select_related(
                    "user",
                    "facility",
                )
                .prefetch_related(
                    "access_to_department",
                    "access_to_facilities__facility",
                )
                .get(id=profile_id)
            )
            if profile.is_deleted:
                return APIResponse(
                    success=False,
                    message="Profile has been delete. Contact admin to restore",
                    code=204,
                )
            serializer = GetProfileSerializer(profile)
            return APIResponse(
                data=serializer.data,
                success=True,
                message="Profile retrieved successfully",
            )
        except Profile.DoesNotExist:
            return APIResponse(
                data=None,
                success=False,
                message="Profile not found for user",
            )
        except Exception as e:
            return APIResponse(
                data=None,
                success=False,
                message="Internal server error",
                code=500,
            )

    def get_profiles(self, params=None) -> APIResponse:
        """
        Retrieves a paginated list of user profiles based on provided filter parameters.

        Args:
            params (dict, optional): A dictionary of filter and pagination parameters. Supported keys:
                - "facility" (int or str): ID of the facility to filter profiles by.
                - "department" (int or str): ID of the department to filter profiles by.
                - "page" (int or str): Page number for pagination (default is 1).
                - "page_size" (int or str): Number of profiles per page (default is 10).
                - "q" (str): Search query to filter profiles by user's first name, last name, or email.
                - "permissions" (str): Filter profiles by permission, e.g., "can_add_review".
                - "sort_by" (str): Field to sort by. Options: "first_name", "last_name", "email",
                                "created_at", "updated_at", "facility", "department", "title".
                - "sort_order" (str): Sort order. Either "asc" (ascending) or "desc" (descending).

        Returns:
            APIResponse: An object containing:
                - success (bool): Indicates if the operation was successful.
                - message (str): A message describing the result.
                - data (dict or None): On success, a dictionary with:
                    - "results": List of serialized profile data.
                    - "count": Total number of matching profiles.
                    - "page": Current page number.
                    - "page_size": Number of profiles per page.
                    - "total_pages": Total number of pages.
                    - "has_next": Whether there is a next page.
                    - "has_previous": Whether there is a previous page.
                - code (int, optional): HTTP status code (present on error).

        Raises:
            Exception: Any exception encountered during processing is logged and results in an error response.
        """
        try:
            params = params or {}
            facility = params.get("facility")
            department = params.get("department")
            page = int(params.get("page", 1))
            page_size = int(params.get("page_size", 10))
            query = params.get("q")
            permissions = params.get("permissions")
            sort_by = params.get("sort_by", "first_name")
            sort_order = params.get("sort_order", "asc")

            profiles = (
                Profile.objects.all()
                .select_related(
                    "user",
                    "facility",
                    "department",
                    "title",
                )
                .prefetch_related(
                    "access_to_department",
                    "access_to_facilities__facility",
                )
                .filter(is_deleted=False)
            )

            if facility:
                profiles = profiles.filter(facility__id=facility)
            if department:
                profiles = profiles.filter(access_to_department__id=department)
            if query:
                profiles = profiles.filter(
                    Q(user__first_name__icontains=query)
                    | Q(user__last_name__icontains=query)
                    | Q(user__email__icontains=query)
                )
            if permissions and permissions == "can_add_review":
                profiles = profiles.filter(review_permissions=True)

            # Implement sorting based on sort_by and sort_order parameters
            valid_sort_fields = {
                "first_name": "user__first_name",
                "last_name": "user__last_name",
                "email": "user__email",
                "created_at": "created_at",
                "updated_at": "updated_at",
                "facility": "facility__name",
                "department": "department__name",
                "title": "title__name",
                "id": "id",
            }

            # Validate and apply sorting
            if sort_by in valid_sort_fields:
                order_field = valid_sort_fields[sort_by]
                if sort_order == "desc":
                    order_field = f"-{order_field}"
                profiles = profiles.order_by(order_field, "id")
            else:
                # Default sorting if invalid sort field provided
                profiles = profiles.order_by("user__first_name", "id")

            # Pagination
            paginator = Paginator(profiles, page_size)
            try:
                page_obj = paginator.page(page)
            except EmptyPage:
                page_obj = paginator.page(paginator.num_pages)

            serializer = GetProfileSerializer(page_obj.object_list, many=True)
            data = {
                "results": serializer.data,
                "count": paginator.count,
                "page": page_obj.number,
                "page_size": page_size,
                "total_pages": paginator.num_pages,
                "has_next": page_obj.has_next(),
                "has_previous": page_obj.has_previous(),
            }

            return APIResponse(
                success=True,
                message="Users retrieved successfully",
                data=data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def delete_profile(self, profile_id) -> APIResponse:
        try:
            profile = Profile.objects.get(id=profile_id)

            profile.soft_delete()

            return APIResponse(
                success=True,
                data=None,
                message="Profile soft deleted successfully",
            )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Profile not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error soft deleting profile",
                code=500,
            )

    def deactivate_profile(self, profile_id) -> APIResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            if not profile:
                return APIResponse(
                    success=False,
                    data=None,
                    message="Profile not found",
                )

            # Deactivate the user
            profile.user.is_active = False
            profile.user.save()

            return APIResponse(
                success=True,
                data=None,
                message="Profile deactivated successfully",
            )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Profile not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error deactivating profile",
                code=500,
            )

    def activate_profile(self, profile_id) -> APIResponse:
        try:
            profile = Profile.objects.get(id=profile_id)
            if not profile:
                return APIResponse(
                    success=False,
                    data=None,
                    message="Profile not found",
                )

            # Activate the user
            profile.user.is_active = True
            profile.user.save()

            # Restore the profile
            profile.restore()

            return APIResponse(
                success=True,
                data=None,
                message="Profile activated successfully",
            )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Profile not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error activating profile",
                code=500,
            )

    def new_profile(self, data) -> APIResponse:
        # delete user if the email is <NAME_EMAIL>, as this is a test user
        CleanUpDatabase(
            {
                "email": data.get("email", ""),
            }
        ).delete_user()
        try:
            # Handle user creation
            new_user_response = utils_service.handle_user(data)
            if not new_user_response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=new_user_response.message,
                )
            new_user: User = new_user_response.data

            # Handle facility assignment
            facility_response = utils_service.handle_facility(data, new_user)
            if not facility_response.success:
                new_user.delete()
                return facility_response

            # Handle department assignment
            department_response = utils_service.handle_department(
                data, new_user, facility_response.data
            )
            if not department_response.success:
                new_user.delete()
                return department_response

            # Handle password generation
            password_response = utils_service.handle_password(new_user)
            if not password_response.success:
                new_user.delete()
                return password_response

            # process title
            title_response = utils_service.handle_title(data)

            if not title_response.success:
                new_user.delete()
                return title_response

            # Handle permissions
            permission_groups = data.get("permissions_groups")
            permissions = data.get("permissions")

            if permission_groups:
                for group in permission_groups:
                    try:
                        workflow_service.add_permissions(
                            user_id=new_user.id,
                            data=[{"id": group}],
                        )
                    except Exception as e:
                        logging_service.log_error(e)
                        new_user.delete()
                        return APIResponse(
                            success=False,
                            data=None,
                            message="Error adding permissions",
                            code=500,
                        )

            if permissions:
                permissions_response = utils_service.handle_user_permissions(
                    user=new_user,
                    permissions=permissions,
                )
                if not permissions_response.success:
                    new_user.delete()
                    return permissions_response

            # Serialize and save profile
            data["title"] = title_response.data.id
            data["user"] = new_user.id
            data["created_by"] = self.user.id
            data["facility"] = facility_response.data.id
            data["department"] = department_response.data
            data.pop("email", None)
            data.pop("first_name", None)
            data.pop("last_name", None)
            data.pop("department_id", None)
            data.pop("password", None)
            data.pop("permissions", None)
            data.pop("permissions_groups", None)
            data.pop("facility_id", None)
            data.pop("title_id", None)
            data["user"] = new_user
            data["facility"] = facility_response.data
            data["title"] = title_response.data
            data["created_by"] = self.user
            access_to_facilities = data.pop("access_to_facilities", None)
            access_to_departments = data.pop("access_to_departments", None)

            profile_response = ProfileRepository().create_profile(new_user, data)
            if not profile_response.success:
                new_user.delete()
                # TODO: remove user from auth backend

                return APIResponse(
                    success=False,
                    message=profile_response.message,
                    data=None,
                    code=400,
                )

            # handle access to facilities
            if access_to_facilities:
                access_to_facilities_response = utils_service.handle_access_to_facility(
                    profile_response.data, access_to_facilities
                )
                if not access_to_facilities_response.success:
                    # new_user.delete()
                    # profile_response.data.delete()
                    return access_to_facilities_response

            # handle access to departments
            if access_to_departments:
                access_to_department_response = (
                    utils_service.handle_access_to_departments(
                        profile_response.data, access_to_departments
                    )
                )
                if not access_to_department_response.success:
                    new_user.delete()
                    profile_response.data.delete()
                    return access_to_department_response
            serializer = GetProfileSerializer(profile_response.data)
            # send a welcome email
            send_welcome_email(
                recipient_email=new_user.email,
                first_name=new_user.first_name,
                password=password_response.data,
                login_link="https://q-control.cohesiveapps.com/",
            )
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Profile created successfully",
            )

        except Exception as e:
            logging_service.log_error(e)
            if "new_user" in locals() and new_user:
                new_user.delete()
            return APIResponse(
                success=False,
                data=None,
                message="Fatal error creating profile",
                code=500,
            )

    def update_user_profile(self, profile_id, data) -> APIResponse:
        try:
            # Clean the input data to remove empty keys
            data = DataCleaner.clean_data(data)
            auth_data = {}
            profile = Profile.objects.get(id=profile_id)
            if not profile:
                return APIResponse(
                    success=False,
                    data=None,
                    message="Profile not found",
                    code=404,
                )

            # Handle user update
            user_update_response = utils_service.handle_user_update(profile.user, data)
            if not user_update_response.success:
                return user_update_response

            # add user data to auth data
            auth_data["first_name"] = user_update_response.data.first_name
            auth_data["last_name"] = user_update_response.data.last_name
            access_to_facilities = data.pop("access_to_facilities", None)
            access_to_departments = data.pop("access_to_departments", None)

            if "email" in data:
                auth_data["email"] = user_update_response.data.email

            # Handle facility assignment
            if "facility_id" in data:
                facility_response = utils_service.handle_update_facility(
                    data, profile.user
                )
                if not facility_response.success:
                    return facility_response
                profile.facility = facility_response.data
                # add facility data to auth data
                auth_data["facility"] = facility_response.data.name

            # Handle department assignment
            if "department_id" in data:
                department_response = utils_service.handle_update_department(
                    data, profile.user, facility_response.data
                )
                if not department_response.success:
                    return department_response
                data["department"] = department_response.data.id

                # add department to auth data
                auth_data["department"] = department_response.data.name

            if "title_id" in data:
                title_response = utils_service.handle_title(data)
                if not title_response.success:
                    return title_response
                profile.title = title_response.data
                auth_data["role"] = title_response.data.name
            # Serialize and save profile

            profile.created_by = self.user
            profile.department = department_response.data

            # handle auth user

            profile.save()

            serializer = GetProfileSerializer(profile, data=data, partial=True)
            if not serializer.is_valid():
                error_message = logging_service.get_serializer_error_message(serializer)
                return APIResponse(
                    success=False,
                    data=None,
                    message=error_message,
                )
            serializer.save()

            if access_to_facilities:
                access_to_facilities_response = utils_service.handle_access_to_facility(
                    profile, access_to_facilities
                )
                if not access_to_facilities_response.success:
                    return access_to_facilities_response
            # handle access to departments
            if access_to_departments:
                access_to_department_response = (
                    utils_service.handle_access_to_departments(
                        profile, access_to_departments
                    )
                )
                if not access_to_department_response.success:
                    return access_to_department_response

            return APIResponse(
                success=True,
                data=serializer.data,
                message="Profile updated successfully",
            )

        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Profile not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Fatal error updating profile",
            )

    def get_user_permissions(self, user: User, user_id):
        response = workflow_service.get_user_permissions(
            user=user,
            user_id=user_id,
        )
        if not response.success:
            return APIResponse(
                success=False,
                data=None,
                message=response.message,
                code=response.code,
            )
        return APIResponse(
            success=True,
            data=response.data,
            message="Permissions retrieved successfully",
            code=200,
        )

    def add_permissions(self, user_id, data) -> APIResponse:
        try:
            user = User.objects.get(id=user_id)
            group = Group.objects.get(id=data["id"])
            response = PermissionsManagement().assign_user_to_group(user, group)

            if not response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=response.message,
                    code=400,
                )
            serializer = UserSerializer(user)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Permissions added successfully",
                code=200,
            )
        except Group.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Group not found",
                code=404,
            )
        except User.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="User not found",
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error adding permissions",
                code=500,
            )

    def remove_permissions(self, user_id, data) -> APIResponse:
        try:
            user = User.objects.get(id=user_id)
            group = Group.objects.get(id=data["id"])

            response = PermissionsManagement().remove_user_from_group(
                user=user,
                group=group,
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=response.message,
                )
            serializer = UserSerializer(user)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Permissions removed successfully",
                code=200,
            )
        except Group.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Group not found",
                code=404,
            )

        except User.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="User not found",
                code=404,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error removing permissions",
                code=500,
            )

    def get_user_incidents(
        self, profile_id, filters=None, logged_in_user=None
    ) -> APIResponse:
        """
        Get all incidents for a user.
        """
        try:
            logged_in_profile = Profile.objects.get(user=logged_in_user)

            permissions = IncidentPermissionsService(
                user=logged_in_user, app_label="accounts"
            )

            has_permission = permissions.can_view_users_incidents(
                profile_id=profile_id, logged_in_profile_id=logged_in_profile.id
            )
            if not has_permission.success:
                return APIResponse(
                    success=False, message=has_permission.message, data=None, code=403
                )
            user_profile = Profile.objects.get(id=profile_id)
            if not user_profile:
                return APIResponse(
                    success=False,
                    message="User profile not found.",
                )
            incidents = UserIncidentDataQuery(
                user_profile=user_profile
            ).get_user_incidents(filters=filters)

            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                )
            return APIResponse(
                success=True,
                data=incidents.data,
                message="User incidents retrieved successfully.",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while getting user incidents.",
            )

    def get_user_documents(self, profile_id) -> APIResponse:
        user_profile = Profile.objects.filter(id=profile_id).first()
        if not user_profile:
            return APIResponse(
                success=False,
                message="User profile not found.",
                code=404,
            )
        try:
            documents = DocumentQuery().get_documents(
                {"created_by": user_profile.user.id}
            )
            if not documents.success:
                return APIResponse(
                    success=False,
                    message=documents.message,
                    code=documents.code,
                )
            return APIResponse(
                success=True,
                data=documents.data,
                message="User documents retrieved successfully.",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while getting user documents.",
                code=500,
            )

    def get_user_complaints(self, profile_id) -> APIResponse:
        """
        Get all complaints for a user.
        """
        try:
            user_profile = Profile.objects.get(id=profile_id)
            complaints = ComplaintsQueryService().get_complaints(
                {"created_by": user_profile.user.id}
            )
            if not complaints.success:
                return APIResponse(
                    success=False,
                    message=complaints.message,
                    code=complaints.code,
                )
            return APIResponse(
                success=True,
                data=complaints.data,
                message="User complaints retrieved successfully.",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while getting user complaints.",
            )

    def get_user_drafts(self, user_id: int = None) -> APIResponse:
        """
        Get all drafts for a specific user or the current user.
        Returns formatted drafts from all incident types.
        """
        try:
            """Use provided user_id or default to current user"""
            target_user = self.user
            if user_id:
                try:
                    target_user = User.objects.get(id=user_id)
                except User.DoesNotExist:
                    return APIResponse(
                        success=False,
                        message="User not found",
                        code=404,
                    )

            """Imported here to avoid circular imports"""
            from api.views.facilities.incidents.oveview import format_drafts

            """Get drafts from all incident types"""
            formatted_drafts = format_drafts(
                GeneralPatientVisitor.objects.filter(status="Draft"),
                "general patient visitor",
                target_user,
            )
            formatted_adr_drafts = format_drafts(
                AdverseDrugReaction.objects.filter(status="Draft"),
                "adverse drug reaction",
                target_user,
            )
            formatted_grievance_drafts = format_drafts(
                Grievance.objects.filter(status="Draft"),
                "patient/visitor grievance",
                target_user,
            )
            formatted_grievance_investigation_drafts = format_drafts(
                GrievanceInvestigation.objects.filter(status="Draft"),
                "grievance investigation",
                target_user,
            )
            formatted_staff_report_drafts = format_drafts(
                StaffIncidentReport.objects.filter(status="Draft"),
                "staff incident report",
                target_user,
            )
            formatted_staff_investigation_drafts = format_drafts(
                StaffIncidentInvestigation.objects.filter(status="Draft"),
                "staff_incident_investigation",
                target_user,
            )
            formatted_lost_and_found_drafts = format_drafts(
                LostAndFound.objects.filter(status="Draft"),
                "lost and found",
                target_user,
            )
            formatted_medical_error_drafts = format_drafts(
                MedicationError.objects.filter(status="Draft"),
                "medication error",
                target_user,
            )
            formatted_workplace_violence_drafts = format_drafts(
                WorkPlaceViolence.objects.filter(status="Draft"),
                "workplace violence",
                target_user,
            )

            all_drafts = {
                "general_incident": formatted_drafts,
                "adverse_drug_reaction": formatted_adr_drafts,
                "grievance_incident": formatted_grievance_drafts,
                "grievance_investigation": formatted_grievance_investigation_drafts,
                "staff_incident_report": formatted_staff_report_drafts,
                "staff_incident_investigation": formatted_staff_investigation_drafts,
                "lost_and_found": formatted_lost_and_found_drafts,
                "medical_error": formatted_medical_error_drafts,
                "workplace_violence": formatted_workplace_violence_drafts,
            }

            return APIResponse(
                success=True,
                data=all_drafts,
                message="User drafts retrieved successfully",
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving user drafts",
                code=500,
            )

    def delete_user_drafts(self, draft_data: dict) -> APIResponse:
        """
        Delete multiple drafts for the current user.
        Expected draft_data format: {
            "draft_ids": [
                {"id": 1, "category": "general_incident"},
                {"id": 2, "category": "adverse_drug_reaction"},
                ...
            ]
        }
        """
        try:
            draft_ids = draft_data.get("draft_ids", [])

            if not isinstance(draft_ids, list) or not draft_ids:
                return APIResponse(
                    success=False,
                    message="draft_ids must be a non-empty list",
                    code=400,
                )

            category_model_map = {
                "general_incident": GeneralPatientVisitor,
                "adverse_drug_reaction": AdverseDrugReaction,
                "grievance_incident": Grievance,
                "grievance_investigation": GrievanceInvestigation,
                "staff_incident_report": StaffIncidentReport,
                "staff_incident_investigation": StaffIncidentInvestigation,
                "lost_and_found": LostAndFound,
                "medical_error": MedicationError,
                "workplace_violence": WorkPlaceViolence,
            }

            deleted_count = 0
            errors = []

            """ Group draft IDs by category for efficient deletion"""
            category_groups = {}
            for draft_item in draft_ids:
                if (
                    not isinstance(draft_item, dict)
                    or "id" not in draft_item
                    or "category" not in draft_item
                ):
                    errors.append(f"Invalid draft item format: {draft_item}")
                    continue

                draft_id = draft_item["id"]
                category = draft_item["category"]

                if category not in category_model_map:
                    errors.append(f"Invalid category: {category}")
                    continue

                if category not in category_groups:
                    category_groups[category] = []
                category_groups[category].append(draft_id)

            for category, ids in category_groups.items():
                model = category_model_map[category]
                try:
                    existing_drafts = model.objects.filter(
                        created_by=self.user,
                        status="Draft",
                        id__in=ids,
                    )
                    draft_count = existing_drafts.count()

                    existing_drafts.delete()

                    deleted_count += draft_count

                    if draft_count > 0:
                        logging_service.log_info(
                            f"User {self.user.id} successfully deleted {draft_count} {category} drafts: {ids}"
                        )
                    else:
                        errors.append(
                            f"No {category} drafts found for deletion with IDs: {ids}"
                        )

                except Exception as e:
                    logging_service.log_error(e)
                    errors.append(f"Error deleting {category} drafts: {str(e)}")

            if errors:
                logging_service.log_error(errors)
                return APIResponse(
                    success=True,
                    message=(
                        f"Partially successful: {deleted_count} drafts deleted with some errors"
                        if deleted_count > 0
                        else "No drafts deleted due to errors"
                    ),
                    data={"deleted_count": deleted_count, "errors": errors},
                    code=200,
                )
            else:
                return APIResponse(
                    success=True,
                    message=f"Successfully deleted {deleted_count} drafts",
                    data={"deleted_count": deleted_count},
                    code=200,
                )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while deleting user drafts",
                code=500,
            )
