# permissions service


from base.services.responses import APIResponse, RepositoryResponse


class AccountsPermissionsService:
    def __init__(self, user):
        self.user = user

    def can_create_account(self) -> APIResponse:
        """
        Checks if a user can create an account

        Conditions: User must be active AND have one of these:
        - User must have the 'accounts.add_profile' permission
        - User must be in a group called 'User editors' or 'Super user'
        """
        # User must be active to perform any actions
        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        # Check if user has direct permission or is in authorized groups
        if self._user_has_per(["accounts.add_profile"]) or self._user_in_group(
            ["User Editor", "Super User"]
        ):
            return RepositoryResponse(
                success=True, message="User can create an account", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to create an account",
        )

    def can_view_account(self, account_id) -> APIResponse:
        """
        Checks if a user can view an account

        Conditions: User must be active AND have one of these:
        - User must have the 'accounts.view_profile' permission
        - User must be in a group called 'User editors' or 'Super user'
        - User can view their own account
        """
        # User must be active to perform any actions
        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        # Check if user is trying to view their own account
        if hasattr(self.user, "profile") and self.user.profile.id == account_id:
            return RepositoryResponse(
                success=True, message="User can view their own account", data=None
            )

        # Check if user has direct permission or is in authorized groups
        if self._user_has_per(["accounts.view_profile"]) or self._user_in_group(
            ["User Editor", "Super User"]
        ):
            return RepositoryResponse(
                success=True, message="User can view account", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to view this account",
        )

    def can_list_accounts(self) -> APIResponse:
        """
        Checks if a user can list accounts

        Conditions: User must be active AND have one of these:
        - User must have the 'accounts.view_list' permission
        - User must be in a group called 'User editors' or 'Super user'
        """
        # User must be active to perform any actions
        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        # Check if user has direct permission or is in authorized groups
        if self._user_has_per(["accounts.view_list"]) or self._user_in_group(
            ["User Editor", "Super User"]
        ):
            return RepositoryResponse(
                success=True, message="User can list accounts", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to list accounts",
        )

    def can_update_account(self, account_id) -> APIResponse:
        """
        Checks if a user can update an account

        Conditions: User must be active AND have one of these:
        - User must have the 'accounts.change_profile' permission
        - User must be in a group called 'User editors' or 'Super user'
        - User can update their own account
        """
        # User must be active to perform any actions
        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        # Check if user is trying to update their own account
        if hasattr(self.user, "profile") and self.user.profile.id == account_id:
            return RepositoryResponse(
                success=True, message="User can update their own account", data=None
            )

        # Check if user has direct permission or is in authorized groups
        if self._user_has_per(["accounts.change_profile"]) or self._user_in_group(
            ["User Editor", "Super User"]
        ):
            return RepositoryResponse(
                success=True, message="User can update account", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to update this account",
        )

    def can_delete_account(self, account_id) -> APIResponse:
        """
        Checks if a user can delete an account

        Conditions: User must be active AND have one of these:
        - User must have the 'accounts.delete_profile' permission
        - User must be in a group called 'User editors' or 'Super user'
        Note: Users cannot delete their own accounts for security reasons
        """
        # User must be active to perform any actions
        if not self.user.is_active:
            return RepositoryResponse(
                success=False,
                message="User account is not active",
            )

        # Prevent users from deleting their own accounts
        if hasattr(self.user, "profile") and self.user.profile.id == account_id:
            return RepositoryResponse(
                success=False,
                message="Users cannot delete their own accounts",
            )

        # Check if user has direct permission or is in authorized groups
        if self._user_has_per(["accounts.delete_profile"]) or self._user_in_group(
            ["User Editor", "Super User"]
        ):
            return RepositoryResponse(
                success=True, message="User can delete account", data=None
            )

        return RepositoryResponse(
            success=False,
            message="Current user does not have enough permissions to delete this account",
        )

    def _user_has_per(self, perms):
        """
        Check if user has any of the specified permissions.
        perms should be a list of permission strings in format 'app_label.permission_codename'
        """
        return any(self.user.has_perm(perm) for perm in perms)

    def _user_in_group(self, group_names):
        """
        Check if user is in any of the specified groups.
        group_names should be a list of group name strings
        """
        return self.user.groups.filter(name__in=group_names).exists()
