from rest_framework import serializers
from django.contrib.auth.models import User, Group
from accounts.models import (
    Profile,
    Title,
    UserPreference,
    UserProfile,
)
from rest_framework import viewsets
from django.db.models import Count

from tasks.models import ReviewGroups, ReviewTemplateTasks, ReviewTemplates


class UserSerializer(serializers.ModelSerializer):
    position = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "position",
        ]

    def get_position(self, obj):
        try:
            if isinstance(obj, User):
                if obj.groups.exists():
                    return ", ".join(group.name for group in obj.groups.all())
            return None
        except Exception as e:
            return None


class TitleSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = Title
        fields = "__all__"


class ProfileSerializer(serializers.ModelSerializer):

    class Meta:
        model = Profile
        fields = "__all__"


class GetProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    facility = serializers.SerializerMethodField()
    title = TitleSerializer()
    access_to_facilities = serializers.SerializerMethodField()
    access_to_department = serializers.SerializerMethodField()
    department = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = Profile
        fields = "__all__"

    def get_facility(self, obj):
        if obj.facility:
            return {"id": obj.facility.id, "name": obj.facility.name}
        return None

    def get_access_to_facilities(self, obj):
        return [
            {"id": facility.id, "name": facility.name}
            for facility in obj.access_to_facilities.all()
        ]

    def get_access_to_department(self, obj):
        return [
            {"id": department.id, "name": department.name}
            for department in obj.access_to_department.all()
        ]

    def get_department(self, obj):
        if obj.department:
            return {"id": obj.department.id, "name": obj.department.name}
        return None

    def get_created_by(self, obj):
        if obj.created_by:
            return {
                "id": obj.created_by.id,
                "name": f"{obj.created_by.first_name} {obj.created_by.last_name}",
            }
        return None

    def get_updated_by(self, obj):
        if obj.updated_by:
            return {
                "id": obj.updated_by.id,
                "name": f"{obj.updated_by.first_name} {obj.updated_by.last_name}",
            }
        return None


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = "__all__"


class UserPreferenceSerializer(serializers.ModelSerializer):

    class Meta:
        model = UserPreference
        fields = "__all__"
        extra_kwargs = {"user": {"read_only": True}}


class ReviewGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewGroups
        fields = [
            "id",
            "title",
            "description",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class GetReviewGroupSerializer(serializers.ModelSerializer):
    members = serializers.IntegerField(source="members_count", read_only=True)
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = ReviewGroups
        fields = "__all__"


class ReviewGroupViewSet(viewsets.ModelViewSet):
    queryset = ReviewGroups.objects.all()
    serializer_class = GetReviewGroupSerializer

    def get_queryset(self):
        return ReviewGroups.objects.annotate(members_count=Count("members"))


class ReviewTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewTemplates
        fields = "__all__"


class ReviewTemplateTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewTemplateTasks
        fields = "__all__"


class GetReviewTemplateTaskSerializer(serializers.ModelSerializer):
    review_template = serializers.SerializerMethodField()
    review_groups = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = ReviewTemplateTasks
        fields = "__all__"

    def get_review_template(self, obj):
        if obj.review_template:
            return {"id": obj.review_template.id, "name": obj.review_template.name}
        return None

    def get_review_groups(self, obj):
        return [
            {"id": group.id, "name": group.title} for group in obj.review_groups.all()
        ]


class GetReviewTemplateSerializer(serializers.ModelSerializer):
    tasks = ReviewTemplateTaskSerializer(many=True, read_only=True)
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = ReviewTemplates
        fields = "__all__"


