from datetime import datetime, timedelta
import json
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
import jwt
from rest_framework.test import APIClient
from rest_framework import status
from activities.models import ActivityLog, ActivityType
from activities.services import ActivityService
from core.settings import SECRET_KEY
from general_patient_visitor.models import GeneralPatientVisitor
from base.models import Facility, Department


class ActivityAPITest(TestCase):
    """Test cases for Activity API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        token_payload = {
            "user_id": self.user.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")
        
        self.facility = Facility.objects.create(
            name='Test Facility',
            address='123 Test St'
        )
        
        self.department = Department.objects.create(
            name='Test Department',
            facility=self.facility
        )
        
        self.incident = GeneralPatientVisitor.objects.create(
            incident_type='General Patient/Visitor',
            status='Draft',
            report_facility=self.facility,
            department=self.department,
            created_by=self.user
        )
        
        self.auth_headers = {'Authorization': f'Bearer {jwt_token}'}
    
    def test_activities_list_legacy_endpoint(self):
        """Test the legacy activities list endpoint"""
        ActivityService.log_creation(user=self.user, content_object=self.incident)
        ActivityService.log_update(user=self.user, content_object=self.incident)
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(url, **self.auth_headers)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('data', data)
        self.assertIn('count', data)
        self.assertEqual(data['count'], 2)
    
    def test_activities_list_with_incident_type(self):
        """Test activities list with incident_type parameter"""
        ActivityService.log_creation(user=self.user, content_object=self.incident)
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(
            url + '?incident_type=general_patient_visitor',
            **self.auth_headers
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['count'], 1)
    
    def test_unauthorized_access(self):
        """Test that unauthorized requests are rejected"""
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        self.client.logout()
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_activity_serialization(self):
        """Test that activities are properly serialized with all fields"""
        target_user = User.objects.create_user(
            username='target',
            email='<EMAIL>',
            password='testpass123'
        )
        
        activity = ActivityService.log_assignment(
            user=self.user,
            content_object=self.incident,
            assignee=target_user,
            facility=self.facility,
            department=self.department,
            ip_address='***********',
            user_agent='Test Browser'
        )
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(url, **self.auth_headers)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        activity_data = data['data'][0]
        
        expected_fields = [
            'id', 'user', 'target_user', 'activity_type', 'timestamp',
            'description', 'details', 'department', 'facility',
            'formatted_description', 'activity_icon', 'content_object_type'
        ]
        
        for field in expected_fields:
            self.assertIn(field, activity_data)
        
        self.assertEqual(activity_data['user']['email'], self.user.email)
        self.assertEqual(activity_data['target_user']['email'], target_user.email)
        
        self.assertEqual(activity_data['department']['name'], self.department.name)
        self.assertEqual(activity_data['facility']['name'], self.facility.name)
