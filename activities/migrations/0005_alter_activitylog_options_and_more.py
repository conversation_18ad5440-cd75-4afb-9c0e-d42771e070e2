# Generated by Django 4.2.23 on 2025-08-07 06:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
        ('activities', '0004_remove_activitylog_activities__content_09a1b5_idx_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='activitylog',
            options={'ordering': ['-timestamp'], 'verbose_name': 'Activity Log', 'verbose_name_plural': 'Activity Logs'},
        ),
        migrations.AlterField(
            model_name='activitylog',
            name='content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='activity_content_types', to='contenttypes.contenttype'),
        ),
        migrations.AlterField(
            model_name='activitylog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='activities', to=settings.AUTH_USER_MODEL),
        ),
    ]
