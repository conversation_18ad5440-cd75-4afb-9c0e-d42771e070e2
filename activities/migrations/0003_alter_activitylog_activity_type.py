# Generated by Django 5.2.1 on 2025-08-06 06:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('activities', '0002_alter_activitylog_options_activitylog_activity_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='activitylog',
            name='activity_type',
            field=models.CharField(choices=[('created', 'Created'), ('updated', 'Updated'), ('status_changed', 'Status Changed'), ('sent_to_department', 'Sent to Department'), ('sent_from_department', 'Sent from Department'), ('document_added', 'Document Added'), ('document_removed', 'Document Removed'), ('review_added', 'Review Added'), ('assigned', 'Assigned'), ('unassigned', 'Unassigned'), ('resolved', 'Resolved'), ('reopened', 'Reopened'), ('investigation_started', 'Investigation Started'), ('investigation_completed', 'Investigation Completed'), ('comment_added', 'Comment Added'), ('notification_sent', 'Notification Sent'), ('workflow_step_completed', 'Workflow Step Completed'), ('deleted', 'Deleted')], default='updated', max_length=50),
        ),
    ]
