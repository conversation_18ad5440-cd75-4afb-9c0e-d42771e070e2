# Generated by Django 4.2.23 on 2025-08-07 09:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('activities', '0005_alter_activitylog_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='activitylog',
            name='destination_content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='destination_activities', to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='destination_object_id',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
    ]
