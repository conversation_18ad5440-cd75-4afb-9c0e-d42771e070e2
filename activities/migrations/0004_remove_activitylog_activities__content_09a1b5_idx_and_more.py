# Generated by Django 4.2.23 on 2025-08-06 15:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
        ('activities', '0003_alter_activitylog_activity_type'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='activitylog',
            name='activities__content_09a1b5_idx',
        ),
        migrations.RemoveIndex(
            model_name='activitylog',
            name='activities__activit_b652ab_idx',
        ),
        migrations.RemoveIndex(
            model_name='activitylog',
            name='activities__timesta_e0bd47_idx',
        ),
        migrations.RemoveIndex(
            model_name='activitylog',
            name='activities__user_id_de6ba1_idx',
        ),
        migrations.RemoveField(
            model_name='activitylog',
            name='action',
        ),
        migrations.RemoveField(
            model_name='activitylog',
            name='department',
        ),
        migrations.RemoveField(
            model_name='activitylog',
            name='details',
        ),
        migrations.RemoveField(
            model_name='activitylog',
            name='facility',
        ),
        migrations.RemoveField(
            model_name='activitylog',
            name='incident_id',
        ),
        migrations.RemoveField(
            model_name='activitylog',
            name='ip_address',
        ),
        migrations.RemoveField(
            model_name='activitylog',
            name='target_user',
        ),
        migrations.RemoveField(
            model_name='activitylog',
            name='user_agent',
        ),
        migrations.AddField(
            model_name='activitylog',
            name='files',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='highlight_content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='highlight_activities', to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='highlight_object_id',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='activitylog',
            name='activity_type',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='activitylog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL),
        ),
    ]
