# Generated by Django 4.2.23 on 2025-07-28 08:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('base', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('activities', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='activitylog',
            options={'ordering': ['-timestamp']},
        ),
        migrations.AddField(
            model_name='activitylog',
            name='activity_type',
            field=models.CharField(choices=[('created', 'Created'), ('updated', 'Updated'), ('status_changed', 'Status Changed'), ('sent_to_department', 'Sent to Department'), ('sent_from_department', 'Sent from Department'), ('document_added', 'Document Added'), ('document_removed', 'Document Removed'), ('review_added', 'Review Added'), ('assigned', 'Assigned'), ('unassigned', 'Unassigned'), ('resolved', 'Resolved'), ('reopened', 'Reopened'), ('investigation_started', 'Investigation Started'), ('investigation_completed', 'Investigation Completed'), ('comment_added', 'Comment Added'), ('notification_sent', 'Notification Sent'), ('workflow_step_completed', 'Workflow Step Completed')], default='updated', max_length=50),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='content_type',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='activitylog',
            name='department',
            field=models.ForeignKey(blank=True, help_text='Department involved in the activity', null=True, on_delete=django.db.models.deletion.SET_NULL, to='base.department'),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='details',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='facility',
            field=models.ForeignKey(blank=True, help_text='Facility where the activity occurred', null=True, on_delete=django.db.models.deletion.SET_NULL, to='base.facility'),
        ),
        migrations.AddField(
            model_name='activitylog',
            name='object_id',
            field=models.PositiveIntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='activitylog',
            name='target_user',
            field=models.ForeignKey(blank=True, help_text='User who is the target of this activity (e.g., assigned user)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='targeted_activities', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='activitylog',
            name='action',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddIndex(
            model_name='activitylog',
            index=models.Index(fields=['content_type', 'object_id'], name='activities__content_09a1b5_idx'),
        ),
        migrations.AddIndex(
            model_name='activitylog',
            index=models.Index(fields=['activity_type'], name='activities__activit_b652ab_idx'),
        ),
        migrations.AddIndex(
            model_name='activitylog',
            index=models.Index(fields=['timestamp'], name='activities__timesta_e0bd47_idx'),
        ),
        migrations.AddIndex(
            model_name='activitylog',
            index=models.Index(fields=['user'], name='activities__user_id_de6ba1_idx'),
        ),
    ]
