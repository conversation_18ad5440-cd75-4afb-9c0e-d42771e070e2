from rest_framework import serializers
from accounts.serializers import UserProfileSerializer, UserSerializer
from facilities.serializers import FacilitySerializer
from lost_and_found.models import LostAndFound
from base.serializers import BaseModelSerializer, DepartmentSerializer


class GetLostAndFoundSerializer(BaseModelSerializer):
    reported_by = serializers.SerializerMethodField()
    found_by = serializers.SerializerMethodField()
    taken_by = serializers.SerializerMethodField()
    report_facility = FacilitySerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)

    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = LostAndFound
        fields = "__all__"

    def get_reported_by(self, obj):
        if obj.reported_by:
            return {
                "id": obj.reported_by.id,
                "first_name": obj.reported_by.first_name,
                "last_name": obj.reported_by.last_name,
                "email": obj.reported_by.email,
            }
        return None

    def get_found_by(self, obj):
        if obj.found_by:
            return {
                "id": obj.found_by.id,
                "first_name": obj.found_by.first_name,
                "last_name": obj.found_by.last_name,
                "email": obj.found_by.email,
            }
        return None

    def get_taken_by(self, obj):
        if obj.taken_by:
            return {
                "id": obj.taken_by.id,
                "first_name": obj.taken_by.first_name,
                "last_name": obj.taken_by.last_name,
                "email": obj.taken_by.email,
            }
        return None
