from django.db import models
from accounts.models import Profile
from base.models import BaseModel
from documents.models import Document
from base.models import Facility
from base.models import Department
from reviews.models import Review


# we need to add search indexes to optimize performance during searching and querying
class LostAndFoundBase(BaseModel):
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES,
        max_length=255,
        default="Draft",
    )
    report_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    person_reporting_info = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="person_reporting_info",
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    property_name = models.CharField(max_length=255, blank=True, null=True)
    person_taking_report_info = models.ForeignKey(
        Profile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="person_taking_report_info",
    )
    date_reported = models.DateField(auto_now_add=True, null=True, blank=True)
    time_reported = models.TimeField(auto_now=True, null=True, blank=True)
    relation_to_patient = models.CharField(max_length=255, null=True, blank=True)
    item_description = models.CharField(max_length=500, null=True, blank=True)
    action_taken = models.CharField(max_length=2550, null=True, blank=True)
    is_found = models.BooleanField(default=False, blank=True)
    date_found = models.DateField(null=True, blank=True)
    time_found = models.TimeField(null=True, blank=True)
    location_found = models.CharField(max_length=255, null=True, blank=True)
    found_by = models.CharField(max_length=255, null=True)
    disposal_of_unclaimed_property = models.CharField(
        max_length=500, null=True, blank=True
    )
    returned_to = models.CharField(max_length=255, null=True, blank=True)
    date_returned = models.DateField(null=True, blank=True)
    time_returned = models.TimeField(null=True, blank=True)
    location_returned = models.CharField(max_length=255, null=True, blank=True)
    resolved = models.BooleanField(blank=True, null=True)
    date_letter_sent = models.DateField(null=True, blank=True)
    letter_sent_to = models.CharField(max_length=255, null=True, blank=True)
    reviews = models.ManyToManyField(
        Review, related_name="lost_and_found_reviews_field", blank=True
    )

    documents = models.ManyToManyField(
        Document, related_name="lost_and_found_incident_documents_field", blank=True
    )
    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )

    is_resolved = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.property_name} - {self.person_taking_report}"


class LostAndFound(LostAndFoundBase):
    is_modified = models.BooleanField(default=False)


class LostAndFoundVersion(LostAndFoundBase):
    original_report = models.ForeignKey(
        LostAndFound,
        on_delete=models.CASCADE,
        related_name="versions",
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "lost_and_found_version"
