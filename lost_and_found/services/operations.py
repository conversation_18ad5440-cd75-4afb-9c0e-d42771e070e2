from accounts.models import Profile
from accounts.services.user_profile.service import UserProfileService
from api.views.incidents.general_incident.new_incident import check_anonymous
from lost_and_found.serializers import (
    LostAndFoundSerializer,
    LostAndFoundUpdateSerializer,
)
from lost_and_found.new_serializers import GetLostAndFoundSerializer
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.models import Department
from base.services.forms import check_user_facility
from base.services.logging.logger import LoggingService
from base.services.notifications import save_notification
from base.services.responses import APIResponse
from lost_and_found.models import LostAndFound
from documents.models import Document
from incidents.services.query import IncidentQueryService
from incidents.views.send_to_department import send_incident_submission_email
from reviews.models import Review
from tasks.models import ReviewProcessTasks
from activities.services import ActivityLogService
from activities.models import ActivityType


user_profile_service = UserProfileService()


class LostAndFoundService:
    def __init__(self, user):
        self.logging_service = LoggingService()
        self.user = user
        self.query_service = IncidentQueryService(
            user=user,
            model=LostAndFound,
            serializer=GetLostAndFoundSerializer,
        )

    """A service class for LostAndFound model"""

    def get_incident_by_id(self, incident_id) -> APIResponse:
        try:
            incident = self.query_service.get_incident_by_id(
                incident_id=incident_id,
            )
            if not incident.success:
                return APIResponse(
                    success=False,
                    message=incident.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Incident retrieved successfully",
                data=incident.data,
                code=200,
            )
        except LostAndFound.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_incidents_list(
        self,
        filters=None,
    ):
        try:
            incidents = self.query_service.get_incidents(
                user=self.user,
                model=LostAndFound,
                serializer=GetLostAndFoundSerializer,
                prefetch_fields=[
                    "documents",
                    "reviews",
                    "review_tasks",
                ],
                related_fields=[
                    "taken_by",
                    "report_facility",
                    "department",
                    "created_by",
                    "found_by",
                    "reported_by",
                    "review_process",
                ],
                filters=filters,
            )
            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Incidents retrieved successfully",
                data=incidents.data,
                code=200,
            )
        except Exception as e:
            LoggingService().log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def create_incident(self, data) -> APIResponse:
        try:
            facility_response = check_user_facility(data, self.user)
            if not facility_response.success:
                return APIResponse(
                    success=False,
                    message=facility_response.message,
                    data=None,
                    code=400,
                )
            facility = facility_response.data

            request_data = data.copy()
            document_ids = request_data.pop("documents", [])
            review_ids = request_data.pop("reviews", [])
            review_task_ids = request_data.pop("review_tasks", [])

            if "reported_by" in data:
                reported_profile = user_profile_service.get_or_create_profile(
                    data["reported_by"]
                )
                if not reported_profile.success:
                    return APIResponse(
                        success=False,
                        message=reported_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["reported_by"] = reported_profile.data.id

            if "taken_by" in data:
                taken_profile = user_profile_service.get_or_create_profile(
                    data["taken_by"]
                )
                if not taken_profile.success:
                    return APIResponse(
                        success=False,
                        message=taken_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["taken_by"] = taken_profile.data.id

            if "found_by" in data:
                found_profile = user_profile_service.get_or_create_profile(
                    data["found_by"]
                )
                if not found_profile.success:
                    return APIResponse(
                        success=False,
                        message=found_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["found_by"] = found_profile.data.id

            request_data["created_by"] = self.user.id
            request_data["report_facility"] = facility
            serializer = LostAndFoundSerializer(data=request_data)

            if serializer.is_valid():
                instance = serializer.save()
                if document_ids:
                    instance.documents.set(Document.objects.filter(id__in=document_ids))
                if review_ids:
                    instance.reviews.set(Review.objects.filter(id__in=review_ids))
                if review_task_ids:
                    instance.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )

                # save notification and assign it to quality managers
                save_notification(
                    facility=serializer.data["report_facility"],
                    group_name="Admin",
                    notification_type="info",
                    notification_category="incident",
                    message="A new incident is submitted",
                    item_id=serializer.data["id"],
                )

                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=instance,
                    activity_type=ActivityType.CREATED,
                    description="Lost and Found created"
                )

                return APIResponse(
                    success=True,
                    message="Lost and Found created successfully",
                    data=serializer.data,
                    code=201,
                )
            self.logging_service.log_error(serializer.errors)
            # invalid data error can be logged or handled here if needed
            return APIResponse(
                success=False,
                message="Invalid data",
                data=None,
                code=400,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def update_incident(self, id, data) -> APIResponse:

        try:
            request_data = data.copy()
            lost_found = LostAndFound.objects.get(id=id)
            profile = Profile.objects.get(user=self.user)
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, profile.facility)
                and not is_manager_user(self.user, lost_found.department)
                and not self.user == lost_found.created_by
            ):
                return APIResponse(
                    success=False,
                    message="You do not have enough rights to update this incident",
                    data=None,
                    code=403,
                )
            document_ids = request_data.pop("documents", None)
            review_ids = request_data.pop("reviews", None)
            review_task_ids = request_data.pop("review_tasks", None)
            facility = lost_found.report_facility
            request_data["report_facility"] = facility

            if "reported_by" in data:
                reported_profile = user_profile_service.get_or_create_profile(
                    data["reported_by"]
                )
                if not reported_profile.success:
                    return APIResponse(
                        success=False,
                        message=reported_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["reported_by"] = reported_profile.data.id

            if "taken_by" in data:
                taken_profile = user_profile_service.get_or_create_profile(
                    data["taken_by"]
                )
                if not taken_profile.success:
                    return APIResponse(
                        success=False,
                        message=taken_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["taken_by"] = taken_profile.data.id

            if "found_by" in data:
                found_profile = user_profile_service.get_or_create_profile(
                    data["found_by"]
                )
                if not found_profile.success:
                    return APIResponse(
                        success=False,
                        message=found_profile.message,
                        data=None,
                        code=400,
                    )
                request_data["found_by"] = found_profile.data.id

            if "facility_id" in data:
                facility_response = self._process_facility(data["facility_id"])
                if not facility_response.success:
                    return APIResponse(
                        success=False,
                        message=facility_response.message,
                        code=400,
                    )
                facility = facility_response.data
                request_data["report_facility"] = facility

            if "department" in data:
                department_response = Department.objects.get(id=data["department"])
                if not department_response:
                    return APIResponse(
                        success=False,
                        message=department_response,
                        code=400,
                    )
                request_data["department"] = department_response.id

            data = check_anonymous(request_data, self.user)
            old_status = lost_found.status
            serializer = LostAndFoundUpdateSerializer(
                lost_found, data=data, partial=True
            )
            if serializer.is_valid():
                serializer.save()
                if document_ids is not None:
                    lost_found.documents.set(
                        Document.objects.filter(id__in=document_ids)
                    )
                if review_ids is not None:
                    lost_found.reviews.set(Review.objects.filter(id__in=review_ids))

                if review_task_ids is not None:
                    lost_found.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )
                
                new_status = data.get("status")
                if old_status != new_status:
                    ActivityLogService.create_activity(
                        user=self.user,
                        content_object=lost_found,
                        activity_type=ActivityType.STATUS_CHANGED,
                        description=f"Status changed from {old_status} to {new_status}",
                    )
                
                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=lost_found,
                    activity_type=ActivityType.UPDATED,
                    description="Lost and Found updated"
                )
                if "status" in data and data.get("status") == "Open":
                    send_incident_submission_email(
                        incident=lost_found,
                        incident_type=data.get("incident_type", None),
                    )
                serialized_data = GetLostAndFoundSerializer(lost_found)
                return APIResponse(
                    success=True,
                    message="Incident updated successfully",
                    data=serialized_data.data,
                    code=200,
                )
            else:
                self.logging_service.log_error(serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data",
                    data=None,
                    code=400,
                )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                message="Admin's profile not found",
                data=None,
                code=404,
            )
        except LostAndFound.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )
