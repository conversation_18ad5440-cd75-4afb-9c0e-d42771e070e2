from django.urls import reverse
from rest_framework import status
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserProfileFactory
from lost_and_found.tests.factory import LostAndFoundFactory
from tasks.tests.factory import ReviewTemplateFactory


class TestLostFoundIncidentsAPIAsSuperUser(BaseTestSetup):
    """Test Lost and Found API endpoints as Super User"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/lost-found/"
        self._authenticate_user(self.super_user)
        taken_by = UserProfileFactory(profile_type="Staff")
        reported_by = UserProfileFactory(profile_type="Staff")
        self.valid_data = {
            "property_name": "Gold Watch",
            "item_description": "18k gold watch with leather strap",
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "reported_by": {"user_id": taken_by.id},
            "taken_by": {"user_id": reported_by.id},
            "is_found": True,
            "location_found": "Hospital Cafeteria",
        }

    def test_get_incidents_list(self):
        """Test GET request returns list of incidents"""
        LostAndFoundFactory.create_batch(3, created_by=self.super_user)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)

    def test_get_incidents_with_filters(self):
        """Test GET request with filters"""
        LostAndFoundFactory(status="Draft", created_by=self.super_user)
        LostAndFoundFactory(status="Open", created_by=self.super_user)

        response = self.client.get(f"{self.url}?status=Draft")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_create_incident_success(self):
        """Test POST request creates incident"""
        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            response.data["property_name"], self.valid_data["property_name"]
        )

    def test_create_incident_invalid_data(self):
        """Test POST request with invalid data fails"""
        invalid_data = self.valid_data.copy()
        invalid_data.get("taken_by")["user_id"] = None

        response = self.client.post(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_incident_unauthenticated(self):
        """Test POST request without authentication fails"""
        self.client.logout()

        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestLostFoundIncidentsAPIAsAdmin(BaseTestSetup):
    """Test Lost and Found API endpoints as Admin"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/lost-found/"
        self._authenticate_user(self.admin_user)

    def test_get_incidents_as_admin(self):
        """Test admin can access incidents from their facility"""
        # Create incidents in admin's facility
        LostAndFoundFactory.create_batch(
            3,
            created_by=self.admin_user,
            department=self.admin_user_dept,
            report_facility=self.admin_user_fac,
        )

        # Create incidents in other facility (should not be accessible)
        LostAndFoundFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)


class TestLostFoundIncidentsAPIAsDirector(BaseTestSetup):
    """Test Lost and Found API endpoints as Director"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/lost-found/"
        self._authenticate_user(self.director_user)

    def test_get_incidents_as_director(self):
        """Test director can access incidents from their facility"""
        # Create incidents in director's facility
        LostAndFoundFactory.create_batch(
            2,
            created_by=self.director_user,
            department=self.director_user_dept,
            report_facility=self.director_user_fac,
        )

        # Create incidents in other facility (should not be accessible)
        LostAndFoundFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)


class TestLostFoundIncidentsAPIAsManager(BaseTestSetup):
    """Test Lost and Found API endpoints as Manager"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/lost-found/"
        self._authenticate_user(self.manager_user)

    def test_get_incidents_as_manager(self):
        """Test manager can access incidents from their department"""
        # Create incidents in manager's department
        LostAndFoundFactory.create_batch(
            2,
            created_by=self.manager_user,
            department=self.manager_user_dept,
            report_facility=self.manager_user_fac,
        )

        # Create incidents in other department (should not be accessible)
        LostAndFoundFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)


class TestLostFoundIncidentsAPIAsRegularUser(BaseTestSetup):
    """Test Lost and Found API endpoints as Regular User"""

    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/lost-found/"
        self._authenticate_user(self.user_user)

    def test_get_incidents_as_regular_user(self):
        """Test regular user can access their own incidents and assigned reviews"""
        # Create incidents created by the regular user
        LostAndFoundFactory.create_batch(
            2,
            created_by=self.user_user,
            department=self.user_user_dept,
            report_facility=self.user_user_fac,
        )

        # Create incidents by other users (should not be accessible unless assigned for review)
        LostAndFoundFactory()

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)


class TestLostFoundIncidentDetailsAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        # Create incident with regular user, but access/modify it with super_user to avoid business rule violations
        self.incident = LostAndFoundFactory(
            created_by=self.user_user,  # Created by regular user
            department=self.super_user_dept,
            report_facility=self.super_user_fac,
        )
        self.url = f"/api/incidents/lost-found/{self.incident.id}/"
        self._authenticate_user(
            self.super_user
        )  # Super user can close incidents created by others
        self.update_data = {
            "property_name": "Updated Watch",
            "item_description": "Updated description",
        }

    def test_get_incident_details(self):
        """Test GET request returns incident details"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_nonexistent_incident(self):
        """Test GET request for non-existent incident"""
        response = self.client.get("/api/incidents/lost-found/99999/")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_incident(self):
        """Test PUT request updates incident"""
        response = self.client.put(self.url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.data["property_name"], self.update_data["property_name"]
        )

    def test_patch_send_for_review(self):
        """Test PATCH request sends incident for review"""
        review_template = ReviewTemplateFactory()
        data = {
            "action": "send-for-review",
            "review_template": review_template.id,
            "description": "Hey, please review this",
        }

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_mark_closed(self):
        """Test PATCH request marks incident as closed"""
        data = {"action": "mark-closed"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_modify_incident(self):
        """Test PATCH request modifies incident"""
        data = {"action": "modify", "property_name": "Modified Watch"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_invalid_action(self):
        """Test PATCH request with invalid action"""
        data = {"action": "invalid-action"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_patch_missing_action(self):
        """Test PATCH request without action"""
        response = self.client.patch(self.url, {}, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
