from base.tests.factory import (
    DepartmentFactory,
    DocumentFactory,
    FacilityFactory,
    ProfileFactory,
    ReviewFactory,
    UserFactory,
    UserProfileFactory,
)
from documents.models import Document
from lost_and_found.tests.factory import LostAndFoundFactory
from base.tests.base_setup import BaseTestSetup
from lost_and_found.services.actions import LostFoundActionsService
from lost_and_found.services.operations import LostAndFoundService
from tasks.tests.factory import ReviewTemplateFactory


class TestLostFoundServiceModifyIncidentNewUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = LostAndFoundFactory(created_by=self.super_user)
        self.service = LostFoundActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "found_by": {
                    "first_name": "<PERSON>",
                    "last_name": "Doe",
                    "email": "<EMAIL>",
                    "profile_type": "Patient",
                },
                "taken_by": {
                    "first_name": "Dr",
                    "last_name": "<PERSON>",
                    "profile_type": "Staff",
                },
                "reported_by": {
                    "first_name": "<PERSON>",
                    "last_name": "<PERSON><PERSON>",
                    "profile_type": "Visitor",
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_fields(self):
        self.service.data.get("found_by").pop("profile_type")

        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestLostFoundServiceModifyIncidentExistingUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = LostAndFoundFactory(created_by=self.super_user)
        self.service = LostFoundActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "found_by": {
                    "user_id": self.patient.id,
                },
                "taken_by": {
                    "user_id": self.visitor.id,
                },
                "reported_by": {
                    "user_id": self.staff.id,
                },
                "status": "Open",
            },
        )

    def test_modify_incident_success(self):
        response = self.service.modify_incident()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_modify_incident_missing_fields(self):
        self.service.data.get("found_by").pop("user_id")

        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_modify_incident_invalid_user_id(self):
        self.service.data.get("found_by")["user_id"] = 9999

        response = self.service.modify_incident()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestLostFoundServiceSendReviewIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = LostAndFoundFactory(created_by=self.super_user)
        self.review_template = ReviewTemplateFactory()
        self.assignee_1 = ProfileFactory()
        self.assignee_2 = ProfileFactory()
        self.assignee_3 = ProfileFactory()
        self.service = LostFoundActionsService(
            user=self.super_user,
            incident_id=self.incident.id,
            data={
                "review_template": self.review_template.id,
            },
        )

    def test_send_for_review_success(self):
        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_send_for_review_invalid_template(self):
        self.service.data["review_template"] = 9999

        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_missing_template(self):
        self.service.data.pop("review_template")

        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_assignees_and_template(self):
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]

        response = self.service.send_for_review()
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_send_for_review_assignees(self):
        self.service.data.pop("review_template")
        self.service.data["assignees"] = [self.assignee_1.id, self.assignee_2.id]

        response = self.service.send_for_review()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestLostFoundServiceMarkClose(BaseTestSetup):
    def setUp(self):
        super().setUp()
        # Create incident with one user, but close it with another user to avoid the business rule
        self.incident = LostAndFoundFactory(created_by=self.user_user)
        self.service = LostFoundActionsService(
            user=self.super_user,  # Super user closing incident created by regular user
            incident_id=self.incident.id,
            data={
                "action": "mark-closed",
            },
        )

    def test_mark_close_success(self):
        response = self.service.mark_closed()
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)


class TestLostFoundServiceGetIncidentById(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = LostAndFoundService(user=self.super_user)

    def test_get_incident_by_id(self):
        incident = LostAndFoundFactory(created_by=self.super_user)
        response = self.service.get_incident_by_id(incident.id)

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_incident_by_id_not_found(self):
        response = self.service.get_incident_by_id(9999)
        self.assertFalse(response.success)


class TestLostFoundServiceCase(BaseTestSetup):
    """Test Lost and Found Service operations as Superuser"""

    def setUp(self):
        super().setUp()
        # Use the super_user from BaseTestSetup
        self.service = LostAndFoundService(user=self.super_user)

    def test_get_incidents_as_superuser(self):
        # create 5 new incidents for new facility
        for _ in range(5):
            LostAndFoundFactory(
                department=self.super_user_dept,
                report_facility=self.super_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            LostAndFoundFactory()

        response = self.service.get_incidents_list()

        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data), 7)


class TestLostFoundServiceAsAdmin(BaseTestSetup):
    """Test Lost and Found Service operations as Admin user"""

    def setUp(self):
        super().setUp()
        self.service = LostAndFoundService(user=self.admin_user)

    def test_get_incidents_as_admin(self):
        """Test getting incidents as an admin user"""
        # Admin should see incidents from their facility
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestLostFoundServiceAsDirector(BaseTestSetup):
    """Test Lost and Found Service operations as Director user"""

    def setUp(self):
        super().setUp()
        self.service = LostAndFoundService(user=self.director_user)

    def test_get_incidents_as_director(self):
        """Test getting incidents as a director user"""
        # Director should see incidents from their facility
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestLostFoundServiceAsManager(BaseTestSetup):
    """Test Lost and Found Service operations as Manager user"""

    def setUp(self):
        super().setUp()
        self.service = LostAndFoundService(user=self.manager_user)

    def test_get_incidents_as_manager(self):
        """Test getting incidents as a manager user"""
        # Manager should see incidents from their department
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestLostFoundServiceAsRegularUser(BaseTestSetup):
    """Test Lost and Found Service operations as Regular user"""

    def setUp(self):
        super().setUp()
        self.service = LostAndFoundService(user=self.user_user)

    def test_get_incidents_as_regular_user(self):
        """Test getting incidents as a regular user"""
        # Regular user should see their own incidents and assigned reviews
        response = self.service.get_incidents_list()

        # Assertions
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertIn("retrieved successfully", response.message)


class TestCreateLostFoundServiceCase(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = LostAndFoundService(user=self.super_user)
        staff = UserProfileFactory(profile_type="Staff")
        visitor = UserProfileFactory(profile_type="Visitor")
        manager_user = UserProfileFactory(profile_type="Manager")
        self.valid_data = {
            # Status and Basic Info
            "status": "Draft",
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "current_step": 1,
            "is_resolved": False,
            "resolved": False,
            # Report Details
            "property_name": "Gold Watch",
            "reported_by": {"user_id": staff.id},
            "taken_by": {"user_id": manager_user.id},
            "date_reported": "2025-05-20",
            "time_reported": "14:30:00",
            "relation_to_patient": "Family Member",
            "item_description": "18k gold watch with leather strap",
            "action_taken": "Searched immediate area and filed report",
            # Found Item Details
            "is_found": True,
            "date_found": "2025-05-21",
            "time_found": "09:15:00",
            "location_found": "Hospital Cafeteria",
            "found_by": {"user_id": visitor.id},
            "disposal_of_unclaimed_property": "Stored in security office safe",
            # Return Details
            "returned_to": "Original Owner",
            "date_returned": "2025-05-22",
            "time_returned": "10:00:00",
            "location_returned": "Security Office",
            # Communication
            "date_letter_sent": "2025-05-21",
            "letter_sent_to": "John Doe",
            # Documents (optional)
            "documents": [],
        }

    def test_create_incident_success(self):
        """Test successful creation of lost and found incident"""
        response = self.service.create_incident(self.valid_data)

        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)
        self.assertIsNotNone(response.data["id"])

        # Verify all fields were saved correctly
        incident = response.data
        self.assertEqual(incident["property_name"], self.valid_data["property_name"])
        self.assertEqual(
            incident["item_description"], self.valid_data["item_description"]
        )
        self.assertEqual(incident["is_found"], self.valid_data["is_found"])
        self.assertEqual(incident["location_found"], self.valid_data["location_found"])

    def test_create_incident_minimum_required_fields(self):
        """Test creation with only required fields"""
        minimal_data = {
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "property_name": "Lost Keys",
            "item_description": "Car and house keys",
        }

        response = self.service.create_incident(minimal_data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)

    def test_create_incident_missing_required_fields(self):
        """Test creation fails when required fields are missing"""
        invalid_data = self.valid_data.copy()
        invalid_data.get("reported_by").pop("user_id")  # Remove required field

        response = self.service.create_incident(invalid_data)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_create_incident_with_new_users(self):
        """Test creation with new users for reported_by, taken_by, found_by"""
        data = self.valid_data.copy()
        data.update(
            {
                "reported_by": {
                    "first_name": "John",
                    "last_name": "Reporter",
                    "email": "<EMAIL>",
                    "profile_type": "Staff",
                },
                "taken_by": {
                    "first_name": "Jane",
                    "last_name": "Handler",
                    "email": "<EMAIL>",
                    "profile_type": "Staff",
                },
                "found_by": {
                    "first_name": "Bob",
                    "last_name": "Finder",
                    "email": "<EMAIL>",
                    "profile_type": "Visitor",
                },
            }
        )

        response = self.service.create_incident(data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 201)

    def test_create_incident_invalid_dates(self):
        """Test creation fails with invalid dates"""
        invalid_data = self.valid_data.copy()
        invalid_data["date_found"] = "2025-13-45"  # Invalid date

        response = self.service.create_incident(invalid_data)
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)


class TestUpdateLostFoundServiceCase(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = LostAndFoundService(user=self.super_user)
        self.incident = LostAndFoundFactory(
            created_by=self.super_user,
            department=self.super_user_dept,
            report_facility=self.super_user_fac,
        )
        staff = UserProfileFactory(profile_type="Staff")
        visitor = UserProfileFactory(profile_type="Visitor")
        manager_user = UserProfileFactory(profile_type="Manager")
        self.valid_update_data = {
            # Status and Basic Info
            "status": "Open",
            "department": self.manager_user_dept.id,
            "current_step": 2,
            "is_resolved": True,
            # Report Details
            "property_name": "Updated Watch Name",
            "reported_by": {"user_id": staff.id},
            "taken_by": {"user_id": manager_user.id},
            "relation_to_patient": "Staff Member",
            "item_description": "Updated description",
            "action_taken": "Item located and secured",
            # Found Item Details
            "is_found": True,
            "date_found": "2025-05-21",
            "time_found": "09:15:00",
            "location_found": "Staff Room",
            "found_by": {"user_id": visitor.id},
            "disposal_of_unclaimed_property": "Updated disposal info",
            # Return Details
            "returned_to": "Security Office",
            "date_returned": "2025-05-22",
            "time_returned": "10:00:00",
            "location_returned": "Main Desk",
            # Documents (optional)
            "documents": [],
        }

    def test_update_incident_success(self):
        """Test successful update of lost and found incident"""
        response = self.service.update_incident(
            self.incident.id, self.valid_update_data
        )

        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

        # Verify fields were updated correctly
        incident = response.data
        self.assertEqual(
            incident["property_name"], self.valid_update_data["property_name"]
        )
        self.assertEqual(
            incident["item_description"], self.valid_update_data["item_description"]
        )
        self.assertEqual(incident["is_found"], self.valid_update_data["is_found"])
        self.assertEqual(
            incident["location_found"], self.valid_update_data["location_found"]
        )

    def test_update_incident_with_new_users(self):
        """Test updating with new users"""
        update_data = self.valid_update_data.copy()
        update_data.update(
            {
                "reported_by": {
                    "first_name": "New",
                    "last_name": "Reporter",
                    "email": "<EMAIL>",
                    "profile_type": "Staff",
                },
                "taken_by": {
                    "first_name": "New",
                    "last_name": "Handler",
                    "email": "<EMAIL>",
                    "profile_type": "Staff",
                },
            }
        )

        response = self.service.update_incident(self.incident.id, update_data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)

    def test_update_nonexistent_incident(self):
        """Test updating non-existent incident"""
        response = self.service.update_incident(99999, self.valid_update_data)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_update_incident_unauthorized(self):
        """Test updating incident without proper permissions"""
        # Create service with regular user
        unauthorized_service = LostAndFoundService(user=self.user_user)

        response = unauthorized_service.update_incident(
            self.incident.id, self.valid_update_data
        )

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)

    def test_update_incident_with_status_change(self):
        """Test updating incident status triggers email"""
        update_data = {"status": "Open", "property_name": "Updated Name"}

        response = self.service.update_incident(self.incident.id, update_data)
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(response.data["status"], "Open")

    def test_update_incident_with_documents(self):
        """Test updating incident with document attachments"""
        document = Document.objects.create(
            name="Test Doc",
            document_url="path/to/file.pdf",
        )

        update_data = self.valid_update_data.copy()
        update_data["documents"] = [document.id]

        response = self.service.update_incident(self.incident.id, update_data)
        # update incident: {response.message}
        pass
        self.assertTrue(response.success)
        self.assertEqual(response.code, 200)
        self.assertEqual(len(response.data["documents"]), 1)
