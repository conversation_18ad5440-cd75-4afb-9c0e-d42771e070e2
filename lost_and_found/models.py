from django.db import models
from accounts.models import UserProfile
from base.models import BaseModel, Department, Facility
from base.services.permissions.mixins import IncidentsPermissionsMixin
from documents.models import Document
from reviews.models import Review
from tasks.models import ReviewProcess, ReviewProcessTasks


class LostAndFoundBase(BaseModel):
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]

    # Status and Basic Info
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES, max_length=255, default="Draft"
    )
    report_facility = models.ForeignKey(
        Facility, blank=True, null=True, on_delete=models.SET_NULL
    )
    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    is_resolved = models.BooleanField(default=False)
    resolved = models.BooleanField(blank=True, null=True)

    # Report Details
    property_name = models.CharField(max_length=255, blank=True, null=True)
    reported_by = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_person_reporting_info",
    )
    taken_by = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_person_taking_report_info",
    )
    date_reported = models.DateField(auto_now_add=True, null=True, blank=True)
    time_reported = models.TimeField(auto_now=True, null=True, blank=True)
    relation_to_patient = models.CharField(max_length=255, null=True, blank=True)
    item_description = models.CharField(max_length=500, null=True, blank=True)
    action_taken = models.CharField(max_length=2550, null=True, blank=True)

    # Found Item Details
    is_found = models.BooleanField(default=False, blank=True)
    date_found = models.DateField(null=True, blank=True)
    time_found = models.TimeField(null=True, blank=True)
    location_found = models.CharField(max_length=255, null=True, blank=True)
    found_by = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_found_by_info",
    )
    disposal_of_unclaimed_property = models.CharField(
        max_length=500, null=True, blank=True
    )

    # Return Details
    returned_to = models.CharField(max_length=255, null=True, blank=True)
    date_returned = models.DateField(null=True, blank=True)
    time_returned = models.TimeField(null=True, blank=True)
    location_returned = models.CharField(max_length=255, null=True, blank=True)

    # Communication
    date_letter_sent = models.DateField(null=True, blank=True)
    letter_sent_to = models.CharField(max_length=255, null=True, blank=True)

    # Related Models
    reviews = models.ManyToManyField(
        Review,
        related_name="%(class)s_lost_and_found_reviews_field",
        blank=True,
    )
    documents = models.ManyToManyField(
        Document,
        related_name="%(class)s_lost_and_found_incident_documents_field",
        blank=True,
    )

    def __str__(self):
        return f"{self.property_name} - {self.reported_by}"

    class Meta:
        abstract = True
        permissions = IncidentsPermissionsMixin.custom_permissions


class LostAndFound(LostAndFoundBase):
    is_modified = models.BooleanField(default=False)
    review_process = models.ForeignKey(
        ReviewProcess,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_review_process",
    )
    review_tasks = models.ManyToManyField(
        ReviewProcessTasks,
        related_name="%(class)s_incident_tasks",
        blank=True,
    )


class LostAndFoundVersion(LostAndFoundBase):
    original_report = models.ForeignKey(
        LostAndFound,
        on_delete=models.CASCADE,
        related_name="versions",
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "lost_and_found_version"
