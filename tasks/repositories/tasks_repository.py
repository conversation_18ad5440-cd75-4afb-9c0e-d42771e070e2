from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from tasks.models import ReviewProcessTasks
from tasks.serializers import ReviewProcessTasksSerializer

logging_service = LoggingService()


class TasksRepository:
    """Repository for handling ReviewProcessTasks database operations."""

    def get_all_tasks(self) -> RepositoryResponse:
        """
        Retrieve all tasks with their related objects.

        Returns:
            RepositoryResponse with tasks queryset containing all tasks
        """
        try:
            tasks_query = ReviewProcessTasks.objects.select_related(
                "review_process",
                "review_template",
                "depends_on",
                "created_by",
                "updated_by"
            ).prefetch_related(
                "reviewers",
                "reviewers_completed",
                "review_groups",
                "review_groups__members",
                "groups_completed"
            ).all()

            return RepositoryResponse(
                success=True, 
                message="Tasks retrieved successfully", 
                data=tasks_query
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, 
                message="Error retrieving tasks", 
                data=None
            )

    def get_task_by_id(self, task_id):
        """
        Retrieve a specific task by ID.

        Args:
            task_id: ID of the task to retrieve

        Returns:
            RepositoryResponse with task object
        """
        try:
            task = ReviewProcessTasks.objects.get(id=task_id)
            return RepositoryResponse(
                success=True, message="Task retrieved successfully", data=task
            )
        except ReviewProcessTasks.DoesNotExist:
            return RepositoryResponse(
                success=False, message="Task not found", data=None
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, message="Error retrieving task", data=None
            )

    def create_task(self, data):
        """
        Create a new task.

        Args:
            data: Dictionary containing task data

        Returns:
            RepositoryResponse with created task
        """
        try:
            serializer = ReviewProcessTasksSerializer(data=data)
            if not serializer.is_valid():
                return RepositoryResponse(
                    success=False, message="Invalid task data", data=serializer.errors
                )

            task = ReviewProcessTasks.objects.create(**serializer.validated_data)
            return RepositoryResponse(
                success=True, message="Task created successfully", data=task
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, message="Error creating task", data=None
            )

    def update_task(self, task_id, data):
        """
        Update an existing task.

        Args:
            task_id: ID of the task to update
            data: Dictionary containing updated task data

        Returns:
            RepositoryResponse with updated task
        """
        try:
            task = ReviewProcessTasks.objects.get(id=task_id)
            serializer = ReviewProcessTasksSerializer(task, data=data, partial=True)

            if not serializer.is_valid():
                return RepositoryResponse(
                    success=False, message="Invalid task data", data=serializer.errors
                )

            updated_task = serializer.save()
            return RepositoryResponse(
                success=True, message="Task updated successfully", data=updated_task
            )
        except ReviewProcessTasks.DoesNotExist:
            return RepositoryResponse(
                success=False, message="Task not found", data=None
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, message="Error updating task", data=None
            )

    def delete_task(self, task_id):
        """
        Delete an existing task.

        Args:
            task_id: ID of the task to delete

        Returns:
            RepositoryResponse with success message
        """
        try:
            task = ReviewProcessTasks.objects.get(id=task_id)
            task.delete()
            return RepositoryResponse(
                success=True, message="Task deleted successfully", data=None
            )
        except ReviewProcessTasks.DoesNotExist:
            return RepositoryResponse(
                success=False, message="Task not found", data=None
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, message="Error deleting task", data=None
            )
