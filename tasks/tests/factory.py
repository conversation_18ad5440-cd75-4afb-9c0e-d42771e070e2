import factory
from factory.django import DjangoModelFactory
from accounts.tests.factory import ProfileFactory
from base.tests.factory import UserFactory
from tasks.models import (
    ReviewGroups,
    ReviewProcess,
    ReviewProcessTasks,
    ReviewTemplateTasks,
    ReviewTemplates,
)
from datetime import date, timedelta


class ReviewGroupFactory(DjangoModelFactory):
    class Meta:
        model = ReviewGroups

    title = factory.Faker("sentence", nb_words=5)
    description = factory.Faker("text", max_nb_chars=1000)
    created_by = factory.SubFactory(UserFactory)
    updated_by = factory.SubFactory(UserFactory)

    @factory.post_generation
    def members(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of members to the review group.
        Usage: ReviewGroupFactory(members=[profile1, profile2])
        """
        if not create:
            return
        if extracted:
            for member in extracted:
                self.members.add(member)
        else:
            # Default: add 3 profiles if none provided
            for _ in range(kwargs.get("size", 3)):
                self.members.add(ProfileFactory.create())

    @factory.post_generation
    def assignees(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of assignees (users) to the review group.
        Usage: ReviewGroupFactory(assignees=[user1, user2])
        """
        if not create:
            return
        if extracted:
            for assignee in extracted:
                self.assignees.add(assignee)
        else:
            # Default: add 1 assignee if none provided
            for _ in range(kwargs.get("size", 1)):
                self.assignees.add(UserFactory.create())


class ReviewTemplateFactory(DjangoModelFactory):
    class Meta:
        model = ReviewTemplates

    incident_type = factory.Faker("word")
    name = factory.Faker("sentence", nb_words=3)
    description = factory.Faker("text", max_nb_chars=1000)
    created_by = factory.SubFactory(UserFactory)
    updated_by = factory.SubFactory(UserFactory)

    @factory.post_generation
    def assignees(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of assignees (users) to the review template.
        Usage: ReviewTemplateFactory(assignees=[user1, user2])
        """
        if not create:
            return
        if extracted:
            for assignee in extracted:
                self.assignees.add(assignee)
        else:
            # Default: add 1 assignee if none provided
            for _ in range(kwargs.get("size", 1)):
                self.assignees.add(UserFactory.create())


class ReviewTemplateTaskFactory(DjangoModelFactory):
    class Meta:
        model = ReviewTemplateTasks

    name = factory.Faker("sentence", nb_words=4)
    description = factory.Faker("text", max_nb_chars=1000)
    review_template = factory.SubFactory(ReviewTemplateFactory)
    require_approval_for_all_groups = factory.Faker(
        "boolean", chance_of_getting_true=50
    )
    task_priority = 1
    created_by = factory.SubFactory(UserFactory)
    updated_by = factory.SubFactory(UserFactory)

    @factory.post_generation
    def review_groups(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of review groups to the task.
        Usage: ReviewTemplateTaskFactory(review_groups=[group1, group2])
        """
        if not create:
            return
        if extracted:
            for group in extracted:
                self.review_groups.add(group)
        else:
            # Default: add 1 review group if none provided
            for _ in range(kwargs.get("size", 1)):
                self.review_groups.add(ReviewGroupFactory.create())

    @factory.post_generation
    def members_completed(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of profiles to members_completed.
        Usage: ReviewTemplateTaskFactory(members_completed=[profile1, profile2])
        """
        if not create:
            return
        if extracted:
            for member in extracted:
                self.members_completed.add(member)
        else:
            # Default: add 0 members_completed (optional field)
            for _ in range(kwargs.get("size", 0)):
                self.members_completed.add(ProfileFactory.create())

    @factory.post_generation
    def assignees(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of assignees (users) to the task.
        Usage: ReviewTemplateTaskFactory(assignees=[user1, user2])
        """
        if not create:
            return
        if extracted:
            for assignee in extracted:
                self.assignees.add(assignee)
        else:
            # Default: add 1 assignee if none provided
            for _ in range(kwargs.get("size", 1)):
                self.assignees.add(UserFactory.create())


class ReviewProcessFactory(DjangoModelFactory):
    class Meta:
        model = ReviewProcess

    name = factory.Faker("sentence", nb_words=3)
    description = factory.Faker("text", max_nb_chars=1000)
    created_by = factory.SubFactory(UserFactory)
    updated_by = factory.SubFactory(UserFactory)


class ReviewProcessTasksFactory(DjangoModelFactory):
    class Meta:
        model = ReviewProcessTasks

    # Fields inherited from ReviewTemplateTasks
    name = factory.Faker("sentence", nb_words=4)
    description = factory.Faker("text", max_nb_chars=1000)
    review_template = factory.SubFactory(ReviewTemplateFactory)
    require_approval_for_all_groups = factory.Faker(
        "boolean", chance_of_getting_true=50
    )
    task_priority = 1
    created_by = factory.SubFactory(UserFactory)
    updated_by = factory.SubFactory(UserFactory)

    # Fields specific to ReviewProcessTasks
    review_process = factory.SubFactory(ReviewProcessFactory)
    status = factory.Faker(
        "random_element",
        elements=[choice[0] for choice in ReviewProcessTasks.TASK_STATUS_CHOICES],
    )
    require_all_members_to_complete = factory.Faker(
        "boolean", chance_of_getting_true=50
    )
    deadline = factory.Faker(
        "date_between",
        start_date=date.today(),
        end_date=date.today() + timedelta(days=30),
    )

    @factory.post_generation
    def groups_completed(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of groups to groups_completed.
        Usage: ReviewProcessTasksFactory(groups_completed=[group1, group2])
        """
        if not create:
            return
        if extracted:
            for group in extracted:
                self.groups_completed.add(group)
        else:
            # Default: add 1 group if none provided
            for _ in range(kwargs.get("size", 1)):
                self.groups_completed.add(ReviewGroupFactory.create())

    @factory.post_generation
    def reviewers(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of profiles to reviewers.
        Usage: ReviewProcessTasksFactory(reviewers=[profile1, profile2])
        """
        if not create:
            return
        if extracted:
            for reviewer in extracted:
                self.reviewers.add(reviewer)
        else:
            # Default: add 2 reviewers if none provided
            for _ in range(kwargs.get("size", 2)):
                self.reviewers.add(ProfileFactory.create())

    @factory.post_generation
    def reviewers_completed(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of profiles to reviewers_completed.
        Usage: ReviewProcessTasksFactory(reviewers_completed=[profile1, profile2])
        """
        if not create:
            return
        if extracted:
            for reviewer in extracted:
                self.reviewers_completed.add(reviewer)
        else:
            # Default: add 0 reviewers_completed (optional field)
            for _ in range(kwargs.get("size", 0)):
                self.reviewers_completed.add(ProfileFactory.create())

    @factory.post_generation
    def assignees(self, create, extracted, **kwargs):
        """
        Allow dynamic addition of assignees (users) to the task.
        Usage: ReviewProcessTasksFactory(assignees=[user1, user2])
        """
        if not create:
            return
        if extracted:
            for assignee in extracted:
                self.assignees.add(assignee)
        else:
            # Default: add 1 assignee if none provided
            for _ in range(kwargs.get("size", 1)):
                self.assignees.add(UserFactory.create())
