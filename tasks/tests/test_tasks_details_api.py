from rest_framework import status
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserFactory
from tasks.tests.factory import (
    ReviewProcessTasksFactory,
    ReviewGroupFactory,
    ReviewProcessFactory,
    ReviewTemplateFactory,
)
from tasks.models import ReviewProcessTasks


class TestTasksDetailsAPIGet(BaseTestSetup):
    """Test GET method for tasks_details_api endpoint"""
    
    def setUp(self):
        super().setUp()
        self.task = ReviewProcessTasksFactory()
        self.endpoint = f"/api/tasks/{self.task.id}/"

    def test_get_task_success(self):
        """Test successful task retrieval"""
        self._authenticate_user(self.admin_user)
        
        response = self.client.get(self.endpoint, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["id"], self.task.id)
        self.assertEqual(response.data["name"], self.task.name)
        self.assertEqual(response.data["description"], self.task.description)

    def test_get_task_not_found(self):
        """Test task retrieval with non-existent task ID"""
        self._authenticate_user(self.admin_user)
        
        non_existent_id = 99999
        response = self.client.get(f"/api/tasks/{non_existent_id}/", format="json")
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("message", response.data)

    def test_get_task_unauthenticated(self):
        """Test task retrieval without authentication"""
        response = self.client.get(self.endpoint, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestTasksDetailsAPIPut(BaseTestSetup):
    """Test PUT method for tasks_details_api endpoint"""
    
    def setUp(self):
        super().setUp()
        self.task = ReviewProcessTasksFactory()
        self.endpoint = f"/api/tasks/{self.task.id}/"
        self.update_data = {
            "name": "Updated Task Name",
            "description": "Updated task description",
            "status": "In Progress",
        }

    def test_update_task_success_as_super_user(self):
        """Test successful task update as super user"""
        self._authenticate_user(self.super_user)
        
        response = self.client.put(self.endpoint, data=self.update_data, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], self.update_data["name"])
        self.assertEqual(response.data["description"], self.update_data["description"])

    def test_update_task_success_as_admin_user(self):
        """Test successful task update as admin user"""
        self._authenticate_user(self.admin_user)

        response = self.client.put(self.endpoint, data=self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], self.update_data["name"])

    def test_update_task_permission_denied_regular_user(self):
        """Test task update permission denied for regular user"""
        self._authenticate_user(self.user_user)
        
        response = self.client.put(self.endpoint, data=self.update_data, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("permission", response.data["message"].lower())

    def test_update_task_with_groups_completed(self):
        """Test task update with groups_completed field"""
        self._authenticate_user(self.super_user)
        
        review_group = ReviewGroupFactory()
        update_data_with_groups = self.update_data.copy()
        update_data_with_groups["groups_completed"] = [review_group.id]
        
        response = self.client.put(self.endpoint, data=update_data_with_groups, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_task_with_invalid_review_group(self):
        """Test task update with non-existent review group"""
        self._authenticate_user(self.super_user)
        
        update_data_with_invalid_group = self.update_data.copy()
        update_data_with_invalid_group["groups_completed"] = [99999]
        
        response = self.client.put(self.endpoint, data=update_data_with_invalid_group, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Review group not found", response.data["message"])

    def test_update_task_not_found(self):
        """Test task update with non-existent task ID"""
        self._authenticate_user(self.super_user)
        
        non_existent_id = 99999
        response = self.client.put(f"/api/tasks/{non_existent_id}/", data=self.update_data, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_task_unauthenticated(self):
        """Test task update without authentication"""
        response = self.client.put(self.endpoint, data=self.update_data, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestTasksDetailsAPIDelete(BaseTestSetup):
    """Test DELETE method for tasks_details_api endpoint"""
    
    def setUp(self):
        super().setUp()
        self.task = ReviewProcessTasksFactory()
        self.endpoint = f"/api/tasks/{self.task.id}/"

    def test_delete_task_success_as_super_user(self):
        """Test successful task deletion as super user"""
        self._authenticate_user(self.super_user)

        response = self.client.delete(self.endpoint, format="json")

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(ReviewProcessTasks.objects.filter(id=self.task.id).exists())

    def test_delete_task_success_as_admin_user(self):
        """Test successful task deletion as admin user"""
        self._authenticate_user(self.admin_user)

        response = self.client.delete(self.endpoint, format="json")

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(ReviewProcessTasks.objects.filter(id=self.task.id).exists())

    def test_delete_task_permission_denied_regular_user(self):
        """Test task deletion permission denied for regular user"""
        self._authenticate_user(self.user_user)
        
        response = self.client.delete(self.endpoint, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("permission", response.data["message"].lower())
        self.assertTrue(ReviewProcessTasks.objects.filter(id=self.task.id).exists())

    def test_delete_task_not_found(self):
        """Test task deletion with non-existent task ID"""
        self._authenticate_user(self.super_user)
        
        non_existent_id = 99999
        response = self.client.delete(f"/api/tasks/{non_existent_id}/", format="json")
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_delete_task_unauthenticated(self):
        """Test task deletion without authentication"""
        response = self.client.delete(self.endpoint, format="json")
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestTasksDetailsAPIMethodNotAllowed(BaseTestSetup):
    """Test unsupported HTTP methods for tasks_details_api endpoint"""

    def setUp(self):
        super().setUp()
        self.task = ReviewProcessTasksFactory()
        self.endpoint = f"/api/tasks/{self.task.id}/"

    def test_post_method_not_allowed(self):
        """Test POST method returns 405 Method Not Allowed"""
        self._authenticate_user(self.admin_user)

        response = self.client.post(self.endpoint, data={}, format="json")

        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)


class TestTasksDetailsAPIErrorHandling(BaseTestSetup):
    """Test error handling and edge cases for tasks_details_api endpoint"""

    def setUp(self):
        super().setUp()
        self.task = ReviewProcessTasksFactory()
        self.endpoint = f"/api/tasks/{self.task.id}/"

    def test_update_task_with_invalid_data(self):
        """Test task update with invalid data"""
        self._authenticate_user(self.super_user)

        invalid_data = {
            "name": "",
            "status": "InvalidStatus",
        }

        response = self.client.put(self.endpoint, data=invalid_data, format="json")

        self.assertIn(response.status_code, [status.HTTP_400_BAD_REQUEST, status.HTTP_200_OK])



    def test_update_task_with_reviewers(self):
        """Test task update with reviewers field"""
        from accounts.tests.factory import ProfileFactory

        self._authenticate_user(self.super_user)

        profile = ProfileFactory()
        update_data = {
            "name": "Task with Reviewers",
            "reviewers": [profile.id]
        }

        response = self.client.put(self.endpoint, data=update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_task_with_related_data(self):
        """Test task retrieval includes related data"""
        self._authenticate_user(self.admin_user)

        review_group = ReviewGroupFactory()
        user = UserFactory()
        task = ReviewProcessTasksFactory()
        task.review_groups.add(review_group)
        task.assignees.add(user)
        task.save()

        endpoint = f"/api/tasks/{task.id}/"
        response = self.client.get(endpoint, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("review_groups", response.data)
        self.assertIn("assignees", response.data)

    def test_update_task_with_depends_on(self):
        """Test task update with depends_on field"""
        self._authenticate_user(self.super_user)

        dependency_task = ReviewProcessTasksFactory()
        update_data = {
            "name": "Dependent Task",
            "depends_on": dependency_task.id
        }

        response = self.client.put(self.endpoint, data=update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_delete_task_with_dependencies(self):
        """Test deleting a task that other tasks depend on"""
        self._authenticate_user(self.super_user)

        dependent_task = ReviewProcessTasksFactory(depends_on=self.task)

        response = self.client.delete(self.endpoint, format="json")

        self.assertIn(response.status_code, [status.HTTP_204_NO_CONTENT, status.HTTP_400_BAD_REQUEST])


class TestTasksDetailsAPIPermissions(BaseTestSetup):
    """Test permission scenarios for tasks_details_api endpoint"""

    def setUp(self):
        super().setUp()
        self.task = ReviewProcessTasksFactory()
        self.endpoint = f"/api/tasks/{self.task.id}/"

    def test_director_user_permissions(self):
        """Test director user permissions"""
        self._authenticate_user(self.director_user)

        response = self.client.get(self.endpoint, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response = self.client.put(self.endpoint, data={"name": "Updated"}, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_manager_user_permissions(self):
        """Test manager user permissions"""
        self._authenticate_user(self.manager_user)

        response = self.client.get(self.endpoint, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response = self.client.delete(self.endpoint, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
