from django.test import TransactionTestCase

from base.tests.factory import UserFactory
from tasks.services.review_templates import ReviewTemplateService
from tasks.tests.factory import ReviewTemplateFactory


class TestGetReviewTemplatesService(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.service = ReviewTemplateService(user=self.user)

    def test_get_review_templates_success(self):
        templates = ReviewTemplateFactory.create_batch(2)
        response = self.service.get_review_templates(params={})

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 2)
        self.assertEqual(response.data["results"][0]["name"], templates[0].name)


class TestGetReviewTemplateByIdService(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.service = ReviewTemplateService(user=self.user)
        self.template = ReviewTemplateFactory()

    def test_get_review_template_by_id_success(self):
        response = self.service.get_review_template_by_id(self.template.id)

        self.assertTrue(response.success)
        self.assertEqual(response.data["id"], self.template.id)
        self.assertEqual(response.data["name"], self.template.name)


class TestCreateReviewTemplateService(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.service = ReviewTemplateService(user=self.user)
        self.valid_data = {
            "incident_type": "Test Incident",
            "name": "Test Template",
            "description": "Test Description",
        }

    def test_create_review_template_success(self):
        response = self.service.create_review_template(self.valid_data)

        self.assertTrue(response.success)
        self.assertEqual(response.data["name"], "Test Template")
        self.assertEqual(response.data["description"], "Test Description")


class TestUpdateReviewTemplateService(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.service = ReviewTemplateService(user=self.user)
        self.template = ReviewTemplateFactory()
        self.valid_data = {
            "name": "Updated Template",
            "description": "Updated Description",
        }

    def test_update_review_template_success(self):
        response = self.service.update_review_template(
            self.template.id, self.valid_data
        )

        self.assertTrue(response.success)
        self.assertEqual(response.data["name"], "Updated Template")
        self.assertEqual(response.data["description"], "Updated Description")


class TestDeleteReviewTemplateService(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.service = ReviewTemplateService(user=self.user)
        self.template = ReviewTemplateFactory()

    def test_delete_review_template_success(self):
        response = self.service.delete_review_template(self.template.id)

        self.assertTrue(response.success)
        self.assertIsNone(response.data)
