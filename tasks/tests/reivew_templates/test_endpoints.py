from django.test import TransactionTestCase
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserFactory
from tasks.models import ReviewTemplates
from tasks.tests.factory import ReviewTemplateFactory


class TestGetReviewTemplatesService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory(email="<EMAIL>")

    def test_get_review_templates_success(self):
        templates = ReviewTemplateFactory.create_batch(2)

        self._authenticate_user(self.admin_user)
        response = self.client.get("/api/permissions/review-templates/")
        if not response.status_code == 200:
            self.fail(f"Failed to get review templates, f{response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 2)
        self.assertEqual(response.data["results"][0]["name"], templates[0].name)


class TestCreateReviewTemplateService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory(email="<EMAIL>")
        self.valid_data = {
            "incident_type": "Test Incident",
            "name": "Test Template",
            "description": "Test Description",
        }

    def test_create_review_template_success(self):
        self._authenticate_user(self.admin_user)
        response = self.client.post(
            "/api/permissions/review-templates/",
            self.valid_data,
            format="json",
        )
        if not response.status_code == 201:
            self.fail(f"Failed to create review template, f{response.data}")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["name"], "Test Template")
        self.assertEqual(response.data["description"], "Test Description")


class TestUpdateReviewTemplateService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory(email="<EMAIL>")
        self.template = ReviewTemplateFactory()
        self.valid_data = {
            "name": "Updated Template",
            "description": "Updated Description",
        }

    def test_update_review_template_success(self):
        self._authenticate_user(self.admin_user)
        response = self.client.put(
            f"/api/permissions/review-templates/{self.template.id}/",
            self.valid_data,
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to update review template, f{response.data}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], "Updated Template")
        self.assertEqual(response.data["description"], "Updated Description")
        self.assertEqual(response.data["id"], self.template.id)


class TestDeleteReviewTemplateService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory(email="<EMAIL>")
        self.template = ReviewTemplateFactory()

    def test_delete_review_template_success(self):
        self._authenticate_user(self.admin_user)
        response = self.client.delete(
            f"/api/permissions/review-templates/{self.template.id}/"
        )
        if not response.status_code == 204:
            self.fail(f"Failed to delete review template, f{response.data}")
        self.assertEqual(response.status_code, 204)
        self.assertFalse(ReviewTemplates.objects.filter(id=self.template.id).exists())


class TestListReviewTemplatesFeatures(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self._authenticate_user(self.admin_user)
        self.templates = [
            ReviewTemplateFactory(name="Alpha Template", description="First desc"),
            ReviewTemplateFactory(name="Beta Template", description="Second desc"),
            ReviewTemplateFactory(name="Gamma Template", description="Alpha in desc"),
        ]

    def test_search_review_templates_by_name_and_description(self):
        # Search by name
        response = self.client.get("/api/permissions/review-templates/?q=Alpha")
        self.assertEqual(response.status_code, 200)
        self.assertTrue(any("Alpha" in r["name"] for r in response.data["results"]))
        self.assertTrue(
            any("Alpha" in r["description"] for r in response.data["results"])
        )
        # Search by description
        response = self.client.get("/api/permissions/review-templates/?q=Second desc")
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            any("Second desc" in r["description"] for r in response.data["results"])
        )

    def test_sort_review_templates_by_name_and_created_at(self):
        # Sort by name ascending
        response = self.client.get(
            "/api/permissions/review-templates/?order_by=name&sort_order=asc"
        )
        self.assertEqual(response.status_code, 200)
        names = [r["name"] for r in response.data["results"]]
        self.assertEqual(names, sorted(names))
        # Sort by name descending
        response = self.client.get(
            "/api/permissions/review-templates/?order_by=name&sort_order=desc"
        )
        self.assertEqual(response.status_code, 200)
        names = [r["name"] for r in response.data["results"]]
        self.assertEqual(names, sorted(names, reverse=True))
        # Sort by created_at ascending
        response = self.client.get(
            "/api/permissions/review-templates/?order_by=created_at&sort_order=asc"
        )
        self.assertEqual(response.status_code, 200)
        created_ats = [r["created_at"] for r in response.data["results"]]
        self.assertEqual(created_ats, sorted(created_ats))
        # Sort by created_at descending
        response = self.client.get(
            "/api/permissions/review-templates/?order_by=created_at&sort_order=desc"
        )
        self.assertEqual(response.status_code, 200)
        created_ats = [r["created_at"] for r in response.data["results"]]
        self.assertEqual(created_ats, sorted(created_ats, reverse=True))

    def test_review_templates_pagination(self):
        # Create more templates for pagination
        for i in range(7):
            ReviewTemplateFactory(name=f"Template {i}")
        response = self.client.get(
            "/api/permissions/review-templates/?page=2&page_size=3"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["page"], 2)
        self.assertEqual(response.data["page_size"], 3)
        self.assertEqual(len(response.data["results"]), 3)
        self.assertTrue(response.data["has_next"])
        self.assertTrue(response.data["has_previous"])
        self.assertGreaterEqual(response.data["count"], 10)
