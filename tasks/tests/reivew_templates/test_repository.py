from django.test import TransactionTestCase
from accounts.repositories.permissions.review_templates import ReviewTemplatesRepository
from base.tests.factory import UserFactory
from tasks.models import ReviewTemplates
from tasks.tests.factory import ReviewTemplateFactory


class TestGetReviewTemplates(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.repo = ReviewTemplatesRepository(user=self.user)

    def test_get_review_templates_success(self):
        ReviewTemplateFactory.create_batch(2)
        response = self.repo.get_review_templates()

        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 2)


class TestGetReviewTemplateById(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.repo = ReviewTemplatesRepository(user=self.user)
        self.template = ReviewTemplateFactory()

    def test_get_review_template_by_id_success(self):
        response = self.repo.get_review_template_by_id(self.template.id)

        self.assertTrue(response.success)
        self.assertEqual(response.data.id, self.template.id)


class TestCreateReviewTemplate(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.repo = ReviewTemplatesRepository(user=self.user)
        self.valid_data = {
            "incident_type": "Test Incident",
            "name": "Test Template",
            "description": "Test Description",
        }

    def test_create_review_template_success(self):
        response = self.repo.create_review_template(self.valid_data)

        self.assertTrue(response.success)
        self.assertEqual(response.data.name, "Test Template")
        self.assertEqual(response.data.description, "Test Description")


class TestUpdateReviewTemplate(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.repo = ReviewTemplatesRepository(user=self.user)
        self.template = ReviewTemplateFactory()
        self.valid_data = {
            "name": "Updated Template",
            "description": "Updated Description",
        }

    def test_update_review_template_success(self):
        response = self.repo.update_review_template(self.template.id, self.valid_data)

        self.assertTrue(response.success)
        self.assertEqual(response.data.name, "Updated Template")
        self.assertEqual(response.data.description, "Updated Description")


class TestDeleteReviewTemplate(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory(email="<EMAIL>")
        self.repo = ReviewTemplatesRepository(user=self.user)
        self.template = ReviewTemplateFactory()

    def test_delete_review_template_success(self):
        response = self.repo.delete_review_template(self.template.id)

        self.assertTrue(response.success)
        self.assertIsNone(response.data)
        self.assertEqual(ReviewTemplates.objects.filter(id=self.template.id).count(), 0)
