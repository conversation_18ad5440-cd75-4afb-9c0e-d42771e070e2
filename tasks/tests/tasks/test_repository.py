from django.test import TestCase
from django.utils import timezone

from tasks.repositories.tasks_repository import TasksRepository
from tasks.models import ReviewProcess, ReviewProcessTasks
from tasks.tests.factory import (
    ReviewProcessTasksFactory,
    ReviewGroupFactory,
)
from base.tests.base_setup import BaseTestSetup
from accounts.models import Profile


class TestTasksRepository(BaseTestSetup):
    def setUp(self):
        super().setUp()
        
        self.user = self.user_user
        self.superuser = self.super_user
        
        self.user_profile = Profile.objects.get(user=self.user)
        
        self.repository = TasksRepository()
        
        self.review_group = ReviewGroupFactory()
        
        self.task = ReviewProcessTasksFactory(
            name="Test Task",
            description="Test Description",
            status="Pending",
            task_priority=1,
            created_by=self.user
        )
        self.task.review_groups.add(self.review_group)
        
        self.task.reviewers.add(self.user_profile)
    
    def test_get_all_tasks(self):
        """Test retrieving all tasks"""
        response = self.repository.get_all_tasks()
        
        self.assertTrue(response.success)
        self.assertGreaterEqual(len(response.data), 1)
        self.assertEqual(response.message, "Tasks retrieved successfully")
        
        task_ids = [task.id for task in response.data]
        self.assertIn(self.task.id, task_ids)
    
    def test_get_all_tasks_with_related_objects(self):
        """Test that related objects are properly loaded"""
        review_process = ReviewProcess.objects.create(
            name="Test Review Process",
            description="Test Review Process Description"
        )
        
        dependent_task = ReviewProcessTasksFactory(
            name="Dependent Task",
            description="This task is a dependency",
            status="Completed",
            review_process=review_process
        )
        
        task_with_relations = ReviewProcessTasksFactory(
            name="Task With Relations",
            description="This task has many relations",
            status="Pending",
            review_process=review_process,
            depends_on=dependent_task
        )
        
        second_review_group = ReviewGroupFactory()
        task_with_relations.review_groups.add(second_review_group)
        
        task_with_relations.reviewers.clear()
        task_with_relations.reviewers.add(self.user_profile)
        
        response = self.repository.get_all_tasks()
        
        self.assertTrue(response.success)
        
        found_task = None
        for task in response.data:
            if task.id == task_with_relations.id:
                found_task = task
                break
        
        self.assertIsNotNone(found_task)
        
        with self.assertNumQueries(0):
            self.assertEqual(found_task.review_process.name, "Test Review Process")
            self.assertEqual(found_task.depends_on.name, "Dependent Task")
            reviewers_list = list(found_task.reviewers.all())
            review_groups_list = list(found_task.review_groups.all())
        
        self.assertEqual(len(reviewers_list), 1)
        self.assertEqual(len(review_groups_list), 1)
        self.assertEqual(review_groups_list[0].id, second_review_group.id)
    
    def test_get_all_tasks_empty_database(self):
        """Test retrieving tasks when there are none in the database"""
        ReviewProcessTasks.objects.all().delete()
        
        response = self.repository.get_all_tasks()
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 0)
    
    def test_get_all_tasks_error_handling(self):
        """Test error handling in get_all_tasks method"""
        original_select_related = ReviewProcessTasks.objects.select_related
        
        try:
            def mock_select_related_error(*args, **kwargs):
                raise Exception("Database error")
            
            ReviewProcessTasks.objects.select_related = mock_select_related_error
            
            response = self.repository.get_all_tasks()
            
            self.assertFalse(response.success)
            self.assertEqual(response.message, "Error retrieving tasks")
            self.assertIsNone(response.data)
            
        finally:
            ReviewProcessTasks.objects.select_related = original_select_related
    
    def test_get_all_tasks_with_filters(self):
        """Test retrieving tasks with filters"""
        response = self.repository.get_all_tasks()
        
        self.assertTrue(response.success)
        self.assertGreaterEqual(len(response.data), 1)
        self.assertEqual(response.data[0].status, "Pending")
    
    def test_get_task_by_id(self):
        """Test retrieving a task by ID"""
        response = self.repository.get_task_by_id(self.task.id)
        
        self.assertTrue(response.success)
        self.assertEqual(response.data.id, self.task.id)
        self.assertEqual(response.data.name, "Test Task")
    
    def test_get_task_by_id_not_found(self):
        """Test retrieving a non-existent task"""
        response = self.repository.get_task_by_id(999999)
        
        self.assertFalse(response.success)
    
    def test_create_task(self):
        """Test creating a new task"""
        review_process = ReviewProcess.objects.create(
            name="Test Review Process",
            description="Test Review Process Description"
        )
        
        data = {
            "name": "New Task",
            "description": "New Description",
            "status": "Pending",
            "task_priority": 2,
            "created_by": self.user.id,
            "review_process": review_process.id
        }
        
        response = self.repository.create_task(data)
        
        if not response.success:
            print(f"Error creating task: {response.message}")
            print(f"Error details: {response.data}")
        
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, "New Task")
        self.assertEqual(response.data.description, "New Description")
    
    def test_update_task(self):
        """Test updating an existing task"""
        data = {
            "name": "Updated Task",
            "description": "Updated Description",
            "status": "Completed",
            "task_priority": 3,
            "created_by": self.user.id
        }
        
        response = self.repository.update_task(self.task.id, data)
        
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, "Updated Task")
        self.assertEqual(response.data.description, "Updated Description")
        self.assertEqual(response.data.status, "Completed")
        self.assertEqual(response.data.task_priority, 3)
    
