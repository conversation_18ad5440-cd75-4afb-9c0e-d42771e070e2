from adverse_drug_reaction.models import AdverseDrugReaction
from adverse_drug_reaction.tests.factory import AdverseDrugReactionFactory
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import ProfileFactory, UserFactory
from tasks.services.review_process_tasks import IncidentTasks
from tasks.tests.factory import (
    ReviewGroupFactory,
    ReviewProcessFactory,
    ReviewProcessTasksFactory,
)


class TestIncidentTasks(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.reviewer = ProfileFactory()
        review_group = ReviewGroupFactory()
        review_group.members.add(self.user_user_profile)
        review_process = ReviewProcessFactory()
        self.incident = AdverseDrugReactionFactory()
        self.tasks = [ReviewProcessTasksFactory() for _ in range(3)]

        self.tasks_with_review_process = [
            ReviewProcessTasksFactory(
                review_process=review_process,
            )
            for _ in range(3)
        ]
        self.tasks_with_reviewers = [ReviewProcessTasksFactory() for _ in range(3)]

        for task in self.tasks_with_review_process:
            task.review_groups.add(review_group)

        for task in self.tasks_with_reviewers:
            task.reviewers.add(self.reviewer)

        self.incident.review_tasks.set(self.tasks)
        self.incident.review_tasks.add(*self.tasks_with_reviewers)
        self.incident.review_tasks.add(*self.tasks_with_review_process)

    def test_get_incident_tasks(self):
        service = IncidentTasks(
            logged_in_user=self.super_user_profile.user,
            instance=self.incident,
        )
        response = service.get_tasks()

        if not response.success:
            self.fail(f"Failed to get tasks: {response.message}")

        self.assertEqual(response.success, True)
        self.assertEqual(
            len(response.data),
            len(
                self.tasks + self.tasks_with_review_process + self.tasks_with_reviewers
            ),
        )

    def test_get_incident_tasks_as_user(self):
        service = IncidentTasks(
            logged_in_user=self.user_user_profile.user,
            instance=self.incident,
        )
        response = service.get_tasks()

        if not response.success:
            self.fail(f"Failed to get tasks: {response.message}")

        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data), len(self.tasks_with_review_process))

    def test_get_incident_as_a_reviewer(self):
        service = IncidentTasks(
            logged_in_user=self.reviewer.user,
            instance=self.incident,
        )

        response = service.get_tasks()

        if not response.success:
            self.fail(f"Failed to get tasks: {response.message}")

        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data), len(self.tasks_with_reviewers))
