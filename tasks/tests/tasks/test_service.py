from django.test import TransactionTestCase

from base.tests.factory import UserFactory
from tasks.models import ReviewTemplates
from tasks.services.template_tasks import ReviewTasksService
from tasks.tests.factory import (
    ReviewGroupFactory,
    ReviewTemplateFactory,
    ReviewTemplateTaskFactory,
)


# create task
class TestCreateTask(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.review_template = ReviewTemplateFactory()
        self.review_groups = [ReviewGroupFactory.create_batch(2)]
        self.service = ReviewTasksService(user=self.user)
        self.valid_data = {
            "name": "Test Task",
            "description": "Test Description",
            "require_approval_for_all_groups": True,
            "task_priority": 1,
            "status": "Pending",
            "number_of_days": 3,
        }

    def test_create_task_success(self):
        response = self.service.create_task(
            template_id=self.review_template.id,
            data=self.valid_data,
        )

        if not response.success:
            self.fail(f"Failed to create task,{response}")

        self.assertTrue(response.success)
        self.assertEqual(response.data["name"], "Test Task")


# get tasks
class TestGetTasks(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.review_template = ReviewTemplateFactory()
        self.review_groups = [
            ReviewTemplateTaskFactory(
                review_template=self.review_template,
            )
            for _ in range(2)
        ]
        self.service = ReviewTasksService(user=self.user)

    def test_get_tasks_success(self):
        response = self.service.get_tasks(self.review_template.id, {})

        if not response.success:
            self.fail(f"Failed to get tasks, f{response.data}")

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 2)

    def test_get_tasks_sorting(self):
        # Create tasks with different names
        ReviewTemplateTaskFactory(review_template=self.review_template, name="Alpha")
        ReviewTemplateTaskFactory(review_template=self.review_template, name="Beta")
        params = {"sort_by": "name", "order": "asc"}
        response = self.service.get_tasks(self.review_template.id, params)
        self.assertTrue(response.success)
        names = [task["name"] for task in response.data["results"]]
        self.assertEqual(names, sorted(names))

    def test_searching(self):
        ReviewTemplateTaskFactory(
            review_template=self.review_template, name="UniqueTaskName"
        )
        params = {"q": "UniqueTaskName"}
        response = self.service.get_tasks(self.review_template.id, params)
        self.assertTrue(response.success)
        self.assertTrue(
            any("UniqueTaskName" in task["name"] for task in response.data["results"])
        )

    def test_filter_with_review_groups(self):
        group = ReviewGroupFactory()
        task_with_group = ReviewTemplateTaskFactory(
            review_template=self.review_template
        )
        task_with_group.review_groups.add(group)
        params = {"review_group_id": group.id}
        response = self.service.get_tasks(self.review_template.id, params)
        self.assertTrue(response.success)
        self.assertTrue(
            any(task_with_group.id == task["id"] for task in response.data["results"])
        )

    def test_require_approval_for_all_groups(self):
        task_with_approval = ReviewTemplateTaskFactory(
            review_template=self.review_template,
            require_approval_for_all_groups=True,
        )
        params = {"require_approval_for_all_groups": "true"}
        response = self.service.get_tasks(self.review_template.id, params)
        self.assertTrue(response.success)
        self.assertTrue(
            any(
                task_with_approval.id == task["id"] for task in response.data["results"]
            )
        )

    def tes_review_template_filter(self):
        # Create another review template and task
        another_review_template = ReviewTemplateFactory()
        ReviewTemplateTaskFactory(review_template=another_review_template)

        # Get tasks for the original review template
        response = self.service.get_tasks(self.review_template.id, {})

        if not response.success:
            self.fail(f"Failed to get tasks, f{response.data}")

        # Ensure only tasks for the original review template are returned
        self.assertTrue(response.success)
        self.assertTrue(
            all(
                task["review_template_id"] == self.review_template.id
                for task in response.data["results"]
            )
        )

    def test_task_priority_filter(self):
        # Create tasks with different priorities
        ReviewTemplateTaskFactory(review_template=self.review_template, task_priority=1)
        ReviewTemplateTaskFactory(review_template=self.review_template, task_priority=2)
        params = {"task_priority": 1}
        response = self.service.get_tasks(self.review_template.id, params)
        self.assertTrue(response.success)
        self.assertTrue(
            all(task["task_priority"] == 1 for task in response.data["results"])
        )

    def test_depends_on_filter(self):
        # Create tasks with different dependencies
        dependency = ReviewTemplateTaskFactory()
        task_with_dependency = ReviewTemplateTaskFactory(
            review_template=self.review_template, depends_on=dependency
        )
        ReviewTemplateTaskFactory(
            review_template=self.review_template, depends_on=dependency
        )
        params = {"depends_on_id": dependency.id}
        response = self.service.get_tasks(self.review_template.id, params)
        self.assertTrue(response.success)
        self.assertTrue(
            any(
                task_with_dependency.id == task["id"]
                for task in response.data["results"]
            )
        )

    def test_get_tasks_pagination(self):
        # Create 15 tasks
        ReviewTemplateTaskFactory.create_batch(15, review_template=self.review_template)
        params = {"page": 2, "page_size": 10}
        response = self.service.get_tasks(self.review_template.id, params)
        self.assertTrue(response.success)
        self.assertEqual(response.data["page"], 2)
        self.assertEqual(
            len(response.data["results"]), 7
        )  # 17 total, 10 on page 1, 7 on page 2
        self.assertTrue(response.data["has_previous"])
        self.assertFalse(response.data["has_next"])

    def test_get_tasks_review_group_filter(self):
        group = ReviewGroupFactory()
        task_with_group = ReviewTemplateTaskFactory(
            review_template=self.review_template
        )
        task_with_group.review_groups.add(group)
        params = {"review_group_id": group.id}
        response = self.service.get_tasks(self.review_template.id, params)
        self.assertTrue(response.success)
        self.assertTrue(
            any(task_with_group.id == task["id"] for task in response.data["results"])
        )


# get task by id
class TestGetTaskById(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.review_template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.review_template,
        )
        self.service = ReviewTasksService(user=self.user)

    def test_get_task_by_id_success(self):
        response = self.service.get_task_by_id(self.task.id)

        if not response.success:
            self.fail(f"Failed to get task by id, f{response.data}")

        self.assertTrue(response.success)
        self.assertEqual(response.data["id"], self.task.id)
        self.assertEqual(response.data["name"], self.task.name)


# update task
class TestUpdateTask(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.review_template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.review_template,
        )
        self.service = ReviewTasksService(user=self.user)
        self.valid_data = {
            "name": "Updated Task",
            "description": "Updated Description",
            "review_template": self.review_template,
            "require_approval_for_all_groups": True,
            "task_priority": 1,
            "status": "Pending",
        }

    def test_update_task_success(self):
        response = self.service.update_task(self.task.id, self.valid_data)

        if not response.success:
            self.fail(f"Failed to update task, f{response}")

        self.assertTrue(response.success)
        self.assertEqual(response.data["name"], "Updated Task")
        self.assertEqual(response.data["description"], "Updated Description")


# delete task
class TestDeleteTask(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.review_template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.review_template,
        )
        self.service = ReviewTasksService(user=self.user)

    def test_delete_task_success(self):
        response = self.service.delete_task(self.task.id)

        if not response.success:
            self.fail(f"Failed to delete task, f{response.data}")

        self.assertTrue(response.success)
        self.assertIsNone(ReviewTemplates.objects.filter(id=self.task.id).first())


# add review groups
class TestAddReviewGroups(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.review_template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.review_template,
        )
        self.review_group = ReviewGroupFactory()
        self.review_group2 = ReviewGroupFactory()
        self.service = ReviewTasksService(user=self.user)
        self.valid_data = {
            "review_groups": [
                self.review_group.id,
                self.review_group2.id,
            ],
        }

    def test_add_review_groups_success(self):
        self.task.review_groups.clear()
        response = self.service.add_review_groups(self.task.id, self.valid_data)

        if not response.success:
            self.fail(f"Failed to add review groups, f{response.data}")

        self.assertTrue(response.success)
        self.task.refresh_from_db()
        self.assertEqual(self.task.review_groups.count(), 2)


# remove review groups
class TestRemoveReviewGroups(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.review_template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.review_template,
        )
        self.review_group = ReviewGroupFactory()
        self.review_group2 = ReviewGroupFactory()
        self.service = ReviewTasksService(user=self.user)
        self.valid_data = {
            "review_groups": [
                self.review_group.id,
                self.review_group2.id,
            ],
        }

    def test_remove_review_groups_success(self):
        self.task.review_groups.clear()
        self.task.review_groups.add(self.review_group, self.review_group2)
        response = self.service.remove_review_groups(self.task.id, self.valid_data)

        if not response.success:
            self.fail(f"Failed to remove review groups, f{response.data}")

        self.assertTrue(response.success)
        self.task.refresh_from_db()
        self.assertEqual(self.task.review_groups.count(), 0)
