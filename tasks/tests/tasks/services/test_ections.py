from accounts.tests.factory import ProfileFactory
from base.tests.base_setup import BaseTestSetup
from tasks.services.actions import TaskActions
from tasks.tests.factory import ReviewGroupFactory, ReviewProcessTasksFactory


class TestSubmitTasks(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = ProfileFactory()
        self.reviewer = ProfileFactory()
        self.review_group = ReviewGroupFactory()
        self.review_group.members.add(self.user)
        self.review_group.save()

        self.task = ReviewProcessTasksFactory(status="Pending")
        self.task.review_groups.add(self.review_group)
        self.task2 = ReviewProcessTasksFactory(
            status="Pending",
        )
        self.task2.reviewers.add(self.reviewer)

    def test_submit_task_as_member(self):
        service = TaskActions(
            task_id=self.task.id,
            user=self.user.user,
        )
        """Test submitting a task as a member of the review group"""
        response = service.submit_task()
        if not response.success:
            self.fail(f"Failed to submit task, {response}")
        self.assertTrue(response.success)

        self.assertIn(self.review_group, self.task.groups_completed.all())

    def test_submit_task_as_reviewer(self):
        service = TaskActions(
            task_id=self.task2.id,
            user=self.reviewer.user,
        )
        """Test submitting a task as a reviewer"""
        response = service.submit_task()
        if not response.success:
            self.fail(f"Failed to submit task, {response}")
        self.assertTrue(response.success)
        self.assertIn(self.reviewer, self.task2.reviewers_completed.all())
        self.assertIn(self.reviewer, self.task2.reviewers.all())

    def test_submit_task_as_member_not_in_group(self):
        service = TaskActions(
            task_id=self.task.id,
            user=ProfileFactory().user,
        )
        """Test submitting a task as a member of the review group"""
        response = service.submit_task()

        self.assertFalse(response.success)
        self.assertNotIn(self.review_group, self.task2.groups_completed.all())

    def test_member_already_completed_task(self):
        user = ProfileFactory()
        self.task2.reviewers_completed.add(user)
        service = TaskActions(
            task_id=self.task2.id,
            user=ProfileFactory().user,
        )
        response = service.submit_task()
        self.task2.refresh_from_db()
        self.assertFalse(response.success)
        self.assertIn(user, self.task2.reviewers_completed.all())

    def test_member_in_group_completed_task(self):
        user = ProfileFactory()
        self.review_group.members.add(user)
        self.task.groups_completed.add(self.review_group)

        service = TaskActions(
            task_id=self.task.id,
            user=ProfileFactory().user,
        )
        response = service.submit_task()
        self.task.refresh_from_db()
        self.assertFalse(response.success)
        self.assertIn(self.review_group, self.task.groups_completed.all())


class TestCompleteTask(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = ProfileFactory()
        self.reviewer = ProfileFactory()
        self.review_group = ReviewGroupFactory()
        self.review_group.members.add(self.user)
        self.review_group.save()

        self.task = ReviewProcessTasksFactory(status="Pending")
        self.task.review_groups.add(self.review_group)
        self.task2 = ReviewProcessTasksFactory(
            status="Pending",
        )
        self.task2.reviewers.add(self.reviewer)

    def test_complete_task_as_quality_risk_manager(self):
        """Test completing a task as Quality Risk Manager (should always succeed)"""
        from django.contrib.auth.models import Group

        # Create Quality Risk Manager user
        quality_manager_user = ProfileFactory()
        quality_risk_group = Group.objects.get_or_create(name="Quality/Risk Manager")[0]
        quality_manager_user.user.groups.add(quality_risk_group)

        service = TaskActions(
            task_id=self.task.id,
            user=quality_manager_user.user,
        )
        response = service.complete_task()

        self.assertTrue(response.success)
        self.task.refresh_from_db()
        self.assertEqual(self.task.status, "Completed")

    def test_complete_task_as_unauthorized_user(self):
        """Test completing a task as non-authorized user (should fail)"""
        unauthorized_user = ProfileFactory()

        service = TaskActions(
            task_id=self.task.id,
            user=unauthorized_user.user,
        )
        response = service.complete_task()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)
        self.task.refresh_from_db()
        self.assertNotEqual(self.task.status, "Completed")

    def test_complete_task_as_reviewer_with_permissions(self):
        """Test completing a task as an authorized reviewer"""
        # Create a fresh reviewer to avoid conflicts with other tests
        fresh_reviewer = ProfileFactory()

        # Create a task that doesn't require all members to complete
        task_flexible = ReviewProcessTasksFactory(
            status="Pending",
            require_all_members_to_complete=False,
            require_approval_for_all_groups=False,
        )
        task_flexible.reviewers.add(fresh_reviewer)

        service = TaskActions(
            task_id=task_flexible.id,
            user=fresh_reviewer.user,
        )
        response = service.complete_task()

        self.assertTrue(response.success)
        task_flexible.refresh_from_db()
        self.assertEqual(task_flexible.status, "Completed")

    def test_complete_task_as_group_member_with_permissions(self):
        """Test completing a task as an authorized group member"""
        # Create a task that doesn't require all groups to complete
        task_flexible = ReviewProcessTasksFactory(
            status="Pending",
            require_approval_for_all_groups=False,
            require_all_members_to_complete=False,
        )
        task_flexible.review_groups.add(self.review_group)

        service = TaskActions(
            task_id=task_flexible.id,
            user=self.user.user,
        )
        response = service.complete_task()

        self.assertTrue(response.success)
        task_flexible.refresh_from_db()
        self.assertEqual(task_flexible.status, "Completed")

    def test_complete_task_as_reviewer_already_completed(self):
        """Test that reviewers cannot complete tasks they've already completed"""
        task_flexible = ReviewProcessTasksFactory(
            status="Pending",
            require_all_members_to_complete=False,
            require_approval_for_all_groups=False,
        )
        task_flexible.reviewers.add(self.reviewer)
        task_flexible.reviewers_completed.add(self.reviewer)

        service = TaskActions(
            task_id=task_flexible.id,
            user=self.reviewer.user,
        )
        response = service.complete_task()

        self.assertFalse(response.success)
        self.assertIn("already completed", response.message.lower())

    def test_complete_task_as_group_member_already_completed(self):
        """Test that group members cannot complete tasks their group has already completed"""
        task_flexible = ReviewProcessTasksFactory(
            status="Pending",
            require_approval_for_all_groups=False,
            require_all_members_to_complete=False,
        )
        task_flexible.review_groups.add(self.review_group)
        task_flexible.groups_completed.add(self.review_group)

        service = TaskActions(
            task_id=task_flexible.id,
            user=self.user.user,
        )
        response = service.complete_task()

        self.assertFalse(response.success)
        self.assertIn("already completed", response.message.lower())

    def test_complete_task_require_all_members_but_not_all_completed(self):
        """Test completing a task when all members are required but not all have completed"""
        # Create a task that requires all members to complete
        task_strict = ReviewProcessTasksFactory(
            status="Pending",
            require_all_members_to_complete=True,
            require_approval_for_all_groups=False,
        )

        # Add multiple reviewers but don't complete for all
        reviewer2 = ProfileFactory()
        task_strict.reviewers.add(self.reviewer)
        task_strict.reviewers.add(reviewer2)
        # Only one reviewer has completed
        task_strict.reviewers_completed.add(reviewer2)

        # Try to complete as the first reviewer
        service = TaskActions(
            task_id=task_strict.id,
            user=self.reviewer.user,
        )
        response = service.complete_task()

        # Should succeed for this individual reviewer
        self.assertTrue(response.success)
        task_strict.refresh_from_db()
        self.assertEqual(task_strict.status, "Completed")

    def test_complete_task_require_all_groups_but_not_all_completed(self):
        """Test completing a task when all groups are required but not all have completed"""
        # Create a task that requires approval from all groups
        task_strict = ReviewProcessTasksFactory(
            status="Pending",
            require_approval_for_all_groups=True,
            require_all_members_to_complete=False,
        )

        # Add multiple groups but don't complete for all
        review_group2 = ReviewGroupFactory()
        user2 = ProfileFactory()
        review_group2.members.add(user2)

        task_strict.review_groups.add(self.review_group)
        task_strict.review_groups.add(review_group2)
        # Only one group has completed
        task_strict.groups_completed.add(review_group2)

        # Try to complete as a member of the first group
        service = TaskActions(
            task_id=task_strict.id,
            user=self.user.user,
        )
        response = service.complete_task()

        # Should succeed for this individual group member
        self.assertTrue(response.success)
        task_strict.refresh_from_db()
        self.assertEqual(task_strict.status, "Completed")

    def test_complete_task_nonexistent_task(self):
        """Test completing a non-existent task"""
        service = TaskActions(
            task_id=99999,  # Non-existent task ID
            user=self.user.user,
        )
        response = service.complete_task()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)
        self.assertIn("Task not found", response.message)

    def test_complete_task_with_require_all_members_to_complete(self):
        """Test completing a task when all members are required to complete"""
        from django.contrib.auth.models import Group

        # Create a task that requires all members to complete
        task_strict = ReviewProcessTasksFactory(
            status="Pending",
            require_all_members_to_complete=True,
            require_approval_for_all_groups=False,
        )

        # Add multiple reviewers
        reviewer2 = ProfileFactory()
        task_strict.reviewers.add(self.reviewer)
        task_strict.reviewers.add(reviewer2)

        # A regular reviewer can still complete (the permission checks individual completion)
        service = TaskActions(
            task_id=task_strict.id,
            user=self.reviewer.user,
        )
        response = service.complete_task()

        self.assertTrue(response.success)
        task_strict.refresh_from_db()
        self.assertEqual(task_strict.status, "Completed")

    def test_complete_task_with_require_approval_for_all_groups(self):
        """Test completing a task when all groups are required to complete"""
        from django.contrib.auth.models import Group

        # Create a task that requires approval from all groups
        task_strict = ReviewProcessTasksFactory(
            status="Pending",
            require_approval_for_all_groups=True,
            require_all_members_to_complete=False,
        )

        # Add multiple groups
        review_group2 = ReviewGroupFactory()
        task_strict.review_groups.add(self.review_group)
        task_strict.review_groups.add(review_group2)

        # A regular group member can still complete (the permission checks individual completion)
        service = TaskActions(
            task_id=task_strict.id,
            user=self.user.user,
        )
        response = service.complete_task()

        self.assertTrue(response.success)
        task_strict.refresh_from_db()
        self.assertEqual(task_strict.status, "Completed")


class TestApproveTask(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = ProfileFactory()
        self.reviewer = ProfileFactory()
        self.review_group = ReviewGroupFactory()
        self.review_group.members.add(self.user)
        self.review_group.save()

        self.task = ReviewProcessTasksFactory(status="Pending")
        self.task.review_groups.add(self.review_group)
        self.task2 = ReviewProcessTasksFactory(
            status="Pending",
        )
        self.task2.reviewers.add(self.reviewer)

    def test_approve_task_as_quality_risk_manager(self):
        """Test approving a task as Quality Risk Manager (should succeed)"""
        from django.contrib.auth.models import Group

        # Create Quality Risk Manager user
        quality_manager_user = ProfileFactory()
        quality_risk_group = Group.objects.get_or_create(name="Quality/Risk Manager")[0]
        quality_manager_user.user.groups.add(quality_risk_group)

        service = TaskActions(
            task_id=self.task.id,
            user=quality_manager_user.user,
        )
        response = service.approve_task()

        self.assertTrue(response.success)
        self.task.refresh_from_db()
        self.assertEqual(self.task.status, "Approved")

    def test_approve_task_as_unauthorized_user(self):
        """Test approving a task as non-Quality Risk Manager (should fail)"""
        unauthorized_user = ProfileFactory()

        service = TaskActions(
            task_id=self.task.id,
            user=unauthorized_user.user,
        )
        response = service.approve_task()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)
        self.task.refresh_from_db()
        self.assertNotEqual(self.task.status, "Approved")

    def test_approve_task_as_regular_reviewer(self):
        """Test approving a task as regular reviewer (should fail)"""
        service = TaskActions(
            task_id=self.task2.id,
            user=self.reviewer.user,
        )
        response = service.approve_task()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)
        self.assertIn("permission", response.message.lower())
        self.task2.refresh_from_db()
        self.assertNotEqual(self.task2.status, "Approved")

    def test_approve_task_as_group_member(self):
        """Test approving a task as group member (should fail)"""
        service = TaskActions(
            task_id=self.task.id,
            user=self.user.user,
        )
        response = service.approve_task()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 403)
        self.assertIn("permission", response.message.lower())
        self.task.refresh_from_db()
        self.assertNotEqual(self.task.status, "Approved")

    def test_approve_nonexistent_task(self):
        """Test approving a non-existent task"""
        from django.contrib.auth.models import Group

        quality_manager_user = ProfileFactory()
        quality_risk_group = Group.objects.get_or_create(name="Quality/Risk Manager")[0]
        quality_manager_user.user.groups.add(quality_risk_group)

        service = TaskActions(
            task_id=99999,  # Non-existent task ID
            user=quality_manager_user.user,
        )
        response = service.approve_task()

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)
        self.assertIn("Task not found", response.message)
