from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserFactory
from tasks.models import ReviewTemplates
from tasks.services.review_templates import ReviewTemplateService
from tasks.tests.factory import (
    ReviewGroupFactory,
    ReviewTemplateFactory,
    ReviewTemplateTaskFactory,
)


# create task
class TestTask(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.service = ReviewTemplateService(user=self.user)
        self.template = ReviewTemplateFactory()
        self.data = {
            "name": "Test Task",
            "description": "Test Description",
            "require_approval_for_all_groups": True,
            "task_priority": 1,
            "status": "Pending",
            "number_of_days": 3,
        }

    def test_create_review_template_success(self):
        self._authenticate_user(self.admin_user)

        response = self.client.post(
            f"/api/permissions/review-templates/{self.template.id}/tasks/",
            data=self.data,
            format="json",
        )
        if not response.status_code == 201:
            self.fail(f"Failed to create review template, f{response.data}")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["name"], self.data["name"])
        self.assertEqual(response.data["description"], self.data["description"])


# get tasks
class TestGetTasks(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.service = ReviewTemplateService(user=self.user)
        self.template = ReviewTemplateFactory()
        self.tasks = [
            ReviewTemplateTaskFactory(
                review_template=self.template,
            )
            for _ in range(2)
        ]

    def test_get_tasks_success(self):
        self._authenticate_user(self.admin_user)
        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["results"]), 2)

    def test_get_tasks_sorting(self):
        self._authenticate_user(self.admin_user)
        ReviewTemplateTaskFactory(review_template=self.template, name="Alpha")
        ReviewTemplateTaskFactory(review_template=self.template, name="Beta")
        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/?sort_by=name&sort_order=asc",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        names = [task["name"] for task in response.data["results"]]
        self.assertEqual(names, sorted(names))

    def test_searching(self):
        self._authenticate_user(self.admin_user)
        ReviewTemplateTaskFactory(review_template=self.template, name="UniqueTaskName")
        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/?q=UniqueTaskName",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            any("UniqueTaskName" in task["name"] for task in response.data["results"])
        )

    def test_filter_with_review_groups(self):
        self._authenticate_user(self.admin_user)
        group = ReviewGroupFactory()
        task_with_group = ReviewTemplateTaskFactory(review_template=self.template)
        task_with_group.review_groups.add(group)
        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/?review_group_id={group.id}",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            any(task_with_group.id == task["id"] for task in response.data["results"])
        )

    def test_require_approval_for_all_groups(self):
        self._authenticate_user(self.admin_user)
        task_with_approval = ReviewTemplateTaskFactory(
            review_template=self.template,
            require_approval_for_all_groups=True,
        )
        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/?require_approval_for_all_groups=true",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            any(
                task_with_approval.id == task["id"] for task in response.data["results"]
            )
        )

    def test_review_template_filter(self):
        self._authenticate_user(self.admin_user)
        another_template = ReviewTemplateFactory()
        ReviewTemplateTaskFactory(review_template=another_template)
        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            all(
                task["review_template"]["id"] == self.template.id
                for task in response.data["results"]
            )
        )

    def test_task_priority_filter(self):
        self._authenticate_user(self.admin_user)
        ReviewTemplateTaskFactory(review_template=self.template, task_priority=1)
        ReviewTemplateTaskFactory(review_template=self.template, task_priority=2)
        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/?task_priority=1",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            all(task["task_priority"] == 1 for task in response.data["results"])
        )

    def test_depends_on_filter(self):
        self._authenticate_user(self.admin_user)
        dependency = ReviewTemplateTaskFactory()
        task_with_dependency = ReviewTemplateTaskFactory(
            review_template=self.template, depends_on=dependency
        )
        ReviewTemplateTaskFactory(review_template=self.template, depends_on=dependency)
        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/?depends_on_id={dependency.id}",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            any(
                task_with_dependency.id == task["id"]
                for task in response.data["results"]
            )
        )

    def test_get_tasks_pagination(self):
        self._authenticate_user(self.admin_user)
        ReviewTemplateTaskFactory.create_batch(15, review_template=self.template)
        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/?page=2&page_size=10",
            format="json",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            len(response.data["results"]), 7
        )  # 17 total, 10 on page 1, 7 on page 2


# get task by id
class TestGetTaskById(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.service = ReviewTemplateService(user=self.user)
        self.template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.template,
        )

    def test_get_task_by_id_success(self):
        self._authenticate_user(self.admin_user)

        response = self.client.get(
            f"/api/permissions/review-templates/{self.template.id}/tasks/{self.task.id}/",
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to get task by id, f{response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], self.task.name)


# update task
class TestUpdateTask(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.service = ReviewTemplateService(user=self.user)
        self.template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.template,
        )
        self.data = {
            "name": "Updated Task",
            "description": "Updated Description",
            "require_approval_for_all_groups": False,
            "task_priority": 2,
            "status": "In Progress",
        }

    def test_update_task_success(self):
        self._authenticate_user(self.admin_user)

        response = self.client.put(
            f"/api/permissions/review-templates/{self.template.id}/tasks/{self.task.id}/",
            data=self.data,
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to update task, f{response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], self.data["name"])
        self.assertEqual(response.data["description"], self.data["description"])


# delete task
class TestDeleteTask(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.service = ReviewTemplateService(user=self.user)
        self.template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.template,
        )

    def test_delete_task_success(self):
        self._authenticate_user(self.admin_user)

        response = self.client.delete(
            f"/api/permissions/review-templates/{self.template.id}/tasks/{self.task.id}/",
            format="json",
        )
        if not response.status_code == 204:
            self.fail(f"Failed to delete task, f{response.data}")

        self.assertEqual(response.status_code, 204)
        self.assertIsNone(ReviewTemplates.objects.filter(id=self.task.id).first())


# add review groups
class TestAddReviewGroups(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.review_template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.review_template,
        )
        self.review_group = ReviewGroupFactory()
        self.review_group2 = ReviewGroupFactory()
        self.valid_data = {
            "action": "add",
            "review_groups": [
                self.review_group.id,
                self.review_group2.id,
            ],
        }

    def test_add_review_groups_success(self):
        self.task.review_groups.clear()
        self._authenticate_user(self.admin_user)
        response = self.client.patch(
            f"/api/permissions/review-templates/{self.review_template.id}/tasks/{self.task.id}/review-groups/",
            data=self.valid_data,
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to add review groups, f{response.data}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.task.review_groups.count(), 2)


# remove review groups
class TestRemoveReviewGroups(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self.user = UserFactory()
        self.review_template = ReviewTemplateFactory()
        self.task = ReviewTemplateTaskFactory(
            review_template=self.review_template,
        )
        self.task.review_groups.clear()
        self.review_group = ReviewGroupFactory()
        self.review_group2 = ReviewGroupFactory()
        self.valid_data = {
            "action": "remove",
            "review_groups": [
                self.review_group.id,
                self.review_group2.id,
            ],
        }

    def test_remove_review_groups_success(self):
        self.task.review_groups.add(self.review_group, self.review_group2)
        self._authenticate_user(self.admin_user)
        response = self.client.patch(
            f"/api/permissions/review-templates/{self.review_template.id}/tasks/{self.task.id}/review-groups/",
            data=self.valid_data,
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to remove review groups, f{response.data}")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.task.review_groups.count(), 0)
