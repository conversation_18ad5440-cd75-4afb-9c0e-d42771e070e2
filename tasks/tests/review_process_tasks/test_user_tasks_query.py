from django.test import TestCase
from django.utils import timezone

from accounts.services.user_profile.query import UserTasksQuery
from tasks.repositories.tasks_repository import TasksRepository
from tasks.models import ReviewProcessTasks, ReviewGroups
from tasks.tests.factory import (
    ReviewProcessTasksFactory,
    ReviewGroupFactory,
)
from base.tests.base_setup import BaseTestSetup
from accounts.models import Profile
from accounts.tests.factory import ProfileFactory
from base.tests.factory import UserFactory


class TestUserTasksQuery(BaseTestSetup):
    def setUp(self):
        super().setUp()
        
        self.user = self.user_user
        self.superuser = self.super_user
        
        self.user_profile = Profile.objects.get(user=self.user)
        self.superuser_profile = Profile.objects.get(user=self.superuser)
        
        self.tasks_repository = TasksRepository()
        
        self.query_service = UserTasksQuery()
        
        self.review_group1 = ReviewGroupFactory()
        self.review_group2 = ReviewGroupFactory()
        
        self.task1 = ReviewProcessTasksFactory(
            name="User Task One",
            description="First user test task",
            status="Pending",
            task_priority=1,
            require_approval_for_all_groups=True,
            created_by=self.user
        )
        self.task1.review_groups.add(self.review_group1)
        self.task1.reviewers.add(self.user_profile)
        
        self.task2 = ReviewProcessTasksFactory(
            name="User Task Two",
            description="Second user test task",
            status="In Progress",
            task_priority=2,
            require_approval_for_all_groups=False,
            created_by=self.user,
            created_at="1990-01-15"
        )
        self.task2.review_groups.add(self.review_group2)
        self.task2.reviewers.add(self.user_profile)
        
        self.task3 = ReviewProcessTasksFactory(
            name="Superuser Task",
            description="High priority superuser task",
            status="Pending",
            task_priority=3,
            require_approval_for_all_groups=True,
            created_by=self.superuser
        )
        self.task3.review_groups.add(self.review_group1)
        self.task3.reviewers.add(self.superuser_profile)
        
        today = timezone.now().date()
        self.task_with_deadline = ReviewProcessTasksFactory(
            name="Deadline Task",
            description="Task with deadline",
            status="Pending",
            task_priority=1,
            deadline=today
        )
        self.task_with_deadline.reviewers.add(self.user_profile)

    def test_get_user_tasks_success(self):
        """Test successful retrieval of user tasks"""
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 3)
        
    def test_get_user_tasks_as_superuser(self):
        """Test superuser can access any user's tasks"""
        response = self.query_service.get_user_tasks(
            self.superuser, self.user_profile.id, {}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 3)
        
    def test_get_user_tasks_unauthorized(self):
        """Test user cannot access another user's tasks"""
        other_user = UserFactory()
        other_profile = ProfileFactory(user=other_user)
        
        response = self.query_service.get_user_tasks(
            other_user, self.user_profile.id, {}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 3)
        
    def test_get_user_tasks_with_status_filter(self):
        """Test filtering user tasks by status"""
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {"status": "Pending"}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 2)
        for task in response.data["results"]:
            self.assertEqual(task["status"], "Pending")
            
    def test_get_user_tasks_with_priority_filter(self):
        """Test filtering user tasks by priority"""
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {"task_priority": 1}
        )
        
        self.assertTrue(response.success)
        for task in response.data["results"]:
            self.assertEqual(task["task_priority"], 1)
            
    def test_get_user_tasks_with_search_query(self):
        """Test searching user tasks by name or description"""
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {"q": "First user"}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "User Task One")
        
    def test_get_user_tasks_with_review_group_filter(self):
        """Test filtering user tasks by review group"""
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {"review_group": self.review_group1.id}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "User Task One")
        
    def test_get_user_tasks_with_deadline_filter(self):
        """Test filtering user tasks by deadline"""
        today = timezone.now().date()
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {"deadline": today.strftime("%Y-%m-%d")}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "Deadline Task")
        
    def test_get_user_tasks_with_invalid_deadline_format(self):
        """Test handling of invalid deadline format"""
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {"deadline": "invalid-date-format"}
        )
        
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)
        self.assertIn("Invalid deadline format", response.message)
        
    def test_get_user_tasks_with_multiple_filters(self):
        """Test applying multiple filters to user tasks"""
        response = self.query_service.get_user_tasks(
            self.user, 
            self.user_profile.id, 
            {
                "status": "Pending",
                "task_priority": 1
            }
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 2)
        for task in response.data["results"]:
            self.assertEqual(task["status"], "Pending")
            self.assertEqual(task["task_priority"], 1)
            
    def test_get_user_tasks_pagination(self):
        """Test pagination of user tasks"""
        for i in range(10):
            task = ReviewProcessTasksFactory(
                name=f"Pagination Task {i}",
                status="Pending",
                task_priority=1
            )
            task.reviewers.add(self.user_profile)
            
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {"page": 2, "page_size": 5}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 5)
        self.assertEqual(response.data["page"], 2)
        self.assertTrue(response.data["has_previous"])
        
    def test_get_user_tasks_invalid_page(self):
        """Test handling of invalid page number"""
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {"page": "invalid"}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(response.data["page"], 1)
        
    def test_get_user_tasks_empty_page(self):
        """Test handling of page number beyond available pages"""
        response = self.query_service.get_user_tasks(
            self.user, self.user_profile.id, {"page": 100}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(response.data["has_next"], False)
        
    def test_get_user_tasks_profile_not_found(self):
        """Test handling of non-existent profile ID"""
        response = self.query_service.get_user_tasks(
            self.user, 99999, {}
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 0)
        
    def test_get_user_tasks_require_approval_filter(self):
        """Test filtering tasks by approval requirement"""
        response = self.query_service.get_user_tasks(
            self.user, 
            self.user_profile.id, 
            {"require_approval_for_all_groups": True}
        )
        
        self.assertTrue(response.success)
        for task in response.data["results"]:
            self.assertTrue(task["require_approval_for_all_groups"])
            
    def test_get_user_tasks_created_by_filter(self):
        """Test filtering tasks by creator"""
        response = self.query_service.get_user_tasks(
            self.user, 
            self.user_profile.id, 
            {"created_by": self.user.id}
        )
        
        self.assertTrue(response.success)
        for task in response.data["results"]:
            self.assertEqual(task["created_by"], self.user.id)
