from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from tasks.tests.factory import (
    ReviewProcessTasksFactory,
    ReviewGroupFactory,
)
from base.tests.base_setup import BaseTestSetup
from accounts.models import Profile


class TestTasksUserAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()

        self.client = APIClient()

        self.user = self.user_user
        self.superuser = self.super_user

        self.user_profile = Profile.objects.get(user=self.user)
        self.superuser_profile = Profile.objects.get(user=self.superuser)

        self.review_group1 = ReviewGroupFactory()
        self.review_group2 = ReviewGroupFactory()

        self.task1 = ReviewProcessTasksFactory(
            name="Task One",
            description="First test task",
            status="Pending",
            task_priority=1,
            require_approval_for_all_groups=True,
            created_by=self.user,
        )
        self.task1.review_groups.add(self.review_group1)

        self.task2 = ReviewProcessTasksFactory(
            name="Task Two",
            description="Second test task",
            status="In Progress",
            task_priority=2,
            require_approval_for_all_groups=False,
            created_by=self.user,
        )
        self.task2.review_groups.add(self.review_group2)

        self.task3 = ReviewProcessTasksFactory(
            name="Alpha Task",
            description="High priority task",
            status="Pending",
            task_priority=3,
            require_approval_for_all_groups=True,
            created_by=self.superuser,
        )
        self.task3.review_groups.add(self.review_group1)

        self.task1.reviewers.add(self.user_profile)

    def test_get_user_tasks_list_success(self):
        """Test successful retrieval of user tasks"""
        self.client.force_authenticate(user=self.user)

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        self.assertGreaterEqual(len(response.data["results"]), 1)

    def test_get_user_tasks_with_status_filter(self):
        """Test filtering user tasks by status"""
        self.client.force_authenticate(user=self.user)

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(f"{url}?status=Pending")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        for task in response.data["results"]:
            self.assertEqual(task["status"], "Pending")

    def test_get_user_tasks_with_priority_filter(self):
        """Test filtering user tasks by priority"""
        self.client.force_authenticate(user=self.user)

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(f"{url}?task_priority=1")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        for task in response.data["results"]:
            self.assertEqual(task["task_priority"], 1)

    def test_get_user_tasks_with_search_query(self):
        """Test searching user tasks by name or description"""
        self.client.force_authenticate(user=self.user)

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(f"{url}?q=First test")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        self.assertTrue(
            any(
                "First test" in task["description"] for task in response.data["results"]
            )
        )

    def test_get_user_tasks_with_review_group_filter(self):
        """Test filtering user tasks by review group"""
        self.client.force_authenticate(user=self.user)

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(f"{url}?review_group={self.review_group1.id}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)

    def test_get_user_tasks_with_multiple_filters(self):
        """Test applying multiple filters to user tasks"""
        self.client.force_authenticate(user=self.user)

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(
            f"{url}?status=Pending&task_priority=1&review_group={self.review_group1.id}"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        for task in response.data["results"]:
            self.assertEqual(task["status"], "Pending")
            self.assertEqual(task["task_priority"], 1)

    def test_get_user_tasks_with_invalid_filter(self):
        """Test handling of invalid filter value"""
        self.client.force_authenticate(user=self.user)

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(f"{url}?deadline=invalid-date-format")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("message", response.data)

    def test_get_user_tasks_pagination(self):
        """Test pagination of user tasks"""
        self.client.force_authenticate(user=self.user)

        for i in range(5):
            task = ReviewProcessTasksFactory(
                name=f"Pagination Task {i}", created_by=self.user
            )
            task.reviewers.add(self.user_profile)

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(f"{url}?page=1&page_size=3")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
        self.assertEqual(len(response.data["results"]), 3)
        self.assertEqual(response.data["page"], 1)
        self.assertTrue(response.data["has_next"])

    def test_get_user_tasks_unauthorized(self):
        """Test unauthorized access to user tasks"""

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_get_user_tasks_superuser_access(self):
        """Test superuser accessing any user's tasks"""
        self.client.force_authenticate(user=self.superuser)

        url = reverse("user_tasks_api", kwargs={"profile_id": self.user_profile.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("results", response.data)
