from django.utils import timezone

from tasks.services.query import TasksQueryService
from tasks.repositories.tasks_repository import TasksRepository
from tasks.models import ReviewProcessTasks, ReviewGroups
from tasks.tests.factory import (
    ReviewProcessTasksFactory,
    ReviewGroupFactory,
)
from base.tests.base_setup import BaseTestSetup
from accounts.models import Profile
from django.test import TestCase


class TestGetTasks(BaseTestSetup):
    def setUp(self):
        super().setUp()
        
        self.user = self.user_user
        self.superuser = self.super_user
        
        self.user_profile = Profile.objects.get(user=self.user)
        self.superuser_profile = Profile.objects.get(user=self.superuser)
        
        self.tasks_repository = TasksRepository()
        
        self.service = TasksQueryService()
        
        self.review_group1 = ReviewGroupFactory()
        self.review_group2 = ReviewGroupFactory()
        
        self.task1 = ReviewProcessTasksFactory(
            name="Task One",
            description="First test task",
            status="Pending",
            task_priority=1,
            require_approval_for_all_groups=True,
            created_by=self.user
        )
        self.task1.review_groups.add(self.review_group1)
        
        self.task2 = ReviewProcessTasksFactory(
            name="Task Two",
            description="Second test task",
            status="In Progress",
            task_priority=2,
            require_approval_for_all_groups=False,
            created_by=self.user
        )
        self.task2.review_groups.add(self.review_group2)
        
        self.task3 = ReviewProcessTasksFactory(
            name="Alpha Task",
            description="High priority task",
            status="Pending",
            task_priority=3,
            require_approval_for_all_groups=True,
            created_by=self.superuser
        )
        self.task3.review_groups.add(self.review_group1)
        
        self.task4 = ReviewProcessTasksFactory(
            name="Dependent Task",
            description="This task depends on another",
            status="Pending",
            task_priority=1,
            depends_on=self.task1
        )
        
        self.task1.reviewers.add(self.user_profile)

    def test_get_tasks_no_filters(self):
        """Test retrieving all tasks with no filters as superuser"""
        response = self.service.get_tasks({})
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 4)
        self.assertEqual(response.data["page"], 1)
        
    def test_get_tasks_user_permission_filtering(self):
        """Test that regular users only see tasks assigned to them"""
        response = self.service.get_tasks({})
        
        self.assertTrue(response.success)
        task_ids = [task["id"] for task in response.data["results"]]
        self.assertIn(self.task1.id, task_ids)
        
    def test_get_tasks_status_filter(self):
        """Test filtering tasks by status"""
        response = self.service.get_tasks({"status": "Pending"})
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 3)
        statuses = [task["status"] for task in response.data["results"]]
        self.assertTrue(all(status == "Pending" for status in statuses))
        
    def test_get_tasks_priority_filter(self):
        """Test filtering tasks by priority"""
        response = self.service.get_tasks({"task_priority": 1})
        
        self.assertTrue(response.success)
        priorities = [task["task_priority"] for task in response.data["results"]]
        self.assertTrue(all(priority == 1 for priority in priorities))
        
    def test_get_tasks_search_query(self):
        """Test searching tasks by name or description"""
        response = self.service.get_tasks({"q": "high priority"})
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "Alpha Task")
        
    def test_get_tasks_review_group_filter(self):
        """Test filtering tasks by review group"""
        response = self.service.get_tasks({"review_group": self.review_group1.id})
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 2)
        
    def test_get_tasks_approval_filter(self):
        """Test filtering tasks by approval requirement"""
        response = self.service.get_tasks(
            {"require_approval_for_all_groups": True}
        )
        
        self.assertTrue(response.success)
        for task in response.data["results"]:
            self.assertTrue(task["require_approval_for_all_groups"])
        
    def test_get_tasks_depends_on_filter(self):
        """Test filtering tasks by dependency"""
        response = self.service.get_tasks({"depends_on": self.task1.id})
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "Dependent Task")
        
    def test_get_tasks_created_by_filter(self):
        """Test filtering tasks by creator"""
        response = self.service.get_tasks({"created_by": self.superuser.id})
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "Alpha Task")
        
    def test_get_tasks_pagination(self):
        """Test pagination of tasks"""
        for i in range(10):
            ReviewProcessTasksFactory(name=f"Pagination Task {i}")
            
        response = self.service.get_tasks({"page": 2, "page_size": 5})
        
        self.assertTrue(response.success)
        self.assertEqual(response.data["page"], 2)
        self.assertEqual(len(response.data["results"]), 5)
        self.assertTrue(response.data["has_previous"])
        
    def test_get_tasks_invalid_page(self):
        """Test handling of invalid page number"""
        response = self.service.get_tasks({"page": "invalid"})
        
        self.assertTrue(response.success)
        self.assertEqual(response.data["page"], 1)
        
    def test_get_tasks_empty_page(self):
        """Test handling of page number beyond available pages"""
        response = self.service.get_tasks({"page": 100})
        
        self.assertTrue(response.success)
        self.assertEqual(response.data["has_next"], False)
        
    def test_get_tasks_reviewer_filter(self):
        """Test filtering tasks by reviewer"""
        response = self.service.get_tasks({"reviewer_id": self.user_profile.id})
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["id"], self.task1.id)
        
    def test_get_tasks_require_all_members_filter(self):
        """Test filtering tasks by require_all_members_to_complete"""
        task_with_all_members = ReviewProcessTasksFactory(
            name="All Members Task",
            require_all_members_to_complete=True
        )
        
        response = self.service.get_tasks(
            {"require_all_members_to_complete": True}
        )
        
        self.assertTrue(response.success)
        task_ids = [task["id"] for task in response.data["results"]]
        self.assertIn(task_with_all_members.id, task_ids)
        
    def test_get_tasks_deadline_filter(self):
        """Test filtering tasks by deadline"""
        today = timezone.now().date()
        task_with_deadline = ReviewProcessTasksFactory(
            name="Deadline Task",
            deadline=today
        )
        
        response = self.service.get_tasks({"deadline": today.strftime("%Y-%m-%d")})
        
        self.assertTrue(response.success)
        task_ids = [task["id"] for task in response.data["results"]]
        self.assertIn(task_with_deadline.id, task_ids)
        
    def test_get_tasks_invalid_deadline_format(self):
        """Test handling of invalid deadline format"""
        today = timezone.now().date()
        task_with_deadline = ReviewProcessTasksFactory(
            name="Deadline Task",
            deadline=today
        )
        
        response = self.service.get_tasks(
            {"deadline": "invalid-date-format"}
        )
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)
        self.assertIn("Invalid deadline format", response.message)


class TestGetTasksMultipleFilters(BaseTestSetup):
    def setUp(self):
        super().setUp()
        
        self.user = self.user_user
        self.superuser = self.super_user
        
        self.user_profile = Profile.objects.get(user=self.user)
        self.superuser_profile = Profile.objects.get(user=self.superuser)
        
        self.tasks_repository = TasksRepository()
        self.service = TasksQueryService()
        
        self.review_group = ReviewGroupFactory()
        
        self.task1 = ReviewProcessTasksFactory(
            name="Task One",
            description="First test task",
            status="Pending",
            task_priority=1,
            require_approval_for_all_groups=True,
            created_by=self.user
        )
        self.task1.review_groups.add(self.review_group)
        
        self.task2 = ReviewProcessTasksFactory(
            name="Task Two",
            description="Second test task",
            status="Pending",
            task_priority=1,
            require_approval_for_all_groups=False,
            created_by=self.user
        )
        
        self.task3 = ReviewProcessTasksFactory(
            name="Task Three",
            description="Third test task",
            status="Pending",
            task_priority=2,
            require_approval_for_all_groups=True,
            created_by=self.user
        )
        
        self.task4 = ReviewProcessTasksFactory(
            name="Task Four",
            description="Fourth test task",
            status="In Progress",
            task_priority=1,
            require_approval_for_all_groups=True,
            created_by=self.superuser
        )
        
    def test_get_tasks_multiple_filters(self):
        """Test applying multiple filters simultaneously"""
        response = self.service.get_tasks(
            {
                "status": "Pending",
                "task_priority": 1,
                "require_approval_for_all_groups": True
            }
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "Task One")
        
    def test_get_tasks_multiple_filters_no_results(self):
        """Test applying multiple filters that result in no matches"""
        response = self.service.get_tasks(
            {
                "status": "Completed",
                "task_priority": 1
            }
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 0)
        
    def test_get_tasks_complex_filter_combination(self):
        """Test a complex combination of filters"""
        self.task1.reviewers.add(self.user_profile)
        
        today = timezone.now().date()
        task_with_deadline = ReviewProcessTasksFactory(
            name="Deadline Task",
            status="Pending",
            task_priority=1,
            require_approval_for_all_groups=True,
            deadline=today,
            created_by=self.user
        )
        task_with_deadline.reviewers.add(self.user_profile)
        
        response = self.service.get_tasks(
            {
                "status": "Pending",
                "task_priority": 1,
                "require_approval_for_all_groups": True,
                "reviewer_id": self.user_profile.id,
                "deadline": today.strftime("%Y-%m-%d")
            }
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "Deadline Task")
        
    def test_get_tasks_filter_with_search_query(self):
        """Test combining filters with a search query"""
        response = self.service.get_tasks(
            {
                "status": "Pending",
                "q": "First test"
            }
        )
        
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 1)
        self.assertEqual(response.data["results"][0]["name"], "Task One")
