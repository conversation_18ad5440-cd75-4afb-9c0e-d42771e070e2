from rest_framework import serializers, viewsets
from django.db.models import Count
from accounts.serializers import UserSerializer
from tasks.models import (
    ReviewGroups,
    ReviewProcessTasks,
    ReviewTemplateTasks,
    ReviewTemplates,
)


class ReviewGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewGroups
        fields = [
            "id",
            "title",
            "description",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class GetReviewGroupSerializer(serializers.ModelSerializer):
    members = serializers.IntegerField(source="members_count", read_only=True)

    class Meta:
        model = ReviewGroups
        fields = [
            "id",
            "title",
            "description",
            "members",
            "created_at",
            "updated_at",
        ]


class ReviewGroupViewSet(viewsets.ModelViewSet):
    queryset = ReviewGroups.objects.all()
    serializer_class = GetReviewGroupSerializer

    def get_queryset(self):
        return ReviewGroups.objects.annotate(members_count=Count("members"))


class ReviewTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewTemplates
        fields = "__all__"


class ReviewTemplateTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewTemplateTasks
        fields = "__all__"


class GetReviewTemplateTaskSerializer(serializers.ModelSerializer):
    review_template = serializers.SerializerMethodField()
    review_groups = serializers.SerializerMethodField()

    class Meta:
        model = ReviewTemplateTasks
        fields = "__all__"

    def get_review_template(self, obj):
        if obj.review_template:
            return {"id": obj.review_template.id, "name": obj.review_template.name}
        return None

    def get_review_groups(self, obj):
        return [
            {"id": group.id, "name": group.title} for group in obj.review_groups.all()
        ]


class GetReviewTemplateSerializer(serializers.ModelSerializer):
    tasks = ReviewTemplateTaskSerializer(many=True, read_only=True)
    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = ReviewTemplates
        fields = "__all__"


class ReviewProcessTasksSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewProcessTasks
        fields = "__all__"


class GetReviewProcessTasksSerializer(serializers.ModelSerializer):
    review_process = serializers.SerializerMethodField()
    groups_completed = serializers.SerializerMethodField()
    reviewers = serializers.SerializerMethodField()
    review_groups = serializers.SerializerMethodField()
    reviewers_completed = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()

    class Meta:
        model = ReviewProcessTasks
        fields = "__all__"

    def get_review_process(self, obj):
        if obj.review_process:
            return {"id": obj.review_process.id, "name": obj.review_process.name}
        return None

    def get_groups_completed(self, obj):
        if obj.groups_completed:
            return [
                {"id": group.id, "name": group.title}
                for group in obj.groups_completed.all()
            ]
        return []

    def get_reviewers(self, obj):
        if obj.reviewers:
            return [
                {
                    "id": reviewer.id,
                    "name": f"{reviewer.user.first_name} {reviewer.user.last_name}".strip(),
                }
                for reviewer in obj.reviewers.all()
            ]
        return []

    def get_reviewers_completed(self, obj):
        if obj.reviewers_completed:
            return [
                {
                    "id": reviewer.id,
                    "name": f"{reviewer.user.first_name} {reviewer.user.last_name}".strip(),
                }
                for reviewer in obj.reviewers_completed.all()
            ]
        return []

    def get_review_groups(self, obj):
        if obj.review_groups:
            return [
                {"id": group.id, "name": group.title}
                for group in obj.review_groups.all()
            ]
        return []

    def get_status(self, obj):
        """
        Return task status based on reviewers and review groups' completion

        Condition:
        Case: reviewers
        - If require_all_members_to_complete is True,
          then check if all reviewers have completed the task.
          If all reviewers have completed the task, return "Completed". If not, return "In Progress".
        - If require_all_members_to_complete is False,
          then check if any reviewer has completed the task.
          If any reviewer has completed the task, return "In Progress". If not, return "Pending".

        Case: Review groups
        - If require_approval_for_all_groups is True,
          then check if all groups have completed the task.
          If all groups have completed the task, return "Completed". If not, return "In Progress".
        - If require_approval_for_all_groups is False,
          then check if any group has completed the task.
          If any group has completed the task, return "In Progress". If not, return "Pending".
        """
        # Handle reviewers case
        if obj.reviewers.exists():
            reviewers_count = obj.reviewers.count()
            reviewers_completed_count = obj.reviewers_completed.count()

            if obj.require_all_members_to_complete:
                # All reviewers must complete
                if reviewers_completed_count == reviewers_count:
                    return "Completed"
                elif reviewers_completed_count > 0:
                    return "In Progress"
                else:
                    return "Pending"
            else:
                # Any reviewer can complete
                if reviewers_completed_count > 0:
                    return "Completed"
                else:
                    return "Pending"

        # Handle review groups case
        if obj.review_groups.exists():
            groups_count = obj.review_groups.count()
            groups_completed_count = obj.groups_completed.count()

            if obj.require_approval_for_all_groups:
                # All groups must complete
                if groups_completed_count == groups_count:
                    return "Completed"
                elif groups_completed_count > 0:
                    return "In Progress"
                else:
                    return "Pending"
            else:
                # Any group can complete
                if groups_completed_count > 0:
                    return "Completed"
                else:
                    return "Pending"

        # If no reviewers or groups assigned, return the stored status or default
        return obj.status if hasattr(obj, "status") else "Pending"
