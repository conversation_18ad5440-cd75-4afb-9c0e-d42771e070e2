from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from tasks.models import ReviewProcessTasks
from tasks.repositories.tasks_repository import TasksRepository
from tasks.serializers import GetReviewProcessTasksSerializer
from django.core.paginator import <PERSON><PERSON><PERSON>, Empty<PERSON><PERSON>, PageNotAnInteger

from tasks.services.filtering import TasksFilterService

logging_service = LoggingService()
tasks_repository = TasksRepository()
tasks_filter_service = TasksFilterService()


class TasksQueryService:
    def __init__(self):
        """
        Initialize the TasksQueryService.
        """
        pass

    def get_tasks(self, filters) -> APIResponse:
        """
        Task Query method to retrieve tasks based filters.
        Args:
            filters: Dictionary containing filter criteria for tasks.
                    -status: Optional; filter tasks by status.
                    -task_priority: Optional; filter tasks by priority.
                    -reviewer_id: Optional; filter tasks by reviewer.
                    -require_all_members_to_complete: Optional; filter tasks by whether all members are required to complete.
                    -deadline: Optional; filter tasks by deadline.
                    -q: Optional; search query to filter tasks by name or description.
                    -review_group: Optional; filter task by review group.
                    -require_approval_for_all_groups: Optional; filter tasks by whether approval is required for all groups.
                    -depends_on: Optional; filter tasks that depend on another task.
                    -created_by: Optional; filter tasks created by a specific user.
                    -updated_by: Optional; filter tasks updated by a specific user.
                    -created_at: Optional; filter tasks created at a specific time.
                    -updated_at: Optional; filter tasks updated at a specific time.

        Returns:
            APIResponse: Response object containing:
                - page: The current page of tasks.
                - total_pages: Total number of pages available.
                - page_size: Number of tasks per page.
                - has_next: Boolean indicating if there are more pages.
                - has_previous: Boolean indicating if there are previous pages.
                - results: List of tasks matching the filters.
        """
        try:
            repository_response = tasks_repository.get_all_tasks()

            if not repository_response.success:
                return APIResponse(
                    success=False,
                    message="Failed to retrieve tasks from repository",
                    code=500,
                )

            tasks_query = repository_response.data

            filtered_tasks = tasks_filter_service._apply_filters(tasks_query, filters)

            if isinstance(filtered_tasks, APIResponse):
                return filtered_tasks

            tasks = filtered_tasks

            page = filters.get("page", 1)
            page_size = filters.get("page_size", 10)

            paginator = Paginator(tasks, page_size)

            try:
                paginated_tasks = paginator.page(page)
            except PageNotAnInteger:
                paginated_tasks = paginator.page(1)
                page = 1
            except EmptyPage:
                paginated_tasks = paginator.page(paginator.num_pages)
                page = paginator.num_pages

            serializer = GetReviewProcessTasksSerializer(paginated_tasks, many=True)

            return APIResponse(
                success=True,
                message="Tasks retrieved successfully",
                data={
                    "page": int(page),
                    "total_pages": paginator.num_pages,
                    "page_size": int(page_size),
                    "has_next": paginated_tasks.has_next(),
                    "has_previous": paginated_tasks.has_previous(),
                    "total_count": paginator.count,
                    "results": serializer.data,
                },
                code=200,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving tasks",
                code=500,
            )
