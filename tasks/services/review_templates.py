from accounts.repositories.permissions.review_templates import ReviewTemplatesRepository
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from tasks.models import ReviewTemplates
from tasks.serializers import GetReviewTemplateSerializer, ReviewTemplateSerializer
from django.db.models import Q


class ReviewTemplateService:
    """Service to manage review templates."""

    def __init__(self, user):
        self.user = user
        self.logging_service = LoggingService()
        self.repository = ReviewTemplatesRepository(user)

    def get_review_templates(self, params) -> APIResponse:
        """
        Retrieves a paginated, optionally filtered and sorted list of review templates.
        Args:
            params (dict): Dictionary of query parameters which may include:
                - "q" (str, optional): Search query to filter review templates by name or description.
                - "order_by" (str, optional): Field to sort by ("name" or "created_at").
                - "sort_order" (str, optional): Sorting order ("asc" for ascending, "desc" for descending). Defaults to "asc".
                - "page" (int, optional): Page number for pagination. Defaults to 1.
                - "page_size" (int, optional): Number of items per page. Defaults to 10.
        Returns:
            APIResponse: An APIResponse object containing:
                - success (bool): Whether the operation was successful.
                - message (str): A message describing the result.
                - data (dict or None): A dictionary with paginated results and metadata, or None on error.
                    - "results": List of serialized review templates.
                    - "count": Total number of review templates matching the query.
                    - "page": Current page number.
                    - "page_size": Number of items per page.
                    - "has_next": Whether there is a next page.
                    - "has_previous": Whether there is a previous page.
                - code (int): HTTP status code (200 on success, 400 on invalid sort field).
        """

        review_templates = ReviewTemplates.objects.all()

        # search name
        search_query = params.get("q", None)
        if search_query:
            review_templates = review_templates.filter(
                Q(name__icontains=search_query)
                | Q(description__icontains=search_query),
            )

        # sorting the review templates by name or created_at
        sort_by = params.get("sort_by", "created_at")
        sorting_order = params.get("sort_order", "asc")
        if sort_by:
            if sort_by == "name":
                review_templates = review_templates.order_by(
                    "name" if sorting_order == "asc" else "-name"
                )
            elif sort_by == "created_at":
                review_templates = review_templates.order_by(
                    "created_at" if sorting_order == "asc" else "-created_at"
                )
            else:
                return APIResponse(
                    success=False,
                    message="Invalid sort field.",
                    data=None,
                    code=400,
                )
        # pagination
        page = int(params.get("page", 1))
        page_size = int(params.get("page_size", 10))
        total_count = review_templates.count()
        start = (page - 1) * page_size
        end = start + page_size
        review_templates = review_templates[start:end]
        has_next = end < total_count
        has_previous = start > 0
        serializer = ReviewTemplateSerializer(
            review_templates,
            many=True,
        )

        data = {
            "results": serializer.data,
            "count": total_count,
            "page": page,
            "page_size": page_size,
            "has_next": has_next,
            "has_previous": has_previous,
        }
        return APIResponse(
            success=True,
            message="Review templates retrieved successfully.",
            data=data,
            code=200,
        )

    def get_review_template_by_id(self, template_id) -> APIResponse:
        """Get a review template by ID."""

        response = self.repository.get_review_template_by_id(template_id)
        if not response.success:
            return APIResponse(
                success=False,
                message=response.message,
                data=None,
                code=400,
            )

        serializer = GetReviewTemplateSerializer(response.data)
        return APIResponse(
            success=True,
            message=response.message,
            data=serializer.data,
            code=200,
        )

    def create_review_template(self, data) -> APIResponse:
        """Create a new review template."""
        try:
            data["created_by_id"] = self.user.id
            response = self.repository.create_review_template(data)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )

            serializer = ReviewTemplateSerializer(response.data)
            return APIResponse(
                success=True,
                message=response.message,
                data=serializer.data,
                code=201,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the review template.",
                data=None,
                code=500,
            )

    def update_review_template(self, template_id, data) -> APIResponse:
        """Update an existing review template."""
        try:
            data["updated_by"] = self.user.id
            response = self.repository.update_review_template(template_id, data)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )

            serializer = ReviewTemplateSerializer(response.data)
            return APIResponse(
                success=True,
                message=response.message,
                data=serializer.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while updating the review template.",
                data=None,
                code=500,
            )

    def delete_review_template(self, template_id) -> APIResponse:
        """Delete a review template."""
        try:
            response = self.repository.delete_review_template(template_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message=response.message,
                data=None,
                code=204,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while deleting the review template.",
                data=None,
                code=500,
            )
