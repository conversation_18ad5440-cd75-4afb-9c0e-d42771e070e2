from accounts.models import Profile
from adverse_drug_reaction.models import AdverseDrugReaction
from django.db.models import Model
from typing import Type

from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from tasks.serializers import GetReviewProcessTasksSerializer
from django.contrib.auth.models import User
from django.db.models import Q

logging_service = LoggingService()


class IncidentTasks:
    def __init__(self, instance: Type[Model], logged_in_user: Type[User], filters=None):
        self.instance = instance
        self.logged_in_user = logged_in_user

    def get_tasks(self) -> RepositoryResponse:
        """
        Get tasks for the incident
        """
        try:
            tasks = self.instance.review_tasks.all()

            # filter tasks

            """
            1. If logged in user is admin, return all tasks
            2. If not
                a. Check of an review process has review_process
                    If yes, filter tasks by checking of logged in user is a in task.review_groups.members
                b. Check if an if a logged user is in instance.reviewers
            """

            if not self.logged_in_user.is_superuser:
                tasks = tasks.filter(
                    Q(
                        review_groups__members__user=self.logged_in_user,
                    )
                    | Q(
                        reviewers__user=self.logged_in_user,
                    ),
                ).distinct()

            serializer = GetReviewProcessTasksSerializer(tasks, many=True)
            return RepositoryResponse(
                data=serializer.data,
                message="Tasks retrieved successfully",
                success=True,
            )

        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Internal server error while getting incident tasks",
            )
