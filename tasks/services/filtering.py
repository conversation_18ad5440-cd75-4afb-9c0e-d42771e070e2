from base.services.logging.logger import LoggingService
from datetime import datetime
from django.db.models import Q
from typing import Type
from django.db.models import Model
from base.services.responses import APIResponse
from base.validadors.date import DateValidator


logging_service = LoggingService()


class TasksFilterService:
    def __init__(self):
        self.date_validator = DateValidator()

    def _apply_filters(self, tasks_query, filters):
        """
        Apply filters to the tasks query.

        Args:
            tasks_query: The initial queryset of tasks
            filters: Dictionary containing filter criteria

        Returns:
            Union[QuerySet, APIResponse]: Filtered queryset or error response
        """
        if "status" in filters and filters.get("status") is not None:
            result = self.handle_status_filter(
                tasks_query=tasks_query, status=filters.get("status")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "task_priority" in filters and filters.get("task_priority") is not None:
            result = self.handle_task_priority_filter(
                tasks_query=tasks_query, task_priority=filters.get("task_priority")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "reviewer_id" in filters and filters.get("reviewer_id") is not None:
            result = self.handle_reviewer_filter(
                tasks_query=tasks_query, reviewer=filters.get("reviewer_id")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if (
            "require_all_members_to_complete" in filters
            and filters.get("require_all_members_to_complete") is not None
        ):
            result = self.handle_require_all_members_to_complete_filter(
                tasks_query=tasks_query,
                require_all_members_to_complete=filters.get(
                    "require_all_members_to_complete"
                ),
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "deadline" in filters and filters.get("deadline") is not None:
            result = self.handle_deadline_filter(
                tasks_query=tasks_query, deadline=filters.get("deadline")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "q" in filters and filters.get("q") is not None:
            result = self.handle_search_query_filter(
                tasks_query=tasks_query, search_query=filters.get("q")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "review_group" in filters and filters.get("review_group") is not None:
            result = self.handle_review_group_filter(
                tasks_query=tasks_query, review_group=filters.get("review_group")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if (
            "require_approval_for_all_groups" in filters
            and filters.get("require_approval_for_all_groups") is not None
        ):
            result = self.handle_require_approval_for_all_groups_filter(
                tasks_query=tasks_query,
                require_approval_for_all_groups=filters.get(
                    "require_approval_for_all_groups"
                ),
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "depends_on" in filters and filters.get("depends_on") is not None:
            result = self.handle_depends_on_filter(
                tasks_query=tasks_query, depends_on=filters.get("depends_on")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "created_by" in filters and filters.get("created_by") is not None:
            result = self.handle_created_by_filter(
                tasks_query=tasks_query, created_by=filters.get("created_by")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "updated_by" in filters and filters.get("updated_by") is not None:
            result = self.handle_updated_by_filter(
                tasks_query=tasks_query, updated_by=filters.get("updated_by")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "created_at" in filters and filters.get("created_at") is not None:
            result = self.handle_created_at_filter(
                tasks_query=tasks_query, created_at=filters.get("created_at")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        if "updated_at" in filters and filters.get("updated_at") is not None:
            result = self.handle_updated_at_filter(
                tasks_query=tasks_query, updated_at=filters.get("updated_at")
            )
            if isinstance(result, APIResponse):
                return result
            tasks_query = result

        # sort tasks
        sort_by = filters.get("sort_by", "created_at")
        sort_order = filters.get("sort_order", "desc")

        model = tasks_query.model
        if not hasattr(model, sort_by):
            pass  # Ignore if sort_by is not a valid field

        if sort_order == "asc":
            tasks_query = tasks_query.order_by(sort_by)
        else:
            tasks_query = tasks_query.order_by(f"-{sort_by}")
        return tasks_query

    def handle_status_filter(self, tasks_query, status):
        """Filter tasks by status"""
        try:
            filtered_query = tasks_query.filter(status=status)
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by status",
                code=400,
            )

    def handle_task_priority_filter(self, tasks_query, task_priority):
        """Filter tasks by priority"""
        try:
            filtered_query = tasks_query.filter(task_priority=task_priority)
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by priority",
                code=400,
            )

    def handle_reviewer_filter(self, tasks_query, reviewer):
        """Filter tasks by reviewer"""
        try:
            filtered_query = tasks_query.filter(reviewers__id=reviewer)
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by reviewer",
                code=400,
            )

    def handle_require_all_members_to_complete_filter(
        self, tasks_query, require_all_members_to_complete
    ):
        """Filter tasks by whether all members are required to complete"""
        try:
            filtered_query = tasks_query.filter(
                require_all_members_to_complete=require_all_members_to_complete
            )
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by require all members to complete",
                code=400,
            )

    def handle_deadline_filter(self, tasks_query, deadline):
        """Filter tasks by deadline"""
        validation_result = self.date_validator.is_valid_date(deadline)
        if not validation_result.success:
            logging_service.log_error(validation_result.message)
            return APIResponse(
                success=False,
                message="Invalid deadline format",
                code=400,
            )

        try:
            filtered_query = tasks_query.filter(deadline=validation_result.data.date())
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by deadline",
                code=400,
            )

    def handle_search_query_filter(self, tasks_query, search_query):
        """Filter tasks by search query (name or description)"""
        try:
            filtered_query = tasks_query.filter(
                Q(name__icontains=search_query) | Q(description__icontains=search_query)
            )
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by search query",
                code=400,
            )

    def handle_review_group_filter(self, tasks_query, review_group):
        """Filter tasks by review group"""
        try:
            filtered_query = tasks_query.filter(review_groups__id=review_group)
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by review group",
                code=400,
            )

    def handle_require_approval_for_all_groups_filter(
        self, tasks_query, require_approval_for_all_groups
    ):
        """Filter tasks by whether approval is required for all groups"""
        try:
            filtered_query = tasks_query.filter(
                require_approval_for_all_groups=require_approval_for_all_groups
            )
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by require approval for all groups",
                code=400,
            )

    def handle_depends_on_filter(self, tasks_query, depends_on):
        """Filter tasks by dependency"""
        try:
            filtered_query = tasks_query.filter(depends_on__id=depends_on)
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by dependency",
                code=400,
            )

    def handle_created_by_filter(self, tasks_query, created_by):
        """Filter tasks by creator"""
        try:
            filtered_query = tasks_query.filter(created_by__id=created_by)
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by creator",
                code=400,
            )

    def handle_updated_by_filter(self, tasks_query, updated_by):
        """Filter tasks by updater"""
        try:
            filtered_query = tasks_query.filter(updated_by__id=updated_by)
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by updater",
                code=400,
            )

    def handle_created_at_filter(self, tasks_query, created_at):
        """Filter tasks by creation date"""
        validation_result = self.date_validator.is_valid_date(created_at)
        if not validation_result.success:
            logging_service.log_error(validation_result.message)
            return APIResponse(
                success=False,
                message="Invalid created_at format",
                code=400,
            )

        try:
            filtered_query = tasks_query.filter(
                created_at__date=validation_result.data.date()
            )
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by creation date",
                code=400,
            )

    def handle_updated_at_filter(self, tasks_query, updated_at):
        """Filter tasks by update date"""
        validation_result = self.date_validator.is_valid_date(updated_at)
        if not validation_result.success:
            logging_service.log_error(validation_result.message)
            return APIResponse(
                success=False,
                message="Invalid updated_at format",
                code=400,
            )

        try:
            filtered_query = tasks_query.filter(
                updated_at__date=validation_result.data.date()
            )
            return filtered_query
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error filtering tasks by update date",
                code=400,
            )
