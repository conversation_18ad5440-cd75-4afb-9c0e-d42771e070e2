from accounts.repositories.permissions.review_groups import ReviewGroupsRepository
from accounts.repositories.permissions.review_templates import ReviewTemplatesRepository
from accounts.repositories.tasks import ReviewTemplateTasksRepository

from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse, APIResponse
from datetime import datetime, timedelta
from django.db.models import Q

from tasks.models import ReviewTemplateTasks
from tasks.serializers import (
    GetReviewTemplateTaskSerializer,
    ReviewTemplateTaskSerializer,
)


class ReviewTasksService:
    def __init__(self, user):
        self.user = user
        self.logging_service = LoggingService()
        self.repository = ReviewTemplateTasksRepository(user)
        self.review_groups_repo = ReviewGroupsRepository(user)
        self.review_templates_repo = ReviewTemplatesRepository(user)

    def get_tasks(self, template_id, params) -> APIResponse:
        """
        Retrieves tasks associated with a specific review template, with support for filtering, searching, sorting, and pagination.

        Args:
            template_id (int): The ID of the review template to retrieve tasks for.
            params (dict): A dictionary of query parameters for filtering, searching, sorting, and pagination. Supported keys include:
                - "q" (str, optional): Search query for task name, description, or review group title.
                - "sort_by" (str, optional): Field to sort by ("name" or "created_at").
                - "sort_order" (str, optional): Sorting order ("asc" or "desc").
                - "review_group_id" (int, optional): Filter tasks by review group ID.
                - "require_approval_for_all_groups" (bool or str, optional): Filter tasks by approval requirement.
                - "review_template_id" (int, optional): Filter tasks by review template ID.
                - "task_priority" (str, optional): Filter tasks by priority.
                - "depends_on_id" (int, optional): Filter tasks that depend on a specific task.
                - "page" (int, optional): Page number for pagination (default is 1).
                - "page_size" (int, optional): Number of tasks per page (default is 10).

        Returns:
            APIResponse: An APIResponse object containing:
                - success (bool): Whether the operation was successful.
                - message (str): A message describing the result.
                - data (dict or None): A dictionary with paginated task results and metadata, or None on error.
                - code (int): HTTP status code.
        """
        try:
            tasks = ReviewTemplateTasks.objects.filter(review_template=template_id)

            # search name, description, review group name
            search_query = params.get("q", None)
            if search_query:
                tasks = tasks.filter(
                    Q(name__icontains=search_query)
                    | Q(description__icontains=search_query)
                    | Q(review_groups__title__icontains=search_query),
                ).distinct()

            # sorting the tasks by name or created_at
            sort_by = params.get("sort_by", None)
            sorting_order = params.get("sort_order", "asc")
            if sort_by:
                if sort_by == "name":
                    tasks = tasks.order_by(
                        "name" if sorting_order == "asc" else "-name"
                    )
                elif sort_by == "created_at":
                    tasks = tasks.order_by(
                        "created_at" if sorting_order == "asc" else "-created_at"
                    )
                else:
                    return APIResponse(
                        success=False,
                        message="Invalid sort field.",
                        data=None,
                        code=400,
                    )
            review_group = params.get("review_group_id", None)
            if review_group:
                tasks = tasks.filter(review_groups__id=review_group).distinct()
            require_approval = params.get("require_approval_for_all_groups", None)

            review_template = params.get("review_template_id", None)
            if review_template:
                tasks = tasks.filter(review_template__id=review_template).distinct()
            if require_approval is not None:
                if isinstance(require_approval, bool):
                    pass
                elif isinstance(require_approval, str):
                    require_approval = require_approval.lower() == "true"
                tasks = tasks.filter(require_approval_for_all_groups=require_approval)
            task_priority = params.get("task_priority", None)
            if task_priority:
                tasks = tasks.filter(task_priority=task_priority)
            depends_on = params.get("depends_on_id", None)
            if depends_on:
                tasks = tasks.filter(depends_on__id=depends_on).distinct()

            # pagination
            page = int(params.get("page", 1))
            page_size = int(params.get("page_size", 10))
            total_count = tasks.count()
            start = (page - 1) * page_size
            end = start + page_size
            tasks = tasks[start:end]
            has_next = end < total_count
            has_previous = start > 0

            serializer = GetReviewTemplateTaskSerializer(
                tasks,
                many=True,
            )
            data = {
                "results": serializer.data,
                "count": total_count,
                "page": page,
                "page_size": page_size,
                "has_next": has_next,
                "has_previous": has_previous,
            }
            return APIResponse(
                success=True,
                message="Tasks retrieved successfully.",
                data=data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving tasks.",
                data=None,
                code=500,
            )

    def get_task_by_id(self, task_id) -> APIResponse:
        """
        Retrieves a specific task by its ID.
        """
        try:
            response = self.repository.get_task_by_id(task_id)
            if not response.success:
                self.logging_service.log_error(response.message)
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = GetReviewTemplateTaskSerializer(response.data)
            return APIResponse(
                success=True,
                message="Task retrieved successfully.",
                data=serializer.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving the task.",
                data=None,
                code=500,
            )

    def create_task(self, template_id, data) -> APIResponse:
        """
        Creates a new task associated with a specific review template.
        """
        try:
            required_fields = ["name", "description", "number_of_days"]
            missing_fields = self.logging_service.check_required_fields(
                data, required_fields
            )
            if missing_fields:
                return APIResponse(
                    success=False,
                    message=missing_fields,
                    data=None,
                    code=400,
                )
            review_template_response = (
                self.review_templates_repo.get_review_template_by_id(template_id)
            )
            if not review_template_response.success:
                return APIResponse(
                    success=False,
                    message=review_template_response.message,
                    data=None,
                    code=400,
                )
            data["review_template"] = review_template_response.data

            # handle deadline
            number_of_days = data["number_of_days"]
            data["deadline"] = datetime.now().date() + timedelta(days=number_of_days)
            data["created_by_id"] = self.user.id
            response = self.repository.create_task(data)
            if not response.success:
                self.logging_service.log_error(response.message)
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = ReviewTemplateTaskSerializer(response.data)
            return APIResponse(
                success=True,
                message="Task created successfully.",
                data=serializer.data,
                code=201,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the task.",
                data=None,
                code=500,
            )

    def update_task(self, task_id, data) -> APIResponse:
        """
        Updates an existing task by its ID.
        """
        try:
            data["updated_by"] = self.user.id
            response = self.repository.update_task(task_id, data)
            if not response.success:
                self.logging_service.log_error(response.message)
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = ReviewTemplateTaskSerializer(response.data)
            return APIResponse(
                success=True,
                message="Task updated successfully.",
                data=serializer.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while updating the task.",
                data=None,
                code=500,
            )

    def delete_task(self, task_id) -> APIResponse:
        """
        Deletes a task by its ID.
        """
        try:
            response = self.repository.delete_task(task_id)
            if not response.success:
                self.logging_service.log_error(response.message)
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            return APIResponse(
                success=True,
                message="Task deleted successfully.",
                data=None,
                code=204,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while deleting the task.",
                data=None,
                code=500,
            )

    def add_review_groups(self, task_id, data) -> APIResponse:
        """
        Adds review groups to a task.
        """
        try:
            task_response = self.repository.get_task_by_id(task_id)
            if not task_response.success:
                return APIResponse(
                    success=False,
                    message=task_response.message,
                    data=None,
                    code=400,
                )
            task = task_response.data
            if not "review_groups" in data or not data.get("review_groups"):
                return APIResponse(
                    success=False,
                    message="No review groups provided.",
                    data=None,
                    code=400,
                )
            review_groups_ids = data.get("review_groups")
            response = self._handle_review_groups(task, review_groups_ids)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = GetReviewTemplateTaskSerializer(task)
            return APIResponse(
                success=True,
                message="Review groups added successfully.",
                data=serializer.data,
                code=200,
            )

        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while adding review groups.",
                data=None,
                code=500,
            )

    def remove_review_groups(self, task_id, data) -> APIResponse:
        """
        Removes review groups from a task.
        """
        try:
            task_response = self.repository.get_task_by_id(task_id)
            if not task_response.success:
                return APIResponse(
                    success=False,
                    message=task_response.message,
                    data=None,
                    code=400,
                )
            task = task_response.data
            if not "review_groups" in data or not data.get("review_groups"):
                return APIResponse(
                    success=False,
                    message="No review groups provided.",
                    data=None,
                    code=400,
                )
            review_groups_ids = data.get("review_groups")
            response = self._remove_review_groups(task, review_groups_ids)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = GetReviewTemplateTaskSerializer(task)
            return APIResponse(
                success=True,
                message="Review groups removed successfully.",
                data=serializer.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while removing review groups.",
                data=None,
                code=500,
            )

    def _handle_review_groups(
        self,
        task: ReviewTemplateTasks,
        review_groups_ids,
    ) -> APIResponse:
        """
        Handles the review groups associated with a task.
        """
        # loop through review_groups_ids, and get groups

        review_groups = []
        for group_id in review_groups_ids:
            response = self.review_groups_repo.get_review_group(group_id)
            if not response.success:
                continue
            review_groups.append(response.data.id)

        if not review_groups:
            return APIResponse(
                success=False,
                message="No valid review groups found.",
                data=None,
                code=400,
            )

        task.review_groups.add(*review_groups)
        return APIResponse(
            success=True,
            message="Review groups added successfully.",
            data=None,
            code=200,
        )

    def _remove_review_groups(
        self,
        task: ReviewTemplateTasks,
        review_groups_ids,
    ) -> APIResponse:
        """
        Removes review groups associated with a task.
        """
        # loop through review_groups_ids, and get groups
        review_groups = []
        for group_id in review_groups_ids:
            response = self.review_groups_repo.get_review_group(group_id)
            if not response.success:
                continue
            review_groups.append(response.data)

        if not review_groups:
            return APIResponse(
                success=False,
                message="No valid review groups found.",
                data=None,
                code=400,
            )

        task.review_groups.remove(*review_groups)
        return APIResponse(
            success=True,
            message="Review groups removed successfully.",
            data=None,
            code=200,
        )
