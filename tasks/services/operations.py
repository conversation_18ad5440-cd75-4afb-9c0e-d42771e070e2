from requests import Response
from accounts.models import Profile
from api.views.auth.permissions_list import is_super_user, check_user_group
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from tasks.models import ReviewGroups
from tasks.repositories.tasks_repository import TasksRepository
from tasks.serializers import (
    GetReviewProcessTasksSerializer
)



class TaskService:
    def __init__(self, user):
        self.user = user
        self.logging_service = LoggingService()
        self.repository = TasksRepository()
    
    def get_task_by_id(self, task_id) -> APIResponse:
        try:
            response = self.repository.get_task_by_id(task_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = GetReviewProcessTasksSerializer(response.data)
            return APIResponse(
                success=True,
                message="Task retrieved successfully.",
                data=serializer.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving the task.",
                data=None,
                code=500,
            )

    def update_task(self, task_id, data) -> APIResponse:
        try:
            if not is_super_user(self.user) and not check_user_group(self.user, "Admin"):
                return APIResponse(
                    success=False,
                    message="You do not have permission to update this task.",
                    data=None,
                    code=403,
                )
            """check fields with relationships if those relationships exist"""
            if "groups_completed" in data:
                review_group_ids = []
                for review_group_id in data["groups_completed"]:
                    try:
                        review_group = ReviewGroups.objects.get(id=review_group_id)
                    except ReviewGroups.DoesNotExist:
                        return APIResponse(
                            success=False,
                            message="Review group not found.",
                            data=None,
                            code=400,
                        )
                    review_group_ids.append(review_group.id)
                data.pop("groups_completed")
                data["groups_completed"] = review_group_ids
            
            if "reviewers" in data:
                reviewer_ids = []
                for reviewer_id in data["reviewers"]:
                    try:
                        reviewer = Profile.objects.get(id=reviewer_id)
                    except Profile.DoesNotExist:
                        return APIResponse(
                            success=False,
                            message="Reviewer not found.",
                            data=None,
                            code=400,
                        )
                    reviewer_ids.append(reviewer.id)
                data.pop("reviewers")
                data["reviewers"] = reviewer_ids
            
            if "reviewers_completed" in data:
                reviewer_ids = []
                for reviewer_id in data["reviewers_completed"]:
                    try:
                        reviewer = Profile.objects.get(id=reviewer_id)
                    except Profile.DoesNotExist:
                        return APIResponse(
                            success=False,
                            message="Reviewer not found.",
                            data=None,
                            code=400,
                        )
                    reviewer_ids.append(reviewer.id)
                data.pop("reviewers_completed")
                data["reviewers_completed"] = reviewer_ids

            data["updated_by"] = self.user.id

            response = self.repository.update_task(task_id, data)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            serializer = GetReviewProcessTasksSerializer(response.data)
            return APIResponse(
                success=True,
                message="Task updated successfully.",
                data=serializer.data,
                code=200,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while updating the task.",
                data=None,
                code=500,
            )

    def delete_task(self, task_id) -> APIResponse:
        try:
            if not is_super_user(self.user) and not check_user_group(self.user, "Admin"):
                return APIResponse(
                    success=False,
                    message="You do not have permission to delete this task.",
                    data=None,
                    code=403,
                )
            response = self.repository.delete_task(task_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    code=400,
                )
            return APIResponse(
                success=True,
                message="Task deleted successfully.",
                data=None,
                code=204,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while deleting the task.",
                data=None,
                code=500,
            )
