from accounts.models import Profile
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse
from tasks.models import ReviewProcessTasks
from typing import Type
from django.contrib.auth.models import User

from tasks.permissions import TaskPermissions
from tasks.serializers import GetReviewProcessTasksSerializer


class TaskActions:

    def __init__(self, task_id, user):
        self.task_id = task_id
        self.user = user
        self.logging_service = LoggingService()

    def submit_task(self) -> APIResponse:
        """Submit task"""
        try:
            # Logic to submit the task
            """
            1. Check if the task exists
            2. Check if the user has permission to submit the task
            3. Update the task status to submitted
            4. Notify the relevant parties
            """

            task = ReviewProcessTasks.objects.get(id=self.task_id)

            # Check if the user has permission to submit the task

            if not self.is_group_member(task, self.user) and not self._is_reviewer(
                task, self.user
            ):
                return APIResponse(
                    success=False,
                    message="You do not have permission to submit this task",
                    data=None,
                    code=403,
                )
            if self.member_has_completed_task(task, self.user):
                return APIResponse(
                    success=False,
                    message="You have already submitted this task as a reviewer",
                    data=None,
                    code=400,
                )
            if self.group_has_completed_task(task, self.user):
                return APIResponse(
                    success=False,
                    message="You have already submitted this task as a group member",
                    data=None,
                    code=400,
                )

            # complete task

            if self.is_group_member(task, self.user):
                return self.submit_group_task(task, self.user)
            elif self._is_reviewer(task, self.user):
                return self.submit_task_as_reviewer(task, self.user)

            else:
                return APIResponse(
                    success=False,
                    message="We could not find a group or reviewer for this task",
                    data=None,
                    code=403,
                )

        except ReviewProcessTasks.DoesNotExist:
            return APIResponse(
                success=False,
                message="Task not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while submitting the task",
                data=None,
                code=500,
            )

    def complete_task(self) -> APIResponse:
        """Complete task"""
        try:
            # Logic to complete the task
            """
            1. Check if the task exists
            2. Check if the user has permission to complete the task
            3. Update the task status to completed
            4. Notify the relevant parties
            """

            task = ReviewProcessTasks.objects.get(id=self.task_id)

            # Check if the user has permission to complete the task
            can_complete_task = TaskPermissions(self.user, task).can_complete_task()
            if not can_complete_task.success:
                return APIResponse(
                    success=False,
                    message=can_complete_task.message,
                    data=None,
                    code=403,
                )

            # complete task
            task.status = "Completed"
            task.save()
            serializer = GetReviewProcessTasksSerializer(task)
            return APIResponse(
                success=True,
                message="Task completed successfully",
                data=serializer.data,
                code=200,
            )

        except ReviewProcessTasks.DoesNotExist:
            return APIResponse(
                success=False,
                message="Task not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while completing the task",
                data=None,
                code=500,
            )

    def approve_task(self) -> APIResponse:
        """Approve task"""
        try:
            # Logic to approve the task
            """
            1. Check if the task exists
            2. Check if the user has permission to approve the task
            3. Update the task status to approved
            4. Notify the relevant parties
            """

            task = ReviewProcessTasks.objects.get(id=self.task_id)

            # Check if the user has permission to approve the task
            can_approve_task = TaskPermissions(self.user, task).can_approve_task()
            if not can_approve_task.success:
                return APIResponse(
                    success=False,
                    message=can_approve_task.message,
                    data=None,
                    code=403,
                )

            # Approve task
            task.status = "Approved"
            task.save()

            serializer = GetReviewProcessTasksSerializer(task)
            return APIResponse(
                success=True,
                message="Task approved successfully",
                data=serializer.data,
                code=200,
            )

        except ReviewProcessTasks.DoesNotExist:
            return APIResponse(
                success=False,
                message="Task not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while approving the task",
                data=None,
                code=500,
            )

    def _is_reviewer(self, task: ReviewProcessTasks, user: User) -> bool:
        """
        Check if the user is a reviewer for the task.

        Args:
            task: ReviewProcessTasks instance
            user: User instance to check

        Returns:
            bool: True if the user is a reviewer, False otherwise
        """
        try:
            profile = Profile.objects.get(user=user)
            return profile in task.reviewers.all()
        except Profile.DoesNotExist:
            return False
        except Exception as e:
            self.logging_service.log_error(e)
            return False

    def is_group_member(self, task: ReviewProcessTasks, user: User) -> bool:
        """
        Check if the user is in any group associated with the task.

        Args:
            task: ReviewProcessTasks instance
            user: User instance to check

        Returns:
            bool: True if the user is in any group, False otherwise
        """
        try:
            profile = Profile.objects.get(user=user)
            return task.review_groups.filter(members=profile).exists()
        except Exception as e:
            self.logging_service.log_error(e)
            return False

    def member_has_completed_task(self, task: ReviewProcessTasks, user: User) -> bool:
        """
        Check if the user has completed the task as a reviewer.

        Args:
            task: ReviewProcessTasks instance
            user: User instance to check

        Returns:
            bool: True if the user has completed the task as a reviewer, False otherwise
        """
        try:
            profile = Profile.objects.get(user=user)
            return profile in task.reviewers_completed.all()
        except Exception as e:
            self.logging_service.log_error(e)
            return False

    def group_has_completed_task(self, task: ReviewProcessTasks, user: User) -> bool:
        """
        Check if the user has completed the task as a group member.

        Args:
            task: ReviewProcessTasks instance
            user: User instance to check

        Returns:
            bool: True if the user has completed the task as a group member, False otherwise
        """
        try:
            profile = Profile.objects.get(user=user)
            return task.groups_completed.filter(members=profile).exists()
        except Exception as e:
            self.logging_service.log_error(e)
            return False

    def submit_group_task(self, task: ReviewProcessTasks, user: User) -> APIResponse:
        """
        Submit a group task.

        Args:
            task: ReviewProcessTasks instance
            user: User instance to check

        Returns:
            APIResponse: Response object with success status and message
        """
        try:
            # get the review group
            review_group = task.review_groups.filter(members=user.profile).first()
            if not review_group:
                return APIResponse(
                    success=False,
                    message="You are not a member of this review group",
                    data=None,
                    code=403,
                )
            if not task.require_approval_for_all_groups:
                # update group Submitted
                task.status = "Submitted"
                task.save()
                task.groups_completed.add(review_group)
                # TODO: notify the group members
                serializer = GetReviewProcessTasksSerializer(task)
                return APIResponse(
                    success=True,
                    message="Group task submitted successfully",
                    data=serializer.data,
                    code=200,
                )
            else:
                task.status = "In Progress"
                task.save()
                # update group completed
                task.groups_completed.add(review_group)
                # TODO: notify the group members
                return APIResponse(
                    success=True,
                    message="Group task submitted successfully, awaiting approval",
                    data=None,
                    code=200,
                )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while submitting the group task",
                data=None,
                code=500,
            )

    def submit_task_as_reviewer(
        self, task: ReviewProcessTasks, user: User
    ) -> APIResponse:
        """
        Submit a task as a reviewer.

        Args:
            task: ReviewProcessTasks instance
            user: User instance to check
        Returns:
            APIResponse: Response object with success status and message
        """

        try:
            # get the review group
            if not task.require_all_members_to_complete:
                # update task to Submitted
                task.status = "Submitted"
                task.save()
                task.reviewers_completed.add(user.profile)
                # TODO: notify the reviewers
                serializer = GetReviewProcessTasksSerializer(task)
                return APIResponse(
                    success=True,
                    message="Task submitted successfully",
                    data=serializer.data,
                    code=200,
                )
            else:
                # update task to In Progress
                task.status = "In Progress"
                task.save()
                task.reviewers_completed.add(user.profile)
                # TODO: notify the reviewers
                return APIResponse(
                    success=True,
                    message="Task submitted successfully, awaiting approval",
                    data=None,
                    code=200,
                )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while submitting the task",
                data=None,
                code=500,
            )
