from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse, APIResponse
from tasks.models import ReviewProcessTasks
from tasks.serializers import GetReviewProcessTasksSerializer

logging_service = LoggingService()


class TasksWorkflow:

    def __init__(self, user, task_id, data):
        self.user = user
        self.task_id = task_id

    def get_task_by_id(self) -> APIResponse:
        """Get task by id"""
        try:
            task = (
                ReviewProcessTasks.objects.select_related(
                    "review_process",
                )
                .prefetch_related(
                    "groups_completed",
                    "review_groups",
                    "reviewers",
                    "reviewers_completed",
                    "review_groups",
                )
                .get(id=self.task_id)
            )
            serializer = GetReviewProcessTasksSerializer(task)

            return APIResponse(
                success=True,
                message="Task retrieved successfully",
                data=serializer.data,
                code=200,
            )
        except ReviewProcessTasks.DoesNotExist:
            return APIResponse(
                success=False,
                message="Task not found",
                data=None,
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving the task",
                data=None,
                code=500,
            )

    def delete_task(self) -> APIResponse:
        """Delete task by id"""

        try:
            # Check permissions

            # Check dependent tasks
            if (
                ReviewProcessTasks.dependent_tasks.exists()
            ):  # Using related_name from ReviewTemplateTasks
                return APIResponse(
                    success=False,
                    message="Cannot delete task because other tasks depend on it.",
                    code=400,
                )

            # Check task status
            if ReviewProcessTasks.status in ["In Progress", "Completed"]:
                return APIResponse(
                    success=False,
                    message=f"Cannot delete task with status '{ReviewProcessTasks.status}'.",
                    code=400,
                )

            # Check reviewers and groups
            if (
                ReviewProcessTasks.reviewers.exists()
                or ReviewProcessTasks.reviewers_completed.exists()
            ):
                return APIResponse(
                    success=False,
                    message="Cannot delete task with assigned or completed reviewers.",
                    code=400,
                )
            if ReviewProcessTasks.groups_completed.exists():
                return APIResponse(
                    success=False,
                    message="Cannot delete task with completed groups.",
                    code=400,
                )

            # Check deadline
            from datetime import date

            if (
                ReviewProcessTasks.deadline
                and ReviewProcessTasks.deadline >= date.today()
            ):
                return APIResponse(
                    success=False,
                    message="Cannot delete task with an active deadline.",
                    code=400,
                )

            # Check if last task in review process
            if ReviewProcessTasks.review_process.review_process_tasks.count() == 1:
                return APIResponse(
                    success=False,
                    message="Cannot delete the last task in the review process.",
                    code=400,
                )

            # TODO: Notify stakeholders (optional, implement send_notification as needed)

            # Perform deletion
            ReviewProcessTasks.delete()

            return APIResponse(
                success=True,
                message=f"Task '{ReviewProcessTasks.name}' deleted successfully.",
                code=200,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An unexpected error occurred during task deletion.",
                code=500,
            )
