from typing import Type
from django.contrib.auth.models import User
from base.services.logging.logger import LoggingService
from base.services.responses import RepositoryResponse
from tasks.models import ReviewProcessTasks
from accounts.models import Profile

logger = LoggingService()


class TaskPermissions:

    def __init__(self, user: Type[User], task: Type[ReviewProcessTasks]):
        self.user = user
        self.task = task

    def can_complete_task(self) -> RepositoryResponse:
        try:
            """
            User can complete a task if one of these conditions is met:
            1. User is a member of a review group assigned to the task and their group has not completed the task,
            and the task requires approval from all groups (require_approval_for_all_groups=True)
            2. User is a reviewer assigned to the task and has not submitted their review,
            and the task requires all members to complete (require_all_members_to_complete=True)
            3. User is a reviewer or group member assigned to the task and neither require_approval_for_all_groups
            nor require_all_members_to_complete is set to True
            4. User is Quality Risk Manager
            """
            profile = Profile.objects.get(user=self.user)

            # Check if user is Quality Risk Manager (always can complete)
            if self.user.groups.filter(name="Quality/Risk Manager").exists():
                return RepositoryResponse(
                    success=True,
                    message="User is Quality Risk Manager and can complete the task.",
                )

            # Check if user is a reviewer assigned to this task
            is_reviewer = profile in self.task.reviewers.all()

            # Check if user is a member of any review group assigned to this task
            is_group_member = self.task.review_groups.filter(members=profile).exists()

            if not is_reviewer and not is_group_member:
                return RepositoryResponse(
                    success=False,
                    message="User is not a reviewer or group member and cannot complete the task.",
                )

            # Check if user has already completed the task
            user_has_completed_as_reviewer = (
                profile in self.task.reviewers_completed.all()
            )
            user_group_has_completed = self.task.groups_completed.filter(
                members=profile
            ).exists()

            if user_has_completed_as_reviewer or user_group_has_completed:
                return RepositoryResponse(
                    success=False,
                    message="User has already completed the task.",
                )

            # Case 1: Task requires approval from all groups
            if self.task.require_approval_for_all_groups and is_group_member:
                # User can complete if they are in a group that hasn't completed yet
                user_groups_for_task = self.task.review_groups.filter(members=profile)
                can_complete = not self.task.groups_completed.filter(
                    id__in=user_groups_for_task.values_list("id", flat=True)
                ).exists()
                if can_complete:
                    return RepositoryResponse(
                        success=True,
                        message="User can complete the task as a group member (all groups required).",
                    )
                else:
                    return RepositoryResponse(
                        success=False,
                        message="User's group has already completed this task.",
                    )

            # Case 2: Task requires all members to complete
            if self.task.require_all_members_to_complete and is_reviewer:
                # User can complete if they haven't completed yet
                can_complete = not user_has_completed_as_reviewer
                if can_complete:
                    return RepositoryResponse(
                        success=True,
                        message="User can complete the task as a reviewer (all members required).",
                    )
                else:
                    return RepositoryResponse(
                        success=False,
                        message="User has already completed this task as a reviewer.",
                    )

            # Case 3: Neither special requirement is set
            if (
                not self.task.require_approval_for_all_groups
                and not self.task.require_all_members_to_complete
            ):
                return RepositoryResponse(
                    success=True,
                    message="User can complete the task (no special requirements).",
                )

            return RepositoryResponse(
                success=False,
                message="User does not meet the requirements to complete this task.",
            )
        except Profile.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="User profile does not exist.",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=f"Error checking task completion permission: {str(e)}",
            )

    def can_create_task(self):
        """
        Only Quality Risk Manager can create tasks.
        """
        try:
            if self.user.groups.filter(name="Quality/Risk Manager").exists():
                return RepositoryResponse(
                    success=True,
                    message="User is Quality Risk Manager and can create the task.",
                )
            else:
                return RepositoryResponse(
                    success=False,
                    message="User does not have permission to create tasks.",
                )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=f"Error checking task creation permission: {str(e)}",
            )

    def can_approve_task(
        self,
    ):
        """
        Only Quality Risk Manager can approve tasks.
        """
        try:
            if self.user.groups.filter(name="Quality/Risk Manager").exists():
                return RepositoryResponse(
                    success=True,
                    message="User is Quality Risk Manager and can approve the task.",
                )

            else:
                logger.log_info(
                    f"User {self.user.username} attempted to approve task {self.task.id} without permission."
                )
                return RepositoryResponse(
                    success=False,
                    message="User does not have permission to approve this task.",
                )
        except Exception as e:
            logger.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error checking task approval permission: {str(e)}",
            )

    def can_mark_task_completed(self) -> RepositoryResponse:
        """
        Check if a task can be marked as completed based on approval requirements.
        This is separate from individual user permissions.
        """
        # If task requires approval from all groups
        if self.task.require_approval_for_all_groups:
            # All assigned groups must have completed
            assigned_groups = self.task.review_groups.all()
            completed_groups = self.task.groups_completed.all()
            all_groups_completed = (
                assigned_groups.count() == completed_groups.count()
                and assigned_groups.count() > 0
            )
            if all_groups_completed:
                return RepositoryResponse(
                    success=True,
                    message="All required groups have completed the task.",
                )
            else:
                pending_count = assigned_groups.count() - completed_groups.count()
                return RepositoryResponse(
                    success=False,
                    message=f"Task requires all groups to complete. {pending_count} group(s) still pending.",
                )

        # If task requires all members to complete
        if self.task.require_all_members_to_complete:
            # All assigned reviewers must have completed
            assigned_reviewers = self.task.reviewers.all()
            completed_reviewers = self.task.reviewers_completed.all()
            all_reviewers_completed = (
                assigned_reviewers.count() == completed_reviewers.count()
                and assigned_reviewers.count() > 0
            )
            if all_reviewers_completed:
                return RepositoryResponse(
                    success=True,
                    message="All required reviewers have completed the task.",
                )
            else:
                pending_count = assigned_reviewers.count() - completed_reviewers.count()
                return RepositoryResponse(
                    success=False,
                    message=f"Task requires all reviewers to complete. {pending_count} reviewer(s) still pending.",
                )

        # If no special requirements, task can be completed when any reviewer or group submits
        has_submissions = (
            self.task.reviewers_completed.exists()
            or self.task.groups_completed.exists()
        )
        if has_submissions:
            return RepositoryResponse(
                success=True,
                message="Task can be marked as completed (at least one submission received).",
            )
        else:
            return RepositoryResponse(
                success=False,
                message="No submissions have been received for this task.",
            )

    def get_pending_approvals(
        self, task: Type[ReviewProcessTasks]
    ) -> RepositoryResponse:
        """
        Get information about pending approvals for a task.
        Returns a RepositoryResponse with pending groups and reviewers data.
        """
        try:
            pending_groups = []
            pending_reviewers = []

            if task.require_approval_for_all_groups:
                assigned_groups = task.review_groups.all()
                completed_groups = task.groups_completed.all()
                pending_groups = assigned_groups.exclude(
                    id__in=completed_groups.values_list("id", flat=True)
                )

            if task.require_all_members_to_complete:
                assigned_reviewers = task.reviewers.all()
                completed_reviewers = task.reviewers_completed.all()
                pending_reviewers = assigned_reviewers.exclude(
                    id__in=completed_reviewers.values_list("id", flat=True)
                )

            completion_status = self.can_mark_task_completed(task)

            data = {
                "pending_groups": list(pending_groups.values("id", "title")),
                "pending_reviewers": list(
                    pending_reviewers.values(
                        "id", "user__first_name", "user__last_name"
                    )
                ),
                "can_complete": completion_status.success,
                "completion_message": completion_status.message,
            }

            total_pending = len(data["pending_groups"]) + len(data["pending_reviewers"])

            if total_pending == 0:
                message = "No pending approvals for this task."
            else:
                message = f"Task has {total_pending} pending approval(s)."

            return RepositoryResponse(
                success=True,
                message=message,
                data=data,
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=f"Error retrieving pending approvals: {str(e)}",
                data=None,
            )

    def get_permissions(self) -> RepositoryResponse:
        """
        Get all permissions related to the task for the user.
        Returns a dictionary with permission checks.
        """
        try:
            return RepositoryResponse(
                success=True,
                message="Permissions retrieved successfully.",
                data={
                    "can_complete_task": self.can_complete_task().success,
                    "can_approve_task": self.can_approve_task().success,
                    "can_mark_task_completed": self.can_mark_task_completed().success,
                    "can_create_task": self.can_create_task().success,
                },
            )
        except Exception as e:
            logger.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Error retrieving task permissions: {str(e)}",
            )
