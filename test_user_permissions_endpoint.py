#!/usr/bin/env python
"""
Test script for the new user permissions by app endpoint.
"""

import requests
import json
import sys


def test_user_permissions_endpoint():
    """
    Test the new /accounts/user-permissions/ endpoint
    """
    base_url = "http://127.0.0.1:8000"

    # Test endpoint without authentication (should fail)
    print("Testing endpoint without authentication...")
    response = requests.get(f"{base_url}/api/accounts/user-permissions/")
    print(f"Status Code: {response.status_code}")

    try:
        print(f"Response: {response.json()}")
    except:
        print(f"Response Text: {response.text}")

    if response.status_code == 401 or response.status_code == 403:
        print("✓ Endpoint correctly requires authentication\n")
    elif response.status_code == 404:
        print("✗ Endpoint not found - check URL configuration\n")
    else:
        print(f"✗ Unexpected status code: {response.status_code}\n")

    # Test the API root to see if URLs are working
    print("Testing API accounts root...")
    response = requests.get(f"{base_url}/api/accounts/")
    print(f"API Accounts Root Status Code: {response.status_code}")

    # For now, let's just test that the endpoint is accessible
    print("To test with real data, you would need to:")
    print("1. Create a user")
    print("2. Assign some permissions to the user")
    print("3. Get an authentication token")
    print("4. Make an authenticated request")


if __name__ == "__main__":
    test_user_permissions_endpoint()
