#!/usr/bin/env python3
"""
End-to-end test for the document upload API endpoint
"""
import os
import sys
import django
from django.test import TestCase, Client
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth import get_user_model
import json

# Add the project directory to Python path
sys.path.insert(0, "/Users/<USER>/Desktop/projects/quality-control/q-control-backend")

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
django.setup()

from adverse_drug_reaction.models import AdverseDrugReaction
from base.models import Facility
from documents.models import Document


def create_test_file():
    """Create a test file for upload"""
    content = b"This is a test document content for upload testing via API endpoint."
    return SimpleUploadedFile(
        name="api_test_document.txt", content=content, content_type="text/plain"
    )


def test_api_endpoint():
    """Test the actual API endpoint for document upload"""
    print("Testing API endpoint for document upload...")

    try:
        # Create test client
        client = Client()

        # Get or create a test user
        User = get_user_model()
        user = User.objects.first()
        if not user:
            print("Creating test user...")
            user = User.objects.create_user(
                username="testuser", email="<EMAIL>", password="testpass123"
            )

        # Get or create a test facility
        facility = Facility.objects.first()
        if not facility:
            print("Creating test facility...")
            facility = Facility.objects.create(
                name="Test Facility", location="Test Location"
            )

        # Get or create a test incident
        incident = AdverseDrugReaction.objects.first()
        if not incident:
            print("Creating test incident...")
            incident = AdverseDrugReaction.objects.create(
                report_facility=facility, created_by=user, status="draft"
            )

        print(f"Using incident ID: {incident.id}")

        # Create test file
        test_file = create_test_file()

        # Login user (simulate authentication)
        client.force_login(user)

        # Test the API endpoint
        url = f"/api/incidents/adverse-drug-reaction/{incident.id}/documents/"

        response = client.post(url, data={"files": [test_file]}, format="multipart")

        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.content}")

        if response.status_code == 201:
            response_data = response.json()
            print(
                f"Success! Response data: {json.dumps(response_data, indent=2, default=str)}"
            )
            return True
        else:
            print(f"API call failed with status {response.status_code}")
            return False

    except Exception as e:
        print(f"Error testing API endpoint: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_document_retrieval():
    """Test retrieving documents from the API"""
    print("\nTesting document retrieval...")

    try:
        client = Client()

        # Get test incident
        incident = AdverseDrugReaction.objects.first()
        if not incident:
            print("No incident found for testing")
            return False

        # Get test user
        User = get_user_model()
        user = User.objects.first()
        if not user:
            print("No user found for testing")
            return False

        client.force_login(user)

        # Test GET endpoint
        url = f"/api/incidents/adverse-drug-reaction/{incident.id}/documents/"
        response = client.get(url)

        print(f"GET Response status: {response.status_code}")
        print(f"GET Response content: {response.content}")

        if response.status_code == 200:
            response_data = response.json()
            print(
                f"Documents retrieved: {json.dumps(response_data, indent=2, default=str)}"
            )
            return True
        else:
            print(f"GET request failed with status {response.status_code}")
            return False

    except Exception as e:
        print(f"Error testing document retrieval: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Starting API endpoint tests...")
    print("=" * 50)

    # Test 1: API endpoint upload
    upload_ok = test_api_endpoint()

    # Test 2: Document retrieval
    retrieval_ok = test_document_retrieval()

    print("\n" + "=" * 50)
    print("API TEST RESULTS:")
    print(f"Document Upload API: {'✓' if upload_ok else '✗'}")
    print(f"Document Retrieval API: {'✓' if retrieval_ok else '✗'}")

    if all([upload_ok, retrieval_ok]):
        print(
            "\n🎉 All API tests passed! The document upload functionality is working correctly."
        )
    else:
        print("\n❌ Some API tests failed. Please check the errors above.")
